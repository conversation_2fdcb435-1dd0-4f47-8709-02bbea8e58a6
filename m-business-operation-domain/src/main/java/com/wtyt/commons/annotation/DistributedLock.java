package com.wtyt.commons.annotation;

import java.lang.annotation.*;

/**
 * @author: <EMAIL>
 * @date: 2019年12月03日 10时11分
 */
@Inherited
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DistributedLock {

    /**
     * 锁定key、当未指定时按照如下顺序生成
     * 当resource存在时、通过resource来生成lockKey
     * 通过userId生成
     * 通过session生成
     * 通过随机数生成、即无锁
     * @return
     */
    String lockKey() default "";

    /**
     * 锁定资源
     * 该资源是指本次请求requestBody当中json的第一层key且key的value需要是字符串类型、默认为userId
     * @return
     */
    String resource() default "";

    /**
     * 锁定提示信息
     * @return
     */
    String lockHint() default "该操作已被锁定、请稍后";

    /**
     * 等待时间、默认0毫秒
     * @return
     */
    long awaitTime() default 0L;

    /**
     * 释放时间
     * 分布式锁的释放时间、默认0毫秒、autoRelease为false的时候该值必须大于0
     * @return
     */
    long releaseTime() default 0L;

    /**
     * 是否自动释放
     * true:执行完自动释放
     * false:根据过期时间释放
     * @return
     */
    boolean autoRelease() default true;
}
