<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.ReviewsSubjectMapper">

    <select id="getReviewsSubject" resultType="com.wtyt.reviews.bean.ReviewsSubjectBean">
        SELECT
            T.PM_REVIEWS_SUBJECT_ID pmReviewsSubjectId,
            T.ORG_ID orgId,
            T.SUBJECT_TYPE subjectType,
            T.SUBJECT_ID subjectId,
            T.REVIEWS_TARGET reviewsTarget,
            T.REVIEWS_TARGET_ID reviewsTargetId,
            T.REVIEWS_TARGET_NAME reviewsTargetName,
            T.REVIEWS_TARGET_MOBILE reviewsTargetMobile
        FROM T_PM_REVIEWS_SUBJECT T
        WHERE T.IS_DEL = 0
            AND T.PM_REVIEWS_SUBJECT_ID = #{pmReviewsSubjectId}
    </select>

    <insert id="insertReviewsSubject">
        INSERT INTO T_PM_REVIEWS_SUBJECT (
            PM_REVIEWS_SUBJECT_ID,
            ORG_ID,
            SUBJECT_TYPE,
            SUBJECT_ID,
            REVIEWS_TARGET,
            REVIEWS_TARGET_ID,
            REVIEWS_TARGET_NAME,
            REVIEWS_TARGET_MOBILE
        ) VALUES (
            #{pmReviewsSubjectId},
            #{orgId},
            #{subjectType},
            #{subjectId},
            #{reviewsTarget},
            #{reviewsTargetId},
            #{reviewsTargetName},
            #{reviewsTargetMobile}
        )
    </insert>

    <select id="getReviewsSubjectByOrgSubject" resultType="com.wtyt.reviews.bean.ReviewsSubjectBean">
        SELECT
            T.PM_REVIEWS_SUBJECT_ID pmReviewsSubjectId,
            T.ORG_ID orgId,
            T.SUBJECT_TYPE subjectType,
            T.SUBJECT_ID subjectId,
            T.REVIEWS_TARGET reviewsTarget,
            T.REVIEWS_TARGET_ID reviewsTargetId,
            T.REVIEWS_TARGET_NAME reviewsTargetName,
            T.REVIEWS_TARGET_MOBILE reviewsTargetMobile
        FROM T_PM_REVIEWS_SUBJECT T
        WHERE T.IS_DEL = 0
            AND T.SUBJECT_ID = #{subjectId}
            AND T.SUBJECT_TYPE = #{subjectType}
    </select>

    <update id="updateReviewsSubjectDelete">
        UPDATE T_PM_REVIEWS_SUBJECT T
        SET T.IS_DEL = 1,
            T.REMARK = #{remark},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
            AND T.PM_REVIEWS_SUBJECT_ID = #{pmReviewsSubjectId}
    </update>

    <update id="updateReviewsSubjectRecovery">
        UPDATE T_PM_REVIEWS_SUBJECT T
        SET T.IS_DEL = 0,
            T.REMARK = '运输任务恢复',
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 1
            AND T.REMARK = '运输任务删除'
            AND T.SUBJECT_ID IN
            <foreach collection="subjectIdList" item="subjectId" open="(" separator="," close=")">
                #{subjectId}
            </foreach>
            AND T.SUBJECT_TYPE = #{subjectType}
    </update>

    <update id="updateReviewsSubjectTarget">
        UPDATE T_PM_REVIEWS_SUBJECT T
        SET T.REVIEWS_TARGET = #{reviewsTarget},
            T.REVIEWS_TARGET_ID = #{reviewsTargetId},
            T.REVIEWS_TARGET_NAME = #{reviewsTargetName},
            T.REVIEWS_TARGET_MOBILE = #{reviewsTargetMobile},
            T.REMARK = '更新评价对象',
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
            AND T.PM_REVIEWS_SUBJECT_ID = #{pmReviewsSubjectId}
    </update>

</mapper>
