<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskCusFieldConfMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTaskCusFieldConfBean" id="BoConfTaskCusFieldMap">
        <result property="boTaskCusFieldConfId" column="BO_TASK_CUS_FIELD_CONF_ID" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
<!--        <result property="companyId" column="COMPANY_ID" jdbcType="VARCHAR"/>-->
        <result property="fieldKey" column="FIELD_KEY" jdbcType="VARCHAR"/>
        <result property="fieldName" column="FIELD_NAME" jdbcType="VARCHAR"/>
        <result property="isRequired" column="IS_REQUIRED" jdbcType="VARCHAR"/>
        <result property="isMemory" column="IS_MEMORY" jdbcType="VARCHAR"/>
        <result property="renderConfig" column="RENDER_CONFIG" jdbcType="VARCHAR"/>
        <result property="sortNo" column="SORT_NO" jdbcType="VARCHAR"/>
        <result property="categoryType" column="CATEGORY_TYPE" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryByOrgId" resultMap="BoConfTaskCusFieldMap">
        SELECT
            BO_TASK_CUS_FIELD_CONF_ID,
            FIELD_KEY,
            FIELD_NAME,
            IS_REQUIRED,
            IS_MEMORY,
            RENDER_CONFIG,
            CATEGORY_TYPE
        FROM
        T_BO_TASK_CUS_FIELD_CONF
        WHERE
        ORG_ID = #{orgId}
        AND CATEGORY_TYPE=#{categoryType}
        AND IS_DEL = 0
        ORDER BY SORT_NO ASC,FIELD_KEY ASC
    </select>

    <update id="deleteByIds">
        UPDATE T_BO_TASK_CUS_FIELD_CONF SET IS_DEL =1,LAST_MODIFIED_TIME =SYSDATE WHERE BO_TASK_CUS_FIELD_CONF_ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateById" parameterType="com.wtyt.dao.bean.syf.BoTaskCusFieldConfBean">
        UPDATE T_BO_TASK_CUS_FIELD_CONF SET FIELD_NAME =#{fieldName},IS_REQUIRED =#{isRequired},IS_MEMORY=#{isMemory},SORT_NO =#{sortNo},LAST_MODIFIED_TIME =SYSDATE
        WHERE BO_TASK_CUS_FIELD_CONF_ID =#{boTaskCusFieldConfId}
    </update>

    <insert id="insert" parameterType="com.wtyt.dao.bean.syf.BoTaskCusFieldConfBean">
        INSERT INTO T_BO_TASK_CUS_FIELD_CONF
        (BO_TASK_CUS_FIELD_CONF_ID, ORG_ID, FIELD_KEY, FIELD_NAME, IS_REQUIRED, IS_MEMORY, CATEGORY_TYPE, SORT_NO)
        VALUES(#{boTaskCusFieldConfId}, #{orgId}, #{fieldKey}, #{fieldName}, #{isRequired}, #{isMemory}, #{categoryType}, #{sortNo})
    </insert>

</mapper>

