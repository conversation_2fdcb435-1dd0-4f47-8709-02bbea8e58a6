package com.wtyt.commons.toolkits;

import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.money.commons.exception.BaseTipException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.function.BiFunction;

@Component
public class MybatisBatchUtil {

    @Autowired
    @Qualifier("syfSqlSessionFactory")
    private SqlSessionFactory syfSqlSessionFactory;

    /**
     * 批量处理修改或者插入
     *
     * @param data        需要被处理的数据
     * @param mapperClass Mybatis的Mapper类
     * @param function    自定义处理逻辑
     * @return int 影响的总行数
     */
    public <T, U> int batchUpdateOrInsert(SqlSessionFactory sqlSessionFactory, List<T> data, Class<U> mapperClass, BiFunction<T, U, Integer> function, int batchSize) throws UnifiedBusinessException {
        int i = 1;
        int result = 0;
        SqlSession batchSqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH);
        try {
            U mapper = batchSqlSession.getMapper(mapperClass);
            int size = data.size();
            for (T element : data) {
                Integer res = function.apply(element, mapper);
                result += res.intValue();
                if ((i % batchSize == 0) || i == size) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            // 非事务环境下强制commit，事务情况下该commit相当于无效
            batchSqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
        } catch (Exception e) {
            if (!TransactionSynchronizationManager.isSynchronizationActive()) {
                //没有事务直接回滚
                batchSqlSession.rollback();
            }
            throw new UnifiedBusinessException(e.getMessage(), e);
        } finally {
            batchSqlSession.close();
        }
        return result;
    }

    /**
     * 批量处理修改或者插入
     * 默认syf库，默认batchSize=1000
     * @param data 需要被处理的数据
     * @param mapperClass Mybatis的Mapper类
     * @param function 自定义处理逻辑
     * @return
     * @param <T>
     * @param <U>
     * @throws UnifiedBusinessException
     */
    public <T, U> int batchUpdateOrInsertSyf(List<T> data, Class<U> mapperClass, BiFunction<T, U, Integer> function) throws UnifiedBusinessException {
        if(CollectionUtils.isEmpty(data)){
            return 0;
        }
        return this.batchUpdateOrInsert(syfSqlSessionFactory, data, mapperClass, function, 1000);
    }
}
