<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransNodeDictMapper">

    <insert id="insertBoTransNodeDict" parameterType="BoTransNodeDictBean">
        INSERT INTO T_BO_TRANS_NODE_DICT(
                        BO_TRANS_NODE_DICT_ID,                
                        NODE_ID,                
                        NODE_NAME,                
                        <if test="hybNodeName != null and hybNodeName !=''">
            HYB_NODE_NAME,
        </if>
        
                        <if test="pcNodeName != null and pcNodeName !=''">
            PC_NODE_NAME,
        </if>
        
                        IS_DEL,                
                        CREATED_TIME,                
                        LAST_MODIFIED_TIME,                
                        <if test="note != null and note !=''">
            NOTE,
        </if>
        
                        <if test="klbNodeName != null and klbNodeName !=''">
            KLB_NODE_NAME
        </if>
        
        )values(
                            #{boTransNodeDictId},                

                            #{nodeId},                

                            #{nodeName},                

                        <if test="hybNodeName != null and hybNodeName !=''">
        #{hybNodeName},
        </if>
        

                        <if test="pcNodeName != null and pcNodeName !=''">
        #{pcNodeName},
        </if>
        

                            #{isDel},                

                            #{createdTime},                

                            #{lastModifiedTime},                

                        <if test="note != null and note !=''">
        #{note},
        </if>
        

                        <if test="klbNodeName != null and klbNodeName !=''">
        #{klbNodeName}
        </if>
        

        )
    </insert>
    
    
    <select id="queryAllNodeDictInfos" resultType="com.wtyt.dao.bean.syf.BoTransNodeDictBean">
        select NODE_ID nodeId,Node_Name nodeName,HYB_NODE_NAME hybNodeName,
         PC_NODE_NAME pcNodeName,KLB_NODE_NAME klbNodeName,DZ_NODE_NAME dzNodeName,NODE_TYPE nodeType
         from T_BO_TRANS_NODE_DICT t where t.is_del = 0
    </select>
</mapper>