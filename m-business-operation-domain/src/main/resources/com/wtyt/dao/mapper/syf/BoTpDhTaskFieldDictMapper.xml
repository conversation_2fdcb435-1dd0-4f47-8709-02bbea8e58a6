<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhTaskFieldDictMapper">

    <select id="queryTaskFieldDictListById" resultType="com.wtyt.dao.bean.syf.BoTpDhTaskFieldDictBean">
        SELECT
            BO_TP_DH_TASK_FIELD_DICT_ID boTpDhTaskFieldDictId,
            FIELD_NAME fieldName,
            FIELD_NAME_DESC fieldNameDesc,
            FIELD_TYPE fieldType,
            MAX_LENGTH maxLength,
            INPUT_TYPE inputType,
            NUMBER_PRECISION numberPrecision,
            UNIT_CONFIG unitConfig,
            ALLOW_NOT_REQUIRED allowNotRequired,
            ALLOW_DEFAULT allowDefault
        FROM T_BO_TP_DH_TASK_FIELD_DICT
        WHERE IS_del = 0
        AND BO_TP_DH_TASK_FIELD_DICT_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
