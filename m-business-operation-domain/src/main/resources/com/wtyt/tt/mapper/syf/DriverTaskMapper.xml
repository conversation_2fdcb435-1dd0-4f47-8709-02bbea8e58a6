<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.tt.mapper.syf.DriverTaskMapper">



    <select id="list" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.ORG_ID orgId,
            TO_CHAR(t.START_TIME, 'yyyy-MM-dd Hh24:mi:ss') startTime,
            ta.ORG_ID allocateOrgId,
            t.state,
            t.HYB_STATE hybState,
            NVL(t.SETTLE_TYPE, t.SETTLE_MODE) settleType,
            t.MOBILE_NO mobileNo,
            t.DRIVER_ID_CARD driverIdCard,
            t.CART_BADGE_NO cartBadgeNo,
            t.CART_BADGE_COLOR cartBadgeColor
        FROM
            T_BO_TRANS_TASK t
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE ta ON ta.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND ta.IS_DEL=0
        WHERE
            t.IS_DEL = 0
            AND(
                1=0
                <if test="mobileNo!=null and mobileNo!=''">
                    OR t.MOBILE_NO = #{mobileNo}
                </if>
                <if test="driverIdCard!=null and driverIdCard!=''">
                    OR t.DRIVER_ID_CARD = #{driverIdCard}
                </if>
                <if test="cartBadgeNo!=null and cartBadgeNo!=''">
                     OR (
                        t.CART_BADGE_NO = #{cartBadgeNo}
                        <if test="cartBadgeColor!=null and cartBadgeColor!=''">
                            AND (t.CART_BADGE_COLOR = #{cartBadgeColor} OR t.CART_BADGE_COLOR IS NULL)
                        </if>
                     )
                </if>
            )
            <if test="state!=null and state.size()>0">
                AND t.STATE IN
                <foreach collection="state" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="settleType!=null and settleType.size()>0">
                AND NVL(t.SETTLE_TYPE, t.SETTLE_MODE) IN
                <foreach collection="settleType" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="hybState!=null and hybState!=''">
                <choose>
                    <when test="hybState == '0'.toString()">
                        AND t.HYB_STATE = 0
                    </when>
                    <otherwise>
                        AND t.HYB_STATE != 0
                    </otherwise>
                </choose>
            </if>
            <if test="createdTimeStart !=null and createdTimeStart!=''">
                AND t.CREATED_TIME &gt;= to_date(#{createdTimeStart},'yyyy-MM-dd')
            </if>
            ORDER BY t.CREATED_TIME DESC
    </select>
</mapper>
