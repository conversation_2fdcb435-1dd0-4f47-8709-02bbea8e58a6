<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardCompleteMapper">
    <update id="updateBoardComplete">
        MERGE INTO T_BO_BOARD_COMPLETE A
        USING (
        <foreach collection="list" index="index" item="item" open=""
                 close="" separator="union">
            SELECT #{item.boBoardCompleteId} BO_BOARD_COMPLETE_ID,
             #{item.boBusinessLineId} BO_BUSINESS_LINE_ID,
             #{item.loadingTonnage} LOADING_TONNAGE,
             #{item.goodsAmountType} GOODS_AMOUNT_TYPE,
             #{item.completeDate} COMPLETE_DATE FROM DUAL
        </foreach>
        ) B
        ON (A.BO_BUSINESS_LINE_ID = B.BO_BUSINESS_LINE_ID AND A.GOODS_AMOUNT_TYPE = B.GOODS_AMOUNT_TYPE AND A.COMPLETE_DATE = TO_DATE(B.COMPLETE_DATE,'YYYY-MM-DD') AND A.IS_DEL=0)
        WHEN MATCHED THEN
        UPDATE SET
        A.LOADING_TONNAGE = B.LOADING_TONNAGE,
        A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
        INSERT (BO_BOARD_COMPLETE_ID,
        BO_BUSINESS_LINE_ID,
        LOADING_TONNAGE,
        GOODS_AMOUNT_TYPE,
        COMPLETE_DATE)
        VALUES
        (B.BO_BOARD_COMPLETE_ID,
        B.BO_BUSINESS_LINE_ID,
        B.LOADING_TONNAGE,
        B.GOODS_AMOUNT_TYPE,
        TO_DATE(B.COMPLETE_DATE,'YYYY-MM-DD'))
    </update>

    <select id="queryBoardCompleteList" resultType="com.wtyt.board.bean.BoardCompleteBean">
        SELECT
            BO_BUSINESS_LINE_ID boBusinessLineId,
            TO_CHAR(COMPLETE_DATE, 'YYYY-MM-DD') completeDate,
            GOODS_AMOUNT_TYPE goodsAmountType,
            TO_CHAR(NVL(SUM(LOADING_TONNAGE), 0), 'FM999999990.0000') loadingTonnage
        FROM
            T_BO_BOARD_COMPLETE
        WHERE
            IS_DEL = 0 AND
            COMPLETE_DATE &lt;= to_date(#{endTime},'YYYY-MM-DD')
            AND COMPLETE_DATE >= to_date(#{beginTime},'YYYY-MM-DD')
            AND BO_BUSINESS_LINE_ID IN
            <foreach collection="boBusinessLineIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            AND GOODS_AMOUNT_TYPE IN
            <foreach collection="goodsAmountTypes" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        GROUP BY
            BO_BUSINESS_LINE_ID,
            TO_CHAR(COMPLETE_DATE, 'YYYY-MM-DD'),
            GOODS_AMOUNT_TYPE
    </select>


</mapper>
