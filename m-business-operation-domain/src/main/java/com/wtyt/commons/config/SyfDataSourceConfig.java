package com.wtyt.commons.config;

import com.wtyt.common.toolkits.SpringContextToolkit;
import com.wtyt.commons.consts.DataSourceConstons;
import com.wtyt.security.wrapper.druid.DruidDataSourceWrapperFactory;
import com.wtyt.security.wrapper.druid.enums.DBOperTypeEnum;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.SpringBootVFS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.interceptor.TransactionInterceptor;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * SYF数据源配置
 * 
 * <AUTHOR> -- 宋浩
 *
 */
@Configuration
@MapperScan(basePackages = "com.wtyt.**.mapper.syf", sqlSessionTemplateRef = "syfSqlSessionTemplate")
public class SyfDataSourceConfig {

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        SpringContextToolkit.initApplicationContext(applicationContext);
    }

    @Bean(name = "syfDataSource")
    public DataSource getDataSource() throws Exception {
        return DruidDataSourceWrapperFactory.createDataSource(DataSourceConstons.NAMESPACE_SYF, DataSourceConstons.PREFIX_SYF, DBOperTypeEnum.READ_WRITE);
    }
    
    @Bean(name = "syfTransactionManager")
    public DataSourceTransactionManager setTransactionManager(@Qualifier("syfDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "syfSqlSessionFactory")
    public SqlSessionFactory setSqlSessionFactory(@Qualifier("syfDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:com/wtyt/**/mapper/syf/*Mapper.xml"));
        bean.setConfigLocation(new DefaultResourceLoader().getResource("classpath:config/mybatis-config.xml"));
        bean.setVfs(SpringBootVFS.class);
        return bean.getObject();
    }

    @Bean(name = "syfSqlSessionTemplate")
    public SqlSessionTemplate setSqlSessionTemplate(@Qualifier("syfSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean(name = "syfTransactionInterceptor")
    public TransactionInterceptor setTransactionInterceptor(@Qualifier("syfTransactionManager") DataSourceTransactionManager transactionManager) {
        TransactionInterceptor transactionInterceptor = new TransactionInterceptor();
        transactionInterceptor.setTransactionManager(transactionManager);
        Properties transactionAttributes = new Properties();
        transactionAttributes.setProperty("propagationRequiresNew*", "PROPAGATION_REQUIRES_NEW,-Exception");
        transactionInterceptor.setTransactionAttributes(transactionAttributes);
        return transactionInterceptor;
    }

    @Bean(name = "syfJdbcTemplate")
    public JdbcTemplate setJdbcTemplate(@Qualifier("syfDataSource") DataSource dataSource){
        return new JdbcTemplate(dataSource);
    }
}
