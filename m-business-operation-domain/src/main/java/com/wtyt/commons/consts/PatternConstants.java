package com.wtyt.commons.consts;

import java.util.regex.Pattern;

/**
 * 
 * @ClassName: PatternConstants
 * @Description: 正则常量
 * <AUTHOR>
 * @date 2022年7月26日
 *
 */
public class PatternConstants {
	/**
	 * 大于等于零保留两位小数
	 * 
	 */
	public final static String MONEY_DECIMAL_2="(?:^[1-9]([0-9]+)?(?:\\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\\.[0-9](?:[0-9])?$)";
	/**
	 * 大于等于零保留4位小数
	 * 
	 */
	public final static String MONEY_DECIMAL_4="(?:^[1-9]([0-9]+)?(?:\\.[0-9]{1,4})?$)|(?:^(?:0)$)|(?:^[0-9]\\.[0-9](?:[0-9])?$)";
	
	/**
	 * 运单里程正则匹配，长度16可以包含两位小数
	 */
	public final static String MILEAGE_2="[0-9]{1,14}(\\.{0,1}[0-9]{1,2})?";
	
	
	
	
}
