<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskExportFieldMapper">
    <resultMap type="com.wtyt.dao.bean.syf.BoTaskExportFieldBean" id="BoTaskExportFieldMap">
        <result property="boTaskExportFieldMapId" column="BO_TASK_EXPORT_FIELD_MAP_ID" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="fieldKey" column="FIELD_KEY" jdbcType="VARCHAR"/>
        <result property="fieldName" column="FIELD_NAME" jdbcType="VARCHAR"/>
        <result property="sort" column="SORT" jdbcType="VARCHAR"/>
        <result property="exportWidth" column="EXPORT_WIDTH" jdbcType="VARCHAR"/>
        <result property="transportType" column="TRANSPORT_TYPE" jdbcType="VARCHAR"/>
        <result property="pageElementCodePc" column="PAGE_ELEMENT_CODE_PC" jdbcType="VARCHAR"/>
        <result property="fdCode" column="FD_CODE" jdbcType="VARCHAR"/>
        <result property="scopes" column="SCOPES" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="query" resultMap="BoTaskExportFieldMap">
        SELECT
            FIELD_KEY,
            FIELD_NAME,
            EXPORT_WIDTH,
            PAGE_ELEMENT_CODE_PC,
            SCOPES,
            FD_CODE
        FROM
            T_BO_TASK_EXPORT_FIELD
        WHERE
            IS_DEL = 0
            <if test="orgId!=null and orgId!=''">
                AND ORG_ID = #{orgId}
            </if>
            <if test="transportType!=null and transportType!=''">
                AND TRANSPORT_TYPE = #{transportType}
            </if>
            <if test="configType!=null and configType!=''">
                AND CONFIG_TYPE = #{configType}
            </if>
        ORDER BY SORT ASC, BO_TASK_EXPORT_FIELD_ID ASC
    </select>


</mapper>
