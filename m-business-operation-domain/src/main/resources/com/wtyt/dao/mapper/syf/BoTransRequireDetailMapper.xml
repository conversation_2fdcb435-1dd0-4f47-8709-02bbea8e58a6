<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransRequireDetailMapper">

    <insert id="insertBoTransRequireDetail" parameterType="BoTransRequireDetailBean">
        INSERT INTO T_BO_TRANS_REQUIRE_DETAIL(
                        BO_TRANS_REQUIRE_DETAIL_ID,                
                        BO_TRANS_REQUIR_ID,                
                        <if test="type != null and type !=''">
            TYPE,
        </if>
        
                        CONTENT,                
                        <if test="sort != null and sort !=''">
            SORT,
        </if>
        
                        <if test="keyword != null and keyword !=''">
            KEYWORD,
        </if>
        
                        IS_DEL,                
                        CREATED_TIME,                
                        LAST_MODIFIED_TIME,                
                        <if test="note != null and note !=''">
            NOTE
        </if>
        
        )values(
                            #{boTransRequireDetailId},                

                            #{boTransRequirId},                

                        <if test="type != null and type !=''">
        #{type},
        </if>
        

                            #{content},                

                        <if test="sort != null and sort !=''">
        #{sort},
        </if>
        

                        <if test="keyword != null and keyword !=''">
        #{keyword},
        </if>
        

                            #{isDel},                

                            #{createdTime},                

                            #{lastModifiedTime},                

                        <if test="note != null and note !=''">
        #{note}
        </if>
        

        )
    </insert>

    <select id="getByTransTaskId" resultType="com.wtyt.dao.bean.syf.BoTransRequireDetailBean">
        SELECT
            CATEGORY_CODE categoryCode,
            CATEGORY_NAME categoryName,
            BO_TRANS_REQUIRE_DETAIL_ID boTransRequireDetailId,
            TYPE,
            CONTENT,
            KEYWORD,
            TIME_FLAG timeFlag
        FROM
            T_BO_TRANS_REQUIRE_DETAIL
        WHERE
            IS_DEL = 0
            <if test="boTransTaskId != null and boTransTaskId !=''">
                AND BO_TRANS_TASK_ID = #{boTransTaskId}
            </if>
            <if test="categoryCode != null and categoryCode !=''">
                AND CATEGORY_CODE = #{categoryCode}
            </if>
            <if test="type != null and type !=''">
                AND TYPE = #{type}
            </if>
    </select>
</mapper>