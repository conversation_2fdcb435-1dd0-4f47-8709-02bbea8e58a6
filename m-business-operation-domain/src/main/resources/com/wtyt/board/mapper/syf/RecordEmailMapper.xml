<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.RecordEmailMapper">


    <select id="queryRecordEmailBaseInfo" resultType="com.wtyt.board.bean.RecordEmailInfoBean">
        SELECT
            TBTT.BO_TRANS_TASK_ID boTransTaskId,
            NVL(TBTT.TAX_WAYBILL_ID, TBTTA.TAX_WAYBILL_ID) taxWaybillId,
            TO_CHAR(TBTT.START_TIME, 'YYYY/MM/DD') startTime,
            TBTT.CART_BADGE_NO cartBadgeNo,
            TBTT.DRIVER_NAME driverName,
            TBTT.START_CITY_NAME startCityName,
            TBTT.END_CITY_NAME endCityName,
            TBTT.END_COUNTY_NAME endCountyName,
            TBTT.CAPACITY_TYPE capacityType,
            TBTT.CAPACITY_TYPE_NAME capacityTypeName,
            TBTT.USER_FREIGHT userFreight,
            TBTT.ALL_FREIGHT allFreight,
            TBTT.FREIGHT_INCR freightIncr,
            TBTT.LOSS_FEE lossFee,
            TBTT.BACK_FEE backFee,
            NVL(TBTT.PAY_STATE, 0) payState,
            TO_CHAR(NVL(TBTTF.CONFIG_VALUE, 0) , 'FM999999990.00') fuelCostFee,
            TBTTF.REASON oilCardNo
        FROM T_BO_TRANS_TASK TBTT
        INNER JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.IS_DEL = 0 AND  TBTTE.BO_TRANS_TASK_ID  = TBTT.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TBTTA ON TBTTA.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTA.IS_DEL =0
        LEFT JOIN T_BO_TRANS_TASK_FEE TBTTF ON TBTTF.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTF.IS_DEL = 0 AND TBTTF.CONFIG_KEY = 'fuelCostFee'
        WHERE TBTT.IS_DEL = 0
        AND TBTT.ORG_ID  = #{orgId}
        AND EXISTS(SELECT 1 FROM T_BO_TRANS_NODE_RECORD TBTNR
        WHERE TBTNR.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID
        AND TBTNR.IS_DEL = 0
        AND TBTNR.NODE_ID = 600)
        ORDER BY TBTT.START_TIME ASC
    </select>


    <select id="queryStoragePlace" resultType="java.util.Map">
        SELECT
            TO_CHAR(T.BO_TRANS_TASK_ID) BO_TRANS_TASK_ID,
            MIN(O.STORAGE_PLACE) STORAGE_PLACE
        FROM T_BO_TRANS_ORDER_REL R
        INNER JOIN T_BO_TRANS_TASK T ON R.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND T.IS_DEL = 0
        INNER JOIN T_BO_TRANS_ORDER O ON O.BO_TRANS_ORDER_ID = R.BO_TRANS_ORDER_ID AND O.IS_DEL = 0 AND O.STORAGE_PLACE IS NOT NULL
        WHERE R.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        GROUP BY T.BO_TRANS_TASK_ID
    </select>
</mapper>