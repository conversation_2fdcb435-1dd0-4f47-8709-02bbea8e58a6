<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoDhConfigMapper">

    <select id="queryDhConfigByGroupCode" resultType="com.wtyt.dao.bean.syf.BoDhConfigBean">
        SELECT
        BO_DH_CONFIG_ID boDhConfigId,
        BUSINESS_TYPE businessType,
        GROUP_TYPE groupType,
        GROUP_CODE groupCode,
        HEADER_ROW_NUM headerRowNum,
        DATA_START_ROW_NUM dataStartRowNum
        FROM
            T_BO_DH_CONFIG
        WHERE
        is_del = 0
        AND BUSINESS_TYPE =#{businessType} AND GROUP_TYPE =#{groupType} and GROUP_CODE =#{groupCode}
        AND VALID_STATE = 1
    </select>


    <select id="getDhConfigInfoById" resultType="com.wtyt.dao.bean.syf.BoDhConfigBean">
        SELECT
            BO_DH_CONFIG_ID boDhConfigId,
            BUSINESS_TYPE businessType,
            GROUP_TYPE groupType,
            GROUP_CODE groupCode,
            HEADER_ROW_NUM headerRowNum,
            DATA_START_ROW_NUM dataStartRowNum
        FROM
            T_BO_DH_CONFIG
        WHERE
        is_del = 0 AND BO_DH_CONFIG_ID =#{boTpDhConfigId} AND VALID_STATE = 1
    </select>



    <insert id="insertBoDhConfig">
        INSERT INTO T_BO_DH_CONFIG(BO_DH_CONFIG_ID, BUSINESS_TYPE,GROUP_TYPE,GROUP_CODE, HEADER_ROW_NUM, DATA_START_ROW_NUM,  VALID_STATE, OPT_USER_ID)
        VALUES(#{boDhConfigId}, #{businessType},#{groupType}, #{groupCode}, #{headerRowNum}, #{dataStartRowNum}, #{validState}, #{optUserId})
    </insert>

    <update id="invalidAllConfig">
        UPDATE T_BO_DH_CONFIG SET VALID_STATE = 0, LAST_MODIFIED_TIME = SYSDATE
                              WHERE VALID_STATE = 1
                                AND IS_DEL = 0
                                AND BUSINESS_TYPE =#{businessType} AND GROUP_TYPE =#{groupType} and GROUP_CODE =#{groupCode}
    </update>










</mapper>
