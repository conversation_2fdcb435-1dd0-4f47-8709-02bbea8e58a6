<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoTransTaskNodeAlarmMapper">
    
    <select id="getUploadLoadingPicTimeoutList" resultType="BoXxlJobAlarmBean">
        SELECT *
        FROM (
            SELECT
                R.TAX_WAYBILL_ID taxWaybillId,
                tt.ORG_ID orgId,
                tt.CART_BADGE_NO cartBadgeNo,
                tt.DRIVER_NAME driverName,
                tt.START_PROVINCE_NAME startProvinceName,
                tt.END_PROVINCE_NAME endProvinceName,
                tt.START_CITY_NAME startCityName,
                tt.END_CITY_NAME endCityName,
                tt.START_COUNTY_NAME startCountyName,
                tt.END_COUNTY_NAME endCountyName,
                tt.MOBILE_NO mobileNo,
                tte.TRANS_VOUCHER transVoucher,
                tt.CREATED_USER_ID createdUserId,
                TO_CHAR(R.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
                TO_CHAR(R.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') comparedTime,
                R.CREATED_TIME,
                '510' nodeId,
                A.BO_TRANS_NODE_ALARM_ID boTransNodeAlarmId,
                nvl(A.NODE_DATA_TYPE,'8') nodeDataType,
                '200200' bossNodeCode,
                tt.TAX_WAYBILL_NO taxWaybillNo,
                tt.BO_TRANS_TASK_ID boTransTaskId,
                tt.IS_PARTAKE_OPERATE isPartakeOperate,
                TT.TRANSPORT_TYPE transportType,
                TT.TRANS_MODE transMode,
                TA.TAX_WAYBILL_ID allocateTaxWaybillId,
                ROW_NUMBER() OVER(PARTITION BY R.BO_TRANS_TASK_ID ORDER BY R.CREATED_TIME DESC) RN
            FROM T_BO_TRANS_NODE_RECORD R
            LEFT JOIN T_BO_TRANS_NODE_ALARM A
                ON R.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
                AND A.NODE_ID ='510'
                AND A.ALARM_TYPE = 1
                AND A.NODE_DATA_TYPE = 8
                AND A.IS_DEL = 0,
            T_BO_TRANS_TASK tt
            JOIN T_BO_TRANS_TASK_EXTRA tte ON tte.BO_TRANS_TASK_ID = tt.BO_TRANS_TASK_ID AND tte.IS_DEL =0
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TA ON TA.BO_TRANS_TASK_ID = TT.BO_TRANS_TASK_ID
            WHERE R.BO_TRANS_TASK_ID = tt.BO_TRANS_TASK_ID
                and tt.is_del = 0
                AND tt.CART_BADGE_NO IS NOT NULL
                AND (
                (tt.TRANS_PATTERN = 1 AND R.node_id = '500')
                OR
                (tt.TRANS_PATTERN = 2 AND R.node_id = '400' AND tt.HYB_STATE IN (1, 2, 3))
                )
                AND R.IS_DEL = 0
                <if test="dispatchedTime != null and dispatchedTime.length > 0">
                    AND tte.DISPATCH_CAR_TIME <![CDATA[ < ]]> TO_DATE(#{dispatchedTime}, 'yyyy-mm-dd hh24:mi:ss')
                </if>
                AND tt.ORG_ID IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            ) t
            WHERE RN = 1
                AND CREATED_TIME &lt; SYSDATE
                and not exists (select 1
                from T_BO_TRANS_NODE_RECORD ER
                where t.boTransTaskId = ER.BO_TRANS_TASK_ID
                AND  er.node_id='510'
                AND er.is_del = 0
        )
    </select>
    
    
    <select id="getUploadReceiptPicTimeoutList" resultType="BoXxlJobAlarmBean">
        SELECT *
        FROM (
            SELECT
                R.TAX_WAYBILL_ID taxWaybillId,
                tt.ORG_ID orgId,
                tt.CART_BADGE_NO cartBadgeNo,
                tt.DRIVER_NAME driverName,
                tt.START_PROVINCE_NAME startProvinceName,
                tt.END_PROVINCE_NAME endProvinceName,
                tt.START_CITY_NAME startCityName,
                tt.END_CITY_NAME endCityName,
                tt.START_COUNTY_NAME startCountyName,
                tt.END_COUNTY_NAME endCountyName,
                tt.MOBILE_NO mobileNo,
                tte.TRANS_VOUCHER transVoucher,
                tt.CREATED_USER_ID createdUserId,
                TO_CHAR(R.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
                TO_CHAR(R.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') comparedTime,
                R.CREATED_TIME,
                '800' nodeId,
                A.BO_TRANS_NODE_ALARM_ID boTransNodeAlarmId,
                nvl(A.NODE_DATA_TYPE,'9') nodeDataType,
                '200400' bossNodeCode,
                tt.TAX_WAYBILL_NO taxWaybillNo,
                tt.BO_TRANS_TASK_ID boTransTaskId,
                tt.TRANSPORT_TYPE transportType,
                tt.LOADING_PLACE_NAME loadingPlaceName,
                tt.UNLOADING_PLACE_NAME unloadingPlaceName,
                tt.IS_PARTAKE_OPERATE isPartakeOperate,
                ROW_NUMBER() OVER(PARTITION BY R.BO_TRANS_TASK_ID ORDER BY R.CREATED_TIME DESC) RN
            FROM T_BO_TRANS_NODE_RECORD R
            LEFT JOIN T_BO_TRANS_NODE_ALARM A
                ON R.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
                AND A.NODE_ID ='800'
                AND A.ALARM_TYPE = 1
                AND A.NODE_DATA_TYPE = 9
                AND A.IS_DEL = 0
            JOIN T_BO_TRANS_TASK tt
                ON R.BO_TRANS_TASK_ID = tt.BO_TRANS_TASK_ID
            JOIN T_BO_TRANS_TASK_EXTRA tte ON
                tte.BO_TRANS_TASK_ID = tt.BO_TRANS_TASK_ID AND tte.IS_DEL =0
            WHERE
                tt.is_del = 0
                AND tt.CART_BADGE_NO IS NOT NULL
                AND R.NODE_ID = 650
                AND R.IS_DEL = 0
                AND tt.ORG_ID IN
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            ) t
            WHERE RN = 1
            <choose>
                <when test="scanTime != null and scanTime != ''">
                    <![CDATA[ AND CREATED_TIME < TO_DATE(#{scanTime}, 'yyyy-mm-dd hh24:mi:ss') ]]>
                </when>
                <otherwise>
                    <![CDATA[ AND CREATED_TIME < SYSDATE ]]>
                </otherwise>
            </choose>
            and not exists (select 1
            from T_BO_TRANS_NODE_RECORD ER
            where t.boTransTaskId = ER.BO_TRANS_TASK_ID
            AND  er.node_id='800'
            AND er.is_del = 0
        )
    </select>

    <select id="getUploadReceiptPicTimeoutBean" resultType="com.wtyt.bo.bean.BoXxlJobAlarmBean"
            parameterType="java.lang.String">
        SELECT *
        FROM (
        SELECT
            R.TAX_WAYBILL_ID taxWaybillId,
            tt.ORG_ID orgId,
            tt.CART_BADGE_NO cartBadgeNo,
            tt.DRIVER_NAME driverName,
            tt.START_PROVINCE_NAME startProvinceName,
            tt.END_PROVINCE_NAME endProvinceName,
            tt.START_CITY_NAME startCityName,
            tt.END_CITY_NAME endCityName,
            tt.START_COUNTY_NAME startCountyName,
            tt.END_COUNTY_NAME endCountyName,
            tt.MOBILE_NO mobileNo,
            tte.TRANS_VOUCHER transVoucher,
            tt.CREATED_USER_ID createdUserId,
            TO_CHAR(R.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
            TO_CHAR(R.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') comparedTime,
            R.CREATED_TIME,
            '800' nodeId,
            A.BO_TRANS_NODE_ALARM_ID boTransNodeAlarmId,
            nvl(A.NODE_DATA_TYPE,'9') nodeDataType,
            '200400' bossNodeCode,
            tt.TAX_WAYBILL_NO taxWaybillNo,
            tt.BO_TRANS_TASK_ID boTransTaskId,
            tt.TRANSPORT_TYPE transportType,
            tt.LOADING_PLACE_NAME loadingPlaceName,
            tt.UNLOADING_PLACE_NAME unloadingPlaceName,
            tt.IS_PARTAKE_OPERATE isPartakeOperate,
            ROW_NUMBER() OVER(PARTITION BY R.BO_TRANS_TASK_ID ORDER BY R.CREATED_TIME DESC) RN
        FROM T_BO_TRANS_NODE_RECORD R
        LEFT JOIN T_BO_TRANS_NODE_ALARM A
        ON R.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
        AND A.NODE_ID ='800'
        AND A.ALARM_TYPE = 1
        AND A.NODE_DATA_TYPE = 9
        AND A.IS_DEL = 0
        JOIN T_BO_TRANS_TASK tt
        ON R.BO_TRANS_TASK_ID = tt.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_TASK_EXTRA tte ON
        tte.BO_TRANS_TASK_ID = tt.BO_TRANS_TASK_ID AND tte.IS_DEL =0
        WHERE
            tt.is_del = 0
            AND tt.CART_BADGE_NO IS NOT NULL
            AND R.NODE_ID = 650
            AND R.IS_DEL = 0
            AND tt.BO_TRANS_TASK_ID = #{boTransTaskId}
        ) t
        WHERE RN = 1
            AND CREATED_TIME &lt; SYSDATE
            and not exists (select 1
            from T_BO_TRANS_NODE_RECORD ER
            where t.boTransTaskId = ER.BO_TRANS_TASK_ID
            AND  er.node_id='800'
            AND er.is_del = 0
        )
    </select>


</mapper>