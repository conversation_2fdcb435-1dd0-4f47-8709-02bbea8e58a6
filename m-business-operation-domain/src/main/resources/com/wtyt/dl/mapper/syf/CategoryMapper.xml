<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.CategoryMapper">
    
    <select id="getCategoryList" resultType="com.wtyt.dl.bean.CategoryBean">
        SELECT
            T.DL_CATEGORY_ID dlCategoryId,
            T.BUSINESS_TYPE businessType,
            T.EXTEND_COLUMN_PLUGIN extendColumnPlugin,
            T.CATEGORY_MODE categoryMode,
            T.CATEGORY_NAME categoryName
        FROM T_DL_CATEGORY T
        WHERE T.IS_DEL = 0
            AND T.CATEGORY_MODE = #{categoryMode}
            AND T.BUSINESS_TYPE = #{businessType}
        ORDER BY T.SORT_NO
    </select>

</mapper>
