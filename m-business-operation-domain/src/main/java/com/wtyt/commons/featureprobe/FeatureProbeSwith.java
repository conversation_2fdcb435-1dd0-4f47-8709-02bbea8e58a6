package com.wtyt.commons.featureprobe;


/**
 * 所有功能开关的标识类
 */
public interface FeatureProbeSwith {

    /**
     * FeatureProbe bean name
     * 路歌新大陆
     */
    String FEATURE_PROBE_PROJECT_NAME_LGXDL = "lgxdl";

    /**
     * FeatureProbe bean name
     * 网络货运
     */
    String FEATURE_PROBE_PROJECT_NAME_WLHY = "wlhy";
    /**
     * 运力类型整合
     */
    String FEATURE_PROBE_PROJECT_NAME_ZYGHBKYLJ = "zyghbkylj";

    /**
     * 综合线路自动计算
     */
    String ZH_LINE_AUTO_FREIGHT ="v1.27.159.394_zlAutoFreigh";
    /**
     * 是否前置校验
     */
    String NEED_PRE_RISK ="v3.200.0.225_preBuildRiskCheck";

    String NEW_YZXZ_SETTLEMENT ="v1.1.0.85_settlementOptimize";
    /**
     * 是否新运费结构项
     */
    String NEW_TASK_FEE ="v3.200.0.186_taskFee";
    String TASK_FEE_COMPONENT ="v3.200.0.254_operateFee";
    /**
     * 是否统一运力整合
     */
    String CAPACITY_INTEGRATION ="v1.1.0.39_capacityIntegration";
    /**
     * 回单签收
     */
    String PAPER_RECEIPT_SIGN ="v1.27.159.198_paper";


    /**
     * 订单新建地址经纬度获取接口调用切换功能开关,
     * 当开关功能开关已发布 或者未发布在灰度白名单中时，需要调用base组新增接口5295018（地址维护页面数据需要）
     * 否则 调用1133002 接口获取地址高德解析的经纬度
     */
    String SR_ADDRESS_QUERY ="v1.27.159.131_sr_address";

    /**
     * 司机身份不一致产品优化灰度开关
     */
    String DRIVER_DIFF_OPT ="v1.27.159.201_driverDiffOpt";

    /**
     * 货物数量单位支持自定义功能开关
     */
    String GOODS_AMOUNT_UNIT = "v1.27.159.244_goodsAmountUnit";
    /**
     * 动态表头查询列表慢sql 优化功能开关,
     * 当开关功能开关已发布 或者未发布在灰度白名单中时，使用优化后的sql 查询
     * 否则 维持老的查询语句
     */
    String SR_DH_ORDER_QUERY_OPT ="v1.27.159.346_sr_dh_order_opt";

    /**
     * 订单excel导入文件解析 切换功能开关,
     * 当开关功能开关已发布 或者未发布在灰度白名单中时，需要使用新的导入模式，流式处理数据 ，防止内存溢出
     * 否则使用poi老的文件导入
     */
    String SR_ORDER_EXCEL_CREATE ="v1.27.159.346_sr_order_excel";

    /**
     * 地址纠正时，若合单中的其他订单收货单位+原始地址相同也同时纠正
     */
    String SR_BATCH_CORRECT_ADDRESS = "v1.27.159.392_sr_correctAddr";

    /**
     * 新大陆运输任务列表全部查询优化
     */
    String TASK_LIST_ALL_SEARCH_OPT ="v1.27.159.381_allSearchOpt";

    /**
     * 运输计划列表合单数据合并展示
     */
    String XE_QUANTITY_FROZEN = "v1.27.159.449_QuantityiFrozen";

    /**
     * 自动计算迁移
     */
    String AUTO_CALC_MIGRATE ="v1.27.159.456_autoCalcMigrate";

    /**
     * 运输节点
     */
    String TRANSPORT_NODE ="v1.27.159.470_transportNode";
    /**
     * 约定到场和约定送达时间走ddcs配置
     */
    String NEW_TIME_CONFIG ="v1.27.159.442_validity";

    /**
     * 新大陆-短信成本优化
     */
    String SMS_COST_OPT ="v3.200.0.405_smsCostOpt";
    /**
     * 取消运单
     */
    String CANCEL_WAYBILL ="20250317_cancelWaybill";

    /**
     * 允许拆分订单灰度
     */
    String XE_SPLIT_ORDER_OPT = "v1.27.159.499_Splitorder";


    String XDL_TASK_IDCARDNO = "xdl_task_idCardNo";

    String YZXZ_JD_POP_VIDEO = "yzxz_jdPopVideo";
    String YZXZ_NEW_SWITCH = "v1.27.159.567_operate_manage";

    String TRANS_TASK_EXPORT_REFACTOR = "trans_task_export_refactor";


    /**
     * 接单分享功能扫码引导至婷姐接单
     */

    String TING_JIE_ORDER ="v1.27.159.552_tingjie_order";
}
