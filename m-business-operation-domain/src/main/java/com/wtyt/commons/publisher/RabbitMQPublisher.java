package com.wtyt.commons.publisher;

import com.wtyt.common.toolkits.AlarmToolkit;
import com.wtyt.pmq.rabbitmq.client.si.PMQSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * RabbitMQ推送消息工具
 * <AUTHOR>
 */
@Component
public class RabbitMQPublisher {

    private static Logger log = LoggerFactory.getLogger(RabbitMQPublisher.class.getName());

    @Autowired
    private PMQSender pmqSender;


    /**
     * 推送消息到MQ
     * @param exchangeName
     * @param topicName
     * @param jsonData
     */
    public void pushMQMessage(String exchangeName, String topicName, String jsonData) {
        pushMQMessage(exchangeName, topicName, jsonData, true);
    }

    /**
     * 推送消息到MQ
     *
     * @param exchangeName
     * @param topicName
     * @param jsonData
     */
    public void pushMQMessage(String exchangeName, String topicName, String jsonData, boolean hasTransaction) {
        try {
            pmqSender.basicPublish(exchangeName, topicName, jsonData, hasTransaction);
        } catch (Exception e) {
            log.error("推送失败", e);
            AlarmToolkit.sendServerAlarm("推送失败", e, "");
        }
    }

}
