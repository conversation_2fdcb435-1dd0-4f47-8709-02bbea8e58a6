<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskFeeVrfReMapper">
    <insert id="insertFeeVerifyRecordBatch">
        INSERT INTO T_BO_TRANS_TASK_FEE_VRF_RE
        (BO_TRANS_TASK_FEE_VRF_RE_ID,
        BO_TRANS_TASK_FEE_ID,
        EXPENSE_NAME,
        MODIFY_USER_ID,
        MODIFY_USER_NAME,
        MODIFY_USER_JOB_NAME,
        OLD_VALUE,
        NEW_VALUE,
        MODIFY_TYPE,
        VERIFY_OPT_TYPE,
        VERIFY_FROM_TYPE)
        <foreach collection="list" index="index" item="item" separator="UNION ALL">
            SELECT
            ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()} BO_TRANS_TASK_FEE_VRF_RE_ID,
            #{item.boTransTaskFeeId} BO_TRANS_TASK_FEE_ID,
            #{item.expenseName} EXPENSE_NAME,
            #{item.modifyUserId} MODIFY_USER_ID,
            #{item.modifyUserName} MODIFY_USER_NAME,
            #{item.modifyUserJobName} MODIFY_USER_JOB_NAME,
            #{item.oldValue} OLD_VALUE,
            #{item.newValue} NEW_VALUE,
            #{item.modifyType} MODIFY_TYPE,
            #{item.verifyOptType} VERIFY_OPT_TYPE,
            #{item.verifyFromType} VERIFY_FROM_TYPE
            FROM DUAL
        </foreach>
    </insert>

    <select id="queryVerifyRecordList" resultType="com.wtyt.settle.bean.BoTransTaskFeeVrfReBean">
        SELECT
            BO_TRANS_TASK_FEE_VRF_RE_ID boTransTaskFeeVrfReId,
            BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
            EXPENSE_NAME expenseName,
            MODIFY_USER_ID modifyUserId,
            MODIFY_USER_NAME modifyUserName,
            MODIFY_USER_JOB_NAME modifyUserJobName,
            TO_CHAR(NVL(OLD_VALUE, 0), 'FM999999990.00') oldValue,
            TO_CHAR(NVL(NEW_VALUE, 0), 'FM999999990.00') newValue,
            MODIFY_TYPE modifyType,
            VERIFY_FROM_TYPE verifyFromType,
            TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
        FROM T_BO_TRANS_TASK_FEE_VRF_RE
        WHERE BO_TRANS_TASK_FEE_ID = #{boTransTaskFeeId}
        AND IS_DEL = 0
        AND MODIFY_TYPE = 0
        ORDER BY CREATED_TIME DESC, BO_TRANS_TASK_FEE_VRF_RE_ID DESC
    </select>
</mapper>

