<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskCusFieldMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTaskCusFieldBean" id="BoTaskCusFieldMap">
        <result property="boTaskCusFieldId" column="BO_TASK_CUS_FIELD_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="fieldKey" column="FIELD_KEY" jdbcType="VARCHAR"/>
        <result property="fieldName" column="FIELD_NAME" jdbcType="VARCHAR"/>
        <result property="fieldValue" column="FIELD_VALUE" jdbcType="VARCHAR"/>
        <result property="isRequired" column="IS_REQUIRED" jdbcType="VARCHAR"/>
        <result property="isMemory" column="IS_MEMORY" jdbcType="VARCHAR"/>
        <result property="renderConfig" column="RENDER_CONFIG" jdbcType="VARCHAR"/>
        <result property="categoryType" column="CATEGORY_TYPE" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryByTaskId" resultMap="BoTaskCusFieldMap">
        SELECT
            BO_TASK_CUS_FIELD_ID,
            FIELD_KEY,
            FIELD_NAME,
            FIELD_VALUE,
            IS_REQUIRED,
            IS_MEMORY,
            CATEGORY_TYPE
        FROM
            T_BO_TASK_CUS_FIELD
        WHERE
            BO_TRANS_TASK_ID = #{taskId}
            AND IS_DEL = 0
        ORDER BY FIELD_KEY ASC
    </select>

    <select id="queryByTaskIdAndCategoryType" resultMap="BoTaskCusFieldMap">
        SELECT
        BO_TASK_CUS_FIELD_ID,
        FIELD_KEY,
        FIELD_NAME,
        FIELD_VALUE,
        IS_REQUIRED,
        IS_MEMORY,
        RENDER_CONFIG,
        CATEGORY_TYPE
        FROM
        T_BO_TASK_CUS_FIELD
        WHERE
        BO_TRANS_TASK_ID = #{taskId}
        AND IS_DEL = 0
        AND CATEGORY_TYPE=#{categoryType}
        ORDER BY SORT_NO ASC,FIELD_KEY ASC
    </select>

    <insert id="saveBatch" parameterType="com.wtyt.dao.bean.syf.BoTaskCusFieldBean">
        INSERT INTO T_BO_TASK_CUS_FIELD
        (BO_TASK_CUS_FIELD_ID, BO_TRANS_TASK_ID, FIELD_KEY, FIELD_NAME, FIELD_VALUE, IS_REQUIRED, IS_MEMORY,SORT_NO,RENDER_CONFIG,CATEGORY_TYPE)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
        SELECT
        #{item.boTaskCusFieldId} boTaskCusFieldId,
        #{item.boTransTaskId} boTransTaskId,
        #{item.fieldKey} fieldKey,
        #{item.fieldName} fieldName,
        #{item.fieldValue} fieldValue,
        #{item.isRequired} isRequired,
        #{item.isMemory} isMemory,
        #{item.sortNo} sortNo,
        #{item.renderConfig} renderConfig,
        #{item.categoryType} categoryType
        FROM
        DUAL
        </foreach>
    </insert>

    <select id="getTaskCusFieldListByTaskIdList" resultMap="BoTaskCusFieldMap">
        SELECT
            T.BO_TRANS_TASK_ID,
            T.FIELD_KEY,
            T.FIELD_NAME,
            T.FIELD_VALUE
        FROM T_BO_TASK_CUS_FIELD T
        WHERE T.IS_DEL = 0
        <choose>
            <when test="boTransTaskIdList != null and boTransTaskIdList.size() > 0">
                AND T.BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
                    #{boTransTaskId}
                </foreach>
            </when>
            <otherwise>
                AND T.BO_TRANS_TASK_ID = -1
            </otherwise>
        </choose>
    </select>

    <update id="updateBatch">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE
                T_BO_TASK_CUS_FIELD
            SET
                FIELD_VALUE = #{item.fieldValue},
                SORT_NO = #{item.sortNo},
                LAST_MODIFIED_TIME = SYSDATE
            WHERE
                BO_TASK_CUS_FIELD_ID = #{item.boTaskCusFieldId}
        </foreach>
    </update>

    <insert id="save">
        INSERT INTO T_BO_TASK_CUS_FIELD
        (BO_TASK_CUS_FIELD_ID, BO_TRANS_TASK_ID, FIELD_KEY, FIELD_NAME, FIELD_VALUE, IS_REQUIRED, IS_MEMORY,SORT_NO,RENDER_CONFIG,CATEGORY_TYPE)
        SELECT
        #{boTaskCusFieldId} boTaskCusFieldId,
        #{boTransTaskId} boTransTaskId,
        #{fieldKey} fieldKey,
        #{fieldName} fieldName,
        #{fieldValue} fieldValue,
        #{isRequired} isRequired,
        #{isMemory} isMemory,
        #{sortNo} sortNo,
        #{renderConfig} renderConfig,
        #{categoryType} categoryType
        FROM
        DUAL
        WHERE NOT EXISTS (SELECT 1 FROM T_BO_TASK_CUS_FIELD WHERE BO_TRANS_TASK_ID=#{boTransTaskId} AND IS_DEL=0 AND FIELD_KEY=#{fieldKey})
    </insert>

    <select id="queryByTaskIdAndFieldKey" resultMap="BoTaskCusFieldMap">
        SELECT
        BO_TASK_CUS_FIELD_ID,
        FIELD_KEY,
        FIELD_NAME,
        FIELD_VALUE,
        IS_REQUIRED,
        IS_MEMORY,
        CATEGORY_TYPE
        FROM
        T_BO_TASK_CUS_FIELD
        WHERE
        BO_TRANS_TASK_ID = #{taskId}
        AND FIELD_KEY = #{fieldKey}
        AND IS_DEL = 0
    </select>

    <select id="queryBatch" resultMap="BoTaskCusFieldMap">
        SELECT
            T.BO_TRANS_TASK_ID,
            T.FIELD_KEY,
            T.FIELD_NAME,
            T.FIELD_VALUE
        FROM T_BO_TASK_CUS_FIELD T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
            #{boTransTaskId}
        </foreach>
        AND CATEGORY_TYPE=#{categoryType}
    </select>

    <insert id="saveOrUpdate">
        MERGE INTO T_BO_TASK_CUS_FIELD A
        USING (
        SELECT #{boTransTaskId} BO_TRANS_TASK_ID, #{fieldKey} FIELD_KEY, #{fieldName} FIELD_NAME,#{fieldValue} FIELD_VALUE,#{categoryType} CATEGORY_TYPE FROM DUAL
        ) B
        ON (A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID AND A.FIELD_KEY=B.FIELD_KEY AND IS_DEL=0)
        WHEN MATCHED THEN
        UPDATE SET
        A.FIELD_VALUE = B.FIELD_VALUE,
        A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
        INSERT
        (BO_TASK_CUS_FIELD_ID, BO_TRANS_TASK_ID, FIELD_KEY, FIELD_NAME, FIELD_VALUE, CATEGORY_TYPE)
        VALUES(${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()}, B.BO_TRANS_TASK_ID, B.FIELD_KEY, B.FIELD_NAME, B.FIELD_VALUE, B.CATEGORY_TYPE)
    </insert>

    <insert id="batchTaskCusFieldInsert" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="item">
            INTO T_BO_TASK_CUS_FIELD
            (
            BO_TASK_CUS_FIELD_ID,
            BO_TRANS_TASK_ID,
            FIELD_KEY,
            FIELD_NAME,
            FIELD_VALUE,
            IS_REQUIRED,
            IS_MEMORY,
            SORT_NO,
            CATEGORY_TYPE,
            IS_DEL,
            CREATED_TIME,
            LAST_MODIFIED_TIME
            ) VALUES (
            #{item.boTaskCusFieldId},
            #{item.boTransTaskId},
            #{item.fieldKey},
            #{item.fieldName},
            #{item.fieldValue},
            #{item.isRequired},
            #{item.isMemory},
            #{item.sortNo},
            #{item.categoryType},
            '0',
            SYSDATE,
            SYSDATE
            )
        </foreach>
        SELECT 1 FROM DUAL
    </insert>

    <select id="batchQueryTaskCusFieldList" resultMap="BoTaskCusFieldMap">
        SELECT
            T.BO_TRANS_TASK_ID,
            T.FIELD_KEY,
            T.FIELD_NAME,
            T.FIELD_VALUE
        FROM T_BO_TASK_CUS_FIELD T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
            #{boTransTaskId}
        </foreach>
        <if test="fieldKeyList != null and fieldKeyList.size() > 0">
            AND T.FIELD_KEY IN
            <foreach collection="fieldKeyList" item="fieldKey" open="(" separator="," close=")">
                #{fieldKey}
            </foreach>
        </if>
        ORDER BY T.CREATED_TIME DESC
    </select>

    <delete id="deleteTaskCusField">
        DELETE FROM T_BO_TASK_CUS_FIELD T
        WHERE T.BO_TASK_CUS_FIELD_ID = #{boTaskCusFieldId}
    </delete>

    <update id="updateTaskCusFieldValue">
        UPDATE T_BO_TASK_CUS_FIELD T
        SET T.FIELD_VALUE = #{fieldValue},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TASK_CUS_FIELD_ID = #{boTaskCusFieldId}
    </update>

    <delete id="deleteTaskCusFieldByFieldKeys">
        DELETE FROM T_BO_TASK_CUS_FIELD T
        WHERE T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.FIELD_KEY IN
        <foreach collection="fieldKeyList" item="fieldKey" open="(" separator="," close=")">
            #{fieldKey}
        </foreach>
    </delete>

    <update id="batchTaskCusFieldUpdate">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE
                T_BO_TASK_CUS_FIELD
            SET
                FIELD_VALUE = #{item.fieldValue},
                SORT_NO = #{item.sortNo},
                LAST_MODIFIED_TIME = SYSDATE
            WHERE
                BO_TASK_CUS_FIELD_ID = #{item.boTaskCusFieldId}
        </foreach>
    </update>

    <select id="getTaskCusFieldByTaskIdsAndFieldKeys" resultType="com.wtyt.dao.bean.syf.BoTaskCusFieldBean">
        SELECT
        T1.BO_TASK_CUS_FIELD_ID boTaskCusFieldId,
        T1.BO_TRANS_TASK_ID boTransTaskId,
        T1.FIELD_KEY fieldKey,
        T1.FIELD_NAME fieldName,
        T1.FIELD_VALUE fieldValue
        FROM T_BO_TASK_CUS_FIELD T1
        WHERE T1.IS_DEL = 0
        AND T1.BO_TRANS_TASK_ID IN
        <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND T1.FIELD_KEY IN
        <foreach collection="fieldKeys" item="fieldKey" open="(" separator="," close=")">
            #{fieldKey}
        </foreach>
    </select>

</mapper>

