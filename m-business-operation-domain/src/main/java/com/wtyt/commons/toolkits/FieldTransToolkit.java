package com.wtyt.commons.toolkits;

import org.apache.commons.lang3.StringUtils;

import com.wtyt.common.toolkits.SpringContextToolkit;



/**
 * 字段翻译工具类
 * 
 * <AUTHOR>
 *
 */
public class FieldTransToolkit {

    private FieldTransToolkit() {

    }
   /**
     * 金融显示名称配置翻译
     *
     * @param financialShowNameCfg
     *            金融显示名称配置
     *
     * @return
     */
    public static String transBlShowType(String financialShowNameCfg) {
        if ("37".equals(financialShowNameCfg)) {
            return "结算保";
        } else if ("34".equals(financialShowNameCfg)) {
            return "商信金";
        } else if ("36".equals(financialShowNameCfg) || StringUtils.isBlank(financialShowNameCfg)) {
            return "保理";
        }
        return "供应链金融";
    }

    public static String goEmptyStr(String value) {
        return StringUtils.isBlank(value) ? "" : value;
    }

    public static String goEmptyStr(String value, String value1) {
        return StringUtils.isBlank(value) ? "" : value + value1;
    }

    public static String defaultStr(String value, String value1) {
        return StringUtils.isBlank(value) ? value1 : value;
    }

    /**
     * 
     * @Title: transPayState
     * <AUTHOR>
     * @Description: 支付状态翻译
     * @param waybillPayState
     * @param payState
     * @param arriveState
     * @return
     * @return String 返回类型
     * @throws
     */
    public static String transPayState(String waybillPayState,String payState,String arriveState) {
    	if("0".equals(payState)) {
    		return "未支付";
    	}else if("5".equals(payState)) {
    		return "核实中";
    	}else if("7".equals(payState)) {
    		return "业务审核";
    	}else if("8".equals(payState)) {
    		return "风控核实中";
    	}else if("3".equals(payState)) {
    		return "预申请";
    	}else if("1".equals(payState)) {
    		return "已申请";
    	}else if("2".equals(payState) && "0".equals(arriveState)) {
    		return "已支付";
    	}else if("2".equals(payState) && "1".equals(arriveState)) {
    		return "已到账";
    	}else if("10".equals(payState)) {
    		return "平台核实中";
    	}else {
    		return "处理中";
    	}
    }

    /**
     * 翻译运输任务中的支付状态
     * @param payState
     * @return
     */
    public static String translatePayState(String payState) {
        // 未支付、已申请、已支付、预申请、部分支付、核实中、业务审核、风控核实中、平台核实中；
        if("0".equals(payState) || StringUtils.isBlank(payState)) {
            return "未支付";
        }else if("7".equals(payState)) {
            return "业务审核";
        }else if("8".equals(payState)) {
            return "风控核实中";
        }else if("6".equals(payState)) {
            return "核实中";
        }else if("5".equals(payState)) {
            return "部分支付";
        }else if("3".equals(payState)) {
            return "预申请";
        }else if("1".equals(payState)) {
            return "已申请";
        }else if("2".equals(payState)) {
            return "已支付";
        }else if("10".equals(payState)) {
            return "平台核实中";
        }else {
            return "";
        }
    }

    /**
     * 翻译支付状态，笼统一点的
     * @param payState
     * @return
     */
    public static String translatePayStateV2(String payState) {
        // 未支付、已申请、已支付、预申请、部分支付、核实中、业务审核、风控核实中、平台核实中；
        if("0".equals(payState) || StringUtils.isBlank(payState)) {
            return "未结算";
        }else if("5".equals(payState)) {
            return "部分结算";
        }else if("2".equals(payState)) {
            return "已结算";
        }else {
            return "申请中";
        }
    }
}
