package com.wtyt.commons.toolkits;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.wtyt.bean.DdcsConfig;
import com.wtyt.bo.bean.request.Req1735209Bean;
import com.wtyt.bo.bean.request.Req1735210Bean;
import com.wtyt.bo.bean.response.InsureInfos;
import com.wtyt.common.constants.Constants;
import com.wtyt.common.constants.OrderCreatedTypeConstants;
import com.wtyt.common.constants.OrgConfigConstants;
import com.wtyt.common.enums.*;
import com.wtyt.common.rpc.bean.Rpc19098OBean;
import com.wtyt.common.rpc.bean.Rpc4008IBean;
import com.wtyt.common.rpc.bean.Rpc4008OBean;
import com.wtyt.common.rpc.service.RpcMCacheService;
import com.wtyt.common.toolkits.*;
import com.wtyt.common.toolkits.BusinessToolkit;
import com.wtyt.commons.consts.WaybillConstons;
import com.wtyt.dao.bean.syf.BoShippingListBean;
import com.wtyt.pus.bean.EventTabSearchBean;
import com.wtyt.tt.bean.BoTaskListParamsBean;
import com.wtyt.tt.bean.TaskTabCountBean;
import com.wtyt.util.OrgCacheDataUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/9 11:40
 * @description 运输任务公共工具类
 */
public class TaskToolKit {
    private final static Logger log = LoggerFactory.getLogger(TaskToolKit.class);

    public static final String YANFA_DO_COMMONS_PAY = "yanfa.do.commons.pay";
    public static final String YBF_CH_ACT_SYS = "ybf.ch.actSys";

    /**
     * 获取发车之前的tabState对应的nodeIdList
     * @param tabCode
     * @return
     */
    public static List<String> getNodeIdListByTabState(String tabCode){
        List<String> nodeIdList = null;
        if (StringUtils.equals(tabCode, TransportTaskTab.NOT_PRINT.getTabCode())) {
            //未打印tabState对应nodeId
            nodeIdList = getNodeIdListByNotPrintTabCode();
        } else if(TransportTaskTab.DAIPAICHE.getTabCode().equals(tabCode) || TransportTaskTab.DAIDAOCHANG.getTabCode().equals(tabCode) || TransportTaskTab.DAIFACHE.getTabCode().equals(tabCode)){
            /**
             * 待派车、待到场、待发车
             */
            TransportTaskTab tab = TransportTaskTab.getTabFromCode(tabCode);
            //根据tab获取对应的nodeId
            List<TransportNode> nodeList = TransportNode.listNodeFromTab(tab);
            if(CollectionUtils.isNotEmpty(nodeList)) {
                nodeIdList = nodeList.stream().map(TransportNode::getNodeId).collect(Collectors.toList());
            }
        }

        return nodeIdList;
    }

    public static List<String> getNodeIdListByNotPrintTabCode(){
        List<TransportNode> nodeList = new ArrayList<>();
        TransportNode[] values = TransportNode.values();
        for (TransportNode value : values) {
            TransportTaskTab notPrintTab = value.getNotPrintTab();
            if (notPrintTab != null) {
                nodeList.add(value);
            }
        }
        //根据tab获取对应的nodeId
        if(CollectionUtils.isNotEmpty(nodeList)) {
            List<String> nodeIdList=new ArrayList<>();
            nodeList.forEach(item-> nodeIdList.add(item.getNodeId()));
            return nodeIdList;
        }else {
            return null;
        }
    }

    /**
     * 转换时间格式
     * @param timeFormat 2-日期级yyyy-MM-dd 1-小时级yyyy-MM-dd HH:mm
     * @param originTime
     * @return
     */
    public static String getTimeFormat(String timeFormat,String originTime){
        if(StringUtils.isNotBlank(originTime)){
            if("2".equals(timeFormat)){
                //日期级
                if (originTime.length() >= 10)
                    originTime = originTime.substring(0,10);
            }else {
                //小时级
                if (originTime.length() >= 16)
                    originTime = originTime.substring(0,16);
            }
        }

        return originTime;
    }

    /**
     * 统计tabState
     * @param taskTabCountBeans
     * @return
     */
    public static Map<String,Integer> countTabState(List<TaskTabCountBean> taskTabCountBeans) {
        Map<String,Integer> map = new HashMap<>();
        for (TaskTabCountBean taskTabCountBean : taskTabCountBeans) {
            String state = taskTabCountBean.getState();
            String payState = taskTabCountBean.getPayState();
            String nodeId = taskTabCountBean.getNodeId();
            Integer count = taskTabCountBean.getCount();
            //获取tabState枚举
            String tabStateCode = getTabStateCode(state, payState, nodeId);
            if (tabStateCode == null) {
                continue;
            }
            Integer oldCount = map.get(tabStateCode);
            if(oldCount == null){
                map.put(tabStateCode,count);
            }else {
                map.put(tabStateCode,oldCount + count);
            }
        }

        return map;
    }

    /**
     * 根据state、payState、nodeId获取tabState
     * @param state
     * @param payState
     * @param nodeId
     * @return
     */
    public static TransportTaskTab getTabState(String state,String payState,String nodeId){
        TransportTaskTab tabState = null;
        if(WaybillConstons.WAYBILL_STATE_1.equals(state)){
            //运输中
            tabState = TransportTaskTab.YUNSHUZHONG;
        }else if(WaybillConstons.WAYBILL_STATE_2.equals(state)){
            if(StringUtils.isBlank(payState) || PayStateEnum.ZERO.getCode().equals(payState)){
                //支付状态为0或者空，设置为运输完成
                tabState = TransportTaskTab.YUNSHUWANCHENG;
            }else if(PayStateEnum.TWO.getCode().equals(payState)){
                //已完成支付，设置为已结算
                tabState = TransportTaskTab.YIJIESUAN;
            }else {
                //payState非空非0和2，设为支付中
                tabState = TransportTaskTab.ZHIFUZHONG;
            }
        }else {
            /**
             * state=0未追踪状态
             * 处理待派车、待到场、待发车
             */
            if(StringUtils.isNotBlank(nodeId)){
                TransportNode node=TransportNode.getFromNodeId(nodeId);
                if(node == null) {
                    log.warn("未知的nodeId:{}",nodeId);
                }else {
                    tabState = node.getTab();
                }
            }
        }

        return tabState;
    }

    public static String getTabStateCode(String state,String payState,String nodeId){
        TransportTaskTab tabState = getTabState(state, payState, nodeId);
        if(tabState!=null){
            return tabState.getTabCode();
        }
        return null;
    }

    /**
     * orderCreateType转为optSource
     * @param orderCreateType
     * @return
     */
    public static String changeOrderCreateTypeToOptSource(String orderCreateType){
        //默认新大陆pc
        String optSource = OptSourceEnum.XIN_DA_LU.getCode();
        if(StringUtils.isBlank(orderCreateType)){
            return optSource;
        }
        if (OrderCreatedTypeConstants.TYPE_2003.equals(orderCreateType)) {
            //快路宝
            optSource = OptSourceEnum.KUAI_LU_BAO.getCode();
        } else if (StringUtils.equalsAny(orderCreateType, OrderCreatedTypeConstants.TYPE_2005, OrderCreatedTypeConstants.TYPE_2007,OrderCreatedTypeConstants.TYPE_2010)) {
            //新大陆app
            optSource = OptSourceEnum.XIN_DA_LU_APP.getCode();
        } else if(StringUtils.equalsAny(orderCreateType, OrderCreatedTypeConstants.TYPE_2006, OrderCreatedTypeConstants.TYPE_2009,OrderCreatedTypeConstants.TYPE_2017)){
            //好运宝
            optSource = OptSourceEnum.HAO_YUN_BAO.getCode();
        } else if(StringUtils.equalsAny(orderCreateType, OrderCreatedTypeConstants.TYPE_2016, OrderCreatedTypeConstants.TYPE_2020, OrderCreatedTypeConstants.TYPE_2022)) {
            // 中铝app
            optSource = OptSourceEnum.ZHONG_LV.getCode();
        } else if (OrderCreatedTypeConstants.TYPE_2008.equals(orderCreateType)) {
            //接口调用
            optSource = OptSourceEnum.INTERFACE_INVOKE.getCode();
        } else if (NumberToolkit.compare(orderCreateType, "3000") > 0) {
            //三方接口
            optSource = OptSourceEnum.THIRD_INVOKE.getCode();
        }
        return optSource;
    }

    /**
     * 计算货损数量
     * @param loadingTonnage
     * @param unloadingTonnage
     * @param allowGainFlag
     * @return
     */
    public static String calculateLossTonnage(String loadingTonnage,String unloadingTonnage,String allowGainFlag){
        if(StringUtils.isAnyBlank(loadingTonnage,unloadingTonnage)){
            return null;
        }
        if(Double.parseDouble(unloadingTonnage)==0){
            return Constants.ZERO;
        }
        String lossTonnage = null;
        BigDecimal lossTonnageDecimal = new BigDecimal(loadingTonnage).subtract(new BigDecimal(unloadingTonnage));
        if(lossTonnageDecimal.compareTo(BigDecimal.ZERO)<0 && !StringUtils.equals(allowGainFlag, Constants.ONE)){
            //非允许涨吨=》负数设为0
            lossTonnage = Constants.ZERO;
        }else {
            lossTonnage = lossTonnageDecimal.stripTrailingZeros().toPlainString();
        }

        return lossTonnage;
    }


    //-----解析相关-----
    public static String getGroupNameByFieldKey(String fieldKey,List<String> groupNames){
        try {
            if(CollectionUtils.isNotEmpty(groupNames)){
                String replace = fieldKey.replace(Constants.GROUP_NAMES_PRE, StringUtils.EMPTY);
                int i = Integer.parseInt(replace);
                if(i<groupNames.size()){
                    return groupNames.get(i);
                }
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(),e);
        }

        return null;
    }

    public static String getCustomerOrderNos(List<BoShippingListBean> shippingListList){
        if (CollectionUtils.isEmpty(shippingListList)) {
            return null;
        }
        return  shippingListList.stream()
                .map(BoShippingListBean::getCustomerOrderNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(","));
    }

    public static String getTransPortStatusDescForExport(String tabState) {
        if (StringUtils.isBlank(tabState) || StringUtils.equals(tabState, TabStatusEnum.ZERO.getName())) {
            return "";
        }
        TabStatusEnum tabStatusEnum = TabStatusEnum.queryByName(tabState);
        return tabStatusEnum != null ? tabStatusEnum.getDesc() : "";
    }

    /**
     * 异常状态信息处理
     * @param transCornerMarkType
     * @return
     */
    public static String getTransCornerMarkTypeInfo(String transCornerMarkType) {
        if (StringUtils.isEmpty(transCornerMarkType)) {
            return "";
        }
        String[] transMarkTypeArray = transCornerMarkType.split(",");
        StringBuilder sb = new StringBuilder();
        for (String mark : transMarkTypeArray) {
            if (sb.toString().length() > 0) {
                sb.append(",");
            }
            if (StringUtils.equals(mark, "1")) {
                sb.append("超时");
            } else if (StringUtils.equals(mark, "2")) {
                sb.append("迟到");
            } else if (StringUtils.equals(mark, "8")) {
                sb.append("异地卸货");
            }
        }
        return sb.toString();
    }

    /**
     * 拼装装卸货地详细地址
     * @param provinceName
     * @param cityName
     * @param countyName
     * @param detailAddress
     * @return
     */
    public static String getFUllAddress(String provinceName, String cityName, String countyName, String detailAddress) {
        StringBuilder sb = new StringBuilder();
        if(StringUtils.isNotBlank(provinceName)) {
            sb.append(provinceName);
        }
        if(StringUtils.isNotBlank(cityName)) {
            sb.append(cityName);
        }
        if(StringUtils.isNotBlank(countyName)) {
            sb.append(countyName);
        }
        if(StringUtils.isNotBlank(detailAddress)) {
            sb.append(detailAddress);
        }
        return sb.toString();
    }

    public static String getGoodsAmountDesc(String goodsAmount, String goodsAmountTypeStr) {
        if (StringUtils.isBlank(goodsAmount))
            return "";
        return StringUtils.isNotBlank(goodsAmountTypeStr) ? goodsAmount + goodsAmountTypeStr : goodsAmount;
    }

    /**
     * 转换差异支付状态（上游看下游、下游看上游）
     * @param diffPayState
     * @return
     */
    public static String transferDiffPayState(String diffPayState){
        //默认0
        String payState = StringUtils.isBlank(diffPayState)? Constants.ZERO:diffPayState;
        if(!StringUtils.equalsAny(payState,PayStateEnum.ZERO.getCode(),PayStateEnum.TWO.getCode(),PayStateEnum.FIVE.getCode())){
            //非0-未支付、2-已支付、5-已部分支付=》都返回已申请
            payState = PayStateEnum.ONE.getCode();
        }

        return payState;
    }

    public static String getPayStatusDescInfo(String payState, String settleMode, String settleType) {
        if (!BusinessToolkit.isOilSettleTask(settleType) && StringUtils.equals(settleMode, SettleModeEnum.TWO.getCode())) {
            return "非平台结算";
        }

        return FieldTransToolkit.translatePayState(payState);
    }

    public static String getPayStatusDescInfoV2(String payState, String settleMode, String settleType) {
        if (!BusinessToolkit.isOilSettleTask(settleType) && StringUtils.equals(settleMode, SettleModeEnum.TWO.getCode())) {
            return "非平台结算";
        }

        return FieldTransToolkit.translatePayStateV2(payState);
    }

    public static String getCarTypeAndLength(String cartType, String cartLength) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotEmpty(cartType)) {
            sb.append(cartType);
        }
        if (sb.toString().length() > 0) {
            sb.append("/");
        }
        if (StringUtils.isNotEmpty(cartLength)) {
            sb.append(cartLength);
        }
        return sb.toString();
    }

    public static String getPaperReceiptStatusInfo(String paperReceiptStatus) {
        if (StringUtils.isEmpty(paperReceiptStatus)) {
            return "";
        }
        PaperReceiptStatusEnum paperReceiptStatusEnum = PaperReceiptStatusEnum.getFromCode(paperReceiptStatus);
        return paperReceiptStatusEnum != null ? paperReceiptStatusEnum.getName() : "";
    }

    /**
     * 获取付款方式描述
     * @param advancePayState
     * @return
     */
    public static String getAdvancedPayStateDesc(String advancePayState) {
        if (StringUtils.isEmpty(advancePayState))
            return "";
        AdvancePayStateEnum advancePayStateEnum = AdvancePayStateEnum.queryByName(advancePayState);
        return advancePayStateEnum != null ? advancePayStateEnum.getDesc() : "";
    }

    public static String getInsureFeeFormat(InsureInfos insureInfos) {
        return insureInfos != null && StringUtils.isNotEmpty(insureInfos.getInsureFee()) && !StringUtils.equals(insureInfos.getIsOpenOrgRiskPackage(), Constants.ONE) ? insureInfos.getInsureFee() : "";
    }

    public static String getDriverGuaranteeStateDesc(String driverGuaranteeState) {
        if (StringUtils.isEmpty(driverGuaranteeState)) {
            return "";
        }
        DriverGuaranteeStateEnum driverGuaranteeStateEnum = DriverGuaranteeStateEnum.queryByName(driverGuaranteeState);
        return driverGuaranteeStateEnum != null ? driverGuaranteeStateEnum.getDesc() : "";
    }

    public static String getBoVoucherCheckState(String boVoucherCheckState) {
        if (StringUtils.isBlank(boVoucherCheckState)) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.equals(boVoucherCheckState, "0")) {
            return "待审核";
        } else if (StringUtils.equals(boVoucherCheckState, "1")) {
            return "合格";
        } else if (StringUtils.equals(boVoucherCheckState, "2")) {
            return "不合格";
        } else if (StringUtils.equals(boVoucherCheckState, "3")) {
            return "未上传";
        } else if (StringUtils.equals(boVoucherCheckState, "4")) {
            return "无需审核";
        }
        return StringUtils.EMPTY;
    }

    /**
     * 上传装货凭证延迟发车小时数
     * @param orgId
     * @return
     */
    public static double getVoucherTriggerStartCarDelayHour(String orgId){
        double intervalHour = 0;
        try {
            Map<String, Object> orgSysCfg = OrgCacheDataUtil.getOrgSysCfg(orgId, "925");
            String sysConfigValue925 = (String)orgSysCfg.get("925");
            if (StringUtils.isBlank(sysConfigValue925)) {
                return intervalHour;
            }
            DdcsConfig sysConfig925 = GsonToolkit.jsonToBean(sysConfigValue925, DdcsConfig.class);
            String autoDispatch = sysConfig925.getAutoDispatch();
            if (!StringUtils.equals(autoDispatch, OrgConfigConstants.ON)) {
                return intervalHour;
            }
            DdcsConfig.UploadVoucher uploadVoucher = sysConfig925.getUploadVoucher();
            if (uploadVoucher != null && StringUtils.equals(uploadVoucher.getChecked(), Constants.ONE)
                    && StringUtils.isNotBlank(uploadVoucher.getIntervalHour()) && Double.parseDouble(uploadVoucher.getIntervalHour())>0) {
                return Double.parseDouble(uploadVoucher.getIntervalHour());
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(),e);
        }

        return intervalHour;
    }

    public static boolean isBusUpOrDownStream(String transMode) {
        if (TransModeEnum.FOUR.getCode().equals(transMode) || TransModeEnum.THREE.getCode().equals(transMode)) {
            return true;
        }
        return false;
    }


    public static boolean isAfterCreatedTime(String configTime,String createdTimeStr){
        if(StringUtils.isBlank(configTime)){
            return false;
        }
        if(StringUtils.equals(configTime,Constants.MINUS_ONE)){
            return true;
        }
        if(configTime.length()==10){
            configTime = configTime + " 00:00:00";
        }
        LocalDateTime beginTime = LocalDateTime.parse(configTime, DateToolkit.DATETIME_FORMATTER);
        LocalDateTime createdTime = LocalDateTime.parse(createdTimeStr, DateToolkit.DATETIME_FORMATTER);
        if(createdTime.isAfter(beginTime)){
            return true;
        }

        return false;
    }

    /**
     * 是否配置了运输任务-权限组
     * @param permissionInfoList
     * @return
     */
    public static boolean hasTaskGroupPermission(List<Rpc19098OBean.DataResourcePermission> permissionInfoList) {
        if (CollectionUtils.isNotEmpty(permissionInfoList)) {
            for (Rpc19098OBean.DataResourcePermission dataResource : permissionInfoList) {
                if (StringUtils.equals(DataResourceCodeEnum.YWYZ_SRWGL.getCode(), dataResource.getDataResourceCode())) {
                    List<Rpc19098OBean.Permission> permissionList = dataResource.getPermissionInfoList();
                    if (CollectionUtils.isNotEmpty(permissionList)) {
                        for (Rpc19098OBean.Permission permission : permissionList) {
                            List<Rpc19098OBean.Scope> scopeList = permission.getScopeList();
                            if (CollectionUtils.isNotEmpty(scopeList)) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    public static void setPermissionList(BoTaskListParamsBean data,List<Rpc19098OBean.DataResourcePermission> permissionInfoList){
        for (Rpc19098OBean.DataResourcePermission dataResource : permissionInfoList) {
            if (StringUtils.equals(DataResourceCodeEnum.YWYZ_SRWGL.getCode(), dataResource.getDataResourceCode())) {
                List<Rpc19098OBean.Permission> permissionList = dataResource.getPermissionInfoList();
                // 设置运输任务权限
                setTransTaskPermission(data, permissionList);
                data.setTransTaskPermissionList(permissionList);
            } else if (StringUtils.equals(DataResourceCodeEnum.YWYZ_SCQD.getCode(), dataResource.getDataResourceCode())) {
                // 随车清单
                List<Rpc19098OBean.Permission> shippingListPermissionInfoList = dataResource.getPermissionInfoList();
                if (CollectionUtils.isNotEmpty(shippingListPermissionInfoList)) {
                    // 随车清单不查询组织相关知查询condition、所以这里需要判断下condition不为空
                    shippingListPermissionInfoList = shippingListPermissionInfoList.stream()
                            .filter(permission -> StringUtils.isNotBlank(permission.getCondition()))
                            .collect(Collectors.toList());
                    // 设置随车清单权限
                    setShippingListPermission(data, shippingListPermissionInfoList);
                    data.setShippingListPermissionList(shippingListPermissionInfoList);
                }
            }
        }
    }

    /**
     * 设置运输任务权限
     * @param data
     * @param permissionList
     */
    private static void setTransTaskPermission(BoTaskListParamsBean data, List<Rpc19098OBean.Permission> permissionList) {
        if (CollectionUtils.isNotEmpty(permissionList)) {
            for (Rpc19098OBean.Permission permission : permissionList) {
                String condition = permission.getCondition();
                // 去除前后空格
                condition = StringUtils.trim(condition);
                // 检查是否存在T_BO_TRANS_TASK表
                if (StringUtils.contains(condition, "T_BO_TRANS_TASK.")) {
                    data.setHasTransTaskCondition(true);
                    condition = StringUtils.replace(condition, "T_BO_TRANS_TASK.", "BT.");
                }
                // 检查是否存在T_BO_TRANS_TASK_EXTRA表
                if (StringUtils.contains(condition, "T_BO_TRANS_TASK_EXTRA.")) {
                    data.setHasTransTaskExtraCondition(true);
                    condition = StringUtils.replace(condition, "T_BO_TRANS_TASK_EXTRA.", "BTE.");
                }
                // 检查是否存在T_BO_TASK_CUS_FIELD表
                if (StringUtils.contains(condition, "T_BO_TASK_CUS_FIELD.")) {
                    data.setHasTransTaskCusCondition(true);
                    condition = StringUtils.replace(condition, "T_BO_TASK_CUS_FIELD.", "CUS.");
                }
                permission.setCondition(condition);
            }
        }
    }

    /**
     * 设置随车清单权限
     * @param data
     * @param permissionList
     */
    private static void setShippingListPermission(BoTaskListParamsBean data, List<Rpc19098OBean.Permission> permissionList) {
        if (CollectionUtils.isNotEmpty(permissionList)) {
            for (Rpc19098OBean.Permission permission : permissionList) {
                String condition = permission.getCondition();
                // 去除前后空格
                condition = StringUtils.trim(condition);
                // 检查是否存在T_BO_SHIPPING_LIST表
                if (StringUtils.contains(condition, "T_BO_SHIPPING_LIST.")) {
                    condition = StringUtils.replace(condition, "T_BO_SHIPPING_LIST.", "BSL.");
                }
                data.setHasShippingListCondition(true);
                permission.setCondition(condition);
            }
        }
    }

    public static void setRequestPermissionList(Req1735209Bean request, List<Rpc19098OBean.DataResourcePermission> permissionInfoList) {
        for (Rpc19098OBean.DataResourcePermission dataResource : permissionInfoList) {
            if (StringUtils.equals("YWYZ-YSRWGL", dataResource.getDataResourceCode())) {
                List<Rpc19098OBean.Permission> permissionList = dataResource.getPermissionInfoList();
                // 运输任务
                processTransTaskPermission(request, permissionList);
                request.setTransTaskPermissionList(permissionList);
            } else if (StringUtils.equals("YWYZ-SCQD", dataResource.getDataResourceCode())) {
                // 随车清单
                List<Rpc19098OBean.Permission> shippingListPermissionInfoList = dataResource.getPermissionInfoList();
                if (CollectionUtils.isNotEmpty(shippingListPermissionInfoList)) {
                    // 随车清单不查询组织相关知查询condition、所以这里需要判断下condition不为空
                    shippingListPermissionInfoList = shippingListPermissionInfoList.stream()
                            .filter(permission -> StringUtils.isNotBlank(permission.getCondition()))
                            .collect(Collectors.toList());
                    // 处理随车清单权限
                    processShippingListPermission(request, shippingListPermissionInfoList);
                    request.setShippingListPermissionList(shippingListPermissionInfoList);
                }
            }
        }
    }

    /**
     * 处理运输任务权限
     * @param request
     * @param permissionList Req1735209Bean Req1735210Bean
     */
    private static void processTransTaskPermission(Object object, List<Rpc19098OBean.Permission> permissionList) {
        Req1735209Bean req1735209Bean = null;
        Req1735210Bean req1735210Bean = null;
        if (object instanceof Req1735209Bean) {
            req1735209Bean = (Req1735209Bean)object;
        } else {
            req1735210Bean = (Req1735210Bean)object;
        }
        if (CollectionUtils.isNotEmpty(permissionList)) {
            for (Rpc19098OBean.Permission permission : permissionList) {
                String condition = permission.getCondition();
                // 去除前后空格
                condition = StringUtils.trim(condition);
                // 检查是否存在T_BO_TRANS_TASK表
                if (StringUtils.contains(condition, "T_BO_TRANS_TASK.")) {
                    if (req1735209Bean != null) {
                        req1735209Bean.setHasTransTaskCondition(true);
                    } else {
                        req1735210Bean.setHasTransTaskCondition(true);
                    }
                    condition = StringUtils.replace(condition, "T_BO_TRANS_TASK.", "BT.");
                }
                // 检查是否存在T_BO_TRANS_TASK_EXTRA表
                if (StringUtils.contains(condition, "T_BO_TRANS_TASK_EXTRA.")) {
                    if (req1735209Bean != null) {
                        req1735209Bean.setHasTransTaskExtraCondition(true);
                    } else {
                        req1735210Bean.setHasTransTaskExtraCondition(true);
                    }
                    condition = StringUtils.replace(condition, "T_BO_TRANS_TASK_EXTRA.", "BTE.");
                }
                permission.setCondition(condition);
            }
        }
    }

    /**
     * 处理随车清单权限
     * @param object
     * @param permissionList
     */
    private static void processShippingListPermission(Object object, List<Rpc19098OBean.Permission> permissionList) {
        Req1735209Bean req1735209Bean = null;
        Req1735210Bean req1735210Bean = null;
        if (object instanceof Req1735209Bean) {
            req1735209Bean = (Req1735209Bean)object;
        } else {
            req1735210Bean = (Req1735210Bean)object;
        }
        if (CollectionUtils.isNotEmpty(permissionList)) {
            for (Rpc19098OBean.Permission permission : permissionList) {
                String condition = permission.getCondition();
                // 去除前后空格
                condition = StringUtils.trim(condition);
                // 检查是否存在T_BO_SHIPPING_LIST表
                if (StringUtils.contains(condition, "T_BO_SHIPPING_LIST.")) {
                    condition = StringUtils.replace(condition, "T_BO_SHIPPING_LIST.", "BSL.");
                }
                if (object instanceof Req1735209Bean) {
                    req1735209Bean.setHasShippingListCondition(true);
                } else {
                    req1735210Bean.setHasShippingListCondition(true);
                }
                permission.setCondition(condition);
            }
        }
    }

    public static List<String> separatorString(String param,List<String> separatorList){
        List<String> list = new LinkedList<>();
        if(StringUtils.isBlank(param)){
            return list;
        }
        if(CollectionUtils.isEmpty(separatorList)){
            //没有分隔符不分割
            list.add(param);
            return list;
        }
        param = param.trim();
        param = param.replace("，",",");
        for (String separator : separatorList) {
            String[] split = param.split(separator);
            if(split.length>1){
                for (String s : split) {
                    s = StringUtils.trim(s);
                    if(StringUtils.isNotBlank(s)){
                        if (!list.contains(s)) {
                            list.add(s);
                        }
                    }
                }
                break;
            }
        }
        if(list.isEmpty()){
            list.add(param);
        }

        return list;
    }

    public static int getMaxInputSearchLength(){
        try {
            String maxLength = ApolloToolkit.getApolloParameter("yanfa.tf.bus.commons","list.search.input.max.length","200");
            return Integer.parseInt(maxLength);
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(),e);
        }

        return 1000;
    }

    /**
     * 转换路歌的车长=>顶通的车长
     * @param origin
     * @return
     */
    public static String convertDingTongCartLength(String origin){
        if(origin.startsWith(".")){
            origin = "0"+origin;
        }
        double doubleOrigin = Double.parseDouble(origin);
        String convert = null;
        if(doubleOrigin<4.2){
            convert = "4.2米以下";
        }else if(doubleOrigin>=18){
            convert = "18B";
        }else if(doubleOrigin==16 || doubleOrigin==16.5 || doubleOrigin==17.5){
            convert = "17.5米";
        }else if(doubleOrigin==15){
            convert = "15M";
        }else if(doubleOrigin==11.7 || doubleOrigin==12.5 || doubleOrigin==13 || doubleOrigin==13.5 || doubleOrigin==13.7){
            convert = "13.5米";
        }else if(doubleOrigin==9.6){
            convert = "9.6米";
        }else if(doubleOrigin==8.2 || doubleOrigin==8.7){
            convert = "8.6米";
        }else if(doubleOrigin==7.6 || doubleOrigin==7.7){
            convert = "7.6米";
        }else if(doubleOrigin==7.2){
            convert = "7.2米";
        }else if(doubleOrigin==6.2 || doubleOrigin==6.5 || doubleOrigin==6.8){
            convert = "6.8米";
        }else if(doubleOrigin==5){
            convert = "5.2米";
        }else if(doubleOrigin==4.2){
            convert = "4.2米";
        }else {
            convert = origin+"米";
        }

        return convert;
    }

    public static String transferSysRoleTypeTo(String sysRoleType){
        if(StringUtils.equals(sysRoleType,SysRoleEnum.DRIVER.getRoleId())){
            //8-司机
            return Constants.EIGHT;
        }else if(StringUtils.equals(sysRoleType,Constants.MINUS_ONE)){
            //7-未知用户（三方接口传入）
            return Constants.SEVEN;
        }

        //2-syf表t_m_user
        return Constants.TWO;
    }

    /**
     * 异步调用通用方法
     * @param function
     * @param executor
     * @return
     * @param <T>
     */
    public static<T> CompletableFuture<T> supplyAsync(Supplier<T> function, Executor executor){
        return CompletableFuture.supplyAsync(()->{
            try {
                return function.get();
            } catch (Exception e) {
                return null;
            }
        },executor);
    }

    /**
     * 是否是撮合体系
     * @param belongActSys
     * @return
     */
    public static boolean isProcessService(String belongActSys){
        if(StringUtils.isBlank(belongActSys)){
            return false;
        }
        Config config = ConfigService.getConfig(YANFA_DO_COMMONS_PAY);
        String scope = config.getProperty(YBF_CH_ACT_SYS,null);
        if(StringUtils.isBlank(scope)){
            return false;
        }

        String[] arr = scope.split(Constants.SEPARATOR_COMMA);
        return StringUtils.equalsAny(belongActSys,arr);
    }

    public static List<String> getProcessServiceActSys(){
        Config config = ConfigService.getConfig(YANFA_DO_COMMONS_PAY);
        String scope = config.getProperty(YBF_CH_ACT_SYS,null);
        if(StringUtils.isBlank(scope)){
            return null;
        }

        return Arrays.asList(scope.split(Constants.SEPARATOR_COMMA));
    }


    public static String transferOptSourceToSwaiOptSrc(String optSource){
        if(StringUtils.isBlank(optSource)){
            return null;
        }
        if(StringUtils.equals(optSource,OptSourceEnum.XIN_DA_LU_APP.getCode())){
            //新大陆app
            return Constants.SIX;
        } else if(StringUtils.equalsAny(optSource,OptSourceEnum.XIN_DA_LU.getCode(),OptSourceEnum.APPROVAL_CENTER_ORG.getCode())){
            //新大陆PC
            return Constants.ONE;
        } else if(StringUtils.equalsAny(optSource,OptSourceEnum.HAO_YUN_BAO.getCode(),OptSourceEnum.APPROVAL_CENTER_DRIVER.getCode())){
            //好运宝
            return Constants.FOUR;
        } else if(StringUtils.equals(optSource,OptSourceEnum.KUAI_LU_BAO.getCode())){
            //快路宝
            return Constants.TWO;
        }else if(StringUtils.equalsAny(optSource,OptSourceEnum.THIRD_INVOKE.getCode())){
            //三方接口
            return Constants.FIVE;
        }

        //系统触发
        return Constants.EIGHT;
    }

    public static List<String> queryOpenYwyzOrgIds(){
        try {
            RpcMCacheService rpcMCacheService = SpringContextToolkit.getBean(RpcMCacheService.class);
            Rpc4008IBean rpc4008IBean = new Rpc4008IBean();
            Rpc4008IBean.cfg cfg = new Rpc4008IBean.cfg();
            cfg.setCfgItem("901");
            cfg.setCfgValue("8");
            rpc4008IBean.setFields(Collections.singletonList(cfg));
            Rpc4008OBean rpc4008OBean = rpcMCacheService.rpc4008(rpc4008IBean);
            if (rpc4008OBean != null && "0".equals(rpc4008OBean.getReCode())) {
                return rpc4008OBean.getResult();
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            AlarmToolkit.logAlarm("查询开通业务运作数字化项目ID配置失败",e,null);
        }

        return null;
    }

    public static List<EventTabSearchBean> buildEventTabSearchBeanList(String taskId){
        List<EventTabSearchBean> list = new ArrayList<>();
        EventTabSearchBean tabSearchBean2 = new EventTabSearchBean("BO_TRANS_TASK_ID", taskId, "SYF", "T_BO_TRANS_TASK");
        list.add(tabSearchBean2);
        return list;
    }

    /**
     * 是否没有运力供应商，是纯直采的项目
     * @param orgId
     * @return
     */
    public static boolean isNoSupplier(String orgId){
        return ApolloToolkit.inWhiteListBoCommons("task.no.supplier.orgIds", orgId);
    }

    /**
     * 拼接卸货信息
     * @param endAddress
     * @param consigneeName
     * @param consigneeContact
     * @return
     */
    public static String buildUnloadingInfo(String endAddress, String consigneeName, String consigneeContact) {
        StringBuilder sb = new StringBuilder();
        if(StringUtils.isNotBlank(endAddress)){
            sb.append(endAddress).append(Constants.SEPARATOR_ASTERISK);
        }
        if(StringUtils.isNotBlank(consigneeName)){
            sb.append(consigneeName).append(Constants.SEPARATOR_ASTERISK);
        }
        if(StringUtils.isNotBlank(consigneeContact)){
            sb.append(consigneeContact).append(Constants.SEPARATOR_ASTERISK);
        }
        return sb.length() > 0 ? sb.substring(0,sb.length()-1) : sb.toString();
    }

    /**
     * 获取线上油开关
     * @param belongActSys
     * @return
     */
    public static String getOnlineOilSwitchKey(String belongActSys){
        return getOnlineOilSwitchKey(isProcessService(belongActSys));
    }

    /**
     * 获取线上油开关
     * @param isProcessService
     * @return
     */
    public static String getOnlineOilSwitchKey(boolean isProcessService){
        if(isProcessService){
            return OrgConfigConstants.KEY_PROCESS_ONLINE_OIL_SWITCH;
        }

        return OrgConfigConstants.KEY_ONLINE_OIL_SWITCH;
    }

    /**
     * 获取线上气开关
     * @param belongActSys
     * @return
     */
    public static String getOnlineGasSwitchKey(String belongActSys){
        return getOnlineGasSwitchKey(isProcessService(belongActSys));
    }

    /**
     * 获取线上气开关
     * @param isProcessService
     * @return
     */
    public static String getOnlineGasSwitchKey(boolean isProcessService){
        if(isProcessService){
            return OrgConfigConstants.KEY_PROCESS_ONLINE_GAS_SWITCH;
        }

        return OrgConfigConstants.KEY_ONLINE_GAS_SWITCH;
    }
}
