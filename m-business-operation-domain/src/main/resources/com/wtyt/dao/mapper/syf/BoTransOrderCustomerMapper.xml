<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransOrderCustomerMapper">

	<insert id="insertOrderCustomer">
	INSERT INTO T_BO_TRANS_ORDER_CUSTOMER (
		BO_TRANS_ORDER_CUSTOMER_ID,
		BO_TRANS_ORDER_ID,
		CUSTOMER_COLLECT,
		CUSTOMER_NAME,
		SUB_CUSTOMER
	) VALUES (
		#{boTransOrderCustomerId},
		#{boTransOrderId},
		#{customerCollect},
		#{customerName},
		#{subCustomer}
	)
	</insert>


	<update id="mergeIntoOrderCustomer">
        merge into T_BO_TRANS_ORDER_CUSTOMER a
		using (select #{boTransOrderCustomerId} boTransOrderCustomerId ,
					  #{boTransOrderId} boTransOrderId,
					  #{customerCollect} customerCollect,
					  #{customerName} customerName,
					  #{subCustomer} subCustomer
			   from dual) b
		on (a.BO_TRANS_ORDER_ID = b.boTransOrderId  AND a.is_del = 0)
		when matched then
			update set
					   CUSTOMER_COLLECT = b.customerCollect,
					   CUSTOMER_NAME = b.customerName,
					   SUB_CUSTOMER = b.subcustomer,
					   last_modified_time = sysdate
		when not matched then
			insert
			(BO_TRANS_ORDER_CUSTOMER_ID, BO_TRANS_ORDER_ID, CUSTOMER_COLLECT, CUSTOMER_NAME, SUB_CUSTOMER)
			values
				(b.boTransOrderCustomerId,b.boTransOrderId,b.customerCollect, b.customerName, b.subCustomer)
    </update>

	<select id="queryByOrderId" resultType="com.wtyt.dao.bean.syf.BoTransOrderCustomerBean">
        SELECT
            T.CUSTOMER_COLLECT customerCollect,
            T.CUSTOMER_NAME customerName,
    		T.SUB_CUSTOMER subCustomer
        FROM T_BO_TRANS_ORDER_CUSTOMER T
        WHERE T.IS_DEL = 0
            AND T.BO_TRANS_ORDER_ID  = #{boTransOrderId}
    </select>




</mapper>
