package com.wtyt.reviews.service;

import com.wtyt.reviews.bean.ReviewsCollectBean;
import com.wtyt.reviews.bean.request.*;
import com.wtyt.reviews.bean.response.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年03月15日 15时20分
 */
public interface ReviewsService {

    /**
     * 创建评价主体
     * @param reqCreate
     */
    void createReviewsSubject(ReqCreateBean reqCreate);

    /**
     * 删除评价主体
     * @param reqDelete
     */
    void deleteReviewsSubject(ReqDeleteBean reqDelete);

    /**
     * 获取评价得分
     * @param reqDetail
     * @return
     */
    List<ReviewsCollectBean> getReviewsScoreList(ReqDetailBean reqDetail);

    /**
     * 恢复评价主体
     * @param reqRecovery
     */
    void recoveryReviewsSubject(ReqRecoveryBean reqRecovery);

    /**
     * 替换评价对象
     * @param reqReplace
     */
    void replaceReviewsTarget(ReqReplaceBean reqReplace);

    /**
     * 5329573-绩效管理-评价主体详情
     * @param data
     * @return
     * @throws Exception
     */
    Resp5329573Bean reviewsSubjectDetail(Req5329573Bean data) throws Exception;

    /**
     * 5329574-绩效管理-提交评价
     * @param data
     * @throws Exception
     */
    void submitReviews(Req5329574Bean data) throws Exception;

    /**
     * 5329575-绩效管理-用户评价列表（汇总）
     * @param data
     * @return
     * @throws Exception
     */
    Resp5329575Bean userReviewsCollect(Req5329575Bean data) throws Exception;

    /**
     * 5329576-绩效管理-用户评价列表
     * @param data
     * @return
     * @throws Exception
     */
    Resp5329576Bean userReviewsList(Req5329576Bean data) throws Exception;

    /**
     * 5329577-绩效管理-用户评价删除
     * @param data
     * @return
     * @throws Exception
     */
    void deleteReviews(Req5329577Bean data) throws Exception;

    /**
     * 5329578-绩效管理-评价中心列表
     * @param data
     * @return
     * @throws Exception
     */
    Resp5329578Bean centerReviewsList(Req5329578Bean data) throws Exception;

    /**
     * 5329579-绩效管理-评价中心-个人评价列表（汇总）
     * @param data
     * @return
     * @throws Exception
     */
    Resp5329579Bean centerPersonalReviewsCollect(Req5329579Bean data) throws Exception;

    /**
     * 5329580-绩效管理-评价中心-个人评价列表
     * @param data
     * @return
     * @throws Exception
     */
    Resp5329580Bean centerPersonalReviewsList(Req5329580Bean data) throws Exception;

    /**
     * 5330279-绩效管理-用户评价数据汇总
     * @param data
     * @return
     * @throws Exception
     */
    Resp5330279Bean userReviewsCollect(Req5330279Bean data) throws Exception;
}
