<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskTemplateBlackMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTaskTemplateBlackBean" id="BoTaskTemplateBlackMap">
        <result property="boTaskTemplateBlackId" column="BO_TASK_TEMPLATE_BLACK_ID" jdbcType="VARCHAR"/>
        <result property="startProvinceName" column="START_PROVINCE_NAME" jdbcType="VARCHAR"/>
        <result property="startCityName" column="START_CITY_NAME" jdbcType="VARCHAR"/>
        <result property="startCountyName" column="START_COUNTY_NAME" jdbcType="VARCHAR"/>
        <result property="loadingAddressName" column="LOADING_ADDRESS_NAME" jdbcType="VARCHAR"/>
        <result property="endProvinceName" column="END_PROVINCE_NAME" jdbcType="VARCHAR"/>
        <result property="endCityName" column="END_CITY_NAME" jdbcType="VARCHAR"/>
        <result property="endCountyName" column="END_COUNTY_NAME" jdbcType="VARCHAR"/>
        <result property="unloadingAddressName" column="UNLOADING_ADDRESS_NAME" jdbcType="VARCHAR"/>
        <result property="goodsName" column="GOODS_NAME" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
    </resultMap>
    <insert id="insert">
        insert into T_BO_TASK_TEMPLATE_BLACK(BO_TASK_TEMPLATE_BLACK_ID, ORG_ID, START_PROVINCE_NAME, START_CITY_NAME, START_COUNTY_NAME, LOADING_ADDRESS_NAME, END_PROVINCE_NAME, END_CITY_NAME, END_COUNTY_NAME, UNLOADING_ADDRESS_NAME, GOODS_NAME)
        values (${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()}, #{orgId},#{startProvinceName}, #{startCityName}, #{startCountyName}, #{loadingAddressName}, #{endProvinceName}, #{endCityName}, #{endCountyName}, #{unloadingAddressName}, #{goodsName})
    </insert>
    <update id="deleteByIds">
        UPDATE T_BO_TASK_TEMPLATE_BLACK SET IS_DEL = 1,LAST_MODIFIED_TIME =SYSDATE WHERE BO_TASK_TEMPLATE_BLACK_ID IN
        <foreach collection="ids" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </update>
    <select id="queryByOrgId" resultMap="BoTaskTemplateBlackMap" fetchSize="200">
        SELECT
            BO_TASK_TEMPLATE_BLACK_ID,
            START_PROVINCE_NAME,
            START_CITY_NAME,
            START_COUNTY_NAME,
            LOADING_ADDRESS_NAME,
            END_PROVINCE_NAME,
            END_CITY_NAME,
            END_COUNTY_NAME,
            UNLOADING_ADDRESS_NAME,
            GOODS_NAME,
            ORG_ID
        FROM
            T_BO_TASK_TEMPLATE_BLACK
        WHERE
            IS_DEL = 0
            AND ORG_ID = #{orgId}
    </select>
    <select id="list" resultMap="BoTaskTemplateBlackMap" >
        SELECT
            BO_TASK_TEMPLATE_BLACK_ID,
            START_PROVINCE_NAME,
            START_CITY_NAME,
            START_COUNTY_NAME,
            LOADING_ADDRESS_NAME,
            END_PROVINCE_NAME,
            END_CITY_NAME,
            END_COUNTY_NAME,
            UNLOADING_ADDRESS_NAME,
            GOODS_NAME,
            ORG_ID
        FROM
            T_BO_TASK_TEMPLATE_BLACK
        WHERE
            IS_DEL = 0
            AND ORG_ID = #{orgId}
            <if test="startProvinceName!=null and startProvinceName!=''">
                AND START_PROVINCE_NAME = #{startProvinceName}
            </if>
            <if test="startCityName!=null and startCityName!=''">
                AND START_CITY_NAME = #{startCityName}
            </if>
            <if test="startCountyName!=null and startCountyName!=''">
                AND START_COUNTY_NAME = #{startCountyName}
            </if>
            <if test="loadingAddressName!=null and loadingAddressName!=''">
                AND LOADING_ADDRESS_NAME = #{loadingAddressName}
            </if>
            <if test="endProvinceName!=null and endProvinceName!=''">
                AND END_PROVINCE_NAME = #{endProvinceName}
            </if>
            <if test="endCityName!=null and endCityName!=''">
                AND END_CITY_NAME = #{endCityName}
            </if>
            <if test="endCountyName!=null and endCountyName!=''">
                AND END_COUNTY_NAME = #{endCountyName}
            </if>
            <if test="unloadingAddressName!=null and unloadingAddressName!=''">
                AND UNLOADING_ADDRESS_NAME = #{unloadingAddressName}
            </if>
            <if test="goodsName!=null and goodsName!=''">
                AND GOODS_NAME = #{goodsName}
            </if>
    </select>

</mapper>

