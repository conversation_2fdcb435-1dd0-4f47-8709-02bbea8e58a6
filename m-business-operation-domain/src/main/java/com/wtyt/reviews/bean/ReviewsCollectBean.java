package com.wtyt.reviews.bean;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年03月15日 15时24分
 */
@Setter
@Getter
public class ReviewsCollectBean implements Serializable {

    private static final long serialVersionUID = -3299985806762968184L;
    private String pmReviewsId;
    private String pmReviewsSubjectId;
    private String parentId;
    private String reviewerIdentity;
    private String reviewerId;
    private String reviewerName;
    private String reviewerMobile;
    private String score;
    private String content;
    private String isAnonymity;

    private String orgId;
    private String subjectType;
    private String subjectId;
    private String reviewsTarget;
    private String reviewsTargetId;
    private String reviewsTargetName;
    private String reviewsTargetMobile;
    private String reviewsTime;
    private String isDel;
    private String createdTime;
    private String minTime;
    private String maxTime;
    private int totalCount;
    private int reviewsCount;
    private double avgScore;
    private double totalScore;
}