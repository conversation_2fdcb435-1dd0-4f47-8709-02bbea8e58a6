<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskBxMailMapper">

    <insert id="insert">
        INSERT INTO T_BO_TASK_BX_MAIL(
            BO_TASK_BX_MAIL_ID,
            BO_TRANS_TASK_ID,
            OPERATION_NAME,
            OPERATION_TIME,
            SENDER,
            RECIPIENTS,
            CC_RECIPIENTS,
            SERIAL_NUMBER,
            DELIVERY_TIME,
            CUSTOMER_NAME,
            LOADING_PLACE_NAME,
            UNLOADING_PLACE_NAME,
            GOODS_NAME,
            GOODS_AMOUNT,
            GOODS_AMOUNT_TYPE,
            BILL_LADING_NUMBER,
            INS_FEE,
            CUSTOMER_REMARK,
            MAIL_SEND_STATE) VALUES (
                                                #{boTaskBxMailId},
                                                #{boTransTaskId},
                                                #{operationName},
                                                to_date(#{operationTime},'yyyy-mm-dd hh24:mi:ss'),
                                                #{sender},
                                                #{recipients},
                                                #{ccRecipients},
                                                #{serialNumber},
                                                to_date(#{deliveryTime},'yyyy-MM-dd'),
                                                #{customerName},
                                                #{loadingPlaceName},
                                                #{unloadingPlaceName},
                                                #{goodsName},
                                                #{goodsAmount},
                                                #{goodsAmountType},
                                                #{billLadingNumber},
                                                #{insFee},
                                                #{customerRemark},
                                                #{mailSendState})
    </insert>

    <select id="selectByBoTransTaskId" resultType="com.wtyt.dao.bean.syf.BoTaskBxMail">
        SELECT BO_TASK_BX_MAIL_ID boTaskBxMailId,
            BO_TRANS_TASK_ID boTransTaskId,
            OPERATION_NAME operationName,
            to_char(OPERATION_TIME,'yyyy-mm-dd hh24:mi:ss') operationTime,
            SENDER sender,
            RECIPIENTS recipients,
            CC_RECIPIENTS ccRecipients,
            SERIAL_NUMBER serialNumber,
            to_char(DELIVERY_TIME,'yyyy-mm-dd') deliveryTime,
            CUSTOMER_NAME customerName,
            LOADING_PLACE_NAME loadingPlaceName,
            UNLOADING_PLACE_NAME unloadingPlaceName,
            GOODS_NAME goodsName,
            GOODS_AMOUNT goodsAmount,
            GOODS_AMOUNT_TYPE goodsAmountType,
            BILL_LADING_NUMBER billLadingNumber,
            TO_CHAR(INS_FEE, 'FM999999990.00') insFee,
            CUSTOMER_REMARK customerRemark,
            MAIL_SEND_STATE mailSendState
        FROM T_BO_TASK_BX_MAIL
        WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <update id="updateById">
        UPDATE T_BO_TASK_BX_MAIL
        SET
            BO_TRANS_TASK_ID = #{boTransTaskId},
            OPERATION_NAME = #{operationName},
            OPERATION_TIME = to_date(#{operationTime},'yyyy-mm-dd hh24:mi:ss'),
            SENDER = #{sender},
            RECIPIENTS = #{recipients},
            CC_RECIPIENTS = #{ccRecipients},
            DELIVERY_TIME = to_date(#{deliveryTime},'yyyy-mm-dd hh24:mi:ss'),
            CUSTOMER_NAME = #{customerName},
            LOADING_PLACE_NAME = #{loadingPlaceName},
            UNLOADING_PLACE_NAME = #{unloadingPlaceName},
            GOODS_NAME = #{goodsName},
            GOODS_AMOUNT = #{goodsAmount},
            GOODS_AMOUNT_TYPE = #{goodsAmountType},
            BILL_LADING_NUMBER = #{billLadingNumber},
            INS_FEE = #{insFee},
            CUSTOMER_REMARK = #{customerRemark},
            MAIL_SEND_STATE = #{mailSendState},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TASK_BX_MAIL_ID = #{boTaskBxMailId} and IS_DEL = 0
    </update>
</mapper>