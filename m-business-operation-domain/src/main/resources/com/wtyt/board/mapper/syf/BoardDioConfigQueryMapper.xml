<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardDioConfigQueryMapper">

    <select id="queryGroupDioConfig" resultType="com.wtyt.board.bean.BoGroupDioConfigBean">
        SELECT
            r.BO_GROUP_DIO_CONFIG_REL_ID boGroupDioConfigRelId,
            r.G<PERSON>UP_TYPE groupType,
            r.GROUP_CODE groupCode,
            c.BO_DIO_CONFIG_ID boDioConfigId,
            c.DATA_QUERY_TYPE dataQueryType,
            c.DIO_SEARCH_CONFIG dioSearchConfig,
            c.DIO_CONFIG dioConfig
        FROM
            T_BO_GROUP_DIO_CONFIG_REL r
            INNER JOIN T_BO_DIO_CONFIG c ON r.BO_DIO_CONFIG_ID = c.BO_DIO_CONFIG_ID
        WHERE
            r.IS_DEL = 0
            AND c.IS_DEL=0
            AND c.DIO_TYPE = #{dioType}
            AND r.GROUP_CODE = #{groupCode}
            AND r.GROUP_TYPE = #{groupType}
    </select>

    <select id="queryGroupDioConfigById" resultType="com.wtyt.board.bean.BoGroupDioConfigBean">
        SELECT
            r.GROUP_TYPE groupType,
            r.GROUP_CODE groupCode,
            c.BO_DIO_CONFIG_ID boDioConfigId,
            c.DATA_QUERY_TYPE dataQueryType,
            c.PRICE_SUB_GUARANTEE priceSubGuarantee
        FROM
            T_BO_DIO_CONFIG c
            INNER JOIN T_BO_GROUP_DIO_CONFIG_REL r on r.BO_DIO_CONFIG_ID = c.BO_DIO_CONFIG_ID
        WHERE
            c.IS_DEL = 0
            AND r.IS_DEL = 0
            AND r.BO_GROUP_DIO_CONFIG_REL_ID = #{boGroupDioConfigRelId}
    </select>

    <resultMap id="customerNameMap" type="com.wtyt.bo.bean.DioCustomerNameBean">
        <result property="customerCollection" column="CUSTOMER_COLLECTION" />
        <collection property="cargoOwnerList" ofType="com.wtyt.bo.bean.DioCustomerNameBean$CustomerName">
            <result property="cargoOwner" column="CARGO_OWNER"/>
        </collection>
    </resultMap>


    <select id="queryGroupDioTaskPageConfigById"
                     resultType="com.wtyt.board.bean.BoGroupDioConfigBean">
        SELECT
            <choose>
                <when test="taskConfigType == '1'.toString()">
                    c.TASK_DETAIL_SEARCH taskDetailSearch
                </when>
                <otherwise>
                    c.TASK_DETAIL_HEAD taskDetailHead,
                    r.GROUP_TYPE groupType,
                    r.GROUP_CODE groupCode,
                    c.BO_DIO_CONFIG_ID boDioConfigId,
                    c.DATA_QUERY_TYPE dataQueryType,
                    c.PRICE_SUB_GUARANTEE priceSubGuarantee
                </otherwise>
            </choose>
        FROM
            T_BO_DIO_CONFIG c
            INNER JOIN T_BO_GROUP_DIO_CONFIG_REL r on r.BO_DIO_CONFIG_ID = c.BO_DIO_CONFIG_ID
        WHERE
            c.IS_DEL = 0
            AND r.IS_DEL = 0
            AND r.BO_GROUP_DIO_CONFIG_REL_ID = #{boGroupDioConfigRelId}
    </select>

    <update id="updateDioConfig">
        UPDATE T_BO_DIO_CONFIG SET
            <if test="dataQueryType != null and dataQueryType != ''">
                DATA_QUERY_TYPE=#{dataQueryType},
            </if>
            <if test="dioSearchConfig != null and dioSearchConfig != ''">
                DIO_SEARCH_CONFIG=#{dioSearchConfig},
            </if>
            <if test="dioConfig != null and dioConfig != ''">
                DIO_CONFIG=#{dioConfig},
            </if>
            <if test="taskDetailSearch != null and taskDetailSearch != ''">
                TASK_DETAIL_SEARCH=#{taskDetailSearch},
            </if>
            <if test="taskDetailHead != null and taskDetailHead != ''">
                TASK_DETAIL_HEAD=#{taskDetailHead},
            </if>
            LAST_MODIFIED_TIME = SYSDATE
         WHERE BO_DIO_CONFIG_ID=#{boDioConfigId}
    </update>
</mapper>
