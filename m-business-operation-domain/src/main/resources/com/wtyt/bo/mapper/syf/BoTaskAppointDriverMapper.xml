<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoTaskAppointDriverMapper">
    <select id="countByCondition" parameterType="com.wtyt.bo.bean.request.Req5330228Bean" resultType="com.wtyt.bo.bean.BoTaskAppointDriverBean">
        SELECT
            TO_CHAR(TE.HYB_RECEIVED_TIME, 'YYYY-MM-DD') receiveTime,
            COUNT(*) appointNum
        FROM
            T_BO_TRANS_TASK TT
        JOIN T_BO_TRANS_TASK_EXTRA TE ON
            TT.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
            AND TE.IS_DEL = 0
        <if test="boTransPlanId != null and boTransPlanId != ''">
            JOIN (
                    SELECT
                    DISTINCT BO_TRANS_TASK_ID
                    FROM
                    T_BO_SHIPPING_LIST
                    WHERE
                    IS_DEL = 0
                    AND BO_TRANS_PLAN_ID = #{boTransPlanId}) SL ON
                SL.BO_TRANS_TASK_ID = TT.BO_TRANS_TASK_ID
        </if>
        WHERE
            TT.IS_DEL = 0
            AND TT.TRANSPORT_TYPE = 1
            <if test="boBusinessLineId != null and boBusinessLineId != ''">
                AND TT.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
            </if>
            <if test="boLineAssignRelId != null and boLineAssignRelId != ''">
                AND TE.BO_LINE_ASSIGN_REL_ID = #{boLineAssignRelId}
            </if>
            AND TE.HYB_RECEIVED_TIME IS NOT NULL
        GROUP BY
            TO_CHAR(TE.HYB_RECEIVED_TIME, 'YYYY-MM-DD')
    </select>
    <select id="countByMobileNoAndPlanId" resultType="com.wtyt.bo.bean.BoTaskAppointDriverBean">
        SELECT
            SL.BO_TRANS_PLAN_ID boTransPlanId,
            COUNT(*) appointNum
        FROM
            T_BO_TRANS_TASK TT
        JOIN T_BO_SHIPPING_LIST SL ON
            SL.BO_TRANS_TASK_ID = TT.BO_TRANS_TASK_ID
            AND SL.IS_DEL = 0
        WHERE
            TT.IS_DEL = 0
            AND TT.TRANSPORT_TYPE = 1
            AND TT.MOBILE_NO =#{driverMobileNo}
            <if test="planId!=null and planId!=''">
                AND SL.BO_TRANS_PLAN_ID = #{planId}
            </if>
        GROUP BY
            SL.BO_TRANS_PLAN_ID
    </select>
    <select id="page" resultType="com.wtyt.bo.bean.BoTaskAppointDriverBean">
        SELECT
            TT.MOBILE_NO mobileNo,
            TT.CART_BADGE_NO cartBadgeNo,
            TT.DRIVER_NAME driverName,
            TT.ORG_ID orgId,
            TE.CART_TYPE cartType,
            TE.CART_LENGTH cartLength,
            TO_CHAR(TE.HYB_RECEIVED_TIME, 'YYYY-MM-DD') receiveTime,
            TO_CHAR(TT.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
        FROM
            T_BO_TRANS_TASK TT
        JOIN T_BO_TRANS_TASK_EXTRA TE ON
            TT.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
            AND TE.IS_DEL = 0
        <if test="planId != null and planId != ''">
            JOIN (
                SELECT
                    DISTINCT BO_TRANS_TASK_ID
                    FROM
                T_BO_SHIPPING_LIST
                    WHERE
                    IS_DEL = 0
                    AND BO_TRANS_PLAN_ID = #{planId}) SL ON
                SL.BO_TRANS_TASK_ID = TT.BO_TRANS_TASK_ID
        </if>
        WHERE
            TT.IS_DEL = 0
            AND TT.TRANSPORT_TYPE = 1
            AND TE.HYB_RECEIVED_TIME IS NOT NULL
            <if test="boBusinessLineId != null and boBusinessLineId != ''">
                AND TT.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
            </if>
            <if test="boLineAssignRelId != null and boLineAssignRelId != ''">
                AND TE.BO_LINE_ASSIGN_REL_ID = #{boLineAssignRelId}
            </if>
            <if test="receiveTime!=null and receiveTime!=''">
                AND TO_CHAR(TE.HYB_RECEIVED_TIME, 'YYYY-MM-DD') = #{receiveTime}
            </if>
        ORDER BY
            TE.HYB_RECEIVED_TIME DESC
    </select>
    <select id="countByPlanIds" resultType="com.wtyt.bo.bean.BoTaskAppointDriverBean">
        SELECT
            SL.BO_TRANS_PLAN_ID boTransPlanId,
            COUNT(*) appointNum
        FROM
            T_BO_TRANS_TASK TT
        JOIN T_BO_TRANS_TASK_EXTRA TE ON
            TT.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
            AND TE.IS_DEL = 0
        JOIN (
            SELECT
                DISTINCT BO_TRANS_TASK_ID,
                BO_TRANS_PLAN_ID
            FROM
                T_BO_SHIPPING_LIST
            WHERE
                IS_DEL = 0
                AND BO_TRANS_PLAN_ID IN
                <foreach collection="list" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            ) SL ON
                SL.BO_TRANS_TASK_ID = TT.BO_TRANS_TASK_ID
        WHERE
            TT.IS_DEL = 0
            AND TT.TRANSPORT_TYPE = 1
            AND TE.HYB_RECEIVED_TIME IS NOT NULL
        GROUP BY
            SL.BO_TRANS_PLAN_ID
    </select>
</mapper>
