<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardBpProgressMapper">

    <select id="queryAlarmTaskNumByLineIds" resultType="java.lang.Integer">
        SELECT
            count(DISTINCT A.TAX_WAYBILL_ID)
        FROM
            T_BO_TRANS_TASK T
        JOIN T_BO_TRANS_TASK_EXTRA E ON
            T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
            AND E.IS_DEL = 0
        JOIN T_BO_TRANS_NODE_ALARM A ON
            T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
            AND A.ALARM_TYPE = 1
            AND A.IS_DEL = 0
            AND A.NODE_DATA_TYPE IN (2, 3, 9, 12, 14, 15)
        WHERE
            T.IS_DEL = 0
            AND E.HYB_RECEIVED_TIME IS NOT NULL
            AND T.BO_BUSINESS_LINE_ID IN
            <foreach collection="boBusinessLineIds" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
    </select>

    <select id="queryTaskNumByLineIds" resultType="java.lang.Integer">
        SELECT
        count(T.BO_TRANS_TASK_ID)
        FROM
        T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_EXTRA E ON
        T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        WHERE
        T.IS_DEL = 0
        AND E.IS_DEL = 0
        AND E.HYB_RECEIVED_TIME IS NOT NULL
        AND T.BO_BUSINESS_LINE_ID IN
        <foreach collection="boBusinessLineIds" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </select>
    <select id="queryTaskNumStateByLineIds" resultType="com.wtyt.board.bean.LineTaskStateBean">
        SELECT
            BO_BUSINESS_LINE_ID boBusinessLineId,
            STATE ,
            count(BO_TRANS_TASK_ID) taskNum,
            SUM(NVL(LOADING_TONNAGE, 0)) loadingTonnage,
            SUM(NVL(UNLOADING_TONNAGE, 0)) unloadingTonnage
        FROM
            T_BO_TRANS_TASK t
        WHERE
            IS_DEL = 0
            AND STATE IN (1, 2)
            AND BO_BUSINESS_LINE_ID IN
            <foreach collection="boBusinessLineIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
             GROUP BY BO_BUSINESS_LINE_ID ,STATE
    </select>


</mapper>
