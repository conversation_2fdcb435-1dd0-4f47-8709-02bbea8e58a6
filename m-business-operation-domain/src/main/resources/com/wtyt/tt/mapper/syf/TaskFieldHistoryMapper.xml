<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.tt.mapper.syf.TaskFieldHistoryMapper">

    <select id="queryDispatchUserIdByTaskAllocateOrgIds" resultType="java.lang.String">
        SELECT
            DISTINCT nr.USER_ID
        FROM
        T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA te ON
        te.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_NODE_RECORD nr ON
        nr.BO_TRANS_NODE_RECORD_ID = te.DISPATCH_CAR_RECORD_ID
        JOIN T_BO_TRANS_TASK_ALLOCATE ta ON ta.BO_TRANS_TASK_ID =t.BO_TRANS_TASK_ID AND ta.IS_DEL =0
        WHERE
        (ta.ORG_ID IN
        <foreach collection="orgIds" index="index" item="orgId" open="(" separator="," close=")">
            <if test="(index % 999) == 998"> NULL) OR ta.ORG_ID IN(</if>#{orgId}
        </foreach>
        )
        <if test="searchDays != null">
            AND t.CREATED_TIME > SYSDATE - #{searchDays}
        </if>
        AND nr.USER_ID !=-1
        AND t.IS_DEL = 0
    </select>

    <select id="queryCusFieldsByOrgIdAndFieldKey" resultType="java.lang.String">
        SELECT
            DISTINCT TCF.FIELD_VALUE
        FROM
        T_BO_TRANS_TASK t
        JOIN T_BO_TASK_CUS_FIELD tcf ON
        tcf.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        WHERE
        t.ORG_ID = #{orgId}
        AND t.IS_DEL = 0
        AND tcf.IS_DEL = 0
        AND TCF.FIELD_KEY = #{fieldKey}
        AND tcf.FIELD_VALUE IS NOT NULL
    </select>

    <select id="queryLatestGoodsName" resultType="java.lang.String" fetchSize="200">
        SELECT
            GOODS_NAME
        FROM
        (
            SELECT
            t.GOODS_NAME,
            t.CREATED_TIME
            FROM
            T_BO_TRANS_TASK t
            WHERE
            t.ORG_ID = #{orgId}
            AND t.IS_DEL = 0
            AND t.CREATED_TIME > SYSDATE -180
            AND t.GOODS_NAME IS NOT NULL
            UNION ALL
            SELECT
            t.GOODS_NAME,
            t.CREATED_TIME
            FROM
            T_BO_TRANS_TASK t
            JOIN T_BO_TRANS_TASK_ALLOCATE a ON
            a.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            WHERE
            a.ORG_ID = #{orgId}
            AND t.IS_DEL = 0
            AND a.IS_DEL = 0
            AND t.CREATED_TIME > SYSDATE -180
            AND t.GOODS_NAME IS NOT NULL
        ) TEMP
        ORDER BY
            CREATED_TIME DESC
    </select>

    <select id="queryDispathUserIdByTaskOrgIds" resultType="java.lang.String">
        SELECT
            DISTINCT nr.USER_ID
        FROM
        T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA te ON
        te.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_NODE_RECORD nr ON
        nr.BO_TRANS_NODE_RECORD_ID = te.DISPATCH_CAR_RECORD_ID
        WHERE
        t.IS_DEL = 0
        AND (t.ORG_ID IN
        <foreach collection="orgIds" index="index" item="orgId" open="(" separator="," close=")">
            <if test="(index % 999) == 998"> NULL) OR t.ORG_ID IN(</if>#{orgId}
        </foreach>
        )
        <if test="searchDays != null">
            AND t.CREATED_TIME > SYSDATE - #{searchDays}
        </if>
        AND t.TRANS_MODE NOT IN (3, 4)
        AND nr.USER_ID !=-1
    </select>

    <select id="queryDispathGroupIdByTaskOrgIds" resultType="java.lang.String">
        SELECT
            DISTINCT TGR.GROUP_ID
        FROM
        T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA te ON
        te.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_TASK_GROUP_REL tgr ON
        tgr.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND TGR.IS_DEL=0 AND TGR.SUPPLIER_TYPE IN (1,2)
        WHERE
        t.IS_DEL = 0
        AND (t.ORG_ID IN
        <foreach collection="orgIds" index="index" item="orgId" open="(" separator="," close=")">
            <if test="(index % 999) == 998"> NULL) OR t.ORG_ID IN(</if>#{orgId}
        </foreach>
        )
        <if test="searchDays != null">
            AND t.CREATED_TIME > SYSDATE - #{searchDays}
        </if>
        AND t.TRANS_MODE IN (3, 4)
        AND TGR.GROUP_ID IS NOT NULL
    </select>

</mapper>
