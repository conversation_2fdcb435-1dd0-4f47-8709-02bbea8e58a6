<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskModifyRecordMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTaskModifyRecord" id="TBoTaskModifyRecordMap">
        <result property="boTaskModifyRecordId" column="BO_TASK_MODIFY_RECORD_ID" jdbcType="VARCHAR"/>
        <result property="modifyEntityId" column="MODIFY_ENTITY_ID" jdbcType="VARCHAR"/>
        <result property="modifyType" column="MODIFY_TYPE" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="modifySource" column="MODIFY_SOURCE" jdbcType="VARCHAR"/>
        <result property="modifyUserId" column="MODIFY_USER_ID" jdbcType="VARCHAR"/>
        <result property="modifyUserName" column="MODIFY_USER_NAME" jdbcType="VARCHAR"/>
        <result property="modifyField" column="MODIFY_FIELD" jdbcType="VARCHAR"/>
        <result property="valueBeforeModify" column="VALUE_BEFORE_MODIFY" jdbcType="VARCHAR"/>
        <result property="valueAfterModify" column="VALUE_AFTER_MODIFY" jdbcType="VARCHAR"/>
        <result property="modifyTime" column="MODIFY_TIME" jdbcType="TIMESTAMP"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="modifyFieldName" column="MODIFY_FIELD_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">
        T_BO_TASK_MODIFY_RECORD
    </sql>

    <!--查询单个-->
    <select id="queryById" parameterType="com.wtyt.dao.bean.syf.BoTaskModifyRecord" resultMap="TBoTaskModifyRecordMap">
        select
        *
        from T_BO_TASK_MODIFY_RECORD
        where BO_TASK_MODIFY_RECORD_ID = #{boTaskModifyRecordId} AND IS_DEL = 0
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="TBoTaskModifyRecordMap">
        select
            BO_TASK_MODIFY_RECORD_ID,
            MODIFY_ENTITY_ID,
            MODIFY_TYPE,
            ORG_ID,
            MODIFY_SOURCE,
            MODIFY_USER_ID,
            MODIFY_USER_NAME,
            MODIFY_FIELD,
            VALUE_BEFORE_MODIFY,
            VALUE_AFTER_MODIFY,
            MODIFY_TIME,
            IS_DEL,
            CREATED_TIME,
            LAST_MODIFIED_TIME,
            NOTE
        from T_BO_TASK_MODIFY_RECORD
        <where>
            <if test="boTaskModifyRecordId != null and boTaskModifyRecordId != ''">
                and BO_TASK_MODIFY_RECORD_ID = #{boTaskModifyRecordId}
            </if>
            <if test="modifyEntityId != null and modifyEntityId != ''">
                and MODIFY_ENTITY_ID = #{modifyEntityId}
            </if>
            <if test="modifyType != null and modifyType != ''">
                and MODIFY_TYPE = #{modifyType}
            </if>
            <if test="orgId != null and orgId != ''">
                and ORG_ID = #{orgId}
            </if>
            <if test="modifySource != null and modifySource != ''">
                and MODIFY_SOURCE = #{modifySource}
            </if>
            <if test="modifyUserId != null and modifyUserId != ''">
                and MODIFY_USER_ID = #{modifyUserId}
            </if>
            <if test="modifyUserName != null and modifyUserName != ''">
                and MODIFY_USER_NAME = #{modifyUserName}
            </if>
            <if test="modifyField != null and modifyField != ''">
                and MODIFY_FIELD = #{modifyField}
            </if>
            <if test="valueBeforeModify != null and valueBeforeModify != ''">
                and VALUE_BEFORE_MODIFY = #{valueBeforeModify}
            </if>
            <if test="valueAfterModify != null and valueAfterModify != ''">
                and VALUE_AFTER_MODIFY = #{valueAfterModify}
            </if>
            <if test="modifyTime != null">
                and MODIFY_TIME = #{modifyTime}
            </if>
            <if test="isDel != null and isDel != ''">
                and IS_DEL = #{isDel}
            </if>
            <if test="createdTime != null">
                and CREATED_TIME = #{createdTime}
            </if>
            <if test="lastModifiedTime != null">
                and LAST_MODIFIED_TIME = #{lastModifiedTime}
            </if>
            <if test="note != null and note != ''">
                and NOTE = #{note}
            </if>
        </where>
    </select>
    <select id="queryByModifyEntity" resultMap="TBoTaskModifyRecordMap">
        select * from <include refid="tableName"/>
        <where>
            MODIFY_ENTITY_ID IN
            <foreach collection="modifiedEntityIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND MODIFY_TYPE = #{modifiedType}
            AND IS_DEL = 0
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into T_BO_TASK_MODIFY_RECORD(BO_TASK_MODIFY_RECORD_ID, MODIFY_ENTITY_ID, MODIFY_TYPE, ORG_ID, MODIFY_SOURCE, MODIFY_USER_ID, MODIFY_USER_NAME, MODIFY_FIELD, VALUE_BEFORE_MODIFY, VALUE_AFTER_MODIFY, MODIFY_TIME, IS_DEL, CREATED_TIME, LAST_MODIFIED_TIME, NOTE)
        values (${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()}, #{modifyEntityId}, #{modifyType}, #{orgId}, #{modifySource}, #{modifyUserId}, #{modifyUserName}, #{modifyField}, #{valueBeforeModify}, #{valueAfterModify}, #{modifyTime}, 0, SYSDATE, SYSDATE, #{note})
    </insert>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/>
        (BO_TASK_MODIFY_RECORD_ID,
        MODIFY_ENTITY_ID,
        MODIFY_TYPE,
        ORG_ID,
        MODIFY_SOURCE,
        MODIFY_USER_ID,
        MODIFY_USER_NAME,
        MODIFY_FIELD,
        VALUE_BEFORE_MODIFY,
        VALUE_AFTER_MODIFY,
        MODIFY_TIME,
        BATCH_NO,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE,
        MODIFY_FIELD_NAME)
        <foreach collection="list" index="index" item="item" separator="UNION ALL">
            SELECT
            ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()} BO_TASK_MODIFY_RECORD_ID,
            #{item.modifyEntityId} MODIFY_ENTITY_ID,
            #{item.modifyType} MODIFY_TYPE,
            #{item.orgId} ORG_ID,
            #{item.modifySource} MODIFY_SOURCE,
            #{item.modifyUserId} MODIFY_USER_ID,
            #{item.modifyUserName} MODIFY_USER_NAME,
            #{item.modifyField} MODIFY_FIELD,
            #{item.valueBeforeModify} VALUE_BEFORE_MODIFY,
            #{item.valueAfterModify} VALUE_AFTER_MODIFY,
            #{item.modifyTime} MODIFY_TIME,
            #{item.batchNo} BATCH_NO,
            0 IS_DEL,
            SYSDATE CREATED_TIME,
            SYSDATE LAST_MODIFIED_TIME,
            #{item.note} NOTE,
            #{item.modifyFieldName}
            FROM DUAL
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update T_BO_TASK_MODIFY_RECORD
        <set>
            <if test="modifyEntityId != null and modifyEntityId != ''">
                MODIFY_ENTITY_ID = #{modifyEntityId},
            </if>
            <if test="modifyType != null and modifyType != ''">
                MODIFY_TYPE = #{modifyType},
            </if>
            <if test="orgId != null and orgId != ''">
                ORG_ID = #{orgId},
            </if>
            <if test="modifySource != null and modifySource != ''">
                MODIFY_SOURCE = #{modifySource},
            </if>
            <if test="modifyUserId != null and modifyUserId != ''">
                MODIFY_USER_ID = #{modifyUserId},
            </if>
            <if test="modifyUserName != null and modifyUserName != ''">
                MODIFY_USER_NAME = #{modifyUserName},
            </if>
            <if test="modifyField != null and modifyField != ''">
                MODIFY_FIELD = #{modifyField},
            </if>
            <if test="valueBeforeModify != null and valueBeforeModify != ''">
                VALUE_BEFORE_MODIFY = #{valueBeforeModify},
            </if>
            <if test="valueAfterModify != null and valueAfterModify != ''">
                VALUE_AFTER_MODIFY = #{valueAfterModify},
            </if>
            <if test="modifyTime != null">
                MODIFY_TIME = #{modifyTime},
            </if>
            <if test="isDel != null and isDel != ''">
                IS_DEL = #{isDel},
            </if>
            <if test="createdTime != null">
                CREATED_TIME = #{createdTime},
            </if>
            <if test="lastModifiedTime != null">
                LAST_MODIFIED_TIME = #{lastModifiedTime},
            </if>
            <if test="note != null and note != ''">
                NOTE = #{note},
            </if>
        </set>
        where BO_TASK_MODIFY_RECORD_ID = #{boTaskModifyRecordId}
    </update>

    <select id="listTaskModifyRecord" resultMap="TBoTaskModifyRecordMap">
        SELECT
            ORG_ID,
            MODIFY_USER_NAME,
            MODIFY_FIELD,
            VALUE_BEFORE_MODIFY,
            VALUE_AFTER_MODIFY,
            MODIFY_TIME,
            MODIFY_FIELD_NAME
        FROM T_BO_TASK_MODIFY_RECORD WHERE MODIFY_TYPE =1 AND MODIFY_ENTITY_ID = #{boTransTaskId} AND IS_DEL =0
        <choose>
            <when test="(modifyFieldInList != null and modifyFieldInList.size() > 0) or (modifyFieldLikeList != null and modifyFieldLikeList.size() > 0)">
                AND (
                <if test="modifyFieldInList != null and modifyFieldInList.size() > 0">
                    MODIFY_FIELD IN
                    <foreach collection="modifyFieldInList" item="item" open="(" separator="," close=")">
                            #{item}
                    </foreach>
                </if>
                <if test="modifyFieldLikeList != null and modifyFieldLikeList.size() > 0">
                    <if test="modifyFieldInList != null and modifyFieldInList.size() > 0">
                        OR
                    </if>
                    <foreach collection="modifyFieldLikeList" item="item" separator=" OR ">
                        MODIFY_FIELD LIKE CONCAT(#{item}, '%')
                    </foreach>
                </if>
                )
            </when>
            <otherwise>
                AND (
                    ORG_ID = #{orgId}
                    OR (INSTR(MODIFY_FIELD, 'dynamicTaskFee') = 0
                    <if test="difModifyFieldList != null and difModifyFieldList.size()>0">
                        AND MODIFY_FIELD NOT IN
                        <foreach collection="difModifyFieldList" item="item" index="index" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    )
                )
            </otherwise>
        </choose>
        <if test="notShowModifyFieldList != null and notShowModifyFieldList.size()>0">
            AND MODIFY_FIELD NOT IN
            <foreach collection="notShowModifyFieldList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY
        MODIFY_TIME DESC, BO_TASK_MODIFY_RECORD_ID DESC
    </select>

    <update id="batchTransferCertificateSnapRecord" parameterType="java.util.List">
        UPDATE T_BO_TASK_MODIFY_RECORD T
        <set>
            T.LAST_MODIFIED_TIME = SYSDATE,
            T.ORG_ID = #{data.toOrgId}
        </set>
        WHERE T.IS_DEL = 0
            AND T.MODIFY_TYPE = 2
            AND T.ORG_ID = #{data.fromOrgId}
            AND T.MODIFY_ENTITY_ID IN
            <foreach collection="snapShotIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </update>
    <update id="batchTransferModifyRecord" parameterType="java.util.List">
        UPDATE T_BO_TASK_MODIFY_RECORD T
        <set>
            T.LAST_MODIFIED_TIME = SYSDATE,
            T.ORG_ID = #{data.toOrgId}
        </set>
        WHERE
            T.MODIFY_TYPE = 1
            AND T.ORG_ID = #{data.fromOrgId}
            AND T.MODIFY_ENTITY_ID IN
            <foreach collection="taskIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </update>

</mapper>

