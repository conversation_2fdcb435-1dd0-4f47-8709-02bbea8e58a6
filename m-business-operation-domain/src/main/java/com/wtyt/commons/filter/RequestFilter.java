package com.wtyt.commons.filter;

import com.wtyt.commons.wrapper.HttpServletRequestWrapper;
import com.wtyt.domain.event.client.collector.wrapper.DomainEventRequestWrapper;
import com.wtyt.lg.bury.point.collector.wrapper.BuryPointRequestWrapper;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 请求过滤器
 * @author: <EMAIL>
 * @date: 2022年05月12日 14时02分
 */
public class RequestFilter implements Filter {


    @Override
    public void init(FilterConfig filterConfig) {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        /*if (request instanceof DomainEventRequestWrapper || request instanceof BuryPointRequestWrapper) {
            filterChain.doFilter(servletRequest, servletResponse);
        } else {*/
            HttpServletRequestWrapper requestWrapper = new HttpServletRequestWrapper(request);
            filterChain.doFilter(requestWrapper, servletResponse);
//        }
    }

    @Override
    public void destroy() {

    }
}