package com.wtyt.commons.featureprobe;

import com.google.common.collect.Maps;
import com.wtyt.common.toolkits.AlarmToolkit;
import com.wtyt.common.toolkits.NumberToolkit;
import com.wtyt.common.toolkits.SpringContextToolkit;
import com.wtyt.fepr.server.FPUser;
import com.wtyt.fepr.server.FeatureProbe;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * @description: 功能开关统一服务类
 * @author: HL
 * @create: 2023-10-20 10:43
 **/
public class FeatureProbeUtil {

    private static final Logger log = LoggerFactory.getLogger(FeatureProbeUtil.class);

    /**
     * 功能开关默认值：0
     * 0代表已发布
     */
    private static final double DEFAULT_FB_SWITCH_VALUE = 0;

    /**
     * 告警描述
     */
    private static final String ALARM_EXPAND_DESC = "功能开关%s调用失败";

    /**
     * 项目id key
     */
    private static final String ATTRS_KEY_ORG_ID = "orgId";

    /**
     * 新大陆动态表头功能开关默认值：2。
     */
    private static final double ORDER_DH_DEFAULT_VALUE = 2;

    private static final String ORDER_DH_DEFAULT_VALUE_STR = "2";


    private FeatureProbeUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static boolean isZhLineAutoFreight(String orgId) {
        double numberValue = 2;
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser().with("orgId", orgId);
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.ZH_LINE_AUTO_FREIGHT, user, 1);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.ZH_LINE_AUTO_FREIGHT + "调用失败");
            }
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }

    public static boolean isNeedPreRisk(String orgId) {
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser().with("orgId", orgId).stableRollout(orgId);
                return SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .boolValue(FeatureProbeSwith.NEED_PRE_RISK, user, true);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.NEED_PRE_RISK + "调用失败");
            }
        }
        return true;
    }

    /**
     * 运作须知结算标准
     * @param orgId
     * @return
     */
    public static boolean isNewYzxzSettlement(String orgId) {
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser().with("orgId", orgId);
                return SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .boolValue(FeatureProbeSwith.NEW_YZXZ_SETTLEMENT, user, false);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.NEW_YZXZ_SETTLEMENT + "调用失败");
            }
        }
        return false;
    }

    /**
     * 是否运费结构项调用新的接口
     * @param orgId
     * @return
     */
    public static boolean isNewTaskFee(String orgId) {
        double numberValue = 2;
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser().with("orgId", orgId);
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_WLHY, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.NEW_TASK_FEE, user, 1);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.NEW_TASK_FEE + "调用失败");
            }
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }

    /**
     * 是否是运作费组件
     * @param orgId
     * @return
     */
    public static boolean isTaskFeeComponent(String orgId) {
        double numberValue = 2;
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser().with("orgId", orgId);
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_WLHY, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.TASK_FEE_COMPONENT, user, 1);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.TASK_FEE_COMPONENT + "调用失败");
            }
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }

    /**
     * 纸质回单签收
     * @param orgId
     * @return
     */
    public static boolean isNewPaperSign(String orgId) {
        double numberValue = 2;
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser().with("orgId", orgId);
                numberValue = SpringContextToolkit.getBean("lgxdl", FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.PAPER_RECEIPT_SIGN, user, 1);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.PAPER_RECEIPT_SIGN + "调用失败");
            }
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }


    /**
     * 新大陆_v1.27.159.131_金颜错误地址客户侧修改
     *
     * @param orgId 项目id
     * @return true 表示代码需要走新的代码逻辑 ，false 表示代码不走新的逻辑，功能开关失效后返回true需要走新的逻辑
     */
    public static boolean isOrderAddressFrom5295018(String orgId) {

        Map<String, String> fpUserAttrs = Maps.newHashMap();
        if (StringUtils.isNotBlank(orgId)) {
            fpUserAttrs.put(ATTRS_KEY_ORG_ID, orgId);
        }
        /*
         * featureProbeSwitchValue 值为0 的时候 ，表示功能开关 已发布  ,返回 true
         * featureProbeSwitchValue 值为1 的时候 ，表示功能开关 未发布在灰度名单中 true
         * featureProbeSwitchValue 值为2 的时候 ，表示功能开关 未发布不在灰度名单中 ，返回false
         * */
        double featureProbeSwitchValue = getLgxdlFeatureProbeSwitchValue(FeatureProbeSwith.SR_ADDRESS_QUERY, fpUserAttrs,
                ORDER_DH_DEFAULT_VALUE);
        log.info("订单地址经纬度信息查询功能开关返回值为{}", featureProbeSwitchValue);
        return !StringUtils.equals(NumberToolkit.format(featureProbeSwitchValue), ORDER_DH_DEFAULT_VALUE_STR);
    }

    /**
     *  订单导入excel文件解析流式处理灰度
     *
     * @param orgId 项目id
     * @return true 表示代码需要走新的代码逻辑 ，false 表示代码不走新的逻辑，功能开关失效后返回true需要走新的逻辑
     */
    public static boolean isOrderExcelAnalysisStreamModel(String orgId) {

        Map<String, String> fpUserAttrs = Maps.newHashMap();
        if (StringUtils.isNotBlank(orgId)) {
            fpUserAttrs.put(ATTRS_KEY_ORG_ID, orgId);
        }
        /*
         * featureProbeSwitchValue 值为0 的时候 ，表示功能开关 已发布  ,返回 true
         * featureProbeSwitchValue 值为1 的时候 ，表示功能开关 未发布在灰度名单中 true
         * featureProbeSwitchValue 值为2 的时候 ，表示功能开关 未发布不在灰度名单中 ，返回false
         * */
        double featureProbeSwitchValue = getLgxdlFeatureProbeSwitchValue(FeatureProbeSwith.SR_ORDER_EXCEL_CREATE, fpUserAttrs,
                ORDER_DH_DEFAULT_VALUE);
        log.info("订单导入文件解析流式处理功能开关返回值为{}", featureProbeSwitchValue);
        return !StringUtils.equals(NumberToolkit.format(featureProbeSwitchValue), ORDER_DH_DEFAULT_VALUE_STR);
    }


    /**
     * true, 开启自定义货物数量单位功能
     * @param orgId 项目id
     * @return boolean
     * <AUTHOR> Qingcheng
     * @since 2024/7/22
     */
    public static boolean enableCustomerGoodsAmountType(String orgId) {

        Map<String, String> fpUserAttrs = Maps.newHashMap();
        if (StringUtils.isNotBlank(orgId)) {
            fpUserAttrs.put(ATTRS_KEY_ORG_ID, orgId);
        }
        /*
         * featureProbeSwitchValue 值为0 的时候 ，表示功能开关 已发布  ,返回 true
         * featureProbeSwitchValue 值为1 的时候 ，表示功能开关 未发布在灰度名单中 true
         * featureProbeSwitchValue 值为2 的时候 ，表示功能开关 未发布不在灰度名单中 ，返回false
         * */
        double featureProbeSwitchValue = getLgxdlFeatureProbeSwitchValue(FeatureProbeSwith.GOODS_AMOUNT_UNIT, fpUserAttrs,
                ORDER_DH_DEFAULT_VALUE);
        log.info("自定义货物货物数量单位功能开关返回值为{}", featureProbeSwitchValue);
        return !StringUtils.equals(NumberToolkit.format(featureProbeSwitchValue), ORDER_DH_DEFAULT_VALUE_STR);
    }

    /**
     * 获取新大陆功能开关值
     * featureProbeSwitchValue 值为0 的时候 ，表示功能开关 已发布
     * featureProbeSwitchValue 值为1 的时候 ，表示功能开关 未发布在灰度名单中
     * featureProbeSwitchValue 值为2 的时候 ，表示功能开关 未发布不在灰度名单中
     *
     * @param toggleKey    功能开关名称
     * @param fpUserAttrs  fpUser信息
     * @param defaultValue 默认值
     * @return String
     * <AUTHOR> Qingcheng
     * @since 2024/2/27
     */
    private static double getLgxdlFeatureProbeSwitchValue(String toggleKey, Map<String, String> fpUserAttrs, double defaultValue) {
        return getFeatureProbeSwitchValue(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, toggleKey, fpUserAttrs, defaultValue);
    }

    /**
     *
     * 获取网络货运功能开关值
     * featureProbeSwitchValue 值为0 的时候 ，表示功能开关 已发布
     * featureProbeSwitchValue 值为1 的时候 ，表示功能开关 未发布在灰度名单中
     * featureProbeSwitchValue 值为2 的时候 ，表示功能开关 未发布不在灰度名单中
     *
     * @param toggleKey    功能开关名称
     * @param fpUserAttrs  fpUser信息
     * @param defaultValue 默认值
     * @return double
     * <AUTHOR> Qingcheng
     * @since 2024/5/10
     */
    private static double getWlhyFeatureProbeSwitchValue(String toggleKey, Map<String, String> fpUserAttrs, double defaultValue) {
        return getFeatureProbeSwitchValue(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_WLHY, toggleKey, fpUserAttrs, defaultValue);
    }


    /**
     * 获取功能开关值
     * featureProbeSwitchValue 值为0 的时候 ，表示功能开关 已发布
     * featureProbeSwitchValue 值为1 的时候 ，表示功能开关 未发布在灰度名单中
     * featureProbeSwitchValue 值为2 的时候 ，表示功能开关 未发布不在灰度名单中
     *
     * @param projectName 项目名
     * @param toggleKey    功能开关名称
     * @param fpUserAttrs  fpUser信息
     * @param defaultValue 默认值
     * @return String
     * <AUTHOR> Qingcheng
     * @since 2024/5/10
     */
    private static double getFeatureProbeSwitchValue(String projectName, String toggleKey, Map<String, String> fpUserAttrs, double defaultValue) {
        if (MapUtils.isEmpty(fpUserAttrs)) {
            return DEFAULT_FB_SWITCH_VALUE;
        }
        try {
            FPUser user = new FPUser();
            user.setAttrs(fpUserAttrs);
            return SpringContextToolkit.getBean(projectName, FeatureProbe.class).numberValue(toggleKey, user,
                    defaultValue);
        } catch (Exception e) {
            AlarmToolkit.logAlarm(e.getLocalizedMessage(), e, String.format(ALARM_EXPAND_DESC, toggleKey));
            return DEFAULT_FB_SWITCH_VALUE;
        }
    }

    /**
     * 司机身份不一致产品优化灰度开关
     * @param orgId
     * @return
     */
    public static boolean driverDiffOptSwitch(String orgId) {
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser().with("orgId", orgId);
                return SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_WLHY, FeatureProbe.class)
                        .boolValue(FeatureProbeSwith.DRIVER_DIFF_OPT, user, false);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.DRIVER_DIFF_OPT + "调用失败");
            }
        }
        return false;
    }


    /**
     * 运力类型灰度
     * @param orgId
     * @return
     */
    public static boolean isTwoCapacityIntegration(String orgId) {
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser().with("orgId", orgId);
                double capacityIntegration = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_ZYGHBKYLJ, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.CAPACITY_INTEGRATION, user, 2);
                return StringUtils.equals(NumberToolkit.format(capacityIntegration), "2");
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.CAPACITY_INTEGRATION + "调用失败");
            }
        }
        return false;
    }


    public static boolean isOrderDhQuerySqlOpt(String orgId) {

        Map<String, String> fpUserAttrs = Maps.newHashMap();
        if (StringUtils.isNotBlank(orgId)) {
            fpUserAttrs.put(ATTRS_KEY_ORG_ID, orgId);
        }
        /*
         * featureProbeSwitchValue 值为0 的时候 ，表示功能开关 已发布  ,返回 true
         * featureProbeSwitchValue 值为1 的时候 ，表示功能开关 未发布在灰度名单中 true
         * featureProbeSwitchValue 值为2 的时候 ，表示功能开关 未发布不在灰度名单中 ，返回false
         * */
        double featureProbeSwitchValue = getLgxdlFeatureProbeSwitchValue(FeatureProbeSwith.SR_DH_ORDER_QUERY_OPT, fpUserAttrs,
                ORDER_DH_DEFAULT_VALUE);
        log.info("订单动态表头列表查询功能开关返回值为{}", featureProbeSwitchValue);
        return !StringUtils.equals(NumberToolkit.format(featureProbeSwitchValue), ORDER_DH_DEFAULT_VALUE_STR);
    }

    public static boolean isSplitOrderOpt(String orgId) {
        Map<String, String> fpUserAttrs = Maps.newHashMap();
        if (StringUtils.isNotBlank(orgId)) {
            fpUserAttrs.put(ATTRS_KEY_ORG_ID, orgId);
        }
        /*
         * featureProbeSwitchValue 值为0 的时候 ，表示功能开关 已发布  ,返回 true
         * featureProbeSwitchValue 值为1 的时候 ，表示功能开关 未发布在灰度名单中 true
         * featureProbeSwitchValue 值为2 的时候 ，表示功能开关 未发布不在灰度名单中 ，返回false
         * */
        double featureProbeSwitchValue = getLgxdlFeatureProbeSwitchValue(FeatureProbeSwith.XE_SPLIT_ORDER_OPT, fpUserAttrs,
                ORDER_DH_DEFAULT_VALUE);
        log.info("等比例拆分订单开关返回值为{}", featureProbeSwitchValue);
        return !StringUtils.equals(NumberToolkit.format(featureProbeSwitchValue), ORDER_DH_DEFAULT_VALUE_STR);
    }


    public static boolean isQuantityFrozen(String orgId) {
        Map<String, String> fpUserAttrs = Maps.newHashMap();
        if (StringUtils.isNotBlank(orgId)) {
            fpUserAttrs.put(ATTRS_KEY_ORG_ID, orgId);
        }
        /*
         * featureProbeSwitchValue 值为0 的时候 ，表示功能开关 已发布  ,返回 true
         * featureProbeSwitchValue 值为1 的时候 ，表示功能开关 未发布在灰度名单中 true
         * featureProbeSwitchValue 值为2 的时候 ，表示功能开关 未发布不在灰度名单中 ，返回false
         * */
        double featureProbeSwitchValue = getLgxdlFeatureProbeSwitchValue(FeatureProbeSwith.XE_QUANTITY_FROZEN, fpUserAttrs,
                ORDER_DH_DEFAULT_VALUE);
        log.info("订单动态表头列表查询功能开关返回值为{}", featureProbeSwitchValue);
        return !StringUtils.equals(NumberToolkit.format(featureProbeSwitchValue), ORDER_DH_DEFAULT_VALUE_STR);
    }

    public static boolean isBatchCorrectAddress(String orgId) {
        Map<String, String> fpUserAttrs = Maps.newHashMap();
        if (StringUtils.isNotBlank(orgId)) {
            fpUserAttrs.put(ATTRS_KEY_ORG_ID, orgId);
        }
        /*
         * featureProbeSwitchValue 值为0 的时候 ，表示功能开关 已发布  ,返回 true
         * featureProbeSwitchValue 值为1 的时候 ，表示功能开关 未发布在灰度名单中 true
         * featureProbeSwitchValue 值为2 的时候 ，表示功能开关 未发布不在灰度名单中 ，返回false
         * */
        double featureProbeSwitchValue = getLgxdlFeatureProbeSwitchValue(FeatureProbeSwith.SR_BATCH_CORRECT_ADDRESS, fpUserAttrs,
                ORDER_DH_DEFAULT_VALUE);
        log.info("纠正地址批量更新合单订单地址功能开关返回值为{}", featureProbeSwitchValue);
        return !StringUtils.equals(NumberToolkit.format(featureProbeSwitchValue), ORDER_DH_DEFAULT_VALUE_STR);
    }

    /**
     * 是否走新的全部查询优化
     * @param orgId
     * @return
     */
    public static boolean isNewAllSearchOpt(String orgId) {
        double numberValue = 2;
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser()
                        .with("orgId", orgId)
                        .stableRollout(orgId);
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.TASK_LIST_ALL_SEARCH_OPT, user, 1);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.TASK_LIST_ALL_SEARCH_OPT + "调用失败");
            }
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }

    /**
     * 是否走新的自动计算
     * @param orgId
     * @return
     */
    public static boolean isNewAutoCalc(String orgId) {
        double numberValue = 2;
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser()
                        .with("orgId", orgId)
                        .stableRollout(orgId);
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.AUTO_CALC_MIGRATE, user, 1);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.AUTO_CALC_MIGRATE + "调用失败");
            }
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }

    /**
     * 是否走新的运输节点
     * @param orgId
     * @return
     */
    public static boolean isNewTransportNode(String orgId, String userId) {
        double numberValue = 2;
        if (!StringUtils.isAllBlank(orgId, userId)) {
            try {
                FPUser user;
                if (StringUtils.isNotBlank(orgId)) {
                    user = new FPUser()
                            .with("orgId", orgId)
                            .stableRollout(orgId);
                } else {
                    user = new FPUser()
                            .with("userId", userId)
                            .stableRollout(userId);
                }
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.TRANSPORT_NODE, user, 1);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.TRANSPORT_NODE + "调用失败");
            }
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }

    /**
     * 是否走新的短信成本优化
     * @param orgId
     * @return
     */
    public static boolean isNewSmsCostOpt(String orgId) {
        double numberValue = 2;
        try {
            if (StringUtils.isNotBlank(orgId)) {
                FPUser user = new FPUser()
                        .with("orgId", orgId)
                        .stableRollout(orgId);
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_WLHY, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.SMS_COST_OPT, user, 1);
            }
        } catch (Exception e) {
            AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                    "功能开关" + FeatureProbeSwith.SMS_COST_OPT + "调用失败");
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }

    /**
     * 是否在取消运单灰度流程
     * @param orgId
     * @return
     */
    public static boolean isCancelWaybill(String orgId) {
        double numberValue = 2;
        try {
            if (StringUtils.isNotBlank(orgId)) {
                FPUser user = new FPUser()
                        .with("orgId", orgId)
                        .stableRollout(orgId);
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_WLHY, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.CANCEL_WAYBILL, user, 1);
            }
        } catch (Exception e) {
            AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                    "功能开关" + FeatureProbeSwith.CANCEL_WAYBILL + "调用失败");
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }

    /**
     * 是否显示司机身份证号码输入框
     * @param orgId
     * @return
     */
    public static boolean isPageShowDriverIdCard(String orgId) {
        double numberValue = 1;
        try {
            if (StringUtils.isNotBlank(orgId)) {
                FPUser user = new FPUser()
                        .with("orgId", orgId)
                        .stableRollout(orgId);
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.XDL_TASK_IDCARDNO, user, 1);
            }
        } catch (Exception e) {
            AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                    "功能开关" + FeatureProbeSwith.XDL_TASK_IDCARDNO + "调用失败");
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }

    public static boolean isYzxzJdPopVideo(String orgId,String mobileNo) {
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser().with("orgId", orgId).with("mobileNo", mobileNo).stableRollout(orgId);
                return SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .boolValue(FeatureProbeSwith.YZXZ_JD_POP_VIDEO, user, true);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.YZXZ_JD_POP_VIDEO + "调用失败");
            }
        }
        return false;
    }

    /**
     * 是否走新的导出
     * @param orgId
     * @return
     */
    public static boolean isNewExport(String orgId, String userId) {
        if (!StringUtils.isAllBlank(orgId, userId)) {
            try {
                FPUser user;
                if (StringUtils.isNotBlank(orgId)) {
                    user = new FPUser()
                            .with("orgId", orgId)
                            .stableRollout(orgId);
                } else {
                    user = new FPUser()
                            .with("userId", userId)
                            .stableRollout(userId);
                }
                return SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .boolValue(FeatureProbeSwith.TRANS_TASK_EXPORT_REFACTOR, user, false);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.TRANS_TASK_EXPORT_REFACTOR + "调用失败");
            }
        }
        return false;
    }

    public static boolean isYzxzNewSwitch(String orgId) {
        double numberValue = 2;
        try {
            if (StringUtils.isNotBlank(orgId)) {
                FPUser user = new FPUser()
                        .with("orgId", orgId)
                        .stableRollout(orgId);
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.YZXZ_NEW_SWITCH, user, 2);
            }
        } catch (Exception e) {
            AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                    "功能开关" + FeatureProbeSwith.YZXZ_NEW_SWITCH + "调用失败");
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }

    /**
     * 是否是接单分享功能扫码引导至婷姐接单
     * @param orgId
     * @return
     */
    public static boolean isTingJieShareQRCode(String orgId) {
        double numberValue = 2;
        if (StringUtils.isNotBlank(orgId)) {
            try {
                FPUser user = new FPUser()
                        .with("orgId", orgId)
                        .stableRollout(orgId);
                numberValue = SpringContextToolkit.getBean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL, FeatureProbe.class)
                        .numberValue(FeatureProbeSwith.TING_JIE_ORDER, user, 1);
            } catch (Exception e) {
                AlarmToolkit.logAlarm(e.getLocalizedMessage(), e,
                        "功能开关" + FeatureProbeSwith.TING_JIE_ORDER + "调用失败");
            }
        }
        return !StringUtils.equals(NumberToolkit.format(numberValue), "2");
    }
}
