<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.DlTemplateMapper">

    <resultMap type="com.wtyt.dl.bean.DlTemplateBean" id="TDlTemplateMap">
        <result property="dlTemplateId" column="DL_TEMPLATE_ID" jdbcType="VARCHAR"/>
        <result property="businessType" column="BUSINESS_TYPE" jdbcType="VARCHAR"/>
        <result property="page" column="PAGE" jdbcType="VARCHAR"/>
        <result property="templateType" column="TEMPLATE_TYPE" jdbcType="VARCHAR"/>
        <result property="userId" column="USER_ID" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="uniqueKey" column="UNIQUE_KEY" jdbcType="VARCHAR"/>
        <result property="templateName" column="TEMPLATE_NAME" jdbcType="VARCHAR"/>
        <result property="isTemporary" column="IS_TEMPORARY" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryTemplateList" resultMap="TDlTemplateMap">
        SELECT
            DL_TEMPLATE_ID,
            TEMPLATE_NAME
        FROM
            T_DL_TEMPLATE
        WHERE
            IS_DEL = 0
            AND NVL(IS_TEMPORARY,0) = #{isTemporary}
            AND BUSINESS_TYPE = #{bean.businessType}
            AND PAGE = #{bean.page}
            AND UNIQUE_KEY = #{bean.uniqueKey}
            AND TEMPLATE_TYPE = #{templateType}
        ORDER BY
            CREATED_TIME DESC
    </select>

    <delete id="deleteByIds">
        DELETE FROM T_DL_TEMPLATE WHERE DL_TEMPLATE_ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="save">
        INSERT INTO T_DL_TEMPLATE
        (DL_TEMPLATE_ID,
        BUSINESS_TYPE,
        PAGE,
        TEMPLATE_TYPE,
        USER_ID,
        ORG_ID,
        UNIQUE_KEY,
        TEMPLATE_NAME,
        IS_TEMPORARY)
        VALUES(#{dlTemplateId},
        #{businessType},
        #{page},
        #{templateType},
        #{userId},
        #{orgId},
        #{uniqueKey},
        #{templateName},
        #{isTemporary})
    </insert>

    <update id="updateDeletedById">
        UPDATE T_DL_TEMPLATE SET IS_DEL = 1,LAST_MODIFIED_TIME=SYSDATE WHERE DL_TEMPLATE_ID = #{id}
    </update>

</mapper>