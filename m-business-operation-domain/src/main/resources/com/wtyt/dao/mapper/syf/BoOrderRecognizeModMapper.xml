<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoOrderRecognizeModMapper">


    <insert id="batchSave">
        INSERT
        INTO
        T_BO_ORDER_RECOGNIZE_MOD (BO_ORDER_RECOGNIZE_MOD_ID,
        BO_ORDER_RECOGNIZE_ID,
        HEADER_NAME,
        RECOGNIZE_FIELD_NAME,
        RECOGNIZE_VALUE,
        UPDATE_VALUE,
        BO_TRANS_ORDER_ID,
        OPERATION_TYPE)
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT #{item.boOrderRecognizeModId} boOrderRecognizeModId,
            #{item.boOrderRecognizeId,javaType=string} boOrderRecognizeId,
            #{item.headerName} headerName,
            #{item.recognizeFieldName} recognizeFieldName,
            #{item.recognizeValue} recognizeValue,
            #{item.updateValue} updateValue,
            #{item.fhOrderId,javaType=string} fhOrderId,
            NVL(#{item.operationType}, 1) operationType
            FROM DUAL
        </foreach>
        ) A
    </insert>


</mapper>
