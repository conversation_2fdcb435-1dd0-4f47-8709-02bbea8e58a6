<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.SortMapper">


    <resultMap id="SortMap" type="com.wtyt.dl.bean.SortBean">
        <result property="dlUserSortId" column="DL_USER_SORT_ID" jdbcType="VARCHAR"/>
        <result property="dlSortId" column="DL_SORT_ID" jdbcType="VARCHAR"/>
        <result property="businessType" column="BUSINESS_TYPE" jdbcType="VARCHAR"/>
        <result property="tabCode" column="TAB_CODE" jdbcType="VARCHAR"/>
        <result property="scopes" column="SCOPES" jdbcType="VARCHAR"/>
        <result property="sortKey" column="SORT_KEY" jdbcType="VARCHAR"/>
        <result property="sortName" column="SORT_NAME" jdbcType="VARCHAR"/>
        <result property="sortSqlAsc" column="SORT_SQL_ASC" jdbcType="VARCHAR"/>
        <result property="sortSqlDesc" column="SORT_SQL_DESC" jdbcType="VARCHAR"/>
        <result property="sortNo" column="SORT_NO" jdbcType="VARCHAR"/>
        <result property="renderConfig" column="RENDER_CONFIG" jdbcType="VARCHAR"/>
        <result property="sortType" column="SORT_TYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="querySortList" resultMap="SortMap">
        SELECT
            T1.DL_SORT_ID,
            T1.SORT_KEY,
            T1.SORT_NAME,
            T1.SCOPES,
            T1.RENDER_CONFIG
        FROM
            T_DL_SORT T1
        WHERE
            T1.IS_DEL =0
            AND T1.BUSINESS_TYPE = #{businessType}
            AND T1.TAB_CODE = #{tabCode}
            AND (T1.PAGES IS NULL OR INSTR(',' || T1.PAGES || ',', ',' || #{page} || ',') > 0)
        ORDER BY SORT_NO
    </select>

    <select id="querySelfSort" resultMap="SortMap">
        SELECT
            T1.DL_USER_SORT_ID,
            T1.DL_SORT_ID,
            T1.SORT_TYPE
        FROM
            T_DL_USER_SORT T1
        WHERE
            T1.IS_DEL =0
            AND T1.BUSINESS_TYPE = #{businessType}
            AND T1.TAB_CODE = #{tabCode}
            AND T1.PAGE = #{page}
            AND T1.UNIQUE_KEY = #{uniqueKey}
            AND ROWNUM =1
    </select>

    <update id="saveOrUpdate">
        MERGE INTO T_DL_USER_SORT A
        USING (
            SELECT #{dlUserSortId} DL_USER_SORT_ID, #{dlSortId} DL_SORT_ID, #{businessType} BUSINESS_TYPE, #{page} PAGE, #{uniqueKey} UNIQUE_KEY, #{tabCode} TAB_CODE, #{sortType} SORT_TYPE, #{userId} USER_ID, #{orgId} ORG_ID FROM DUAL
        ) B
        ON (A.BUSINESS_TYPE = B.BUSINESS_TYPE AND A.PAGE=B.PAGE AND A.UNIQUE_KEY=B.UNIQUE_KEY AND A.TAB_CODE = B.TAB_CODE)
        WHEN MATCHED THEN
            UPDATE SET
            A.DL_SORT_ID = B.DL_SORT_ID,
            A.SORT_TYPE = B.SORT_TYPE,
            A.IS_DEL = 0,
            A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
        INSERT
            (DL_USER_SORT_ID, DL_SORT_ID, BUSINESS_TYPE, PAGE, UNIQUE_KEY, TAB_CODE, SORT_TYPE, USER_ID, ORG_ID)
            VALUES(B.DL_USER_SORT_ID, B.DL_SORT_ID, B.BUSINESS_TYPE, B.PAGE, B.UNIQUE_KEY, B.TAB_CODE, B.SORT_TYPE, B.USER_ID, B.ORG_ID)
    </update>

    <select id="queryBySortId" resultMap="SortMap">
        SELECT
        T1.DL_SORT_ID,
        T1.SORT_KEY,
        T1.SORT_SQL_ASC,
        T1.SORT_SQL_DESC
        FROM
        T_DL_SORT T1
        WHERE
        T1.IS_DEL =0
        AND T1.DL_SORT_ID =#{dlSortId}
    </select>
</mapper>