<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskChargeDetailMapper">
    <resultMap type="com.wtyt.dao.bean.syf.BoTaskChargeDetailBean" id="TaskChargeDetailMap">
        <result property="boTaskChargeDetailId" column="BO_TASK_CHARGE_DETAIL_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="companyId" column="COMPANY_ID" jdbcType="VARCHAR"/>
        <result property="userId" column="USER_ID" jdbcType="VARCHAR"/>
        <result property="userName" column="USER_NAME" jdbcType="VARCHAR"/>
        <result property="jobName" column="JOB_NAME" jdbcType="VARCHAR"/>
        <result property="sysRoleType" column="SYS_ROLE_TYPE" jdbcType="VARCHAR"/>
        <result property="mobileNo" column="MOBILE_NO" jdbcType="VARCHAR"/>
        <result property="chargeAmount" column="CHARGE_AMOUNT" jdbcType="VARCHAR"/>
        <result property="chargeType" column="CHARGE_TYPE" jdbcType="VARCHAR"/>
        <result property="chargeTime" column="CHARGE_TIME" jdbcType="VARCHAR"/>
        <result property="posReportId" column="POS_REPORT_ID" jdbcType="VARCHAR"/>
        <result property="settleMode" column="SETTLE_MODE" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryOrgIdsByCompanyId" resultType="java.lang.String">
        SELECT
            DISTINCT ORG_ID
        FROM
            T_BO_TASK_CHARGE_DETAIL
        WHERE
            IS_DEL = 0
            AND COMPANY_ID = #{companyId}
    </select>

    <select id="countHasChargeYearFee" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM T_BO_TASK_CHARGE_DETAIL
        WHERE IS_DEL =0 AND COMPANY_ID =#{companyId} AND CHARGE_TYPE IN (1,3)
    </select>

    <select id="getTaskChargeDetailList" resultMap="TaskChargeDetailMap">
        SELECT T.*
        FROM T_BO_TASK_CHARGE_DETAIL T
        WHERE T.IS_DEL = 0
        AND T.CHARGE_TYPE IN
        <foreach collection="chargeTypeList" item="chargeType" open="(" separator="," close=")">
            #{chargeType}
        </foreach>
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="queryExistsTechFee" resultType="java.lang.Integer">
        SELECT decode(COUNT(*), 0, 0, 1)  FROM T_BO_TASK_CHARGE_DETAIL WHERE ORG_ID = #{orgId} AND IS_DEL = 0 AND CHARGE_TYPE IN (2,3)
    </select>

    <insert id="saveChargeDetailIfNotExist">
        INSERT INTO T_BO_TASK_CHARGE_DETAIL (
            BO_TASK_CHARGE_DETAIL_ID,
            BO_TRANS_TASK_ID,
            ORG_ID,
            COMPANY_ID,
            USER_ID,
            USER_NAME,
            JOB_NAME,
            SYS_ROLE_TYPE,
            MOBILE_NO,
            CHARGE_AMOUNT,
            CHARGE_TYPE,
            CHARGE_TIME,
            POS_REPORT_ID,
            SETTLE_MODE
        )
        SELECT
            #{boTaskChargeDetailId} boTaskChargeDetailId,
            #{boTransTaskId} boTransTaskId,
            #{orgId} orgId,
            #{companyId} companyId,
            #{userId} userId,
            #{userName} userName,
            #{jobName} jobName,
            #{sysRoleType} sysRoleType,
            #{mobileNo} mobileNo,
            #{chargeAmount} chargeAmount,
            #{chargeType} chargeType,
            SYSDATE chargeTime,
            #{posReportId} posReportId,
            #{settleMode} settleMode
        FROM DUAL
        WHERE NOT EXISTS (
            SELECT 1
            FROM T_BO_TASK_CHARGE_DETAIL
            WHERE IS_DEL = 0
                AND CHARGE_TYPE = #{chargeType}
                AND BO_TRANS_TASK_ID = #{boTransTaskId}
        )
        <if test="chargeType == 1">
            AND NOT EXISTS (
                SELECT 1
                FROM T_BO_TASK_CHARGE_DETAIL
                WHERE IS_DEL = 0
                    AND COMPANY_ID = #{companyId}
                    AND CHARGE_TYPE IN (1, 3)
            )
        </if>
    </insert>

    <update id="updatePosReportId">
        UPDATE T_BO_TASK_CHARGE_DETAIL SET POS_REPORT_ID =#{posReportId} WHERE BO_TASK_CHARGE_DETAIL_ID =#{boTaskChargeDetailId}
    </update>

</mapper>
