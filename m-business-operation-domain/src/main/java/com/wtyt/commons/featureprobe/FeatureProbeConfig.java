package com.wtyt.commons.featureprobe;

import com.ctrip.framework.apollo.util.ConfigUtil;
import com.wtyt.fepr.server.FeatureProbe;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@Profile({"!junitRun"})
public class FeatureProbeConfig {

    /**
     * 路歌新大陆产品
     * @return
     */
    @Bean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL)
    public FeatureProbe xdlFebrClient() {
        return FeatureProbe.createProductClient(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_LGXDL);
    }

    /**
     * 网络货运
     * @return
     */
    @Bean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_WLHY)
    public FeatureProbe wlhyFebrClient() {
        return FeatureProbe.createProductClient(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_WLHY);
    }

    @Bean(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_ZYGHBKYLJ)
    public FeatureProbe zyghbkyljFebrClient() {
        String env = new ConfigUtil().getApolloEnv().toString();
        if(StringUtils.equalsAnyIgnoreCase(env,"SIT")){
            return null;
        }
        return FeatureProbe.createProductClient(FeatureProbeSwith.FEATURE_PROBE_PROJECT_NAME_ZYGHBKYLJ);
    }
}
