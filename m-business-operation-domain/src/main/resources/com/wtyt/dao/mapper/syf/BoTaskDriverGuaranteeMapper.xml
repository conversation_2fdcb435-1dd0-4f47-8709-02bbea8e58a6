<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskDriverGuaranteeMapper">

    <select id="getTaskDriverGuarantee" resultType="BoTaskDriverGuaranteeBean">
        SELECT
            T.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            CASE
            WHEN T.GUARANTEE_AMOUNT - TRUNC(T.GUARANTEE_AMOUNT) = 0 THEN
            TO_CHAR(TRUNC(T.GUARANTEE_AMOUNT), 'FM999999990')
            ELSE
            TO_CHAR(T.GUARANTEE_AMOUNT, 'FM999999990.9999')
            END guaranteeAmount,
            CASE
            WHEN T.GUARANTEE_PAY_AMOUNT - TRUNC(T.GUARANTEE_PAY_AMOUNT) = 0 THEN
            TO_CHAR(TRUNC(T.GUARANTEE_PAY_AMOUNT), 'FM999999990')
            ELSE
            TO_CHAR(T.GUARANTEE_PAY_AMOUNT, 'FM999999990.9999')
            END guaranteePayAmount,
            T.GUARANTEE_STATE guaranteeState,
            T.GUARANTEE_REASON guaranteeReason,
            T.GUARANTEE_PIC guaranteePic,
            TO_CHAR(T.GUARANTEE_PAY_TIME, 'yyyy-mm-dd hh24:mi:ss') guaranteePayTime,
            T.GUARANTEE_PAY_NO guaranteePayNo,
            TO_CHAR(T.GUARANTEE_DEAL_TIME, 'yyyy-mm-dd hh24:mi:ss') guaranteeDealTime,
            T.GUARANTEE_DEAL_REASON guaranteeDealReason,
            T.GUARANTEE_DEAL_MODE guaranteeDealMode,
            T.GUARANTEE_DEAL_USER_ID guaranteeDealUserId,
            T.GUARANTEE_WITHDRAW_STATE guaranteeWithdrawState,
            T.GUARANTEE_CHANNEL guaranteeChannel,
            T.DRIVER_LEVEL driverLevel,
            T.DRIVER_ID driverId,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.GUARANTEE_AUTO_BACK_RULE guaranteeAutoBackRule,
            T.GUARANTEE_AUTO_BACK_DAY guaranteeAutoBackDay,
            T.GUARANTEE_AUTO_BACK_UNIT guaranteeAutoBackUnit,
            T.DISPATCH_USER_ID dispatchUserId,
            T.DRIVER_NAME driverName,
            T.GUARANTEE_DEAL_REASON_TYPE guaranteeDealReasonType,
            T.GUARANTEE_AUTO_UNBACK_RULE guaranteeAutoUnbackRule,
            T.GUARANTEE_AUTO_UNBACK_DAY guaranteeAutoUnbackDay,
            T.GUARANTEE_AUTO_UNBACK_UNIT guaranteeAutoUnbackUnit,
            T.GUARANTEE_DISPATCH_CAR_RULE guaranteeDispatchCarRule,
            T.GUARANTEE_SUBMIT_TYPE guaranteeSubmitType,
            TO_CHAR(T.GUARANTEE_SUBMIT_TIME, 'yyyy-mm-dd hh24:mi:ss') guaranteeSubmitTime,
            T.DRIVER_CONFIRM_STATUS driverConfirmStatus,
            T.CAPITAL_FLOW_CHANNEL capitalFlowChannel,
            TO_CHAR(T.DRIVER_CONFIRM_TIME, 'yyyy-mm-dd hh24:mi:ss') driverConfirmTime
        FROM T_BO_TASK_DRIVER_GUARANTEE T
        WHERE T.IS_DEL = 0
        AND T.BO_TASK_DRIVER_GUARANTEE_ID = #{boTaskDriverGuaranteeId}
    </select>

    <update id="updateTaskDriverGuaranteePaymentSuccess">
        UPDATE T_BO_TASK_DRIVER_GUARANTEE T
        SET T.GUARANTEE_STATE = #{guaranteeState},
            T.GUARANTEE_PAY_NO = #{guaranteePayNo},
            T.GUARANTEE_PAY_AMOUNT = #{guaranteePayAmount},
            T.GUARANTEE_PAY_TIME = TO_DATE(#{guaranteePayTime}, 'yyyy-mm-dd hh24:mi:ss'),
            T.DRIVER_ID = #{driverId},
            T.DRIVER_LEVEL = #{driverLevel},
            T.CAPITAL_FLOW_CHANNEL = #{capitalFlowChannel},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.GUARANTEE_STATE = 1
        AND T.BO_TASK_DRIVER_GUARANTEE_ID = #{boTaskDriverGuaranteeId}
    </update>

    <update id="updateTaskDriverGuaranteeNoPaymentSuccess">
        UPDATE T_BO_TASK_DRIVER_GUARANTEE T
        SET T.GUARANTEE_PAY_AMOUNT = 0,
            T.GUARANTEE_PAY_TIME = SYSDATE,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.GUARANTEE_STATE = #{guaranteeState}
        AND T.BO_TASK_DRIVER_GUARANTEE_ID = #{boTaskDriverGuaranteeId}
    </update>

    <update id="updateTaskDriverGuaranteeNoRefund">
        UPDATE T_BO_TASK_DRIVER_GUARANTEE T
        SET T.GUARANTEE_STATE = 4,
            T.GUARANTEE_DEAL_TIME = SYSDATE,
            T.GUARANTEE_DEAL_MODE = #{guaranteeDealMode},
            T.GUARANTEE_DEAL_REASON_TYPE = #{guaranteeDealReasonType},
            T.GUARANTEE_DEAL_REASON = #{guaranteeDealReason},
            T.GUARANTEE_DEAL_USER_ID = #{guaranteeDealUserId},
            T.GUARANTEE_WITHDRAW_STATE = 0,
            T.GUARANTEE_SUBMIT_TYPE =
            CASE
                WHEN T.GUARANTEE_SUBMIT_TYPE IS NULL THEN 1
                ELSE T.GUARANTEE_SUBMIT_TYPE
            END,
            T.GUARANTEE_SUBMIT_TIME =
            CASE
                WHEN T.GUARANTEE_SUBMIT_TIME IS NULL THEN SYSDATE
                ELSE T.GUARANTEE_SUBMIT_TIME
            END,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.GUARANTEE_STATE = 2
        AND T.BO_TASK_DRIVER_GUARANTEE_ID = #{boTaskDriverGuaranteeId}
    </update>

    <update id="updateTaskDriverGuaranteeRefund">
        UPDATE T_BO_TASK_DRIVER_GUARANTEE T
        SET T.GUARANTEE_STATE = 3,
            T.GUARANTEE_DEAL_TIME = SYSDATE,
            T.GUARANTEE_DEAL_MODE = #{guaranteeDealMode},
            T.GUARANTEE_DEAL_REASON_TYPE = #{guaranteeDealReasonType},
            T.GUARANTEE_DEAL_REASON = #{guaranteeDealReason},
            T.GUARANTEE_DEAL_USER_ID = #{guaranteeDealUserId},
            T.GUARANTEE_WITHDRAW_STATE = NULL,
            T.GUARANTEE_SUBMIT_TYPE =
            CASE
                WHEN T.GUARANTEE_SUBMIT_TYPE IS NULL THEN 0
                ELSE T.GUARANTEE_SUBMIT_TYPE
            END,
            T.GUARANTEE_SUBMIT_TIME =
            CASE
                WHEN T.GUARANTEE_SUBMIT_TIME IS NULL THEN SYSDATE
                ELSE T.GUARANTEE_SUBMIT_TIME
            END,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.GUARANTEE_STATE = 2
        AND T.BO_TASK_DRIVER_GUARANTEE_ID = #{boTaskDriverGuaranteeId}
    </update>

    <update id="updateTaskDriverGuaranteeRefundStatusConfirm">
        UPDATE T_BO_TASK_DRIVER_GUARANTEE T
        SET T.GUARANTEE_STATE = 3,
            T.GUARANTEE_WITHDRAW_STATE = NULL,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.GUARANTEE_STATE = 2
        AND T.BO_TASK_DRIVER_GUARANTEE_ID = #{boTaskDriverGuaranteeId}
    </update>

    <select id="getByTaskId" resultType="com.wtyt.dao.bean.syf.BoTaskDriverGuaranteeBean">
        SELECT
            B.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            B.BO_TRANS_TASK_ID boTransTaskId,
            CASE
            WHEN B.GUARANTEE_AMOUNT - TRUNC(B.GUARANTEE_AMOUNT) = 0 THEN
            TO_CHAR(TRUNC(B.GUARANTEE_AMOUNT), 'FM999999990')
            ELSE
            TO_CHAR(B.GUARANTEE_AMOUNT, 'FM999999990.9999')
            END guaranteeAmount,
            B.GUARANTEE_STATE guaranteeState,
            B.GUARANTEE_REASON guaranteeReason,
            B.GUARANTEE_PIC guaranteePic,
            B.DRIVER_LEVEL driverLevel,
            CASE
            WHEN B.GUARANTEE_PAY_AMOUNT - TRUNC(B.GUARANTEE_PAY_AMOUNT) = 0 THEN
            TO_CHAR(TRUNC(B.GUARANTEE_PAY_AMOUNT), 'FM999999990')
            ELSE
            TO_CHAR(B.GUARANTEE_PAY_AMOUNT, 'FM999999990.9999')
            END guaranteePayAmount,
            TO_CHAR(B.GUARANTEE_PAY_TIME, 'YYYY-MM-DD HH24:MI:SS') guaranteePayTime,
            TO_CHAR(B.GUARANTEE_DEAL_TIME, 'YYYY-MM-DD HH24:MI:SS') guaranteeDealTime,
            B.GUARANTEE_DEAL_REASON guaranteeDealReason,
            B.GUARANTEE_DEAL_MODE guaranteeDealMode,
            B.GUARANTEE_AUTO_BACK_RULE guaranteeAutoBackRule,
            B.GUARANTEE_AUTO_BACK_DAY guaranteeAutoBackDay,
            B.GUARANTEE_AUTO_BACK_UNIT guaranteeAutoBackUnit,
            B.GUARANTEE_CHANNEL guaranteeChannel,
            B.GUARANTEE_AUTO_UNBACK_RULE guaranteeAutoUnbackRule,
            B.GUARANTEE_AUTO_UNBACK_DAY guaranteeAutoUnbackDay,
            B.GUARANTEE_AUTO_UNBACK_UNIT guaranteeAutoUnbackUnit,
            B.GUARANTEE_DISPATCH_CAR_RULE guaranteeDispatchCarRule,
            B.GUARANTEE_DISPATCH_CAR_SWITCH guaranteeDispatchCarSwitch,
            B.GUARANTEE_SUBMIT_TYPE guaranteeSubmitType,
            TO_CHAR(B.GUARANTEE_SUBMIT_TIME, 'yyyy-mm-dd hh24:mi:ss') guaranteeSubmitTime,
            B.DRIVER_CONFIRM_STATUS driverConfirmStatus,
            B.CAPITAL_FLOW_CHANNEL capitalFlowChannel,
            TO_CHAR(B.DRIVER_CONFIRM_TIME, 'yyyy-mm-dd hh24:mi:ss') driverConfirmTime
        FROM
            T_BO_TRANS_TASK_EXTRA A
        JOIN T_BO_TASK_DRIVER_GUARANTEE B ON
            A.BO_TASK_DRIVER_GUARANTEE_ID = B.BO_TASK_DRIVER_GUARANTEE_ID
            AND B.IS_DEL = 0
        WHERE
            A.BO_TRANS_TASK_ID = #{taskId}
    </select>

    <select id="getDriverGuaranteeListByTransTaskIdList" resultType="com.wtyt.dao.bean.syf.BoTaskDriverGuaranteeBean">
        SELECT
            T1.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            T1.BO_TRANS_TASK_ID boTransTaskId,
            T1.GUARANTEE_STATE guaranteeState,
            T1.DRIVER_CONFIRM_STATUS driverConfirmStatus
        FROM T_BO_TASK_DRIVER_GUARANTEE T1
        JOIN T_BO_TRANS_TASK_EXTRA T2
            ON T2.BO_TASK_DRIVER_GUARANTEE_ID = T1.BO_TASK_DRIVER_GUARANTEE_ID
            AND T2.IS_DEL = 0
        WHERE T1.IS_DEL = 0
        AND T2.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
            #{boTransTaskId}
        </foreach>
    </select>

    <insert id="save" parameterType="com.wtyt.dao.bean.syf.BoTaskDriverGuaranteeBean">
        INSERT INTO T_BO_TASK_DRIVER_GUARANTEE
        (
            BO_TASK_DRIVER_GUARANTEE_ID,
            BO_TRANS_TASK_ID,
            GUARANTEE_AMOUNT,
            GUARANTEE_STATE,
            GUARANTEE_REASON,
            GUARANTEE_PIC,
            MOBILE_NO,
            CART_BADGE_NO,
            CART_BADGE_COLOR,
            DISPATCH_USER_ID,
            JOB_NAME,
            DRIVER_NAME,
            GUARANTEE_AUTO_BACK_RULE,
            GUARANTEE_AUTO_BACK_DAY,
            GUARANTEE_AUTO_BACK_UNIT,
            GUARANTEE_CHANNEL,
            GUARANTEE_AUTO_UNBACK_RULE,
            GUARANTEE_AUTO_UNBACK_DAY,
            GUARANTEE_AUTO_UNBACK_UNIT,
            GUARANTEE_DISPATCH_CAR_RULE,
            GUARANTEE_DISPATCH_CAR_SWITCH,
            DRIVER_CONFIRM_STATUS
        )
        VALUES(
            #{boTaskDriverGuaranteeId},
            #{boTransTaskId},
            #{guaranteeAmount},
            #{guaranteeState},
            #{guaranteeReason},
            #{guaranteePic},
            #{mobileNo},
            #{cartBadgeNo},
            #{cartBadgeColor},
            #{dispatchUserId},
            #{jobName},
            #{driverName},
            #{guaranteeAutoBackRule},
            #{guaranteeAutoBackDay},
            #{guaranteeAutoBackUnit},
            #{guaranteeChannel},
            #{guaranteeAutoUnbackRule},
            #{guaranteeAutoUnbackDay},
            #{guaranteeAutoUnbackUnit},
            #{guaranteeDispatchCarRule},
            #{guaranteeDispatchCarSwitch},
            0
        )
    </insert>

    <update id="updateDriverGuaranteeInfoWhenUnPay" parameterType="com.wtyt.dao.bean.syf.BoTaskDriverGuaranteeBean">
        UPDATE
            T_BO_TASK_DRIVER_GUARANTEE
        SET
            <if test="guaranteeAmount != null and guaranteeAmount != ''">
                GUARANTEE_AMOUNT = TO_NUMBER(#{guaranteeAmount}),
            </if>
            <if test="guaranteeState != null and guaranteeState != ''">
                GUARANTEE_STATE = CASE WHEN GUARANTEE_PAY_TIME IS NOT NULL THEN GUARANTEE_STATE ELSE TO_NUMBER(#{guaranteeState}) END,
            </if>
            <if test="guaranteeReason != null">
                GUARANTEE_REASON = #{guaranteeReason},
            </if>
            <if test="guaranteePic != null">
                GUARANTEE_PIC = #{guaranteePic},
            </if>
            <if test="guaranteeDispatchCarRule != null">
                GUARANTEE_DISPATCH_CAR_RULE = TO_NUMBER(#{guaranteeDispatchCarRule}),
            </if>
            <if test="guaranteeChannel != null">
                GUARANTEE_CHANNEL = #{guaranteeChannel},
            </if>
            <if test="driverName != null">
                DRIVER_NAME = #{driverName},
            </if>
            <if test="mobileNo != null and mobileNo != ''">
                MOBILE_NO = #{mobileNo},
            </if>
            <if test="cartBadgeNo != null and cartBadgeNo != ''">
                CART_BADGE_NO = #{cartBadgeNo},
            </if>
            <if test="cartBadgeColor != null and cartBadgeColor != ''">
                CART_BADGE_COLOR = #{cartBadgeColor},
            </if>
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TASK_DRIVER_GUARANTEE_ID = #{boTaskDriverGuaranteeId}
            AND IS_DEL = 0
    </update>

    <update id="batchUpdateDriverGuaranteeWithdraw">
        UPDATE T_BO_TASK_DRIVER_GUARANTEE T
        SET T.GUARANTEE_WITHDRAW_STATE = 1,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.GUARANTEE_STATE = 4
        AND T.GUARANTEE_WITHDRAW_STATE = 0
        <choose>
            <when test="list !=null and list.size() > 0">
                AND T.BO_TASK_DRIVER_GUARANTEE_ID IN
                <foreach collection="list" item="boTaskDriverGuaranteeId" open="(" separator="," close=")">
                    #{boTaskDriverGuaranteeId}
                </foreach>
            </when>
            <otherwise>
                AND T.BO_TASK_DRIVER_GUARANTEE_ID = -1
            </otherwise>
        </choose>
    </update>

    <update id="batchUpdateDriverGuaranteeWithdrawRollback">
        UPDATE T_BO_TASK_DRIVER_GUARANTEE T
        SET T.GUARANTEE_WITHDRAW_STATE = 0,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.GUARANTEE_STATE = 4
        AND T.GUARANTEE_WITHDRAW_STATE = 1
        <choose>
            <when test="list !=null and list.size() > 0">
                AND T.BO_TASK_DRIVER_GUARANTEE_ID IN
                <foreach collection="list" item="boTaskDriverGuaranteeId" open="(" separator="," close=")">
                    #{boTaskDriverGuaranteeId}
                </foreach>
            </when>
            <otherwise>
                AND T.BO_TASK_DRIVER_GUARANTEE_ID = -1
            </otherwise>
        </choose>
    </update>

    <update id="updateTaskDriverGuaranteeAwaitConfirm">
        UPDATE T_BO_TASK_DRIVER_GUARANTEE T
        SET T.GUARANTEE_DEAL_REASON_TYPE = #{guaranteeDealReasonType},
            T.GUARANTEE_DEAL_REASON = #{guaranteeDealReason},
            T.GUARANTEE_DEAL_MODE = #{guaranteeDealMode},
            T.GUARANTEE_DEAL_USER_ID = #{guaranteeDealUserId},
            T.GUARANTEE_SUBMIT_TYPE =
            CASE
                WHEN T.GUARANTEE_SUBMIT_TYPE IS NULL THEN 1
                ELSE T.GUARANTEE_SUBMIT_TYPE
            END,
            T.GUARANTEE_SUBMIT_TIME =
            CASE
                WHEN T.GUARANTEE_SUBMIT_TIME IS NULL THEN SYSDATE
                ELSE T.GUARANTEE_SUBMIT_TIME
            END,
            T.DRIVER_CONFIRM_STATUS = 1,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.DRIVER_CONFIRM_STATUS = 0
        AND T.GUARANTEE_STATE = 2
        AND T.BO_TASK_DRIVER_GUARANTEE_ID = #{boTaskDriverGuaranteeId}
    </update>

    <update id="updateTaskDriverGuaranteeConfirmAgree">
        UPDATE T_BO_TASK_DRIVER_GUARANTEE T
        SET T.GUARANTEE_STATE = 4,
            T.GUARANTEE_DEAL_TIME = SYSDATE,
            T.GUARANTEE_WITHDRAW_STATE = 0,
            T.DRIVER_CONFIRM_STATUS = 2,
            T.DRIVER_CONFIRM_TIME = SYSDATE,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.GUARANTEE_STATE = 2
        AND T.DRIVER_CONFIRM_STATUS = 1
        AND T.BO_TASK_DRIVER_GUARANTEE_ID = #{boTaskDriverGuaranteeId}
    </update>

    <update id="updateTaskDriverGuaranteeConfirmReject">
        UPDATE T_BO_TASK_DRIVER_GUARANTEE T
        SET T.GUARANTEE_WITHDRAW_STATE = NULL,
            T.DRIVER_CONFIRM_STATUS = 3,
            T.DRIVER_CONFIRM_TIME = SYSDATE,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.GUARANTEE_STATE = 2
        AND T.DRIVER_CONFIRM_STATUS = 1
        AND T.BO_TASK_DRIVER_GUARANTEE_ID = #{boTaskDriverGuaranteeId}
    </update>

</mapper>
