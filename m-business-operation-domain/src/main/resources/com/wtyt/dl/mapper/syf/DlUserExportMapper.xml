<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.DlUserExportMapper">

    <resultMap type="com.wtyt.dl.bean.DlExportBean" id="DlExportMap">
        <result property="dlExportId" column="DL_EXPORT_ID" jdbcType="VARCHAR"/>
        <result property="exportKey" column="EXPORT_KEY" jdbcType="VARCHAR"/>
        <result property="exportName" column="EXPORT_NAME" jdbcType="VARCHAR"/>
        <result property="selectedMode" column="SELECTED_MODE" jdbcType="VARCHAR"/>
        <result property="forceSelect" column="FORCE_SELECT" jdbcType="VARCHAR"/>
        <result property="exportSql" column="EXPORT_SQL" jdbcType="VARCHAR"/>
        <result property="exportWidth" column="EXPORT_WIDTH" jdbcType="VARCHAR"/>
        <result property="renderConfig" column="RENDER_CONFIG" jdbcType="VARCHAR"/>
        <result property="sortNo" column="SORT_NO" jdbcType="VARCHAR"/>
    </resultMap>

    <delete id="deleteByTemplateIds">
        DELETE FROM T_DL_USER_EXPORT WHERE DL_TEMPLATE_ID IN
        <foreach collection="templateIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="save">
        INSERT INTO T_DL_USER_EXPORT
        (
            DL_USER_EXPORT_ID,
            DL_TEMPLATE_ID,
            DL_EXPORT_ID,
            SORT_NO,
            EXPORT_KEY,
            EXPORT_NAME
        )
        VALUES(
            #{dlUserExportId},
            #{dlTemplateId},
            #{dlExportId},
            #{sortNo},
            #{exportKey},
            #{exportName}
        )
    </insert>

    <select id="queryByTemplateId" resultMap="DlExportMap">
        SELECT
            a.DL_EXPORT_ID,
            NVL(b.EXPORT_KEY,a.EXPORT_KEY) EXPORT_KEY,
            NVL(b.EXPORT_NAME,a.EXPORT_NAME) EXPORT_NAME,
            b.EXPORT_WIDTH,
            b.EXPORT_SQL,
            b.RENDER_CONFIG,
            a.SORT_NO,
            b.FORCE_SELECT
        FROM
            T_DL_USER_EXPORT a
        LEFT JOIN T_DL_EXPORT b ON
            b.DL_EXPORT_ID = a.DL_EXPORT_ID AND b.IS_DEL =0
        JOIN T_DL_TEMPLATE c ON
            c.DL_TEMPLATE_ID = a.DL_TEMPLATE_ID
        WHERE
            a.IS_DEL = 0
            AND c.IS_DEL =0
            AND a.DL_TEMPLATE_ID = #{templateId}
        ORDER BY
            a.SORT_NO ASC
    </select>
</mapper>