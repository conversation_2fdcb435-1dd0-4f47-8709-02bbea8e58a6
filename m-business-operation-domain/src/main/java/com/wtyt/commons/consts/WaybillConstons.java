package com.wtyt.commons.consts;

import java.util.HashSet;
import java.util.Set;

/**
 * 运单常量
 * 
 * <AUTHOR>
 * @time 2021-10-08 17:06
 */
public class WaybillConstons {

    /*********************** 运单支付状态 开始 *********************************/
    /**
     * 未支付
     */
    public static final String WAYBILL_PAY_STATE_0 = "0";
    /**
     * 已申请
     */
    public static final String WAYBILL_PAY_STATE_1 = "1";
    /**
     * 已支付
     */
    public static final String WAYBILL_PAY_STATE_2 = "2";
    /**
     * 预申请中,未确定支付类型
     */
    public static final String WAYBILL_PAY_STATE_3 = "3";
    /**
     * 已部分支付
     */
    public static final String WAYBILL_PAY_STATE_5 = "5";
    /**
     * 核实中
     */
    public static final String WAYBILL_PAY_STATE_6 = "6";
    /**
     * 业务审核
     */
    public static final String WAYBILL_PAY_STATE_7 = "7";
    /**
     * 风控数据模型未审核
     */
    public static final String WAYBILL_PAY_STATE_8 = "8";
    /**
     * 平台财务核实中
     */
    public static final String WAYBILL_PAY_STATE_10 = "10";
    /*********************** 运单支付状态 结束 *********************************/

    /*********************** 运单状态 开始 *********************************/

    /**
     * 未追踪
     */
    public static final String WAYBILL_STATE_0 = "0";
    /**
     * 跟踪中
     */
    public static final String WAYBILL_STATE_1 = "1";
    /**
     * 已终结
     */
    public static final String WAYBILL_STATE_2 = "2";
    /*********************** 运单状态 结束 *********************************/

    /*********************** 运单起始地/目的地轨迹覆盖状态 开始 *********************************/

    /**
     * 起始地未覆盖
     */
    public static final String WAYBILL_START_STATE_0 = "0";
    /**
     * 起始地已覆盖
     */
    public static final String WAYBILL_START_STATE_1 = "1";
    /**
     * 目的地未覆盖
     */
    public static final String WAYBILL_END_STATE_0 = "0";

    /**
     * 目的地已覆盖
     */
    public static final String WAYBILL_END_STATE_1 = "1";
    /*********************** 运单起始地/目的地轨迹覆盖状态 结束 *********************************/

    /*********************** 运单签署协议类型 开始 *********************************/
    /**
     * 运输协议
     */
    public static final String CONTRACT_TYTP_0 = "0";
    /**
     * 托运协议
     */
    public static final String CONTRACT_TYTP_1 = "1";
    /**
     * 结算协议
     */
    public static final String CONTRACT_TYTP_2 = "2";
    /**
     * 油卡相关协议
     */
    public static final String CONTRACT_TYTP_3 = "3";
    /**
     * 运费收入管理协议
     */
    public static final String CONTRACT_TYTP_4 = "4";
    /**
     * 则一运输协议
     */
    public static final String CONTRACT_TYTP_5 = "5";

    /*********************** 运单签署协议类型 结束 *********************************/

    /*********************** 运单定位类型 开始 *********************************/
    /**
     * 运营商定位
     */
    public static final String WB_LOG_TYPE__0 = "0";
    /**
     * 北斗定位
     */
    public static final String WB_LOG_TYPE_1 = "1";
    /**
     * GPS定位
     */
    public static final String WB_LOG_TYPE_2 = "2";
    /**
     * 好运宝定位
     */
    public static final String WB_LOG_TYPE_3 = "3";
    /**
     * 外部接口
     */
    public static final String WB_LOG_TYPE_4 = "4";
    /*********************** 运单定位类型 结束 *********************************/

    /*********************** 运单模式 开始 *********************************/
    /**
     * 自有（默认）票据用户才可以开票
     */
    public static final String WB_MODE_0 = "0";
    /**
     * 上游建单（指定收款人，可以开票，不可以保理）
     */
    public static final String WB_MODE_1 = "1";
    /**
     * 上游共享（不可以开票，可以保理）
     */
    public static final String WB_MODE_2 = "2";
    /**
     * 下游建单（指定付款人，不可以开票，可以保理）
     */
    public static final String WB_MODE_3 = "3";
    /**
     * 下游共享（可以开票，不可以保理）
     */
    public static final String WB_MODE_4 = "4";
    /*********************** 运单模式 结束 *********************************/

    /*********************** 运输类型 开始 *********************************/
    /**
     * 综合运输(空默认为0)
     */
    public static final String TRANSPORT_TYPE_0 = "0";

    /**
     * 大宗运输
     */
    public static final String TRANSPORT_TYPE_1 = "1";
    /*********************** 运输类型 结束 *********************************/

    /*********************** 经理人运单标识 开始 *********************************/
    /**
     * 非经理人运单
     */
    public static final String MANAGER_WAYBILL_FLAG_0 = "0";
    /**
     * 经理人运单
     */
    public static final String MANAGER_WAYBILL_FLAG_1 = "1";

    /*********************** 经理人运单标识 结束 *********************************/

    /************************ 运单业务类型 开始 **********************************/
    /**
     * 运单业务类型-外协派单
     */
    public static final String WB_BUSINESS_TYPE_1 = "1";
    /**
     * 运单业务类型-路优运单3
     */
    public static final String WB_BUSINESS_TYPE_2 = "2";
    /**
     * 运单业务类型-大宗结算单
     */
    public static final String WB_BUSINESS_TYPE_3 = "3";
    /**
     * 运单业务类型-大宗外协
     */
    public static final String WB_BUSINESS_TYPE_4 = "4";
    /**
     * 运单业务类型-城配
     */
    public static final String WB_BUSINESS_TYPE_5 = "5";
    /**
     * 运单业务类型-运力供应商
     */
    public static final String WB_BUSINESS_TYPE_6 = "6";

    /************************ 运单业务类型 结束 **********************************/

    /**
     * 订金是否退还-退还
     */
    public static final String DEPOSIT_REFUND_FLAG_2 = "2";

    /**
     * 订金是否退还-不退还
     */
    public static final String DEPOSIT_REFUND_FLAG_3 = "3";

    /**
     * 开票标志-不开票
     */
    public static final String INVOICE_FLAG_1 = "1";

    /**
     * 开票标志-网货
     */
    public static final String INVOICE_FLAG_2 = "2";

    /**
     * 确认收货-未确认
     */
    public static final String CONFIRM_GOODS_1 = "1";

    /**
     * 确认收货-已退回
     */
    public static final String CONFIRM_GOODS_2 = "2";

    /**
     * 确认收货-已解冻
     */
    public static final String CONFIRM_GOODS_3 = "3";

    /**
     * 运采类型-车找货
     */
    public static final String YUNCAI_TYPE_1 = "1";

    /**
     * 运采类型-货找车
     */
    public static final String YUNCAI_TYPE_2 = "2";

    /**
     * 运采类型-人找车
     */
    public static final String YUNCAI_TYPE_3 = "3";

    /**
     * 运价审批状态-未审批
     */
    public static final String FREIGHT_APPROVAL_STATE_0 = "0";

    /**
     * 运价审批状态-已审批
     */
    public static final String FREIGHT_APPROVAL_STATE_1 = "1";

    /**
     * 未开票
     */
    public static final String INVOICE_STATE_0="0";

    /**
     * 申请中
     */
    public static final String INVOICE_STATE_1="1";

    /**
     * 未通过
     */
    public static final String INVOICE_STATE_2="2";

    /**
     * 已开票
     */
    public static final String INVOICE_STATE_3="3";

    /**
     * 开票状态异常
     */
    public static final String INVOICE_STATE_4="4";

    /**
     * 部分开票
     */
    public static final String INVOICE_STATE_5="5";

    /**
     * 运价审批状态-撤销
     */
    public static final String FREIGHT_APPROVAL_STATE_2 = "2";

    public static final Set<String> FREIGHT_APPROVAL_STATE_SET=new HashSet<String>(4);

    static {
    	FREIGHT_APPROVAL_STATE_SET.add(FREIGHT_APPROVAL_STATE_0);
    	FREIGHT_APPROVAL_STATE_SET.add(FREIGHT_APPROVAL_STATE_1);
    	FREIGHT_APPROVAL_STATE_SET.add(FREIGHT_APPROVAL_STATE_2);
    }

    /**
     * 运输凭证——回单
     */
    public static final String TRANS_VOUCHER_1 = "1";

    /**
     * 运输凭证——装卸货照
     */
    public static final String TRANS_VOUCHER_2 = "2";
    /**
     * 运输凭证——出库单+回单
     */
    public static final String TRANS_VOUCHER_3 = "3";
    /**
     * 运输凭证——装货照+回单
     */
    public static final String TRANS_VOUCHER_4 = "4";
    /**
     * 运输凭证——出库单+卸货照
     */
    public static final String TRANS_VOUCHER_5 = "5";

    /**
     * 到场打卡模式
     */
    public static final String TRANS_PATTERN_1 = "1";

    /**
     * 到场签署协议模式
     */
    public static final String TRANS_PATTERN_2 = "2";
    
    /**
     * 运营工具（MM组）
     */
    public static final String WAYBILL_MODIFY_SRC_8="8";

    public static final String TRANS_VOUCHER_ZHZ = "装货照";
    public static final String TRANS_VOUCHER_CKD = "出库单";
    public static final String TRANS_VOUCHER_HD = "回单";
    public static final String TRANS_VOUCHER_XHZ = "卸货照";
}
