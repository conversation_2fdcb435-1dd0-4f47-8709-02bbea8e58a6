<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskExtraMapper">
    <resultMap type="com.wtyt.dao.bean.syf.BoTransTaskExtraBean" id="BoTransTaskExtraMap">
        <result property="boTransTaskExtraId" column="BO_TRANS_TASK_EXTRA_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="transVoucherAckStatus" column="TRANS_VOUCHER_ACK_STATUS" jdbcType="VARCHAR"/>
        <result property="transVoucherAuditStatus" column="TRANS_VOUCHER_AUDIT_STATUS" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="electronicReceiptStatus" column="ELECTRONIC_RECEIPT_STATUS" jdbcType="VARCHAR"/>
        <result property="paperReceiptStatus" column="PAPER_RECEIPT_STATUS" jdbcType="VARCHAR"/>
        <result property="paperReceiptNeedPostType" column="PAPER_RECEIPT_NEED_POST_TYPE" jdbcType="VARCHAR"/>
        <result property="customerOrderNo" column="CUSTOMER_ORDER_NO" jdbcType="VARCHAR"/>
        <result property="paperEstimatedDeliveryTime" column="PAPER_ESTIMATED_DELIVERY_TIME" jdbcType="VARCHAR"/>
        <result property="electronicReceiptAuditTime" column="ELECTRONIC_RECEIPT_AUDIT_TIME" jdbcType="VARCHAR"/>
        <result property="lineTimeRequire" column="LINE_TIME_REQUIRE" jdbcType="VARCHAR"/>
        <result property="boTaskDriverGuaranteeId" column="BO_TASK_DRIVER_GUARANTEE_ID" jdbcType="VARCHAR"/>
        <result property="cartLength" column="CART_LENGTH" jdbcType="VARCHAR"/>
        <result property="cartType" column="CART_TYPE" jdbcType="VARCHAR"/>
        <result property="transVoucher" column="TRANS_VOUCHER" jdbcType="VARCHAR"/>
        <result property="loadingAddressName" column="LOADING_ADDRESS_NAME" jdbcType="VARCHAR"/>
        <result property="unloadingAddressName" column="UNLOADING_ADDRESS_NAME" jdbcType="VARCHAR"/>
        <result property="arriveEndTimeFormat" column="ARRIVE_END_TIME_FORMAT" jdbcType="VARCHAR"/>
        <result property="unloadTimeFormat" column="UNLOAD_TIME_FORMAT" jdbcType="VARCHAR"/>
        <result property="arriveTime" column="ARRIVE_TIME" jdbcType="VARCHAR"/>
        <result property="hybReceivedTime" column="HYB_RECEIVED_TIME" jdbcType="VARCHAR"/>
        <result property="dispatchCarRecordId" column="DISPATCH_CAR_RECORD_ID" jdbcType="VARCHAR"/>
        <result property="dispatchCarTime" column="DISPATCH_CAR_TIME" jdbcType="VARCHAR"/>
        <result property="receivedNumber" column="RECEIVED_NUMBER" jdbcType="VARCHAR"/>
        <result property="deleteStatus" column="DELETE_STATUS" jdbcType="VARCHAR"/>
        <result property="auditStatus" column="AUDIT_STATUS" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">
        T_BO_TRANS_TASK_EXTRA
    </sql>

    <update id="updateTransExtra">
        UPDATE T_BO_TRANS_TASK_EXTRA SET LAST_MODIFIED_TIME = SYSDATE
        <if test="thirdTaskNo != null and thirdTaskNo != '' ">
            ,THIRD_TASK_NO = #{thirdTaskNo}
        </if>
        <if test="transportTime!=null">
            ,TRANSPORT_TIME =#{transportTime}
        </if>
        <if test="receivedNumber!=null and receivedNumber!=''">
            ,RECEIVED_NUMBER =#{receivedNumber}
        </if>
        <if test="loadingRemark!=null and loadingRemark!=''">
            ,LOADING_REMARK =#{loadingRemark}
        </if>
        <if test="loadingContactName!=null and loadingContactName!=''">
            ,LOADING_CONTACT_NAME =#{loadingContactName}
        </if>
        <if test="loadingContactMobileNo!=null and loadingContactMobileNo!=''">
            ,LOADING_CONTACT_MOBILE_NO =#{loadingContactMobileNo}
        </if>
        <if test="unloadingRemark!=null and unloadingRemark!=''">
            ,UNLOADING_REMARK =#{unloadingRemark}
        </if>
        <if test="unloadingContactName!=null and unloadingContactName!=''">
            ,UNLOADING_CONTACT_NAME =#{unloadingContactName}
        </if>
        <if test="unloadingContactMobileNo!=null and unloadingContactMobileNo!=''">
            ,UNLOADING_CONTACT_MOBILE_NO =#{unloadingContactMobileNo}
        </if>
        <if test="transVoucherAckStatus!=null and transVoucherAckStatus!=''">
            ,TRANS_VOUCHER_ACK_STATUS =#{transVoucherAckStatus}
        </if>
        <if test="transVoucherAuditStatus!=null and transVoucherAuditStatus!=''">
            ,TRANS_VOUCHER_AUDIT_STATUS =#{transVoucherAuditStatus}
        </if>
        <if test="customerOrderNo != null">
            ,CUSTOMER_ORDER_NO =#{customerOrderNo}
        </if>
        <if test="lineTimeRequire!=null and lineTimeRequire!=''">
            ,LINE_TIME_REQUIRE =#{lineTimeRequire}
        </if>
        <if test="paperReceiptNeedPostType!=null and paperReceiptNeedPostType!=''">
            ,PAPER_RECEIPT_NEED_POST_TYPE =#{paperReceiptNeedPostType}
            ,PAPER_RECEIPT_STATUS =
            CASE
            WHEN PAPER_RECEIPT_STATUS = 2 AND #{paperReceiptNeedPostType} = 0 THEN 3
            WHEN PAPER_RECEIPT_STATUS = 3 AND #{paperReceiptNeedPostType} = 1 THEN 2
            ELSE PAPER_RECEIPT_STATUS
            END
        </if>
        <!-- 20230509物理解耦新增 -->
        <if test="arriveEndTime != null and arriveEndTime!=''">
            ,ARRIVE_END_TIME = TO_DATE(#{arriveEndTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="unloadTime != null">
            ,UNLOAD_TIME = TO_DATE(#{unloadTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="dispatchCarTime != null and dispatchCarTime!=''">
            ,DISPATCH_CAR_TIME = TO_DATE(#{dispatchCarTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="payType != null and payType != ''">
            ,PAY_TYPE = #{payType}
        </if>
        <if test="payName != null">
            ,PAY_NAME = #{payName}
        </if>
        <if test="payBankNo != null">
            ,PAY_BANK_NO = #{payBankNo}
        </if>
        <if test="payBankName != null">
            ,PAY_BANK_NAME = #{payBankName}
        </if>
        <if test="cityName != null">
            ,CITY_NAME = #{cityName}
        </if>
        <if test="province != null">
            ,PROVINCE = #{province}
        </if>
        <if test="payMobileNo != null">
            ,PAY_MOBILE_NO = #{payMobileNo}
        </if>
        <if test="payIdCard != null">
            ,PAY_ID_CARD = #{payIdCard}
        </if>
        <if test="cartType != null">
            ,CART_TYPE = #{cartType}
        </if>
        <if test="cartLength != null">
            ,CART_LENGTH = #{cartLength}
        </if>
        <if test="loadingAddressName != null">
            ,LOADING_ADDRESS_NAME = #{loadingAddressName}
        </if>
        <if test="unloadingAddressName != null">
            ,UNLOADING_ADDRESS_NAME = #{unloadingAddressName}
        </if>
        <if test="serviceRequire != null">
            ,SERVICE_REQUIRE = #{serviceRequire}
        </if>
        <if test="tranRequire != null and tranRequire != ''">
            ,TRAN_REQUIRE = #{tranRequire}
        </if>
        <if test="cartTonnage != null and cartTonnage != ''">
            ,CART_TONNAGE = #{cartTonnage}
        </if>
        <if test="transVoucher != null and transVoucher != ''">
            ,TRANS_VOUCHER = #{transVoucher}
        </if>
        <if test="oilCardNo != null">
            ,OIL_CARD_NO = #{oilCardNo}
        </if>
        <if test="gasCardNo != null">
            ,GAS_CARD_NO = #{gasCardNo}
        </if>
        <if test="shipmentPhoto != null and shipmentPhoto != ''">
            ,SHIPMENT_PHOTO = #{shipmentPhoto}
        </if>
        <if test="customizeNo != null and customizeNo != ''">
            ,CUSTOMIZE_NO = #{customizeNo}
        </if>
        <if test="receiptBzState != null and receiptBzState != ''">
            ,RECEIPT_BZ_STATE = #{receiptBzState}
        </if>
        <if test="receiptReceiveTime != null and receiptReceiveTime != ''">
            ,RECEIPT_RECEIVE_TIME = TO_DATE(#{receiptReceiveTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="transVoucherExpressNumber != null and transVoucherExpressNumber != ''">
            ,TRANS_VOUCHER_EXPRESS_NUMBER = #{transVoucherExpressNumber}
        </if>
        <if test="isFreightShow != null and isFreightShow != ''">
            ,IS_FREIGHT_SHOW = #{isFreightShow}
        </if>
        <if test="arriveTime != null and arriveTime!=''">
            ,ARRIVE_TIME = TO_DATE(#{arriveTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="extendJson != null and extendJson!=''">
            ,EXTEND_JSON = #{extendJson}
        </if>
        <if test="freightGuarantee != null and freightGuarantee!=''">
            ,FREIGHT_GUARANTEE=#{freightGuarantee}
        </if>
        <if test="hybUserId != null">
            ,HYB_USER_ID=#{hybUserId}
        </if>
        <if test="driverId != null">
            ,DRIVER_ID=#{driverId}
        </if>
        <if test="boTaskDriverGuaranteeId != null and boTaskDriverGuaranteeId!=''">
            ,BO_TASK_DRIVER_GUARANTEE_ID=#{boTaskDriverGuaranteeId}
        </if>
        <if test="arriveEndTimeFormat != null and arriveEndTimeFormat!=''">
            ,ARRIVE_END_TIME_FORMAT=#{arriveEndTimeFormat}
        </if>
        <if test="operateFeeStatus != null and operateFeeStatus!=''">
            ,OPERATE_FEE_STATUS = #{operateFeeStatus}
        </if>
        <if test="salesmanName != null and salesmanName!=''">
            ,SALESMAN_NAME = #{salesmanName}
        </if>
        <if test="expressNumber != null">
            ,EXPRESS_NUMBER = #{expressNumber}
        </if>
        <if test="payer != null">
            ,PAYER = #{payer}
        </if>
        <if test="collectionMode != null and collectionMode!=''">
            ,COLLECTION_MODE = #{collectionMode}
        </if>
        <if test="loadingRoughWeight != null">
            ,LOADING_ROUGH_WEIGHT = #{loadingRoughWeight}
        </if>
        <if test="unloadingRoughWeight != null">
            ,UNLOADING_ROUGH_WEIGHT = #{unloadingRoughWeight}
        </if>
        <if test="loadingTare != null">
            ,LOADING_TARE = #{loadingTare}
        </if>
        <if test="unloadingTare != null">
            ,UNLOADING_TARE = #{unloadingTare}
        </if>
        <if test="cpdPoolGroupId != null and cpdPoolGroupId != ''">
            ,CPD_POOL_GROUP_ID = #{cpdPoolGroupId}
        </if>
        <if test="cpdPoolGroupName != null and cpdPoolGroupName != ''">
            ,CPD_POOL_GROUP_NAME = #{cpdPoolGroupName}
        </if>
        <if test="cpdSecondPoolGroupName != null">
            ,CPD_SECOND_POOL_GROUP_NAME = #{cpdSecondPoolGroupName}
        </if>
        <if test="actualLoadTime != null">
            ,ACTUAL_LOAD_TIME = TO_DATE(#{actualLoadTime}, 'YYYY-MM-DD')
        </if>
        <if test="actualUnloadTime != null">
            ,ACTUAL_UNLOAD_TIME = TO_DATE(#{actualUnloadTime}, 'YYYY-MM-DD')
        </if>
        <if test="boxNo != null">
            ,BOX_NO = #{boxNo}
        </if>
        <if test="orgMailingAddressId!=null">
            ,ORG_MAILING_ADDRESS_ID = #{orgMailingAddressId}
        </if>
        <if test="deductTonnage != null">
            ,DEDUCT_TONNAGE = #{deductTonnage}
        </if>
        <if test="trailerCartBadgeNo != null">
            ,TRAILER_CART_BADGE_NO = #{trailerCartBadgeNo}
        </if>
        <if test="lossAmount != null">
            ,LOSS_AMOUNT = #{lossAmount}
        </if>
        <if test="lossUnitPrice != null">
            ,LOSS_UNIT_PRICE = #{lossUnitPrice}
        </if>
        <if test="allowLossWeight != null">
            ,ALLOW_LOSS_WEIGHT = #{allowLossWeight}
        </if>
        <if test="lossActualTonnage != null">
            ,LOSS_ACTUAL_TONNAGE = #{lossActualTonnage}
        </if>
        <if test="lossConfigValue != null">
            ,LOSS_CONFIG_VALUE = #{lossConfigValue}
        </if>
        <if test="settleFlag != null">
            ,SETTLE_FLAG = #{settleFlag}
        </if>
        <if test="belongDispatcherId != null">
            ,BELONG_DISPATCHER_ID = #{belongDispatcherId}
        </if>
        <if test="isRebate != null">
            ,IS_REBATE = #{isRebate}
        </if>
        <if test="businessType != null and businessType!=''">
            ,BUSINESS_TYPE = #{businessType}
        </if>
        <if test="taskFeeVerifyState!=null and taskFeeVerifyState!=''">
            ,TASK_FEE_VERIFY_STATE= CASE WHEN TASK_FEE_VERIFY_STATE IS NULL THEN TO_NUMBER(#{taskFeeVerifyState}) ELSE TASK_FEE_VERIFY_STATE END
        </if>
        WHERE BO_TRANS_TASK_ID=#{boTransTaskId} AND IS_DEL =0
    </update>
    
    <update id="batchUpdTransExtra">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_TRANS_TASK_EXTRA SET LAST_MODIFIED_TIME = SYSDATE
            <if test="item.hybUserId != null">
                ,HYB_USER_ID = #{item.hybUserId}
            </if>
            <if test="item.driverId != null">
                ,DRIVER_ID = #{item.driverId}
            </if>
            WHERE BO_TRANS_TASK_ID=#{item.boTransTaskId} AND IS_DEL =0
        </foreach>
    </update>

    <!-- 只清空订货证的businessType-->
    <update id="updateTaskExtraWhenCancelDispatch">
        UPDATE
            T_BO_TRANS_TASK_EXTRA
        SET
            TRANS_VOUCHER_ACK_STATUS =
            CASE
            WHEN TRANS_VOUCHER_ACK_STATUS = 2 THEN 1
            ELSE TRANS_VOUCHER_ACK_STATUS
            END,
            TRANS_VOUCHER_AUDIT_STATUS =
            CASE
            WHEN TRANS_VOUCHER_AUDIT_STATUS = 2 THEN 1
            ELSE TRANS_VOUCHER_AUDIT_STATUS
            END,
            DISPATCH_CAR_TIME =NULL,
            ARRIVE_TIME = NULL,
            HYB_RECEIVED_TIME = NULL,
            DISPATCH_CAR_RECORD_ID = NULL,
            ARRIVE_END_TIME = NULL,
            HYB_USER_ID = NULL,
            DRIVER_ID = NULL,
            USER_FREIGHT_APPROVAL_STATUS =0,
            USER_FREIGHT_APPROVAL_TIME =SYSDATE,
            CART_LENGTH = NULL,
            CART_TYPE = NULL,
            OPERATE_FEE_STATUS = NULL,
            SALESMAN_NAME = NULL,
            COLLECTION_MODE = NULL,
            BO_TASK_DRIVER_GUARANTEE_ID = NULL,
            FREIGHT_GUARANTEE = NULL,
            ARRIVE_CONFIRM_TIME = NULL,
            CONTACT_DRIVER_TIME = NULL,
            SETTLE_FLAG = NULL,
            BELONG_DISPATCHER_ID = NULL,
            APPOINT_STATUS =
                CASE
                    WHEN TRANS_TASK_FLAG = 1 THEN 0
                    ELSE APPOINT_STATUS
                END,
            SIGNIN_STATUS =
                CASE
                    WHEN TRANS_TASK_FLAG = 1 THEN 0
                    ELSE SIGNIN_STATUS
                END,
            CPD_POOL_GROUP_ID = NULL,
            CPD_POOL_GROUP_NAME =NULL,
            CPD_SECOND_POOL_GROUP_NAME = NULL,
            BUSINESS_TYPE =
                CASE WHEN BUSINESS_TYPE = 23 THEN NULL
                ELSE BUSINESS_TYPE
                END,
            FREEZE_STATUS = NULL,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TRANS_TASK_ID = #{taskId}
            AND IS_DEL = 0
    </update>

    <update id="updateSelective" parameterType="com.wtyt.dao.bean.syf.BoTransTaskExtraBean">
        update <include refid="tableName"/>
        <set>
            <if test="boTransTaskId != null and boTransTaskId != ''">
                BO_TRANS_TASK_ID = #{boTransTaskId},
            </if>
            <if test="transVoucherAckStatus != null and transVoucherAckStatus != ''">
                TRANS_VOUCHER_ACK_STATUS = #{transVoucherAckStatus},
            </if>
            <if test="transVoucherAuditStatus != null and transVoucherAuditStatus != ''">
                TRANS_VOUCHER_AUDIT_STATUS = #{transVoucherAuditStatus},
            </if>
            <if test="isDel != null and isDel != ''">
                IS_DEL = #{isDel},
            </if>
            <if test="note != null and note != ''">
                NOTE = #{note},
            </if>
            <if test="electronicReceiptStatus != null and electronicReceiptStatus != ''">
                ELECTRONIC_RECEIPT_STATUS = #{electronicReceiptStatus},
            </if>
            <if test="paperReceiptStatus != null and paperReceiptStatus != ''">
                PAPER_RECEIPT_STATUS = #{paperReceiptStatus},
            </if>
            <if test="receiptBzState != null and receiptBzState != ''">
                RECEIPT_BZ_STATE = #{receiptBzState},
            </if>
            <if test="receiptReceiveTime != null and receiptReceiveTime != ''">
                RECEIPT_RECEIVE_TIME = to_date(#{receiptReceiveTime},'YYYY-MM-DD HH24:MI:SS'),
            </if>
            <if test="transVoucherExpressNumber != null and transVoucherExpressNumber != ''">
                TRANS_VOUCHER_EXPRESS_NUMBER = #{transVoucherExpressNumber},
            </if>
            <if test="paperReceiptNeedPostType != null and paperReceiptNeedPostType != ''">
                PAPER_RECEIPT_NEED_POST_TYPE = #{paperReceiptNeedPostType},
            </if>
            <if test="customerOrderNo != null and customerOrderNo != ''">
                CUSTOMER_ORDER_NO = #{customerOrderNo},
            </if>
            <if test="paperEstimatedDeliveryTime != null and paperEstimatedDeliveryTime != ''">
                PAPER_ESTIMATED_DELIVERY_TIME = to_date(#{paperEstimatedDeliveryTime},'YYYY-MM-DD HH24:MI:SS'),
            </if>
            <if test="electronicReceiptAuditTime != null and electronicReceiptAuditTime != ''">
                ELECTRONIC_RECEIPT_AUDIT_TIME = to_date(#{electronicReceiptAuditTime},'YYYY-MM-DD HH24:MI:SS'),
            </if>
            LAST_MODIFIED_TIME = SYSDATE,
        </set>
        <where>
            BO_TRANS_TASK_ID = #{boTransTaskId}
            AND IS_DEL = 0
        </where>
    </update>
    <update id="updateReceiptStatusWhenUpload">
        UPDATE T_BO_TRANS_TASK_EXTRA SET LAST_MODIFIED_TIME=SYSDATE
        ,ELECTRONIC_RECEIPT_STATUS = CASE WHEN ELECTRONIC_RECEIPT_STATUS IN (1,4) THEN 2 ELSE ELECTRONIC_RECEIPT_STATUS END
        ,PAPER_RECEIPT_STATUS =
        CASE
        WHEN PAPER_RECEIPT_STATUS = 1 AND PAPER_RECEIPT_NEED_POST_TYPE = 0 THEN 3
        WHEN PAPER_RECEIPT_STATUS = 1 AND PAPER_RECEIPT_NEED_POST_TYPE = 1 THEN 2
        ELSE PAPER_RECEIPT_STATUS END
        WHERE BO_TRANS_TASK_ID = #{taskId} AND IS_DEL =0
    </update>
    <update id="updatePaperReceiptStatusWhenSign">
        UPDATE T_BO_TRANS_TASK_EXTRA SET LAST_MODIFIED_TIME=SYSDATE,PAPER_RECEIPT_STATUS = 5
        WHERE BO_TRANS_TASK_ID = #{taskId} AND IS_DEL =0 AND PAPER_RECEIPT_STATUS!=5
    </update>
    <update id="updateReceiptStatusWhenUploadLoadingPhonto">
        UPDATE T_BO_TRANS_TASK_EXTRA SET LAST_MODIFIED_TIME=SYSDATE,ELECTRONIC_RECEIPT_STATUS = 2
        WHERE BO_TRANS_TASK_ID = #{taskId} AND IS_DEL =0 AND ELECTRONIC_RECEIPT_STATUS =4
    </update>
    <select id="queryByTaskId" resultMap="BoTransTaskExtraMap">
        SELECT
            BO_TRANS_TASK_EXTRA_ID,
            TRANS_VOUCHER,
            ELECTRONIC_RECEIPT_STATUS,
            PAPER_RECEIPT_STATUS,
            PAPER_RECEIPT_NEED_POST_TYPE,
            BO_TASK_DRIVER_GUARANTEE_ID,
            ARRIVE_END_TIME_FORMAT,
            UNLOAD_TIME_FORMAT,
            CART_LENGTH,
            CART_TYPE,
            LOADING_ADDRESS_NAME,
            UNLOADING_ADDRESS_NAME,
            DISPATCH_CAR_RECORD_ID,
            AUDIT_STATUS,
            DISPATCH_CAR_RECORD_ID,
            TO_CHAR(ARRIVE_TIME, 'yyyy-mm-dd hh24:mi:ss') ARRIVE_TIME,
            TO_CHAR(DISPATCH_CAR_TIME, 'yyyy-mm-dd hh24:mi:ss') DISPATCH_CAR_TIME,
            TO_CHAR(HYB_RECEIVED_TIME, 'yyyy-mm-dd hh24:mi:ss') HYB_RECEIVED_TIME
        FROM
            T_BO_TRANS_TASK_EXTRA
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID = #{taskId}
    </select>
    <select id="queryDeletedTaskExtraByTransTaskId" resultMap="BoTransTaskExtraMap">
        SELECT
            BO_TRANS_TASK_EXTRA_ID,
            ELECTRONIC_RECEIPT_STATUS,
            PAPER_RECEIPT_STATUS,
            PAPER_RECEIPT_NEED_POST_TYPE,
            BO_TASK_DRIVER_GUARANTEE_ID
        FROM
            T_BO_TRANS_TASK_EXTRA
        WHERE
            IS_DEL = 1
            AND BO_TRANS_TASK_ID = #{taskId}
    </select>

    <insert id="insertTransExtra">
        INSERT INTO T_BO_TRANS_TASK_EXTRA(BO_TRANS_TASK_EXTRA_ID,
            BO_TRANS_TASK_ID,
            TRANS_VOUCHER_ACK_STATUS,
            TRANS_VOUCHER_AUDIT_STATUS,
            PAPER_RECEIPT_STATUS,
            ELECTRONIC_RECEIPT_STATUS,
            CUSTOMER_ORDER_NO,
            PAPER_RECEIPT_NEED_POST_TYPE,
            LINE_TIME_REQUIRE,
            ARRIVE_END_TIME,
            UNLOAD_TIME,
            DISPATCH_CAR_TIME,
            <if test="payType!=null and payType!=''">
                PAY_TYPE,
            </if>
            PAY_NAME,
            PAY_BANK_NO,
            PAY_BANK_NAME,
            CITY_NAME,
            PROVINCE,
            PAY_MOBILE_NO,
            PAY_ID_CARD,
            CART_TYPE,
            CART_LENGTH,
            LOADING_ADDRESS_NAME,
            UNLOADING_ADDRESS_NAME,
            SERVICE_REQUIRE,
            TRAN_REQUIRE,
            CART_TONNAGE,
            TRANS_VOUCHER,
            OIL_CARD_NO,
            GAS_CARD_NO,
            IS_UNIT_PRICE_SHOW,
            IS_FREIGHT_SHOW,
            FREIGHT_GUARANTEE,
            HYB_USER_ID,
            DRIVER_ID,
            USER_FREIGHT_APPROVAL_STATUS,
            ARRIVE_END_TIME_FORMAT,
            UNLOAD_TIME_FORMAT,
            EXTEND_JSON,
            OPERATE_FEE_STATUS,
            SALESMAN_NAME,
            EXPRESS_NUMBER,
            PAYER,
            LOADING_ROUGH_WEIGHT,
            UNLOADING_ROUGH_WEIGHT,
            LOADING_TARE,
            UNLOADING_TARE,
            <if test="collectionMode != null and collectionMode!=''">
                COLLECTION_MODE,
            </if>
            <if test="cpdPoolGroupId != null and cpdPoolGroupId != ''">
                CPD_POOL_GROUP_ID,
            </if>
            CPD_POOL_GROUP_NAME,
            CPD_SECOND_POOL_GROUP_NAME,
            IS_THIRD_INTERFACE,
            SETTLE_ISSUE,
            ORG_MAILING_ADDRESS_ID,
            BUSINESS_TYPE,
            LOAD_TYPE,
            LOADING_GOODS_TIME,
            TASK_FEE_VERIFY_STATE,
            BO_LINE_ASSIGN_REL_ID,
            ALLOW_GAIN_FLAG,
            <if test="transportTime != null and transportTime.length > 0">
                TRANSPORT_TIME,
            </if>
            <if test="receivedNumber != null and receivedNumber.length > 0">
                RECEIVED_NUMBER,
            </if>
            <if test="loadingRemark != null and loadingRemark.length > 0">
                LOADING_REMARK,
            </if>
            <if test="loadingContactName != null and loadingContactName.length > 0">
                LOADING_CONTACT_NAME,
            </if>
            <if test="loadingContactMobileNo != null and loadingContactMobileNo.length > 0">
                LOADING_CONTACT_MOBILE_NO,
            </if>
            <if test="unloadingRemark != null and unloadingRemark.length > 0">
                UNLOADING_REMARK,
            </if>
            <if test="unloadingContactName != null and unloadingContactName.length > 0">
                UNLOADING_CONTACT_NAME,
            </if>
            <if test="unloadingContactMobileNo != null and unloadingContactMobileNo.length > 0">
                UNLOADING_CONTACT_MOBILE_NO,
            </if>
            <if test="belongDispatcherId != null and belongDispatcherId.length > 0">
                BELONG_DISPATCHER_ID,
            </if>
            <if test="transTaskFlag != null and transTaskFlag.length > 0">
                TRANS_TASK_FLAG,
            </if>
            <if test="appointStatus != null and appointStatus.length > 0">
                APPOINT_STATUS,
            </if>
            <if test="signinStatus != null and signinStatus.length > 0">
                SIGNIN_STATUS,
            </if>
            <if test="thirdTaskNo != null and thirdTaskNo.length > 0">
                THIRD_TASK_NO,
            </if>
            SETTLE_FLAG,
            <if test="isRebate != null and isRebate!=''">
                IS_REBATE,
            </if>
            <if test="voucherConfigType != null and voucherConfigType!=''">
                VOUCHER_CONFIG_TYPE,
            </if>
            TRAILER_CART_BADGE_NO
        )
        VALUES ( ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault() },
            #{boTransTaskId},
            #{transVoucherAckStatus},
            #{transVoucherAuditStatus},
            #{paperReceiptStatus},
            #{electronicReceiptStatus},
            #{customerOrderNo},
            #{paperReceiptNeedPostType},
            #{lineTimeRequire},
            to_date(#{arriveEndTime},'YYYY-MM-DD HH24:MI:SS'),
            to_date(#{unloadTime},'YYYY-MM-DD HH24:MI:SS'),
            to_date(#{dispatchCarTime},'YYYY-MM-DD HH24:MI:SS'),
            <if test="payType!=null and payType!=''">
                #{payType},
            </if>
            #{payName},
            #{payBankNo},
            #{payBankName},
            #{cityName},
            #{province},
            #{payMobileNo},
            #{payIdCard},
            #{cartType},
            #{cartLength},
            #{loadingAddressName},
            #{unloadingAddressName},
            #{serviceRequire},
            #{tranRequire},
            #{cartTonnage},
            #{transVoucher},
            #{oilCardNo},
            #{gasCardNo},
            #{isUnitPriceShow},
            #{isFreightShow},
            #{freightGuarantee},
            #{hybUserId},
            #{driverId},
            #{userFreightApprovalStatus},
            #{arriveEndTimeFormat},
            #{unloadTimeFormat},
            #{extendJson},
            #{operateFeeStatus},
            #{salesmanName},
            #{expressNumber},
            #{payer},
            #{loadingRoughWeight},
            #{unloadingRoughWeight},
            #{loadingTare},
            #{unloadingTare},
            <if test="collectionMode != null and collectionMode!=''">
                #{collectionMode},
            </if>
            <if test="cpdPoolGroupId != null and cpdPoolGroupId != ''">
                #{cpdPoolGroupId},
            </if>
            #{cpdPoolGroupName},
            #{cpdSecondPoolGroupName},
            #{isThirdInterface},
            #{settleIssue},
            #{orgMailingAddressId},
            #{businessType},
            #{loadType},
            to_date(#{loadingGoodsTime},'YYYY-MM-DD HH24:MI:SS'),
            #{taskFeeVerifyState},
            #{boLineAssignRelId},
            #{allowGainFlag},
            <if test="transportTime != null and transportTime.length > 0">
                #{transportTime},
            </if>
            <if test="receivedNumber != null and receivedNumber.length > 0">
                #{receivedNumber},
            </if>
            <if test="loadingRemark != null and loadingRemark.length > 0">
                #{loadingRemark},
            </if>
            <if test="loadingContactName != null and loadingContactName.length > 0">
                #{loadingContactName},
            </if>
            <if test="loadingContactMobileNo != null and loadingContactMobileNo.length > 0">
                #{loadingContactMobileNo},
            </if>
            <if test="unloadingRemark != null and unloadingRemark.length > 0">
                #{unloadingRemark},
            </if>
            <if test="unloadingContactName != null and unloadingContactName.length > 0">
                #{unloadingContactName},
            </if>
            <if test="unloadingContactMobileNo != null and unloadingContactMobileNo.length > 0">
                #{unloadingContactMobileNo},
            </if>
            <if test="belongDispatcherId != null and belongDispatcherId.length > 0">
                #{belongDispatcherId},
            </if>
            <if test="transTaskFlag != null and transTaskFlag.length > 0">
                #{transTaskFlag},
            </if>
            <if test="appointStatus != null and appointStatus.length > 0">
                #{appointStatus},
            </if>
            <if test="signinStatus != null and signinStatus.length > 0">
                #{signinStatus},
            </if>
            <if test="thirdTaskNo != null and thirdTaskNo.length > 0">
                #{thirdTaskNo},
            </if>
            #{settleFlag},
            <if test="isRebate != null and isRebate!=''">
                #{isRebate},
            </if>
            <if test="voucherConfigType != null and voucherConfigType!=''">
                #{voucherConfigType},
            </if>
            #{trailerCartBadgeNo}
        )
    </insert>

    <update id="syncWaybillExtraInfo" parameterType="BoTransTaskExtraBean">
        UPDATE T_BO_TRANS_TASK_EXTRA
        SET
            LAST_MODIFIED_TIME           = SYSDATE,
            CART_TYPE                    = #{boTransTaskExtraBean.cartType},
            <if test="boTransTaskExtraBean.cartTonnage != null and boTransTaskExtraBean.cartTonnage !=''">
                CART_TONNAGE                 = #{boTransTaskExtraBean.cartTonnage},
            </if>
            CART_LENGTH                  = #{boTransTaskExtraBean.cartLength},
            TRAN_REQUIRE                 = #{boTransTaskExtraBean.tranRequire},
            SHIPMENT_PHOTO               = #{boTransTaskExtraBean.shipmentPhoto},
            CUSTOMIZE_NO                 = #{boTransTaskExtraBean.customizeNo},
            IS_FREIGHT_SHOW              = #{boTransTaskExtraBean.isFreightShow},
            LOADING_ADDRESS_NAME         = #{boTransTaskExtraBean.loadingAddressName},
            UNLOADING_ADDRESS_NAME       = #{boTransTaskExtraBean.unloadingAddressName},
            CANCEL_STATE                 = #{boTransTaskExtraBean.cancelState},
            FREEZE_STATUS                = #{boTransTaskExtraBean.freezeStatus},
            <if test="boTransTaskExtraBean.receiptBzState != null and boTransTaskExtraBean.receiptBzState !=''">
                RECEIPT_BZ_STATE         = CASE WHEN RECEIPT_BZ_STATE =3 THEN '3' ELSE #{boTransTaskExtraBean.receiptBzState} END,
            </if>
            <if test="boTransTaskExtraBean.receiptReceiveTime != null and boTransTaskExtraBean.receiptReceiveTime !=''">
                RECEIPT_RECEIVE_TIME     = TO_DATE(#{boTransTaskExtraBean.receiptReceiveTime}, 'YYYY-MM-DD HH24:MI:SS'),
            </if>
            TRANS_VOUCHER                = #{boTransTaskExtraBean.transVoucher},
            <if test="boTransTaskExtraBean.transVoucherExpressNumber != null and boTransTaskExtraBean.transVoucherExpressNumber !=''">
                TRANS_VOUCHER_EXPRESS_NUMBER = #{boTransTaskExtraBean.transVoucherExpressNumber},
            </if>
            IS_UNIT_PRICE_SHOW           = #{boTransTaskExtraBean.isUnitPriceShow},
            LOSS_AMOUNT                  = #{boTransTaskExtraBean.lossAmount},
            LOSS_UNIT_PRICE              = #{boTransTaskExtraBean.lossUnitPrice},
            LOSS_ACTUAL_TONNAGE          = #{boTransTaskExtraBean.lossActualTonnage},
            ALLOW_LOSS_WEIGHT            = #{boTransTaskExtraBean.allowLossWeight},
            LOSS_CONFIG_VALUE            = #{boTransTaskExtraBean.lossConfigValue},
            SETTLE_ISSUE                 = #{boTransTaskExtraBean.settleIssue},
            GOODS_AMOUNT_OCR_STATE       = #{boTransTaskExtraBean.goodsAmountOcrState},
            TRAILER_CART_BADGE_NO        = #{boTransTaskExtraBean.trailerCartBadgeNo},
            <if test="boTransTaskExtraBean.hybUserId != null">
                HYB_USER_ID              = #{boTransTaskExtraBean.hybUserId},
            </if>
            <if test="boTransTaskExtraBean.driverId != null">
                DRIVER_ID                = #{boTransTaskExtraBean.driverId},
            </if>
            HYB_RECEIVED_TIME            = TO_DATE(#{boTransTaskExtraBean.hybReceivedTime}, 'YYYY-MM-DD HH24:MI:SS')
            <if test="transMode != null and transMode != ''">
                ,PAY_TYPE                    = #{boTransTaskExtraBean.payType},
                PAY_NAME                     = #{boTransTaskExtraBean.payName},
                PAY_BANK_NO                  = #{boTransTaskExtraBean.payBankNo},
                CITY_NAME                    = #{boTransTaskExtraBean.cityName},
                PAY_MOBILE_NO                = #{boTransTaskExtraBean.payMobileNo},
                PROVINCE                     = #{boTransTaskExtraBean.province},
                PAY_ID_CARD                  = #{boTransTaskExtraBean.payIdCard},
                PAY_BANK_NAME                = #{boTransTaskExtraBean.payBankName},
                FREIGHT_GUARANTEE            = #{boTransTaskExtraBean.freightGuarantee},
                OIL_CARD_NO                  = #{boTransTaskExtraBean.oilCardNo},
                GAS_CARD_NO                  = #{boTransTaskExtraBean.gasCardNo},
                COLLECTION_MODE              = #{boTransTaskExtraBean.collectionMode}
            </if>
        WHERE BO_TRANS_TASK_ID = #{boTransTaskExtraBean.boTransTaskId}
    </update>

    <update id="updateReceiveTime">
        UPDATE T_BO_TRANS_TASK_EXTRA SET LAST_MODIFIED_TIME = SYSDATE , HYB_RECEIVED_TIME = to_date(#{receiveTime},'YYYY-MM-DD HH24:MI:SS')
        where BO_TRANS_TASK_ID = #{boTransTaskId} and HYB_RECEIVED_TIME is null
    </update>
    <update id="updateExtraSendCarRecordId">
        UPDATE T_BO_TRANS_TASK_EXTRA SET
            DISPATCH_CAR_RECORD_ID = #{boTransNodeRecordId},
            DISPATCH_CAR_TIME = SYSDATE,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <select id="queryServiceRequire" resultType="String">
        SELECT
        e.SERVICE_REQUIRE serviceRequire
        from T_BO_TRANS_TASK_EXTRA e
        where e.is_del = 0
        and e.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="getArressNameByTaskId" resultType="com.wtyt.dao.bean.syf.BoTransTaskExtraBean">
        SELECT
        A.LOADING_ADDRESS_NAME loadingAddressName,
        A.UNLOADING_ADDRESS_NAME unloadingAddressName
        FROM
        T_BO_TRANS_TASK_EXTRA A
        WHERE
        A.IS_DEL = 0
        AND A.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="getDispatchCartState" resultType="java.lang.String">
    SELECT
        DECODE(E.DISPATCH_CAR_RECORD_ID, NULL, '0', '1') AS isDispatch
    FROM
        T_BO_TRANS_TASK_EXTRA E
    WHERE
        E.IS_DEL = 0
        AND E.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>
    <update id="updateUserFreightApprovalStatus">
        UPDATE T_BO_TRANS_TASK_EXTRA SET USER_FREIGHT_APPROVAL_STATUS =#{userFreightApprovalStatus}
        <choose>
            <when test="userFreightApprovalTime!=null and userFreightApprovalTime!=''">
                ,USER_FREIGHT_APPROVAL_TIME=TO_DATE(#{userFreightApprovalTime},'YYYY-MM-DD HH24:MI:SS')
            </when>
            <otherwise>
                ,USER_FREIGHT_APPROVAL_TIME=SYSDATE
            </otherwise>
        </choose>
        WHERE BO_TRANS_TASK_ID =#{boTransTaskId} AND IS_DEL =0
        <!--<if test="oldApprovalStatus!=null">
            AND USER_FREIGHT_APPROVAL_STATUS =#{oldApprovalStatus}
        </if>
        <if test="userFreightApprovalTime!=null and userFreightApprovalTime!=''">
            AND (USER_FREIGHT_APPROVAL_TIME IS NULL OR USER_FREIGHT_APPROVAL_TIME &lt;= TO_DATE(userFreightApprovalTime,'YYYY-MM-DD HH24:MI:SS'))
        </if>-->
    </update>

    <update id="updateTaskCart">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.CART_LENGTH = #{cartLength},
            T.CART_TYPE = #{cartType},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.CART_LENGTH IS NULL
        AND T.CART_TYPE IS NULL
        AND T.DISPATCH_CAR_RECORD_ID IS NOT NULL
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="clearTransVoucher" parameterType="java.lang.String">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.TRANS_VOUCHER = NULL,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND (T.BUSINESS_TYPE IS NULL OR T.BUSINESS_TYPE !=5)
    </update>

    <update id="dispatchedCancelClear" parameterType="java.lang.String">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.TRANSPORT_NODE = NULL,
            T.TRANSPORT_NODE_TIME = NULL,
            T.TRANS_VOUCHER = (
                CASE
                    WHEN T.BUSINESS_TYPE IS NULL OR T.BUSINESS_TYPE != 5 THEN NULL
                    ELSE T.TRANS_VOUCHER
                END
            ),
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="clearGuarantee" parameterType="java.lang.String">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.BO_TASK_DRIVER_GUARANTEE_ID = NULL,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TASK_DRIVER_GUARANTEE_ID IS NOT NULL
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>
    <update id="reDistribute">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET
        <if test="freightGuarantee != null">
            T.FREIGHT_GUARANTEE = #{freightGuarantee},
        </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL =0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateGoodsAmountOcrState">
        UPDATE
        T_BO_TRANS_TASK_EXTRA
        SET
        GOODS_AMOUNT_OCR_STATE = #{goodsAmountOcrState},
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        IS_DEL = 0
        AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateTransExtraArriveConfirmTime">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.ARRIVE_CONFIRM_TIME = SYSDATE,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
            AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateTransExtraContactDriverTime">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.CONTACT_DRIVER_TIME = SYSDATE,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0
            AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateTaskFeeVerifyState">
        UPDATE
        T_BO_TRANS_TASK_EXTRA
        SET
        TASK_FEE_VERIFY_STATE = #{taskFeeVerifyState},
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        IS_DEL = 0
        AND BO_TRANS_TASK_ID = #{taskId}
    </update>

    <update id="batchUpdateTaskFeeVerifyState">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE
            T_BO_TRANS_TASK_EXTRA
            SET
            TASK_FEE_VERIFY_STATE = #{item.taskFeeVerifyState},
            LAST_MODIFIED_TIME = SYSDATE
            WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID = #{item.boTransTaskId}
        </foreach>
    </update>
    
    
    <update id="updateTaskVoucherCheckState">
        UPDATE
          T_BO_TRANS_TASK_EXTRA
        SET
          BO_VOUCHER_CHECK_STATE = #{voucherCheckState},
          LAST_MODIFIED_TIME = SYSDATE
        WHERE
          IS_DEL = 0
          AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateWhenReceiptDelivery">
        UPDATE
        T_BO_TRANS_TASK_EXTRA
        SET
        PAPER_RECEIPT_STATUS = 4,
        PAPER_ESTIMATED_DELIVERY_TIME = to_date(#{paperEstimatedDeliveryTime}, 'YYYY-MM-DD HH24:MI:SS'),
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        BO_TRANS_TASK_ID = #{taskId}
        AND IS_DEL = 0
        AND PAPER_RECEIPT_STATUS != 5
    </update>

    <update id="updateTaskDeleteStatus">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.DELETE_STATUS = #{deleteStatus},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateTaskAuditStatus">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.AUDIT_STATUS = #{auditStatus},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateTaskAppointStatus">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET <if test="appointStatus != null">
                T.APPOINT_STATUS = #{appointStatus},
            </if>
            <if test="signinStatus != null">
                T.SIGNIN_STATUS = #{signinStatus},
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateTaskThirdTaskNo">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.THIRD_TASK_NO = #{thirdTaskNo},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.THIRD_TASK_NO IS NULL
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="clearTaskThirdTaskNo">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.THIRD_TASK_NO = NULL,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.THIRD_TASK_NO IS NOT NULL
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateOperateFeeStatusBatch">
        UPDATE T_BO_TRANS_TASK_EXTRA SET OPERATE_FEE_STATUS=#{operateFeeStatus},LAST_MODIFIED_TIME=SYSDATE
        WHERE BO_TRANS_TASK_ID IN
        <foreach collection="taskIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateCancelState">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.CANCEL_STATE = #{cancelState},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateCancelWaybillDeleteState">
        UPDATE T_BO_TRANS_TASK_EXTRA SET CANCEL_WAYBILL_DELETE_STATE=#{cancelWaybillDeleteState},LAST_MODIFIED_TIME = SYSDATE WHERE BO_TRANS_TASK_ID=#{boTransTaskId}
    </update>

    <update id="batchUpdateCancelState">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.CANCEL_STATE = #{cancelState},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
            #{boTransTaskId}
        </foreach>
    </update>

    <update id="updateTaskFreezeStatus">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.FREEZE_STATUS = #{freezeStatus},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateTransTaskTransportNode">
        UPDATE T_BO_TRANS_TASK_EXTRA T
        SET T.TRANSPORT_NODE = #{transportNode},
            T.TRANSPORT_NODE_TIME = TO_DATE(#{transportNodeTime}, 'yyyy-mm-dd hh24:mi:ss'),
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND (T.TRANSPORT_NODE IS NULL OR T.TRANSPORT_NODE <![CDATA[<=]]> #{transportNode})
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="clearWhenConvertSettleMode">
        UPDATE T_BO_TRANS_TASK_EXTRA
        SET FREEZE_STATUS = NULL,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0
        AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

</mapper>
