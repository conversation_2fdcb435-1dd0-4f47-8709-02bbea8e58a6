package com.wtyt.commons.components;

import com.google.common.collect.Lists;
import com.wtyt.common.constants.Constants;
import com.wtyt.common.rpc.bean.*;
import com.wtyt.common.rpc.bean.Rpc22081OBean.Rpc22081OSubBean;
import com.wtyt.common.rpc.bean.Rpc5214029IBean.Rpc5214029SubBean;
import com.wtyt.common.rpc.bean.Rpc5214029OBean.Rpc5214029OSubBean;
import com.wtyt.common.rpc.service.RpcBaseBiopsyDomainService;
import com.wtyt.common.rpc.service.RpcMMMssorgService;
import com.wtyt.common.rpc.service.RpcMbauService;
import com.wtyt.common.toolkits.AssertToolkit;
import com.wtyt.common.toolkits.ListToolkit;
import com.wtyt.common.toolkits.StringToolkit;
import com.wtyt.commons.bean.DriverInfo;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.money.commons.toolkit.CollectionToolkit;
import com.wtyt.tt.bean.DpTaskResultBean;
import com.wtyt.util.OrgCacheDataUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * @ClassName: DriverComponent
 * @Description: 司机相关的组件
 * <AUTHOR>
 * @date 2022年4月28日
 *
 */
@Component
public class DriverComponent {
    private static Logger log = org.slf4j.LoggerFactory.getLogger(DriverComponent.class);
    @Autowired
    private RpcMMMssorgService rpcMMMssorgService;
    @Autowired
    private RpcMbauService rpcMbauService;
    @Autowired
    private RpcBaseBiopsyDomainService rpcBaseBiopsyDomainService;

    /**
     *
     * @Title: getDriverAuthStateFromDataCenter
     * <AUTHOR>
     * @Description: 获取平台级会员认证状态
     * @param orgId
     * @param drivers
     * @return
     * @throws Exception
     * @return Map<String,String> 返回类型
     * @throws
     */
    public Map<String,String> getDriverAuthStateFromDataCenter(String orgId,List<DriverInfo> drivers,String sceneId)throws Exception{
        Map<String,String> result=new HashMap<String,String>();
        AssertToolkit.notBlank(orgId, "orgId不能为空");
        if(CollectionUtils.isEmpty(drivers)) {
            return result;
        }
        if (sceneId == null) {
            log.warn("获取场景id为空,orgId:{}", orgId);
            return result;
        }

        List<Rpc5214029OSubBean> userInfoList = this.getDriverUserInfoListByAsync(drivers, sceneId, orgId);

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.warn("5214029请求结果集为空");
            return result;
        }
        Map<String,String> rpcDriverAuthMap=new HashMap<String,String>();
        for (Rpc5214029OSubBean item : userInfoList) {
            if (sceneId.equals(item.getSceneId())) {
                String key=generateDriverKey(item.getMobileNo(), item.getRealName(), item.getCartBadgeNo());
                rpcDriverAuthMap.put(key, item.getAuthState());
            }
        }
        for(DriverInfo driver:drivers) {
            String key=generateDriverKey(driver.getDriverMobileNo(), driver.getDriverName(), driver.getCartBadgeNo());
            String authState=rpcDriverAuthMap.get(key);
            result.put(key, authState);
        }
        return result;
    }

    public Map<String,String> getDriverAuthStateFromDataCenter(List<Rpc5214029IBean.Rpc5214029SubBean> drivers){
        Map<String,String> result=new HashMap<>();
        if(CollectionUtils.isEmpty(drivers)) {
            return result;
        }
        //是否带时间的查询条件
        boolean hasQueryTime = drivers.stream().filter(o -> StringUtils.isNotBlank(o.getQueryTime())).count() > 0;
        if(!hasQueryTime){
            // 没有时间查询条件 =》对司机根据项目ID、司机号、手机号、车牌号去重
            drivers = drivers.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(p -> StringToolkit.getBlankStr(p.getOrgId()) + Constants.SEPARATOR_UNDERLINE + StringToolkit.getBlankStr(p.getMobileNo()) + Constants.SEPARATOR_UNDERLINE + StringToolkit.getBlankStr(p.getCartBadgeNo())
                                    + Constants.SEPARATOR_UNDERLINE + StringToolkit.getBlankStr(p.getRealName())))), ArrayList::new));
        }

        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(3, 6, 5, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), new ThreadPoolExecutor.CallerRunsPolicy());
        List<Rpc5214029OSubBean> userInfoList = new ArrayList<>();
        try {
            List<CompletableFuture<Rpc5214029OBean>> futures = new ArrayList<>();
            for (List<Rpc5214029IBean.Rpc5214029SubBean> tempDriverList : Lists.partition(drivers, 10)) {
                CompletableFuture<Rpc5214029OBean> tempFuture = new CompletableFuture<>();
                futures.add(tempFuture);
                CompletableFuture.runAsync(new Runnable() {
                    @Override
                    public void run() {
                        Rpc5214029IBean rpc5214029IBean = new Rpc5214029IBean();
                        rpc5214029IBean.setUserList(tempDriverList);
                        Rpc5214029OBean rpc5214029OBean = null;
                        try {
                            rpc5214029OBean = rpcBaseBiopsyDomainService.rpc5214029(rpc5214029IBean);
                        } catch (Exception e) {
                            log.error("调用5214029发生异常{}", e);
                        }finally {
                            tempFuture.complete(rpc5214029OBean);
                        }
                    }
                },poolExecutor);
            }

            futures.stream().forEach(f -> {
                try {
                    Rpc5214029OBean rpc5214029OBean = f.get();
                    if (rpc5214029OBean != null && CollectionToolkit.isNotEmpty(rpc5214029OBean.getUserInfoList())) {
                        userInfoList.addAll(rpc5214029OBean.getUserInfoList());
                    }
                } catch (InterruptedException e) {
                    log.warn("调用5214029异步处理发生中断异常{}", e);
                } catch (ExecutionException e) {
                    log.warn("调用5214029异步处理发生异常{}", e);
                }
            });
        } catch (Exception e) {
            log.error("getDriverUserInfoListByAsync exception {}", e);
        } finally {
            poolExecutor.shutdown();
        }

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("5214029请求结果集为空");
            return result;
        }
        for (Rpc5214029OSubBean item : userInfoList) {
            String key = null;
            if (hasQueryTime) {
                key = generateDriverKeyWithOrgId(item.getOrgId(),item.getMobileNo(), item.getRealName(), item.getCartBadgeNo(),item.getQueryTime());
            }else {
                key = generateDriverKeyWithOrgId(item.getOrgId(),item.getMobileNo(), item.getRealName(), item.getCartBadgeNo());
            }
            result.put(key, item.getAuthState());
        }

        return result;
    }

    /**
     * 获取五证认证信息
     * 不用传场景ID
     * @param drivers
     * @return
     */
    public Map<String,Rpc5214029OSubBean> getDriverAuthInfo(List<Rpc5214029IBean.Rpc5214029SubBean> drivers) {
        Map<String,Rpc5214029OSubBean> result=new HashMap<>();
        if(CollectionUtils.isEmpty(drivers)) {
            return result;
        }
        List<String> orgIds = drivers.stream().map(Rpc5214029IBean.Rpc5214029SubBean::getOrgId).distinct().collect(Collectors.toList());
        //查询sceneId
        Map<String, String> orgSceneIdMap = null;
        try {
            orgSceneIdMap = this.getOrgSceneIdMap(orgIds);
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(),e);
        }
        if(MapUtils.isEmpty(orgSceneIdMap)){
            return result;
        }

        Iterator<Rpc5214029IBean.Rpc5214029SubBean> it = drivers.iterator();
        while (it.hasNext()){
            Rpc5214029IBean.Rpc5214029SubBean tempBean = it.next();
            String sceneId = orgSceneIdMap.get(tempBean.getOrgId());
            if(StringUtils.isBlank(sceneId)){
                //没有场景ID，移除
                it.remove();
            }else {
                tempBean.setSceneId(sceneId);
            }
        }
        return this.getDriverAuthInfoHasSceneId(drivers);
    }
    /**
     * 获取五证认证信息
     * 需要传场景ID
     * @param drivers
     * @return
     */
    public Map<String,Rpc5214029OSubBean> getDriverAuthInfoHasSceneId(List<Rpc5214029IBean.Rpc5214029SubBean> drivers){
        Map<String,Rpc5214029OSubBean> result=new HashMap<>();
        if(CollectionUtils.isEmpty(drivers)) {
            return result;
        }
        //是否带时间的查询条件
        boolean hasQueryTime = drivers.stream().filter(o -> StringUtils.isNotBlank(o.getQueryTime())).count() > 0;
        if(!hasQueryTime){
            // 没有时间查询条件 =》对司机根据项目ID、司机号、手机号、车牌号去重
            drivers = drivers.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(p -> StringToolkit.getBlankStr(p.getOrgId()) + Constants.SEPARATOR_UNDERLINE + StringToolkit.getBlankStr(p.getMobileNo()) + Constants.SEPARATOR_UNDERLINE + StringToolkit.getBlankStr(p.getCartBadgeNo())
                                    + Constants.SEPARATOR_UNDERLINE + StringToolkit.getBlankStr(p.getRealName())))), ArrayList::new));
        }

        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(3, 6, 5, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), new ThreadPoolExecutor.CallerRunsPolicy());
        List<Rpc5214029OSubBean> userInfoList = new ArrayList<>();
        try {
            List<CompletableFuture<Rpc5214029OBean>> futures = new ArrayList<>();
            for (List<Rpc5214029IBean.Rpc5214029SubBean> tempDriverList : Lists.partition(drivers, 10)) {
                CompletableFuture<Rpc5214029OBean> tempFuture = new CompletableFuture<>();
                futures.add(tempFuture);
                CompletableFuture.runAsync(new Runnable() {
                    @Override
                    public void run() {
                        Rpc5214029IBean rpc5214029IBean = new Rpc5214029IBean();
                        rpc5214029IBean.setUserList(tempDriverList);
                        Rpc5214029OBean rpc5214029OBean = null;
                        try {
                            rpc5214029OBean = rpcBaseBiopsyDomainService.rpc5214029(rpc5214029IBean);
                        } catch (Exception e) {
                            log.error("调用5214029发生异常{}", e);
                        }finally {
                            tempFuture.complete(rpc5214029OBean);
                        }
                    }
                },poolExecutor);
            }

            futures.stream().forEach(f -> {
                try {
                    Rpc5214029OBean rpc5214029OBean = f.get();
                    if (rpc5214029OBean != null && CollectionToolkit.isNotEmpty(rpc5214029OBean.getUserInfoList())) {
                        userInfoList.addAll(rpc5214029OBean.getUserInfoList());
                    }
                } catch (InterruptedException e) {
                    log.warn("调用5214029异步处理发生中断异常{}", e);
                } catch (ExecutionException e) {
                    log.warn("调用5214029异步处理发生异常{}", e);
                }
            });
        } catch (Exception e) {
            log.error("getDriverUserInfoListByAsync exception {}", e);
        } finally {
            poolExecutor.shutdown();
        }

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("5214029请求结果集为空");
            return result;
        }
        for (Rpc5214029OSubBean item : userInfoList) {
            String key = null;
            if (hasQueryTime) {
                key = generateDriverKeyWithOrgId(item.getOrgId(),item.getMobileNo(), item.getRealName(), item.getCartBadgeNo(),item.getQueryTime());
            }else {
                key = generateDriverKeyWithOrgId(item.getOrgId(),item.getMobileNo(), item.getRealName(), item.getCartBadgeNo());
            }
            result.put(key, item);
        }

        return result;
    }

    private CompletableFuture<Map<String, String>> getDriverAuthStateByAsync(List<Rpc5214029IBean.Rpc5214029SubBean> drivers, ThreadPoolExecutor executorPool) {
        if(CollectionUtils.isEmpty(drivers)){
            return null;
        }
        return CompletableFuture.supplyAsync(() -> {
            Map<String,String> driverAuthStateMap = null;
            try {
                List<String> orgIdList = drivers.stream().filter(o -> StringUtils.isNotBlank(o.getOrgId()))
                        .map(Rpc5214029SubBean::getOrgId)
                        .distinct().collect(Collectors.toList());
                //查询sceneId
                Map<String, String> orgSceneIdMap = getOrgSceneIdMap(orgIdList);
                Iterator<Rpc5214029SubBean> it = drivers.iterator();
                while (it.hasNext()){
                    Rpc5214029SubBean tempBean = it.next();
                    String sceneId = orgSceneIdMap.get(tempBean.getOrgId());
                    if(StringUtils.isBlank(sceneId)){
                        //没有场景ID，移除
                        it.remove();
                    }else {
                        tempBean.setSceneId(sceneId);
                    }
                }
                driverAuthStateMap=getDriverAuthStateFromDataCenter(drivers);
            } catch (Exception e) {
                log.warn("查询司机认证状态异常：{}", e);
            }
            return driverAuthStateMap;
        }, executorPool);
    }

    /**
     * 查询司机平台级会员认证状态
     * @param orgId
     * @param drivers
     * @param sceneId
     * @return
     * @throws Exception
     */
    public Map<String, Rpc5214029OSubBean> getDriverAuthAllStateFromDataCenter(String orgId, List<DriverInfo> drivers, String sceneId)throws Exception{
        Map<String,Rpc5214029OSubBean> result=new HashMap<>();
        AssertToolkit.notBlank(orgId, "orgId不能为空");
        if(CollectionUtils.isEmpty(drivers)) {
            return result;
        }
        if (sceneId == null) {
            log.warn("获取场景id为空,orgId:{}", orgId);
            return result;
        }

        List<Rpc5214029OSubBean> userInfoList = this.getDriverUserInfoListByAsync(drivers, sceneId, orgId);

        if (CollectionUtils.isEmpty(userInfoList)) {
            log.warn("5214029请求结果集为空");
            return result;
        }
        Map<String, Rpc5214029OSubBean> rpcDriverAuthMap=new HashMap<>();
        for (Rpc5214029OSubBean item : userInfoList) {
            if (sceneId.equals(item.getSceneId())) {
                String key=generateDriverKey(item.getMobileNo(), item.getRealName(), item.getCartBadgeNo());
                rpcDriverAuthMap.put(key, item);
            }
        }
        for(DriverInfo driver:drivers) {
            String key=generateDriverKey(driver.getDriverMobileNo(), driver.getDriverName(), driver.getCartBadgeNo());
            result.put(key, rpcDriverAuthMap.get(key));
        }
        return result;
    }

    /**
     * 异步处理获取司机平台认证信息
     * 根据小武反馈，由于调用5214029O接口过多司机会导致接口响应慢，所以将每次30条司机调用改为每次5条，然后并行调用，也就是每次6个线程调用
     * @param drivers
     * @param sceneId
     * @param orgId
     * @return
     */
    private List<Rpc5214029OSubBean> getDriverUserInfoListByAsync(List<DriverInfo> drivers, String sceneId, String orgId) {
        long startTime = System.currentTimeMillis();

        // 对司机去重
        drivers = drivers.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(p -> StringToolkit.getBlankStr(p.getCartBadgeNo()) + ";" + StringToolkit.getBlankStr(p.getDriverName()) + ";" + StringToolkit.getBlankStr(p.getDriverMobileNo()) + ";" + StringToolkit.getBlankStr(p.getQueryTime())))), ArrayList::new));
        Rpc5214029IBean rpc5214029IBean = new Rpc5214029IBean();
        List<Rpc5214029OSubBean> userInfoList = new ArrayList<>();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(3, 6, 5, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), new ThreadPoolExecutor.CallerRunsPolicy());

        try {
            List<CompletableFuture<Rpc5214029OBean>> futures = new ArrayList<>();
            for (List<DriverInfo> driverList : Lists.partition(drivers, 10)) {
                List<Rpc5214029SubBean> userList = new ArrayList<Rpc5214029SubBean>();
                for(DriverInfo driver:driverList) {
                    Rpc5214029SubBean subBean = new Rpc5214029SubBean(driver.getDriverMobileNo(), sceneId, driver.getCartBadgeNo(), orgId, driver.getDriverName());
                    subBean.setQueryTime(driver.getQueryTime());
                    userList.add(subBean);
                }
                CompletableFuture<Rpc5214029OBean> tempFuture = new CompletableFuture<>();
                futures.add(tempFuture);
                CompletableFuture.runAsync(new Runnable() {
                    @Override
                    public void run() {
                        rpc5214029IBean.setUserList(userList);
                        Rpc5214029OBean rpc5214029OBean = null;
                        try {
                            rpc5214029OBean = rpcBaseBiopsyDomainService.rpc5214029(rpc5214029IBean);
                        } catch (Exception e) {
                            log.error("调用5214029发生异常{}", e);
                        }finally {
                            tempFuture.complete(rpc5214029OBean);
                        }
                    }
                },poolExecutor);
            }

            futures.stream().forEach(f -> {
                try {
                    Rpc5214029OBean rpc5214029OBean = f.get();
                    if (rpc5214029OBean != null && CollectionToolkit.isNotEmpty(rpc5214029OBean.getUserInfoList())) {
                        userInfoList.addAll(rpc5214029OBean.getUserInfoList());
                    }
                } catch (InterruptedException e) {
                    log.warn("调用5214029异步处理发生中断异常{}", e);
                } catch (ExecutionException e) {
                    log.warn("调用5214029异步处理发生异常{}", e);
                }
            });
        } catch (Exception e) {
            log.error("getDriverUserInfoListByAsync exception {}", e);
        } finally {
            poolExecutor.shutdown();
        }

        long costTime = System.currentTimeMillis() - startTime;
        log.warn("5214029 costTime = {}, 新逻辑", costTime);
        return userInfoList;
    }

    public Map<String,String> getDriverAuthState(String orgId,List<DriverInfo> drivers,String sceneId)throws Exception{
        Map<String,String> result=new HashMap<>();
        AssertToolkit.notBlank(orgId, "orgId不能为空");
        if(CollectionUtils.isEmpty(drivers)) {
            return result;
        }
        if (sceneId == null) {
            log.warn("获取场景id为空,orgId:{}", orgId);
            return result;
        }
        Rpc5214029IBean rpc5214029IBean = new Rpc5214029IBean();
        List<Rpc5214029SubBean> userList = new ArrayList<>();
        for(DriverInfo driver:drivers) {
            Rpc5214029SubBean subBean = new Rpc5214029SubBean(driver.getDriverMobileNo(), sceneId, driver.getCartBadgeNo(), orgId, driver.getDriverName());
            subBean.setQueryTime(driver.getQueryTime());
            userList.add(subBean);
        }
        rpc5214029IBean.setUserList(userList);
        Rpc5214029OBean rpc5214029OBean = rpcBaseBiopsyDomainService.rpc5214029(rpc5214029IBean);
        if (CollectionUtils.isEmpty(rpc5214029OBean.getUserInfoList())) {
            log.warn("5214029请求结果集为空");
            return result;
        }
        Map<String,String> rpcDriverAuthMap=new HashMap<>();
        for (Rpc5214029OSubBean item : rpc5214029OBean.getUserInfoList()) {
            if (sceneId.equals(item.getSceneId())) {
                String key=generateDriverKey(item.getMobileNo(), item.getRealName(), item.getCartBadgeNo(),item.getQueryTime());
                rpcDriverAuthMap.put(key, item.getAuthState());
            }
        }
        for(DriverInfo driver:drivers) {
            String key=generateDriverKey(driver.getDriverMobileNo(), driver.getDriverName(), driver.getCartBadgeNo(),driver.getQueryTime());
            String authState=rpcDriverAuthMap.get(key);
            result.put(key, authState);
        }
        return result;
    }

    public Rpc5214029OSubBean getDriverAuthInfo(String sceneId, String driverName, String mobileNo, String cartBadgeNo){
        Rpc5214029IBean rpc5214029IBean = new Rpc5214029IBean();
        List<Rpc5214029SubBean> userList = new ArrayList<>();
        Rpc5214029SubBean subBean = new Rpc5214029SubBean(mobileNo, sceneId, cartBadgeNo, null, driverName);
        userList.add(subBean);
        rpc5214029IBean.setUserList(userList);
        try {
            Rpc5214029OBean rpc5214029OBean = rpcBaseBiopsyDomainService.rpc5214029(rpc5214029IBean);
            if(rpc5214029OBean!=null && CollectionUtils.isNotEmpty(rpc5214029OBean.getUserInfoList())){
                return rpc5214029OBean.getUserInfoList().get(0);
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(),e);
        }

        return null;
    }

    public String getDriverAuthState(String sceneId, String driverName, String mobileNo, String cartBadgeNo){
        Rpc5214029OSubBean driverAuthInfo = this.getDriverAuthInfo(sceneId, driverName, mobileNo, cartBadgeNo);
        if(driverAuthInfo==null){
            return null;
        }
        return driverAuthInfo.getAuthState();
    }

    public String generateDriverKey(String mobileNo, String driverName, String cartBadgeNo) {
        StringBuilder key = new StringBuilder();
        key.append(mobileNo);
        if (StringUtils.isNotBlank(driverName)) {
            key.append(Constants.SEPARATOR_VERTICAL)
                    .append(driverName);
        }
        key.append(Constants.SEPARATOR_VERTICAL)
                .append(cartBadgeNo);
        return key.toString();
    }

    public String generateDriverKeyWithOrgId(String orgId, String mobileNo, String driverName, String cartBadgeNo) {
        return this.generateDriverKeyWithOrgId(orgId, mobileNo, driverName, cartBadgeNo,null);
    }

    public String generateDriverKeyWithOrgId(String orgId, String mobileNo, String driverName, String cartBadgeNo,String queryTime) {
        StringBuilder key=new StringBuilder();
        key.append(orgId).append(Constants.SEPARATOR_VERTICAL).append(mobileNo).append(Constants.SEPARATOR_VERTICAL).append(cartBadgeNo);
        if (StringUtils.isNotBlank(driverName)) {
            key.append(Constants.SEPARATOR_VERTICAL)
                    .append(driverName);
        }
        if (StringUtils.isNotBlank(queryTime)) {
            key.append(Constants.SEPARATOR_VERTICAL)
                    .append(queryTime);
        }
        return key.toString();
    }

    public String generateDriverKey(String mobileNo, String driverName, String cartBadgeNo, String queryTime) {
        final String separator = "|";
        StringBuilder key = new StringBuilder();
        key.append(mobileNo);
        if (StringUtils.isNotBlank(driverName)) {
            key.append(separator)
                    .append(driverName);
        }
        key.append(separator)
                .append(cartBadgeNo)
                .append(separator)
                .append(queryTime);
        return key.toString();
    }


    public String getSceneId(String orgId)throws Exception {
        Rpc22081IBean rpc22081Ibean = new Rpc22081IBean();
        List<String> orgIds = new ArrayList<>();
        orgIds.add(orgId);
        rpc22081Ibean.setOrgIds(orgIds);
        Rpc22081OBean rpc22081oBean = rpcMMMssorgService.rpc22081(rpc22081Ibean);
        if(rpc22081oBean == null || CollectionUtils.isEmpty(rpc22081oBean.getOrgSceneList())){
            return null;
        }
        String sceneId = null;
        for (Rpc22081OSubBean item : rpc22081oBean.getOrgSceneList()) {
            if (orgId.equals(item.getOrgId())) {
                sceneId = item.getSceneId();
                break;
            }
        }

        return sceneId;
    }

    public Map<String, String> getOrgSceneIdMap(List<String> orgIds)throws Exception {
        Map<String, String> result = new HashMap();
        if(CollectionUtils.isEmpty(orgIds)){
            return result;
        }
        Rpc22081IBean rpc22081Ibean = new Rpc22081IBean();
        rpc22081Ibean.setOrgIds(orgIds);
        Rpc22081OBean rpc22081oBean = rpcMMMssorgService.rpc22081(rpc22081Ibean);
        if (CollectionUtils.isNotEmpty(rpc22081oBean.getOrgSceneList())) {
            for (Rpc22081OSubBean item : rpc22081oBean.getOrgSceneList()) {
                result.put(item.getOrgId(), item.getSceneId());
            }
        }
        return result;
    }

    /**
     * 按orgId查询司机的身份证号码
     * @param orgId
     * @param mobileNo
     * @param plateNo
     * @param realName
     * @return
     * @throws Exception
     */
    public String getDriverIdCardByOrgId(String orgId, String mobileNo, String plateNo, String realName) throws Exception {
        //验证必传参数
        if (StringUtils.isAnyEmpty(orgId, mobileNo, realName, plateNo)) {
            return StringUtils.EMPTY;
        }
        //直接查询5214029接口，取已认证的数据
        String sceneId = getSceneId(orgId);
        if(StringUtils.isBlank(sceneId)){
            return StringUtils.EMPTY;
        }
        Rpc5214029IBean rpc5214029IBean = new Rpc5214029IBean();
        List<Rpc5214029IBean.Rpc5214029SubBean> userList = new ArrayList<>();
        userList.add(new Rpc5214029IBean.Rpc5214029SubBean(mobileNo, sceneId, plateNo, orgId, realName));
        rpc5214029IBean.setUserList(userList);
        Rpc5214029OBean rpc5214029OBean = rpcBaseBiopsyDomainService.rpc5214029(rpc5214029IBean);
        if (CollectionUtils.isNotEmpty(rpc5214029OBean.getUserInfoList())) {
            for (Rpc5214029OSubBean subBean : rpc5214029OBean.getUserInfoList()) {
                if (StringUtils.isNotEmpty(subBean.getIdCard()) && StringUtils.equals("0", subBean.getIdCardCheckState())) {
                    return subBean.getIdCard();
                }
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取司机身份证号
     * @param orgId
     * @param driverName
     * @param mobileNo
     * @param cartBadgeNo
     * @return
     */
    public String getDriverIdCard(String orgId, String driverName, String mobileNo, String cartBadgeNo) {
        try {
            return this.getDriverIdCardByOrgId(orgId, mobileNo, cartBadgeNo, driverName);
        } catch (Exception e) {
            String errorMsg = e.getLocalizedMessage();
            log.warn("获取司机身份证失败:{}", errorMsg, e);
        }
        return StringUtils.EMPTY;
    }


    public String getDriverIdCardForUpdate(String orgId, String mobileNo, String cartBadgeNo,String driverName) throws Exception {
        //验证必传参数
        if (StringUtils.isAnyBlank(orgId, mobileNo, cartBadgeNo)) {
            return StringUtils.EMPTY;
        }
        //直接查询5214029接口，取已认证的数据
        String sceneId = getSceneId(orgId);
        if(StringUtils.isBlank(sceneId)){
            //注意不要改成空字符串，代码中有逻辑
            return null;
        }
        Rpc5214029IBean rpc5214029IBean = new Rpc5214029IBean();
        List<Rpc5214029IBean.Rpc5214029SubBean> userList = new ArrayList<>();
        userList.add(new Rpc5214029IBean.Rpc5214029SubBean(mobileNo, sceneId, cartBadgeNo, orgId, driverName));
        rpc5214029IBean.setUserList(userList);
        Rpc5214029OBean rpc5214029OBean = rpcBaseBiopsyDomainService.rpc5214029(rpc5214029IBean);
        if (CollectionUtils.isNotEmpty(rpc5214029OBean.getUserInfoList())) {
            for (Rpc5214029OSubBean subBean : rpc5214029OBean.getUserInfoList()) {
                if (StringUtils.isNotEmpty(subBean.getIdCard())) {
                    return subBean.getIdCard();
                }
            }
        }
        //注意不要改动，代码中有逻辑
        return StringUtils.EMPTY;
    }
}
