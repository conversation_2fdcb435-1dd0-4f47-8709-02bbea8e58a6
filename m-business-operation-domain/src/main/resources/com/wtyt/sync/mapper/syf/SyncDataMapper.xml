<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.sync.mapper.syf.SyncDataMapper">

    <insert id="syncRecoveryWaybill">
        DECLARE
        CNT NUMBER(10);
        BEGIN
        CNT := 0;
        FOR TEMP IN (
            SELECT
            bt.BO_TRANS_TASK_ID
            FROM
            SYF.T_BO_TRANS_TASK bt
            JOIN SYF.T_TAX_WAYBILL ttw ON
            TTW.TAX_WAYBILL_ID = bt.TAX_WAYBILL_ID
            AND ttw.IS_DEL = 0
            WHERE
            bt.ORG_ID = #{orgId}
            AND bt.is_del = 1
            AND bt.CREATED_TIME > SYSDATE -30
        )
        LOOP
            UPDATE SYF.T_BO_TRANS_TASK SET IS_DEL =0,LAST_MODIFIED_TIME =SYSDATE,NOTE ='运单删除恢复' WHERE BO_TRANS_TASK_ID =TEMP.BO_TRANS_TASK_ID;
        CNT := CNT + 1;
        IF (MOD(CNT, 1000) = 0) THEN
        COMMIT;
        END IF;
        END LOOP;
        COMMIT;
        END;
    </insert>

    <insert id="syncTaskOld">
        INSERT INTO T_BO_TRANS_TASK(BO_TRANS_TASK_ID,ORG_ID,OWNER_ORG_ID,TAX_WAYBILL_ID,TAX_WAYBILL_NO,CREATED_TIME,DRIVER_NAME,MOBILE_NO,CART_BADGE_NO,TRANS_MODE,TRANSPORT_TYPE,OFFER_TYPE,node_id,node_time,SETTLE_MODE,NOTE)
        SELECT
        '1' || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********)), 9, 0) || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********0)), 10, 0) AS id,
        TTW.ORG_ID,
        TTW.ORG_ID,
        TTW.TAX_WAYBILL_ID,
        TTW.TAX_WAYBILL_NO,
        TTW.CREATED_TIME,
        TTW.DRIVER_NAME,
        TTW.MOBILE_NO,
        TTW.CART_BADGE_NO,
        '1' TRANS_MODE,
        '0' TRANSPORT_TYPE,
        '1' OFFER_TYPE,
        '200' node_id,
        TTW.CREATED_TIME node_time,
        '1' SETTLE_MODE,
        '中部陆港演示大屏要初始化数据'  NOTE
        FROM T_TAX_WAYBILL ttw
        WHERE TTW.ORG_ID = #{orgId}
        AND TTW.IS_DEL = 0
        AND TTW.Wb_Mode=0
        AND TTW.CREATED_TIME > SYSDATE - 30
        AND NOT EXISTS (SELECT 1 FROM T_BO_TRANS_TASK bt WHERE bt.TAX_WAYBILL_ID=ttw.TAX_WAYBILL_ID AND bt.is_del=0)
    </insert>

    <insert id="syncTask">
        INSERT INTO T_BO_TRANS_TASK(BO_TRANS_TASK_ID,ORG_ID,OWNER_ORG_ID,TAX_WAYBILL_ID,TAX_WAYBILL_NO,CREATED_TIME,DRIVER_NAME,MOBILE_NO,CART_BADGE_NO,TRANS_MODE,TRANSPORT_TYPE,OFFER_TYPE,node_id,node_time,SETTLE_MODE,NOTE)
        SELECT
        '1' || TO_CHAR(SYSDATE, 'YYMMDDHH24') || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********00)), 11, 0) AS id,
        TTW.ORG_ID,
        TTW.ORG_ID,
        TTW.TAX_WAYBILL_ID,
        TTW.TAX_WAYBILL_NO,
        TTW.CREATED_TIME,
        TTW.DRIVER_NAME,
        TTW.MOBILE_NO,
        TTW.CART_BADGE_NO,
        '1' TRANS_MODE,
        '0' TRANSPORT_TYPE,
        '1' OFFER_TYPE,
        '200' node_id,
        TTW.CREATED_TIME node_time,
        '1' SETTLE_MODE,
        '中部陆港演示大屏要初始化数据'  NOTE
        FROM T_TAX_WAYBILL ttw
        WHERE TTW.ORG_ID = #{orgId}
        AND TTW.IS_DEL = 0
        AND TTW.Wb_Mode=0
        AND TTW.CREATED_TIME > SYSDATE - 30
        AND NOT EXISTS (SELECT 1 FROM T_BO_TRANS_TASK bt WHERE bt.TAX_WAYBILL_ID=ttw.TAX_WAYBILL_ID AND bt.is_del=0)
    </insert>

    <insert id="syncTaskExtra">
        INSERT INTO T_BO_TRANS_TASK_EXTRA(BO_TRANS_TASK_EXTRA_ID,BO_TRANS_TASK_ID,USER_FREIGHT_APPROVAL_STATUS,NOTE)
        SELECT
        '1' || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********)), 9, 0) || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********0)), 10, 0) AS id,
        t.BO_TRANS_TASK_ID,
        '0' USER_FREIGHT_APPROVAL_STATUS,
        '中部陆港演示大屏要初始化数据' NOTE
        FROM
        T_BO_TRANS_TASK t left join T_BO_TRANS_TASK_EXTRA e on t.bo_trans_task_id = e.bo_trans_task_id and e.is_del=0
        WHERE  t.note = '中部陆港演示大屏要初始化数据' and t.org_id = #{orgId} and e.bo_trans_task_id is null
    </insert>

    <delete id="syncTaskGroup">
        INSERT INTO T_BO_TRANS_TASK_GROUP_REL(BO_TRANS_TASK_GROUP_REL_ID,BO_TRANS_TASK_ID,ORG_ID,GROUP_ID,SUPPLIER_TYPE,NOTE)
        SELECT
        '1' || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********)), 9, 0) || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********0)), 10, 0) AS id,
        t.BO_TRANS_TASK_ID,
        t.ORG_ID,
        up.GROUP_ID  GROUP_ID,
        '0' SUPPLIER_TYPE,
        '中部陆港演示大屏要初始化数据' NOTE
        FROM
        T_BO_TRANS_TASK t left join T_BO_TRANS_TASK_GROUP_REL gr on t.bo_trans_task_id = gr.bo_trans_task_id and gr.is_del = 0
        LEFT JOIN T_U_GROUP up ON up.GROUP_CODE = t.ORG_ID AND up.IS_DEL =0
        where  t.note = '中部陆港演示大屏要初始化数据' and t.org_id = #{orgId} and gr.bo_trans_task_id is null
    </delete>

    <insert id="syncTaskNodeRecord100">
        INSERT INTO T_BO_TRANS_NODE_RECORD
        (BO_TRANS_NODE_RECORD_ID,
        TAX_WAYBILL_ID,
        NODE_ID,
        OPT_SOURCE,
        USER_ID,
        DRIVER_ID,
        REASON,
        SYS_ROLE_TYPE,
        BOSS_NODE_CODE,
        BO_TRANS_TASK_ID,
        ORG_ID,
        CREATED_TIME,
        NOTE
        )
        SELECT
        '1' || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********)), 9, 0) || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********0)), 10, 0) AS id,
        t.TAX_WAYBILL_ID,
        '100' NODE_ID,
        '-1' OPT_SOURCE,
        '-1' USER_ID,
        e.driver_id,
        '' reason,
        '' sys_role_type,
        '' BOSS_NODE_CODE,
        t.bo_trans_task_id,
        t.ORG_ID,
        nvl(t.created_time, sysdate),
        '中部陆港演示大屏要初始化数据' NOTE
        FROM T_BO_TRANS_TASK t
        left join T_BO_TRANS_NODE_RECORD nr
        on t.bo_trans_task_id = nr.bo_trans_task_id
        and nr.is_del = 0
        and nr.node_id = '100', t_Bo_Trans_Task_Extra e
        WHERE t.bo_trans_task_id = e.bo_trans_task_id
        and t.note = '中部陆港演示大屏要初始化数据'
        and t.org_id = #{orgId}
        and nr.bo_trans_task_id is null
    </insert>

    <insert id="syncTaskNodeRecord200">
        INSERT INTO T_BO_TRANS_NODE_RECORD
        (BO_TRANS_NODE_RECORD_ID,
        TAX_WAYBILL_ID,
        NODE_ID,
        OPT_SOURCE,
        USER_ID,
        DRIVER_ID,
        REASON,
        SYS_ROLE_TYPE,
        BOSS_NODE_CODE,
        BO_TRANS_TASK_ID,
        ORG_ID,
        CREATED_TIME,
        NOTE
        )
        SELECT '1' || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********)), 9, 0) || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********0)), 10, 0) AS id,
        t.TAX_WAYBILL_ID,
        '200' NODE_ID,
        '-1' OPT_SOURCE,
        '-1' USER_ID,
        e.driver_id,
        '' reason,
        '' sys_role_type,
        '' BOSS_NODE_CODE,
        t.bo_trans_task_id,
        t.ORG_ID,
        nvl(t.created_time, sysdate),
        '中部陆港演示大屏要初始化数据' NOTE
        FROM T_BO_TRANS_TASK t
        left join T_BO_TRANS_NODE_RECORD nr
        on t.bo_trans_task_id = nr.bo_trans_task_id
        and nr.is_del = 0
        and nr.node_id = '200', t_Bo_Trans_Task_Extra e
        WHERE t.bo_trans_task_id = e.bo_trans_task_id
        and t.note = '中部陆港演示大屏要初始化数据'
        and t.org_id = #{orgId}
        and nr.bo_trans_task_id is null
    </insert>

    <insert id="syncTaskNodeRecord400">
        INSERT INTO T_BO_TRANS_NODE_RECORD
        (BO_TRANS_NODE_RECORD_ID,
        TAX_WAYBILL_ID,
        NODE_ID,
        OPT_SOURCE,
        USER_ID,
        DRIVER_ID,
        REASON,
        SYS_ROLE_TYPE,
        BOSS_NODE_CODE,
        BO_TRANS_TASK_ID,
        ORG_ID,
        CREATED_TIME,
        NOTE)
        SELECT '1' || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********)), 9, 0) || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********0)), 10, 0) AS id,
        t.TAX_WAYBILL_ID,
        '400' NODE_ID,
        '-1' OPT_SOURCE,
        '-1' USER_ID,
        e.driver_id,
        '' reason,
        '' sys_role_type,
        '' BOSS_NODE_CODE,
        t.bo_trans_task_id,
        t.ORG_ID,
        te.hyb_received_time,
        '中部陆港演示大屏要初始化数据' NOTE
        FROM T_BO_TRANS_TASK t
        left join T_BO_TRANS_NODE_RECORD nr
        on t.bo_trans_task_id = nr.bo_trans_task_id
        and nr.is_del = 0
        and nr.node_id = '400', t_Bo_Trans_Task_Extra e,
        t_Tax_Waybill w, t_Tax_Waybill_Extra te
        WHERE t.bo_trans_task_id = e.bo_trans_task_id
        and t.tax_waybill_id = w.tax_waybill_id
        and w.tax_waybill_id = te.tax_waybill_id
        and w.is_del = 0
        and w.hyb_state != 0
        and te.hyb_received_time is not null
        and t.note = '中部陆港演示大屏要初始化数据'
        and t.org_id = #{orgId}
        and nr.bo_trans_task_id is null
    </insert>

    <insert id="syncTaskNodeRecord500">
        INSERT INTO T_BO_TRANS_NODE_RECORD
        (BO_TRANS_NODE_RECORD_ID,
        TAX_WAYBILL_ID,
        NODE_ID,
        OPT_SOURCE,
        USER_ID,
        DRIVER_ID,
        REASON,
        SYS_ROLE_TYPE,
        BOSS_NODE_CODE,
        BO_TRANS_TASK_ID,
        ORG_ID,
        CREATED_TIME,
        NOTE)
        SELECT '1' || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********)), 9, 0) || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********0)), 10, 0) AS id,
        t.TAX_WAYBILL_ID,
        '500' NODE_ID,
        '-1' OPT_SOURCE,
        '-1' USER_ID,
        e.driver_id,
        '' reason,
        '' sys_role_type,
        '' BOSS_NODE_CODE,
        t.bo_trans_task_id,
        t.ORG_ID,
        w.created_time+1/24/60 arrived_time,
        '中部陆港演示大屏要初始化数据' NOTE
        FROM T_BO_TRANS_TASK t
        left join T_BO_TRANS_NODE_RECORD nr
        on t.bo_trans_task_id = nr.bo_trans_task_id
        and nr.is_del = 0
        and nr.node_id = '500', t_Bo_Trans_Task_Extra e,
        t_Tax_Waybill w, t_Tax_Waybill_Extra te
        WHERE t.bo_trans_task_id = e.bo_trans_task_id
        and t.tax_waybill_id = w.tax_waybill_id
        and w.tax_waybill_id = te.tax_waybill_id
        and w.is_del = 0
        and w.hyb_state != 0
        and w.start_time is not null
        and t.note = '中部陆港演示大屏要初始化数据'
        and t.org_id = #{orgId}
        and nr.bo_trans_task_id is null
    </insert>

    <insert id="syncTaskNodeRecord600">
        INSERT INTO T_BO_TRANS_NODE_RECORD
        (BO_TRANS_NODE_RECORD_ID,
        TAX_WAYBILL_ID,
        NODE_ID,
        OPT_SOURCE,
        USER_ID,
        DRIVER_ID,
        REASON,
        SYS_ROLE_TYPE,
        BOSS_NODE_CODE,
        BO_TRANS_TASK_ID,
        ORG_ID,
        CREATED_TIME,
        NOTE)
        SELECT '1' || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********)), 9, 0) || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********0)), 10, 0) AS id,
        t.TAX_WAYBILL_ID,
        '600' NODE_ID,
        '-1' OPT_SOURCE,
        '-1' USER_ID,
        e.driver_id,
        '' reason,
        '' sys_role_type,
        '' BOSS_NODE_CODE,
        t.bo_trans_task_id,
        t.ORG_ID,
        w.start_time,
        '中部陆港演示大屏要初始化数据' NOTE
        FROM T_BO_TRANS_TASK t
        left join T_BO_TRANS_NODE_RECORD nr
        on t.bo_trans_task_id = nr.bo_trans_task_id
        and nr.is_del = 0
        and nr.node_id = '600', t_Bo_Trans_Task_Extra e,
        t_Tax_Waybill w, t_Tax_Waybill_Extra te
        WHERE t.bo_trans_task_id = e.bo_trans_task_id
        and t.tax_waybill_id = w.tax_waybill_id
        and w.tax_waybill_id = te.tax_waybill_id
        and w.is_del = 0
        and w.hyb_state != 0
        and w.start_time is not null
        and t.note = '中部陆港演示大屏要初始化数据'
        and t.org_id = #{orgId}
        and nr.bo_trans_task_id is null
    </insert>

    <insert id="syncTaskNodeRecord700">
        INSERT INTO T_BO_TRANS_NODE_RECORD
        (BO_TRANS_NODE_RECORD_ID,
        TAX_WAYBILL_ID,
        NODE_ID,
        OPT_SOURCE,
        USER_ID,
        DRIVER_ID,
        REASON,
        SYS_ROLE_TYPE,
        BOSS_NODE_CODE,
        BO_TRANS_TASK_ID,
        ORG_ID,
        CREATED_TIME,
        NOTE)
        SELECT '1' || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********)), 9, 0) || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********0)), 10, 0) AS id,
        t.TAX_WAYBILL_ID,
        '700' NODE_ID,
        '-1' OPT_SOURCE,
        '-1' USER_ID,
        e.driver_id,
        '' reason,
        '' sys_role_type,
        '' BOSS_NODE_CODE,
        t.bo_trans_task_id,
        t.ORG_ID,
        w.end_time,
        '中部陆港演示大屏要初始化数据' NOTE
        FROM T_BO_TRANS_TASK t
        left join T_BO_TRANS_NODE_RECORD nr
        on t.bo_trans_task_id = nr.bo_trans_task_id
        and nr.is_del = 0
        and nr.node_id = '700', t_Bo_Trans_Task_Extra e,
        t_Tax_Waybill w, t_Tax_Waybill_Extra te
        WHERE t.bo_trans_task_id = e.bo_trans_task_id
        and t.tax_waybill_id = w.tax_waybill_id
        and w.tax_waybill_id = te.tax_waybill_id
        and w.is_del = 0
        and w.hyb_state != 0
        and w.end_time is not null
        and t.note = '中部陆港演示大屏要初始化数据'
        and t.org_id = #{orgId}
        and nr.bo_trans_task_id is null
    </insert>

    <insert id="syncTaskDetail">
        DECLARE
        CNT NUMBER(10);
        BEGIN
        CNT := 0;
        FOR TEMP IN (
        SELECT
        T2.BO_TRANS_TASK_ID,
        T1.*
        FROM T_TAX_WAYBILL T1
        LEFT JOIN T_BO_TRANS_TASK T2
        ON T1.TAX_WAYBILL_ID = T2.TAX_WAYBILL_ID
        WHERE T1.ORG_ID = #{orgId}
        AND T1.IS_DEL = 0
        AND T1.WB_MODE=0
        AND T2.BO_TRANS_TASK_ID is not null
        AND T2.STATE != 2
        )
        LOOP

        UPDATE T_BO_TRANS_TASK T3
        SET
        T3.MOBILE_NO = TEMP.MOBILE_NO,
        T3.START_CITY_CODE = TEMP.START_CITY_CODE,
        T3.START_CITY_NAME = TEMP.START_CITY_NAME,
        T3.END_CITY_CODE = TEMP.END_CITY_CODE,
        T3.END_CITY_NAME = TEMP.END_CITY_NAME,
        T3.START_TIME = TEMP.START_TIME,
        T3.END_TIME = TEMP.END_TIME,
        T3.STATE = TEMP.STATE,
        T3.ALL_FREIGHT = TEMP.ALL_FREIGHT,
        T3.SERVICE_FEE = TEMP.SERVICE_FEE,
        T3.PAY_STATE = TEMP.PAY_STATE,
        T3.DRIVER_NAME = TEMP.DRIVER_NAME,
        T3.CART_BADGE_NO = TEMP.CART_BADGE_NO,
        T3.GOODS_NAME = TEMP.GOODS_NAME,
        T3.GOODS_AMOUNT = TEMP.GOODS_AMOUNT,
        T3.LAST_MODIFIED_TIME = SYSDATE,
        T3.START_PROVINCE_NAME = TEMP.START_PROVINCE_NAME,
        T3.START_PROVINCE_CODE = TEMP.START_PROVINCE_CODE,
        T3.END_PROVINCE_NAME = TEMP.END_PROVINCE_NAME,
        T3.END_PROVINCE_CODE = TEMP.END_PROVINCE_CODE,
        T3.TAX_WAYBILL_NO = TEMP.TAX_WAYBILL_NO,
        T3.GOODS_AMOUNT_TYPE = TEMP.GOODS_AMOUNT_TYPE,
        T3.PREPAYMENTS = TEMP.PREPAYMENTS,
        T3.ORDER_CREATE_TYPE = TEMP.ORDER_CREATE_TYPE,
        T3.START_COUNTY_NAME = TEMP.START_COUNTY_NAME,
        T3.END_COUNTY_NAME = TEMP.END_COUNTY_NAME,
        T3.START_COUNTY_CODE = TEMP.START_COUNTY_CODE,
        T3.END_COUNTY_CODE = TEMP.END_COUNTY_CODE,
        T3.BACK_FEE = TEMP.BACK_FEE,
        T3.UNIT_PRICE = TEMP.UNIT_PRICE,
        T3.LOSS_FEE = TEMP.LOSS_FEE,
        T3.MILEAGE = TEMP.MILEAGE,
        T3.USER_FREIGHT = TEMP.USER_FREIGHT,
        T3.FREIGHT_INCR = TEMP.FREIGHT_INCR,
        T3.WB_ITEM = TEMP.WB_ITEM,
        T3.RECEIVER = TEMP.RECEIVER,
        T3.RECEIVER_MOBILE = TEMP.RECEIVER_MOBILE,
        T3.GOODS_COST = TEMP.GOODS_COST,
        T3.PREPAYMENTS_OILCARD = TEMP.PREPAYMENTS_OILCARD,
        T3.HYB_STATE = TEMP.HYB_STATE,
        T3.INS_FEE = TEMP.INS_FEE,
        T3.TRANSPORT_TYPE = TEMP.TRANSPORT_TYPE,
        T3.LOADING_TONNAGE = TEMP.LOADING_TONNAGE,
        T3.UNLOADING_TONNAGE = TEMP.UNLOADING_TONNAGE,
        T3.TRANSPORT_LINE_ID = TEMP.TRANSPORT_LINE_ID,
        T3.FIRST_RECEIPT_TIME = TEMP.FIRST_RECEIPT_TIME,
        T3.PAY_OVER_TIME = TEMP.PAY_OVER_TIME,
        T3.ADVANCE_PAY_STATE = TEMP.ADVANCE_PAY_STATE,
        T3.LOSS_ENSURE_STATE = TEMP.LOSS_ENSURE_STATE,
        T3.PREPAYMENTS_GASCARD = TEMP.PREPAYMENTS_GASCARD,
        T3.WAYBILL_BELONG_ACT_SYS = TEMP.WAYBILL_BELONG_ACT_SYS,
        T3.START_LONGITUDE = TEMP.START_LONGITUDE,
        T3.START_LATITUDE = TEMP.START_LATITUDE,
        T3.END_LONGITUDE = TEMP.END_LONGITUDE,
        T3.END_LATITUDE = TEMP.END_LATITUDE,
        T3.CART_BADGE_COLOR = TEMP.CART_BADGE_COLOR,
        T3.MODIFY_USER_ID = TEMP.MODIFY_USER_ID,
        T3.MODIFY_DRIVER_ID = TEMP.MODIFY_DRIVER_ID,
        T3.XCY_USER_ID = TEMP.XCY_USER_ID,
        T3.MODIFY_SYS_ROLE_TYPE = TEMP.MODIFY_SYS_ROLE_TYPE,
        T3.FROZEN_BACK_FEE_STATE = TEMP.FROZEN_BACK_FEE_STATE
        WHERE T3.BO_TRANS_TASK_ID = TEMP.BO_TRANS_TASK_ID;

        CNT := CNT + 1;
        IF (MOD(CNT, 1000) = 0) THEN
        COMMIT;
        END IF;
        END LOOP;
        COMMIT;
        END;
    </insert>

    <insert id="syncTaskExtraDetail">
        DECLARE
        CNT NUMBER(10);
        BEGIN
        CNT := 0;
        FOR TEMP IN (
        SELECT
        te.BO_TRANS_TASK_EXTRA_ID AS PRIMARY_KEY,
        twe.PAY_TYPE,
        twe.PAY_NAME,
        twe.PAY_BANK_NO,
        twe.CART_TYPE,
        twe.TRAN_REQUIRE,
        twe.CART_TONNAGE,
        twe.PAY_BANK_NAME,
        twe.CITY_NAME,
        twe.PAY_MOBILE_NO,
        twe.CART_LENGTH,
        twe.PROVINCE,
        twe.PAY_ID_CARD,
        twe.HYB_RECEIVED_TIME,
        twe.OIL_CARD_NO,
        twe.IS_UNIT_PRICE_SHOW,
        twe.LOSS_AMOUNT,
        twe.SHIPMENT_PHOTO,
        twe.FREIGHT_GUARANTEE,
        twe.CUSTOMIZE_NO,
        twe.SETTLE_ISSUE,
        twe.COLLECTION_MODE,
        twe.GAS_CARD_NO,
        twe.IS_FREIGHT_SHOW,
        twe.LOADING_ADDRESS_NAME,
        twe.UNLOADING_ADDRESS_NAME,
        twe.RECEIPT_BZ_STATE,
        twe.RECEIPT_RECEIVE_TIME,
        twe.ORG_MAILING_ADDRESS_ID,
        twe.TRANS_VOUCHER,
        twe.TRANS_VOUCHER_EXPRESS_NUMBER,
        twe.SERVICE_REQUIRE,
        twe.GOODS_AMOUNT_OCR_STATE
        FROM
        t_bo_trans_task t
        JOIN T_BO_TRANS_TASK_EXTRA te
        ON
        te.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        AND te.IS_DEL = 0
        JOIN T_TAX_WAYBILL_EXTRA twe ON
        t.TAX_WAYBILL_ID = twe.TAX_WAYBILL_ID
        AND twe.IS_DEL = 0
        WHERE
        t.org_id = #{orgId}
        and t.is_del=0
        and t.note='中部陆港演示大屏要初始化数据'
        and  te.LOADING_ADDRESS_NAME is null
        )
        LOOP
        UPDATE T_BO_TRANS_TASK_EXTRA te SET
        te.PAY_TYPE = TEMP.PAY_TYPE,
        te.PAY_NAME = TEMP.PAY_NAME,
        te.PAY_BANK_NO = TEMP.PAY_BANK_NO,
        te.CART_TYPE = TEMP.CART_TYPE,
        te.TRAN_REQUIRE = TEMP.TRAN_REQUIRE,
        te.CART_TONNAGE = TEMP.CART_TONNAGE,
        te.PAY_BANK_NAME = TEMP.PAY_BANK_NAME,
        te.CITY_NAME = TEMP.CITY_NAME,
        te.PAY_MOBILE_NO = TEMP.PAY_MOBILE_NO,
        te.CART_LENGTH = TEMP.CART_LENGTH,
        te.PROVINCE = TEMP.PROVINCE,
        te.PAY_ID_CARD = TEMP.PAY_ID_CARD,
        te.HYB_RECEIVED_TIME = TEMP.HYB_RECEIVED_TIME,
        te.OIL_CARD_NO = TEMP.OIL_CARD_NO,
        te.IS_UNIT_PRICE_SHOW = TEMP.IS_UNIT_PRICE_SHOW,
        te.LOSS_AMOUNT = TEMP.LOSS_AMOUNT,
        te.SHIPMENT_PHOTO = TEMP.SHIPMENT_PHOTO,
        te.FREIGHT_GUARANTEE = TEMP.FREIGHT_GUARANTEE,
        te.CUSTOMIZE_NO = TEMP.CUSTOMIZE_NO,
        te.SETTLE_ISSUE = TEMP.SETTLE_ISSUE,
        te.COLLECTION_MODE = TEMP.COLLECTION_MODE,
        te.GAS_CARD_NO = TEMP.GAS_CARD_NO,
        te.IS_FREIGHT_SHOW = TEMP.IS_FREIGHT_SHOW,
        te.LOADING_ADDRESS_NAME = TEMP.LOADING_ADDRESS_NAME,
        te.UNLOADING_ADDRESS_NAME = TEMP.UNLOADING_ADDRESS_NAME,
        te.RECEIPT_BZ_STATE = TEMP.RECEIPT_BZ_STATE,
        te.RECEIPT_RECEIVE_TIME = TEMP.RECEIPT_RECEIVE_TIME,
        te.ORG_MAILING_ADDRESS_ID = TEMP.ORG_MAILING_ADDRESS_ID,
        te.TRANS_VOUCHER = TEMP.TRANS_VOUCHER,
        te.TRANS_VOUCHER_EXPRESS_NUMBER = TEMP.TRANS_VOUCHER_EXPRESS_NUMBER,
        te.SERVICE_REQUIRE = TEMP.SERVICE_REQUIRE,
        te.GOODS_AMOUNT_OCR_STATE = TEMP.GOODS_AMOUNT_OCR_STATE
        WHERE te.BO_TRANS_TASK_EXTRA_ID=TEMP.PRIMARY_KEY;
        CNT := CNT + 1;
        IF (MOD(CNT, 1000) = 0) THEN
        COMMIT;
        END IF;
        END LOOP;
        COMMIT;
        END;
    </insert>

    <insert id="syncTaskNodeId">
        DECLARE
        CNT NUMBER(10);
        BEGIN
        CNT := 0;
        FOR TEMP IN (
        select *
        from (select nr.bo_trans_task_id,
        nr.node_id,
        nr.created_time,
        ROW_NUMBER() over(partition by nr.bo_trans_task_id order by nr.node_id desc) n
        from T_BO_TRANS_NODE_RECORD nr
        where nr.org_id = #{orgId}
        and nr.note = '中部陆港演示大屏要初始化数据') n
        where n.n = 1
        )
        LOOP
        update T_BO_TRANS_TASK t set t.node_id = TEMP.node_id,t.node_time=TEMP.created_time,t.last_modified_time = sysdate where t.bo_trans_task_id = TEMP.bo_trans_task_id;

        CNT := CNT + 1;
        IF (MOD(CNT, 1000) = 0) THEN
        COMMIT;
        END IF;
        END LOOP;
        COMMIT;
        END;
    </insert>

    <insert id="syncTaskDriverLoginState">
        DECLARE
        CNT NUMBER(10);
        BEGIN
        CNT := 0;
        FOR TEMP IN (
        SELECT T.BO_TRANS_TASK_ID  FROM T_BO_TRANS_TASK T WHERE T.IS_DEL =0 AND T.SETTLE_MODE =1 AND T.DRIVER_ID_CARD =#{driverIdCard} AND T.DRIVER_NAME =#{driverName}
        AND T.WAYBILL_BELONG_ACT_SYS IN
        <foreach collection="belongActSysList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND (T.PAY_STATE =0 OR T.CREATED_TIME>SYSDATE-#{maxDays})
        AND NOT EXISTS(SELECT 1 FROM T_BO_TASK_CUS_FIELD CUS WHERE CUS.BO_TRANS_TASK_ID =T.BO_TRANS_TASK_ID AND CUS.IS_DEL =0 AND CUS.FIELD_KEY='driverLoginState' AND CUS.FIELD_VALUE=#{loginState})
        )
        LOOP
        MERGE INTO T_BO_TASK_CUS_FIELD A
        USING (
        SELECT TEMP.BO_TRANS_TASK_ID BO_TRANS_TASK_ID, 'driverLoginState' FIELD_KEY, '税务临时登记状态' FIELD_NAME,#{loginState} FIELD_VALUE,'2' CATEGORY_TYPE FROM DUAL
        ) B
        ON (A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID AND A.FIELD_KEY=B.FIELD_KEY AND IS_DEL=0)
        WHEN MATCHED THEN
        UPDATE SET
        A.FIELD_VALUE = B.FIELD_VALUE,
        A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
        INSERT
        (BO_TASK_CUS_FIELD_ID, BO_TRANS_TASK_ID, FIELD_KEY, FIELD_NAME, FIELD_VALUE, CATEGORY_TYPE)
        VALUES('1' || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********)), 9, 0) || LPAD(ABS(MOD(DBMS_RANDOM.RANDOM, *********0)), 10, 0), B.BO_TRANS_TASK_ID, B.FIELD_KEY, B.FIELD_NAME, B.FIELD_VALUE, B.CATEGORY_TYPE);
        CNT := CNT + 1;
        IF (MOD(CNT, 1000) = 0) THEN
        COMMIT;
        END IF;
        END LOOP;
        COMMIT;
        END;
    </insert>
</mapper>
