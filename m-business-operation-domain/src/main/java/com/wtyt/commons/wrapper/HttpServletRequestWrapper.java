package com.wtyt.commons.wrapper;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.wtyt.logger.commons.toolkits.GsonToolkit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.*;

/**
 * <AUTHOR>
 * @date 2023年10月31日 20时33分
 */
@Slf4j
public class HttpServletRequestWrapper extends javax.servlet.http.HttpServletRequestWrapper {

    private final byte[] body;
    private final String charset;

    private static String ORG_ID = "orgId";
    private static String DATA = "data";
    private static String OPERATE_ORG_ID = "operateOrgId";

    public HttpServletRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        this.body = readBytes(request.getInputStream());
        this.charset = request.getCharacterEncoding();
    }

    private byte[] readBytes(InputStream inputStream) throws IOException {
        int buffSize = 4096;
        try (BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
             ByteArrayOutputStream out = new ByteArrayOutputStream(buffSize)) {
            byte[] temp = new byte[buffSize];
            int size;
            while ((size = bufferedInputStream.read(temp)) != -1) {
                out.write(temp, 0, size);
            }
            byte[] bytes = out.toByteArray();
            return this.changeBody(bytes);
        }
    }

    @Override
    public ServletInputStream getInputStream() {
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(this.body);
        return new ServletInputStream() {

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }

            @Override
            public boolean isFinished() {
                return byteArrayInputStream.available() > 0;
            }

            @Override
            public int read() {
                return byteArrayInputStream.read();
            }
        };
    }

    @Override
    public BufferedReader getReader() {
        try {
            if (StringUtils.isNotBlank(this.charset)) {
                return new BufferedReader(new InputStreamReader(this.getInputStream(), this.charset));
            }
        } catch (Exception e) {
            String errorMsg = e.getLocalizedMessage();
            log.error("HttpServletRequestWrapper getReader异常:{}", errorMsg, e);
        }
        return new BufferedReader(new InputStreamReader(this.getInputStream()));
    }

    private byte[] changeBody(byte[] body){
        try {
            if(body==null || body.length<1){
                return body;
            }
            String bodyString = null;
            if(StringUtils.isNotBlank(this.charset)){
                bodyString = new String(body, this.charset);
            }else {
                bodyString = new String(body);
            }
            JsonObject bodyJson = GsonToolkit.toJsonObject(bodyString);
            if(bodyJson == null || !bodyJson.has(DATA)){
                return body;
            }
            JsonObject dataJson = bodyJson.getAsJsonObject(DATA);
            if(dataJson!=null){
                JsonElement jsonElement = dataJson.get(OPERATE_ORG_ID);
                if(jsonElement!=null){
                    String operateOrgId = jsonElement.getAsString();
                    if(StringUtils.isNotBlank(operateOrgId)){
                        dataJson.addProperty(ORG_ID,operateOrgId);
                        byte[] newBody = null;
                        if(StringUtils.isNotBlank(this.charset)){
                            newBody = bodyJson.toString().getBytes(this.charset);
                        }else {
                            newBody = bodyJson.toString().getBytes();
                        }
                        return newBody;
                    }
                }
            }
        } catch (Exception e) {
        }

        return body;
    }
}
