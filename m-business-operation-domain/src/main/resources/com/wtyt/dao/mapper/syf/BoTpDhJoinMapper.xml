<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhJoinMapper">

    <select id="selectTpDhHeaderConfigInfoList" resultType="com.wtyt.bo.bean.TpDhHeaderConfigInfoBean">
        SELECT
            I.BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
            I.BO_TP_DH_CONFIG_ID boTpDhConfigId,
            I.HEADER_NAME headerName,
            I.SAMPLE_DATA sampleData,
            I.IS_REQUIRED isRequired,
            I.SHOW_TYPE showType,
            I.SEARCH_TYPE headerSearchType,
            I.IS_PRECISE headerIsPrecise,
            I.IS_EXPORT isExport,
            I.IS_INPUT isInput,
            I.IS_UNIQUE isUnique,
            I.DEFAULT_VALUE headerDefaultValue,
            I.SORT_NUM sortNum,
            I.FROM_SOURCE fromSource,
            I.MERGE_SHOW_ID mergeShowId,
            I.HEADER_TYPE headerType,
            C.BO_TP_DH_HEADER_COLUMN_ID boTpDhHeaderColumnId,
            C.BO_TP_DH_TASK_FIELD_DICT_ID boTpDhTaskFieldDictId,
            C.DEFAULT_VALUE mapperDefaultValue,
            C.SEARCH_TYPE mapperSearchType,
            C.IS_PRECISE mapperIsPrecise,
            C.BO_TP_DH_TASK_FIELD_RULE_ID boTpDhTaskFieldRuleId,
            C.UNIT unit,
            D.FIELD_NAME fieldName,
            D.FIELD_NAME_DESC fieldNameDesc,
            D.FIELD_TYPE fieldType,
            D.MAX_LENGTH maxLength,
            D.INPUT_TYPE inputType,
            D.NUMBER_PRECISION numberPrecision,
            D.UNIT_CONFIG unitConfig,
            D.ALLOW_NOT_REQUIRED allowNotRequired,
            D.ALLOW_DEFAULT allowDefault
        FROM
            T_BO_TP_DH_HEADER_IMPORT I
        LEFT JOIN T_BO_TP_DH_HEADER_COLUMN C ON
            C.BO_TP_DH_HEADER_IMPORT_ID = I.BO_TP_DH_HEADER_IMPORT_ID
            AND C.IS_DEL = 0
        LEFT JOIN T_BO_TP_DH_TASK_FIELD_DICT D ON
            D.BO_TP_DH_TASK_FIELD_DICT_ID = C.BO_TP_DH_TASK_FIELD_DICT_ID
            AND D.IS_DEL = 0
        WHERE
            I.IS_DEL = 0 and  I.HEADER_TYPE=1
            AND I.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
            ORDER BY I.SORT_NUM ,c.BO_TP_DH_HEADER_COLUMN_ID
    </select>
</mapper>
