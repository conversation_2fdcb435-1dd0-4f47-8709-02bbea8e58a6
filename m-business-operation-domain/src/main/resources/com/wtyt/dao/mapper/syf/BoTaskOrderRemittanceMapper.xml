<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskOrderRemittanceMapper">

    <insert id="insertTaskOrderRemittance" parameterType="BoTaskOrderRemittanceBean">
        INSERT INTO T_BO_TASK_ORDER_REMITTANCE (
            BO_TASK_ORDER_REMITTANCE_ID,
            PAY_ORDER_NO,
            ORDER_NO,
            TYPE,
            BUSINESS_TYPE,
            BUSINESS_ID,
            CHANNEL,
            APPLY_TIME,
            REMITTANCE_TIME,
            AMOUNT,
            REASON,
            STATUS,
            RESULT
        ) VALUES (
            #{boTaskOrderRemittanceId},
            #{payOrderNo},
            #{orderNo},
            #{type},
            #{businessType},
            #{businessId},
            #{channel},
            TO_DATE(#{applyTime}, 'yyyy-mm-dd hh24:mi:ss'),
            TO_DATE(#{remittanceTime}, 'yyyy-mm-dd hh24:mi:ss'),
            #{amount},
            #{reason},
            <choose>
                <when test="status != null and status.length > 0">
                    #{status},
                </when>
                <otherwise>-1,</otherwise>
            </choose>
            #{result}
        )
    </insert>

    <select id="getTaskOrderRemittance" resultType="BoTaskOrderRemittanceBean">
        SELECT
            T.BO_TASK_ORDER_REMITTANCE_ID boTaskOrderRemittanceId,
            T.PAY_ORDER_NO payOrderNo,
            T.ORDER_NO orderNo,
            T.TYPE type,
            T.BUSINESS_TYPE businessType,
            T.BUSINESS_ID businessId,
            T.CHANNEL channel,
            TO_CHAR(T.APPLY_TIME, 'yyyy-mm-dd hh24:mi:ss') applyTime,
            TO_CHAR(T.REMITTANCE_TIME, 'yyyy-mm-dd hh24:mi:ss') remittanceTime,
            T.AMOUNT amount,
            T.REASON reason,
            T.STATUS status,
            T.RESULT result
        FROM T_BO_TASK_ORDER_REMITTANCE T
        WHERE T.IS_DEL = 0
        AND T.BO_TASK_ORDER_REMITTANCE_ID = #{boTaskOrderRemittanceId}
    </select>

    <select id="getTaskOrderRemittanceByBusiness" resultType="BoTaskOrderRemittanceBean">
        SELECT
            T.BO_TASK_ORDER_REMITTANCE_ID boTaskOrderRemittanceId,
            T.PAY_ORDER_NO payOrderNo,
            T.ORDER_NO orderNo,
            T.TYPE type,
            T.BUSINESS_TYPE businessType,
            T.BUSINESS_ID businessId,
            T.CHANNEL channel,
            TO_CHAR(T.APPLY_TIME, 'yyyy-mm-dd hh24:mi:ss') applyTime,
            TO_CHAR(T.REMITTANCE_TIME, 'yyyy-mm-dd hh24:mi:ss') remittanceTime,
            T.AMOUNT amount,
            T.REASON reason,
            T.STATUS status,
            T.RESULT result
        FROM T_BO_TASK_ORDER_REMITTANCE T
        WHERE T.IS_DEL = 0
        AND T.BUSINESS_ID = #{businessId}
        AND T.BUSINESS_TYPE = #{businessType}
        AND T.TYPE = #{type}
    </select>

    <update id="updateTaskOrderRemittanceSuccess">
        UPDATE T_BO_TASK_ORDER_REMITTANCE T
        SET T.STATUS = 0,
            T.RESULT = #{result},
            T.REMITTANCE_TIME = TO_DATE(#{remittanceTime}, 'yyyy-mm-dd hh24:mi:ss'),
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.STATUS IN (-1, 1)
        AND T.BO_TASK_ORDER_REMITTANCE_ID = #{boTaskOrderRemittanceId}
    </update>

    <update id="updateTaskOrderRemittanceFailure">
        UPDATE T_BO_TASK_ORDER_REMITTANCE T
        SET T.STATUS = 1,
            T.RESULT = #{result},
            T.REMITTANCE_TIME = TO_DATE(#{remittanceTime}, 'yyyy-mm-dd hh24:mi:ss'),
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.STATUS = -1
        AND T.BO_TASK_ORDER_REMITTANCE_ID = #{boTaskOrderRemittanceId}
    </update>

    <select id="getTaskOrderRemittanceByOrderNo" resultType="BoTaskOrderRemittanceBean">
        SELECT
            T.BO_TASK_ORDER_REMITTANCE_ID boTaskOrderRemittanceId,
            T.PAY_ORDER_NO payOrderNo,
            T.ORDER_NO orderNo,
            T.TYPE type,
            T.BUSINESS_TYPE businessType,
            T.BUSINESS_ID businessId,
            T.CHANNEL channel,
            TO_CHAR(T.APPLY_TIME, 'yyyy-mm-dd hh24:mi:ss') applyTime,
            TO_CHAR(T.REMITTANCE_TIME, 'yyyy-mm-dd hh24:mi:ss') remittanceTime,
            T.AMOUNT amount,
            T.REASON reason,
            T.STATUS status,
            T.RESULT result
        FROM T_BO_TASK_ORDER_REMITTANCE T
        WHERE T.IS_DEL = 0
        AND T.ORDER_NO = #{orderNo}
    </select>

    <select id="getTaskOrderRemittanceByPayOrderNo" resultType="BoTaskOrderRemittanceBean">
        SELECT
            T.BO_TASK_ORDER_REMITTANCE_ID boTaskOrderRemittanceId,
            T.PAY_ORDER_NO payOrderNo,
            T.ORDER_NO orderNo,
            T.TYPE type,
            T.BUSINESS_TYPE businessType,
            T.BUSINESS_ID businessId,
            T.CHANNEL channel,
            TO_CHAR(T.APPLY_TIME, 'yyyy-mm-dd hh24:mi:ss') applyTime,
            TO_CHAR(T.REMITTANCE_TIME, 'yyyy-mm-dd hh24:mi:ss') remittanceTime,
            T.AMOUNT amount,
            T.REASON reason,
            T.STATUS status,
            T.RESULT result
        FROM T_BO_TASK_ORDER_REMITTANCE T
        WHERE T.IS_DEL = 0
        AND T.PAY_ORDER_NO = #{payOrderNo}
    </select>

</mapper>