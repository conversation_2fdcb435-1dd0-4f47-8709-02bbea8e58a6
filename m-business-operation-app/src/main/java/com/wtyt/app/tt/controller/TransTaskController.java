package com.wtyt.app.tt.controller;

import com.wtyt.app.bo.bean.RespWaybillInfo;
import com.wtyt.app.commons.interceptor.ThirdInterface;
import com.wtyt.bean.BaseQueryBean;
import com.wtyt.bo.bean.request.*;
import com.wtyt.bo.bean.response.*;
import com.wtyt.commons.featureprobe.FeatureProbeUtil;
import com.wtyt.bo.service.BoYCService;
import com.wtyt.bo.service.BusinessOperationService;
import com.wtyt.common.beans.CommonTipsBean;
import com.wtyt.common.beans.DownloadFileResultBean;
import com.wtyt.common.enums.ElectronicReceiptStatusEnum;
import com.wtyt.common.enums.NodeStatusEnum;
import com.wtyt.common.rpc.bean.Rpc1705006OBean;
import com.wtyt.common.rpc.bean.Rpc1705086OBean;
import com.wtyt.common.toolkits.CommonToolkit;
import com.wtyt.common.toolkits.StringToolkit;
import com.wtyt.common.toolkits.VerifyToolkit;
import com.wtyt.commons.annotation.DistributedLock;
import com.wtyt.commons.annotation.Logger;
import com.wtyt.commons.bean.BaseValidBean;
import com.wtyt.commons.bean.TaskReqCommonBean;
import com.wtyt.dao.bean.syf.BoDriverInfoBean;
import com.wtyt.dao.bean.syf.BoDriverInfoDetailBean;
import com.wtyt.dao.bean.syf.Req1735204Bean;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.money.commons.bean.BaseBean;
import com.wtyt.money.commons.bean.ResDataBean;
import com.wtyt.tt.bean.*;
import com.wtyt.tt.service.PaperReceiptService;
import com.wtyt.tt.service.TransTaskMixService;
import com.wtyt.tt.service.TransTaskService;
import com.wtyt.ttf.service.TransTaskChangeService;
import com.wtyt.util.validate.HibernateValidate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.groups.Default;
import java.util.List;
import java.util.Objects;

/**
 *
 * @ClassName: TransTaskController
 * @Description: 运输任务控制器
 * <AUTHOR>
 * @date 2022年11月23日
 *
 */
@RestController
@RequestMapping(value ="/bo/transTask/")
public class TransTaskController {
	@Autowired
	private BusinessOperationService businessOperationService;

	@Autowired
	private BoYCService boYCService;

	@Autowired
	private TransTaskChangeService transTaskChangeService;

	@Autowired
	private TransTaskService transTaskService;
	@Autowired
	private TransTaskMixService transTaskMixService;
	@Autowired
	private PaperReceiptService paperReceiptService;

	@Logger("5329053-获取新的运输任务id接口")
	@PostMapping("queryTransportId")
	public ResDataBean<RespWaybillInfo> generateWaybillId() throws Exception {
		RespWaybillInfo respWaybillInfo = new RespWaybillInfo();
		respWaybillInfo.setBoTransTaskId(UidToolkit.generateUidString());
		return new ResDataBean<RespWaybillInfo>().success(respWaybillInfo);
	}

	/**
	 *
	 * @Title: getTaxwaybillPosInfoList
	 * <AUTHOR>
	 * @Description: 1735215-查询运输任务支付状态详情
	 * @return
	 * @return ResDataBean<Resp1735215Bean> 返回类型
	 * @throws
	 */
	@PostMapping("getTaxwaybillPosInfoList")
	@Logger("5329215-查询运输任务支付状态详情接口")
	public ResDataBean<List<Resp1735215Bean>> getTaxwaybillPosInfoList(@RequestBody BaseBean<Req1735215Bean> req)throws Exception{
		List<Resp1735215Bean> result=businessOperationService.getWaybillPostInfo(req.getData());
		return new ResDataBean().success(result);
	}

	/**
	 * 5329212-任务-历史运输任务模板查询接口
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "getHistoryTemplate")
	@ResponseBody
	@Logger("5329212-任务-历史运输任务模板查询接口")
	public ResDataBean<Resp5329212OBean> getHistoryTemplate(@RequestBody BaseBean<Req5329212Bean> bean) throws Exception {
		return new ResDataBean<Resp5329212OBean>().success(transTaskService.getHistoryTemplate(bean.getData()));
	}

	/**
	 * 5329205-任务-业务运作司机查询承运信息接口（模糊搜索）
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "getDriverInfo")
	@ResponseBody
	@Logger("5329205-任务-业务运作司机查询承运信息接口（模糊搜索）")
	public ResDataBean<BoDriverInfoBean> getDriverInfo(@RequestBody BaseBean<Req1735205Bean> bean) throws Exception {
		return new ResDataBean<BoDriverInfoBean>().success(businessOperationService.getDriverInfo(bean.getData()));
	}

    /**
     * 页面新增运输任务接口
     *
     * @param iBean
     * @return
     * @throws Exception
     */
    @PostMapping(value = "createdTransportBo")
	@Logger("5329200-任务-新增业务运作运输任务接口")
    @ResponseBody
    public ResDataBean<Resp1735200Bean> createdTransportBo(@RequestBody BaseBean<Req1735200Bean> iBean) throws Exception {
        return new ResDataBean<Resp1735200Bean>().success(transTaskService.createdTask(iBean.getData()));
    }

    /**
     * 页面修改运输任务接口
     *
     * @param iBean
     * @return
     * @throws Exception
     */
    @PostMapping(value = "updateTransportBo")
	@Logger("5329201-任务-修改运业务运作输任务接口")
    @ResponseBody
    public ResDataBean<Resp1735201Bean> updateTransportBo(@RequestBody BaseBean<Req1735201Bean> iBean) throws Exception {
        return new ResDataBean<Resp1735201Bean>().success(transTaskService.updateTask(iBean.getData()));
    }

	/**
	 * @return com.wtyt.money.commons.bean.ResDataBean<com.wtyt.dao.bean.syf.BoDriverInfoBean>
	 * <AUTHOR>
	 * @Description 1735204-业务运作司机关键信息查询接口
	 * @Date 11:49 2022/11/24
	 * @Param [bean]
	 **/
	@PostMapping(value = "getDriverInfoDetail")
	@ResponseBody
	@Logger("5329204-业务运作司机关键信息查询")
	public ResDataBean<BoDriverInfoDetailBean> getDriverInfoDetail(@RequestBody BaseBean<Req1735204Bean> bean) throws Exception {
		return new ResDataBean<BoDriverInfoDetailBean>().success(businessOperationService.getDriverInfoDetail(bean.getData()));
	}

	/**
	 * 运输任务管理tab汇总接口
	 * @param iBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "queryTransTaskTabInfo")
	@Logger("5329210-任务-运输任务管理tab汇总接口")
    public ResDataBean<List<Resp1735210Bean>> queryTransTaskTabInfo(@RequestBody BaseBean<Req1735210Bean> iBean) throws Exception {
		Req1735210Bean bean = CommonToolkit.checkReq(iBean);
		HibernateValidate.validateWithException(bean, Default.class);
		return new ResDataBean<List<Resp1735210Bean>>().success(transTaskService.queryTransTaskTabInfo(iBean.getData()));
    }

    /**
     * 批量删除运单
     * @param iBean
     * @return
     * @throws Exception
     */
    @PostMapping(value = "batchDelWaybill")
	@Logger("5329202-任务-业务运作批量删除运输任务接口")
    public ResDataBean batchDelWaybill(@RequestBody BaseBean<Req1735202Bean> iBean) throws Exception {
        transTaskService.batchDelWaybill(iBean.getData());
        return new ResDataBean<List<Resp1735210Bean>>().success();
    }

	/**
	 * 5329216-查询运输任务详情接口
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "queryTaskDetail")
	@ResponseBody
	@Logger("5329216-查询运输任务详情接口")
	public ResDataBean<Resp1735216Bean> queryTaskDetail(@RequestBody BaseBean<Req1735216Bean> bean) throws Exception {
		return new ResDataBean<Resp1735216Bean>().success(transTaskService.queryTaskDetail(bean.getData()));
	}

	/**
	 * 5329219-取消派车接口
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "cancelDispatch")
	@ResponseBody
	@Logger("5329219-取消派车接口")
	public ResDataBean<?> cancelDispatch(@RequestBody @Valid BaseValidBean<Req1735219Bean> bean) throws Exception{
		this.transTaskService.cancelDispatch(bean.getData());
		return new ResDataBean<>().success();
	}

	/**
	 *
	 * @Title: sendNodeMsg
	 * <AUTHOR>
	 * @Description: 发送节点消息接口
	 * @param request
	 * @return
	 * @throws Exception
	 * @return ResDataBean 返回类型
	 * @throws
	 */
	@PostMapping("sendNodeMsg")
	@Logger("5329220-业务运作消息发送接口")
	public ResDataBean sendNodeMsg(@RequestBody BaseBean<Req1735220Bean> request)throws Exception {
		transTaskService.sendNodeMsg(request.getData());
		return new ResDataBean().success();
	}

	/**
	 * 获取货物清单
	 * @param bean
	 * @return
	 */
	@PostMapping("getGoodsList")
	@Logger("5329111-计划-查询清单信息")
	public ResDataBean<Resp1735111Bean> getGoodsList(@RequestBody @Valid BaseBean<Req1735111Bean> bean) throws Exception {
		return new ResDataBean<Resp1735111Bean>().success(businessOperationService.getGoodsList(bean.getData()));
	}

	/**
	 * @return com.wtyt.money.commons.bean.ResDataBean<com.wtyt.bo.bean.response.Resp1735111Bean>
	 * <AUTHOR>
	 * @Description 运输任务待认领列表
	 * @Date 09:15 2023/3/22
	 * @Param [bean]
	 **/
	@PostMapping("waitClaimList")
	@Logger("5329400-运输任务待认领列表")
	public ResDataBean<Rpc1705006OBean> waitClaimList(@RequestBody @Valid BaseBean<Rpc5329400IBean> bean) throws Exception {
		return new ResDataBean<Rpc1705006OBean>().success(boYCService.queryWaitClaimList(bean.getData()));
	}

	@PostMapping("waitDispatchList")
	@Logger("5329401-运输任务待分配列表")
	public ResDataBean<Rpc1705086OBean> waitDispatchList(@RequestBody @Valid BaseBean<Rpc5329400IBean> bean) throws Exception {
		return new ResDataBean<Rpc1705086OBean>().success(boYCService.queryWaitDispatchList(bean.getData()));
	}

	/**
	 * 新大陆app
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@PostMapping("queryTransportTaskPageAppV2")
	@Logger("5329458-业务运作运输任务列表查询接口（APP）")
	public ResDataBean<Resp1735209NewBean> queryTransportTaskPageAppV2(@RequestBody BaseBean<Req1735209Bean> request)throws Exception {
		return new ResDataBean<Resp1735209NewBean>().success(transTaskService.queryTransportTaskPageAppV2(request.getData()));
	}

	/**
	 * (提醒司机接单)推送好运宝消息或发送短息
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@PostMapping("remindDriver")
	@Logger("5329411-(提醒司机接单)推送好运宝消息或发送短息")
	public ResDataBean<Void> remindDriver(@RequestBody BaseBean<Req5329411Bean> request)throws Exception {
		businessOperationService.remindDriver(request.getData());
		return new ResDataBean<Void>().success();
	}

	@PostMapping("fiveCardsInfo")
	@Logger("5329412-获取五证组件所需数据（APP）")
	public ResDataBean<Resp5329412Bean> fiveCardsInfo(@RequestBody BaseBean<Req5329412Bean> request)throws Exception {
		return new ResDataBean<Resp5329412Bean>().success(transTaskService.queryFiveCardsInfo(request.getData()));
	}

	@PostMapping("transVoucherAck")
	@Logger("5329422-任务-运输凭证确认/审核")
	public ResDataBean<Void> transVoucherAck(@RequestBody BaseBean<Req5329422Bean> request)throws Exception {
		businessOperationService.transVoucherAck(request.getData());
		return new ResDataBean<Void>().success();
	}

	@PostMapping("showApplyStatus")
	@Logger("5329425-任务-是否显示当前条件列（审批状态、纸质回单状态、电子回单状态）")
	public ResDataBean<Resp5329425Bean> showApplyStatus(@RequestBody BaseBean<Req5329425Bean> request)throws Exception {
		return new ResDataBean<Resp5329425Bean>().success(businessOperationService.showApplyStatus(request.getData()));
	}

	/**
	 * 运输任务管理-查看运费变动
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@PostMapping("queryTransFeeChangeData")
	@Logger("5329416-任务-查看运费变动")
	public ResDataBean<Resp5329416Bean> queryTransFeeChangeData(@RequestBody BaseBean<Req5329416Bean> request)throws Exception {
		if (request.getData() == null || StringUtils.isEmpty(request.getData().getBoTransTaskId())) {
			return new ResDataBean<Resp5329416Bean>().tipFail("boTransTaskId参数不能为空", null);
		}
		return new ResDataBean<Resp5329416Bean>().success(transTaskChangeService.queryTransFeeChangeData(request.getData()));
	}

	@PostMapping("requiredConfig")
	@Logger("5329426-配置项接口")
	public ResDataBean<RespRequiredConfigBean> requiredConfig(@RequestBody BaseBean<ReqRequiredConfigBean> request)throws Exception {

		return new ResDataBean<RespRequiredConfigBean>().success(businessOperationService.requiredConfig(request.getData()));
	}

	@PostMapping("evaluate")
	@Logger("5329429-运输任务评价")
	public ResDataBean<Void> evaluate(@RequestBody BaseBean<ReqEvaluateBean> request)throws Exception {
		businessOperationService.evaluate(request.getData());
		return new ResDataBean<Void>().success();
	}

	/**
	 * 确认收到纸质回单
	 * <AUTHOR>
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@PostMapping("updateReceivePaperReceipt")
	@Logger("5329427-确认收到纸质回单接口")
	public ResDataBean<Void> updateReceivePaperReceipt(@RequestBody BaseBean<Req5329427Bean> request)throws Exception {
		String error = HibernateValidate.fastValidate(request.getData());
		if (error != null) {
			throw new UnifiedBusinessException(error);
		}
		paperReceiptService.updateReceivePaperReceipt(request.getData());
		return new ResDataBean<Void>().success();
	}

	/**
	 * 电子回单审核
	 * <AUTHOR>
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@PostMapping("updateElectronicReceiptAudit")
	@Logger("5329434-电子回单审核")
	public ResDataBean<Void> updateElectronicReceiptAudit(@RequestBody BaseBean<Req5329434Bean> request)throws Exception {
		String error = HibernateValidate.fastValidate(request.getData());
		if (error != null) {
			throw new UnifiedBusinessException(error);
		}
		String electronicReceiptStatus = request.getData().getElectronicReceiptStatus();
		if (!ElectronicReceiptStatusEnum.FOUR.getCode().equals(electronicReceiptStatus) && !ElectronicReceiptStatusEnum.THREE.getCode().equals(electronicReceiptStatus)) {
			throw new UnifiedBusinessException("electronicReceiptStatus参数非法");
		}
		transTaskService.updateElectronicReceiptAudit(request.getData());
		return new ResDataBean<Void>().success();
	}

	/**
	 * 查询回单的邮寄进度信息
	 * <AUTHOR>
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@PostMapping("queryDeliveryStatusInfo")
	@Logger("5329428-查询回单的邮寄进度信息")
	public ResDataBean<Resp5329428Bean> queryDeliveryStatusInfo(@RequestBody BaseBean<Req5329428Bean> request) throws Exception {
		String error = HibernateValidate.fastValidate(request.getData());
		if (error != null) {
			throw new UnifiedBusinessException(error);
		}
		return new ResDataBean<Resp5329428Bean>().success(paperReceiptService.queryDeliveryStatusInfo(request.getData()));
	}

	/**
	 * 5329452-运输任务-运作过程详情
	 * <AUTHOR>
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@PostMapping("queryYzgcDetailInfo")
	@Logger("5329452-运输任务-运作过程详情")
	public ResDataBean<Resp5329452Bean> queryYzgcDetailInfo(@RequestBody BaseBean<Req5329452Bean> request) throws Exception {
		if (Objects.isNull(request.getData())) {
			throw new UnifiedBusinessException("请求数据不能为空");
		}
		String error = HibernateValidate.fastValidate(request.getData());
		if (error != null) {
			throw new UnifiedBusinessException(error);
		}
		return new ResDataBean<Resp5329452Bean>().success(transTaskService.queryYzgcDetailInfo(request.getData()));
	}

	/**
	 * 项目运作看板-运输任务详情列表（点击线路弹框触发）
	 * <AUTHOR>
	 * @param queryBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping("queryTaskPageForDashboard")
	@Logger("5329455")
	public ResDataBean<Resp5329455Bean> queryTaskPageForDashboard(@RequestBody BaseBean<Req5329455Bean> queryBean) throws Exception {
		// 校验
		if (queryBean == null || queryBean.getData() == null) {
			throw new UnifiedBusinessException("请求参数非法");
		}
		String errorMsg = HibernateValidate.fastValidate(queryBean.getData());
		if (!StringToolkit.isEmpty(errorMsg)) {
			throw new UnifiedBusinessException(errorMsg);
		}
		if (!StringToolkit.isEmpty(queryBean.getData().getStatus())) {
			NodeStatusEnum nodeStatusEnum = NodeStatusEnum.queryNodeEventNameEnumByType(queryBean.getData().getStatus());
			if (nodeStatusEnum == null) {
				throw new UnifiedBusinessException("状态非法");
			}
		}
		return new ResDataBean<Resp5329455Bean>().success(transTaskService.queryTaskPageForDashboard(queryBean.getData()));
	}

	/**
	 * 项目运作看板-运输任务详情列表-导出文件
	 * <AUTHOR>
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "downloadPlanForDashboard")
	@ResponseBody
	@Logger("5329453-项目运作看板-运输任务详情列表一键导出")
	public ResDataBean<DownloadFileResultBean> downloadPlanForDashboard(@RequestBody BaseBean<Req5329455Bean> bean) throws Exception {
		if (bean == null || bean.getData() == null) {
			throw new UnifiedBusinessException("请求参数非法");
		}
		if (StringUtils.isEmpty(bean.getData().getGroupId())) {
			throw new UnifiedBusinessException("组织ID不能为空");
		}
		if (StringUtils.isEmpty(bean.getData().getBoBusinessProjectId())) {
			throw new UnifiedBusinessException("业务项目ID不能为空");
		}

		if (!StringToolkit.isEmpty(bean.getData().getStatus())) {
			NodeStatusEnum nodeStatusEnum = NodeStatusEnum.queryNodeEventNameEnumByType(bean.getData().getStatus());
			if (nodeStatusEnum == null) {
				throw new UnifiedBusinessException("状态非法");
			}
		}
		return new ResDataBean<DownloadFileResultBean>().success(transTaskService.downloadPlanForDzDashboard(bean.getData()));
	}

	@PostMapping(value = "copyPic")
	@ResponseBody
	@Logger("5329213-任务-派车成功一键复制接口")
	public ResDataBean<CopyPicOBean> copyPic(@RequestBody BaseBean<CopyPicIBean> bean) throws Exception {
		boolean enableShareToTingJie = this.isEnableShareToTingJie(bean.getData().getOrgId());
		if (enableShareToTingJie){
			return new ResDataBean<CopyPicOBean>().success(transTaskMixService.copyPic(bean.getData()));
		}
		return new ResDataBean<CopyPicOBean>().success(transTaskService.copyPic(bean.getData()));
	}

	/**
	 * 5329217-查询结算模式
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "getSettleMode")
	@ResponseBody
	@Logger("5329217-查询结算模式")
	public ResDataBean<Resp5329217Bean> getSettleMode(@RequestBody BaseBean<Req5329217Bean> bean) throws Exception {
		return new ResDataBean<Resp5329217Bean>().success(this.transTaskService.getSettleMode(bean.getData()));
	}

	/**
	 * 5329218-获取字段显示状态
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "getFieldDisplayStatus")
	@ResponseBody
	@Logger("5329218-获取字段显示状态（20230913后可直接占用/废弃该接口）")
	public ResDataBean<Resp5329218Bean> getFieldDisplayStatus(@RequestBody BaseBean<Req5329218Bean> bean) throws Exception {
		return new ResDataBean<Resp5329218Bean>().success(this.transTaskService.getFieldDisplayStatus(bean.getData()));
	}

	/**
	 * 5329469-运输任务-计算费用
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "calculateCost")
	@Logger("5329469-运输任务-自动计算运费")
	public ResDataBean<Resp5329469OBean> calculateCost(@RequestBody BaseBean<Req5329469IBean> bean) throws Exception {
		return new ResDataBean<Resp5329469OBean>().success(this.transTaskService.calculateCost(bean.getData()));
	}

	/**
	 * 5329467-运输任务-重新分发接口
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping("reDistribute")
	@Logger("5329467-运输任务-重新分发接口")
	public ResDataBean<Resp5329467Bean> reDistribute(@RequestBody BaseBean<Req5329467Bean> bean)throws Exception {
		return new ResDataBean<Resp5329467Bean>().success(transTaskService.reDistribute(bean.getData()));
	}

	/**
	 * 回单快递路由查询
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "getReceiptRoute")
	@ResponseBody
	@Logger("5329106-回单快递路由查询")
	public ResDataBean<BoTransReceiptOBean> getReceiptRoute(@RequestBody BaseBean<TaskReqCommonBean> bean) throws Exception {
		return new ResDataBean<BoTransReceiptOBean>().success(transTaskService.getReceiptRoute(bean.getData()));
	}

	/**
	 * 5329470-运输任务基本信息修改
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping("simpleUpdateTask")
	@Logger("5329470-运输任务基本信息修改")
	public ResDataBean<Void> updateTaskBasicInfo(@RequestBody BaseBean<Req5329470Bean> bean)throws Exception {
		transTaskService.updateTaskBasicInfo(bean.getData());
		return new ResDataBean<Void>().success();
	}

	/**
	 * 5329473-根据手机号码、车牌号查询路歌平台司机信息
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "queryDriverInfo")
	@ResponseBody
	@Logger("5329473-根据手机号码、车牌号查询路歌平台司机信息")
	public ResDataBean<Resp5329473Bean> queryDriverInfo(@RequestBody BaseBean<Req5329473IBean> bean) throws Exception {
		return new ResDataBean<Resp5329473Bean>().success(transTaskService.queryDriverInfo(bean.getData()));
	}


	/**
	 * 5329222-查询结算模式
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "querySettleMode")
	@ResponseBody
	@Logger("5329222-查询结算模式")
	public ResDataBean<Resp5329222Bean> querySettleMode(@RequestBody BaseBean<Req5329222Bean> bean) throws Exception {
		return new ResDataBean<Resp5329222Bean>().success(this.transTaskService.querySettleMode(bean.getData()));
	}

	/**
	 * 5329483-货损保障企业版（货损保障）--获取保障凭证
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "queryGoodsInsureCertificate")
	@ResponseBody
	@Logger("5329483-货损保障企业版（货损保障）--获取保障凭证")
	public ResDataBean<Resp5329483Bean> queryGoodsInsureCertificate(@RequestBody BaseBean<Req5329483Bean> bean) throws Exception {
		return new ResDataBean<Resp5329483Bean>().success(this.transTaskService.queryGoodsInsureCertificate(bean.getData()));
	}

	/**
	 * 运输任务-回单押金解冻/确认收到回单接口
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping("optFrozenBackFee")
	@Logger("5329484-运输任务-回单押金解冻/确认收到回单接口")
	public ResDataBean<Void> optFrozenBackFee(@RequestBody BaseBean<Req5329484Bean> bean)throws Exception {
		transTaskService.optFrozenBackFee(bean.getData());
		return new ResDataBean<Void>().success();
	}


	/**
	 * 查询回单冻结支付信息
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping("frozenBackFeePayInfo")
	@Logger("5329485-运输任务-查看回单冻结对应支付信息")
	public ResDataBean<Resp5329485Bean> frozenBackFeePayInfo(@RequestBody BaseBean<BaseQueryBean> bean)throws Exception {
		return new ResDataBean<Resp5329485Bean>().success(transTaskService.frozenBackFeePayInfo(bean.getData()));
	}

	@PostMapping("checkGoods")
	@Logger("5329494-校验货物数量是否超载")
	public ResDataBean<Resp5329494Bean> checkGoods(@RequestBody BaseBean<Req5329494Bean> baseBean)throws Exception {

		return new ResDataBean<Resp5329494Bean>().success(transTaskService.checkGoods(baseBean.getData()));
	}

	/**
	 * 5329497-运输任务基础详情
	 * @param baseBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping("getBasicsDetail")
	@Logger("5329497-运输任务基础详情")
	public ResDataBean<Resp5329497Bean> getTransTaskBasicsDetail(@RequestBody BaseBean<Req5329497Bean> baseBean)throws Exception {
		return new ResDataBean<Resp5329497Bean>().success(transTaskService.getTransTaskBasicsDetail(baseBean.getData()));
	}

	/**
	 * 5329498-运输任务终结
	 * @param baseBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping("over")
	@Logger("5329498-运输任务终结")
	@DistributedLock(resource = "boTransTaskId")
	public ResDataBean<?> transTaskOver(@RequestBody BaseBean<Req5329498Bean> baseBean)throws Exception {
		transTaskService.transTaskOver(baseBean.getData());
		return new ResDataBean<>().success();
	}

	/**
	 * 5329504-查运输任务导出状态
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "queryTransTaskExportStatus")
	@ResponseBody
	@Logger("5329504-查运输任务导出状态")
	public ResDataBean<Resp5329504Bean> queryTransTaskExportStatus(@RequestBody BaseBean<Req5329504Bean> bean) throws Exception {
		return transTaskService.queryTransTaskExportStatus(bean.getData());
	}

	/**
	 * 运输任务二级标签汇总
	 * @param baseBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "queryTaskSecondLabel")
	@Logger("5329501-任务-app运输任务管理二级tab汇总接口")
	public ResDataBean<Resp5329501Bean> queryTaskSecondLabel(@RequestBody BaseBean<Req1735209Bean> baseBean) throws Exception {
		return new ResDataBean<Resp5329501Bean>().success(transTaskService.queryTaskSecondLabel(baseBean.getData()));
	}

	/**
	 * 5329511-App筛选页条件选项统一查询
	 * @param baseBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "queryAppFilterCondition")
	@Logger("5329511-App筛选页条件选项统一查询")
	public ResDataBean<Resp5329511Bean> queryAppFilterCondition(@RequestBody BaseBean<Req5329511Bean> baseBean) throws Exception {
		return new ResDataBean<Resp5329511Bean>().success(transTaskService.queryAppFilterCondition(baseBean.getData()));
	}

	/**
	 * 三方接口新增运输任务接口
	 *
	 * @param iBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "interface/createdTransport")
	@Logger("5329900-新增业务运作运输任务接口(给三方接口)")
	@ResponseBody
	@ThirdInterface
	public ResDataBean<Resp1735200Bean> interfaceCreatedTransportBo(@RequestBody BaseBean<Req5329900Bean> iBean) throws Exception {
		Req5329900Bean data = iBean.getData();
		if(data==null){
			throw new UnifiedBusinessException("data参数不能为空");
		}
		if(StringUtils.isNotBlank(data.getThirdAid())){
			//TI组透传的三方aid
			data.setAid(data.getThirdAid());
		}else {
			//三方直接调用时的aid
			data.setAid(iBean.getAid());
		}
		return new ResDataBean<Resp1735200Bean>().success(transTaskService.interfaceCreatedTransportBo(data));
	}

	@PostMapping(value = "interface/createdTaskSupplierMode")
	@Logger("5329920-新增业务运作运输任务接口-上下游模式(给三方接口) ")
	@ResponseBody
	@ThirdInterface
	public ResDataBean<Resp1735200Bean> interfaceCreatedTaskSupplierMode(@RequestBody BaseBean<Req5329900Bean> iBean) throws Exception {
		Req5329900Bean data = iBean.getData();
		if(data==null){
			throw new UnifiedBusinessException("data参数不能为空");
		}
		if(StringUtils.isNotBlank(data.getThirdAid())){
			//TI组透传的三方aid
			data.setAid(data.getThirdAid());
		}else {
			//三方直接调用时的aid
			data.setAid(iBean.getAid());
		}
		return new ResDataBean<Resp1735200Bean>().success(transTaskService.interfaceCreatedTaskSupplierMode(data));
	}

	/**
	 * 5329921-新增业务运作运输任务接口-大宗线路模式(三方接口)
	 * @param iBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "interface/createdTaskDzLineMode")
	@Logger("5329921-新增业务运作运输任务接口-大宗线路模式(三方接口)")
	@ThirdInterface
	public ResDataBean<?> interfaceCreatedTaskDzLineMode(@RequestBody BaseBean<Req5329921Bean> iBean) throws Exception {
		Req5329921Bean data = iBean.getData();
		VerifyToolkit.verifyObjectEmpty(data, "data参数不能为空");
		if (StringUtils.isNotBlank(data.getThirdAid())) {
			// TI组透传的三方aid
			data.setAid(data.getThirdAid());
		} else {
			// 三方直接调用时的aid
			data.setAid(iBean.getAid());
		}
		Resp5329921Bean resp5329921Bean = this.transTaskService.interfaceCreatedTaskDzLineMode(data);
		return new ResDataBean<>().success(resp5329921Bean);
	}

	/**
	 * 5329922-运输任务-删除申请（三方接口）
	 * @param iBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "interface/delete/apply")
	@Logger("5329922-运输任务-删除申请（三方接口）")
	@ThirdInterface
	public ResDataBean<?> interfaceDeleteApply(@RequestBody BaseBean<Req5329922Bean> iBean) throws Exception {
		this.transTaskService.interfaceDeleteApply(iBean.getData());
		return new ResDataBean<>().success();
	}

	/**
	 * 5329923-运输任务-删除确认（三方接口）
	 * @param iBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "interface/delete/confirm")
	@Logger("5329923-运输任务-删除确认（三方接口）")
	@ThirdInterface
	public ResDataBean<?> interfaceDeleteConfirm(@RequestBody BaseBean<Req5329923Bean> iBean) throws Exception {
		this.transTaskService.interfaceDeleteConfirm(iBean.getData());
		return new ResDataBean<>().success();
	}

	/**
	 * 5329924-运输任务-审核状态变更（三方接口）
	 * @param iBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "interface/audit/status/change")
	@Logger("5329924-运输任务-审核状态变更（三方接口）")
	@ThirdInterface
	public ResDataBean<?> interfaceAuditStatusChange(@RequestBody BaseBean<Req5329924Bean> iBean) throws Exception {
		this.transTaskService.interfaceAuditStatusChange(iBean.getData());
		return new ResDataBean<>().success();
	}

	/**
	 * 三方接口修改运输任务接口
	 *
	 * @param iBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "interface/updateTransport")
	@Logger("5329901-修改运业务运作输任务接口(给三方接口)")
	@ResponseBody
	@ThirdInterface
	public ResDataBean<Resp1735201Bean> interfaceUpdateTransport(@RequestBody BaseBean<Req5329901Bean> iBean) throws Exception {
		return new ResDataBean<Resp1735201Bean>().success(transTaskService.interfaceUpdateTransport(iBean.getData()));
	}

	/**
	 * 查询运输任务详情（三方接口）
	 * @param iBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "interface/queryTaskDetail")
	@Logger("5329907-查询运输任务详情（三方接口）")
	@ResponseBody
	@ThirdInterface
	public ResDataBean<Resp5329907Bean> interfaceQueryTaskDetail(@RequestBody BaseBean<Req1735216Bean> iBean) throws Exception {
		return new ResDataBean<Resp5329907Bean>().success(transTaskService.interfaceQueryTaskDetail(iBean.getData()));
	}


	/**
	 * 三方接口删除运单
	 * @param iBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "interface/batchDelWaybill")
	@Logger("5329902-业务运作批量删除运输任务接口（三方接口）")
	@ThirdInterface
	public ResDataBean<?> interfaceBatchDelWaybill(@RequestBody BaseBean<Req5329902Bean> iBean) throws Exception {
		transTaskService.interfaceBatchDelWaybill(iBean.getData());
		return new ResDataBean().success();
	}

	/**
	 * 5329519-删除运输任务恢复（单笔）
	 *
	 * @param iBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "singleDelWaybill")
	@Logger("5329519-删除运输任务恢复（单笔）")
	public ResDataBean<?> singleDelWaybill(@RequestBody @Valid BaseValidBean<Req5329519Bean> iBean) throws Exception {
		this.checkReq5329519Bean(iBean);
		Req5329519Bean data = iBean.getData();
		Req5330252Bean reqBean = new Req5330252Bean();
		BeanUtils.copyProperties(data, reqBean);
		reqBean.setOperationUserId(data.getUserId());
		reqBean.setBoTransTaskIdList(data.getBoTransTaskId());
		reqBean.setOperationUserName(data.getRealName());
		reqBean.setRequestSource(data.getRequestSource());
		businessOperationService.recoveryDeletedTransTask(reqBean);
		return new ResDataBean().success();
	}


	/**
	 * 5329540-查询运输任务最新一笔支付单
	 * @param bean
	 * @return
	 * @throws Exception
	 */
	@PostMapping("lastPosInfo")
	@Logger("5329540-查询运输任务最新一笔支付单")
	public ResDataBean<Resp5329540Bean> lastPosInfo(@RequestBody BaseBean<BaseQueryBean> bean) throws Exception {
		return new ResDataBean<Resp5329540Bean>().success(transTaskService.lastPosInfo(bean.getData()));
	}

	private void checkReq5329519Bean(BaseValidBean<Req5329519Bean> iBean) {
		if (iBean == null || iBean.getData() == null) {
			throw new UnifiedBusinessException("请求参数不能为空");
		}

	}

	@PostMapping("exportField/list")
	@Logger("5329568-任务导出字段查询")
	public ResDataBean<Resp5329568Bean> exportFieldList(@RequestBody BaseBean<Req5329568Bean> bean)throws Exception {
		return new ResDataBean<Resp5329568Bean>().success(transTaskService.exportFieldList(bean.getData()));
	}

	@PostMapping(value = "createCityTask")
	@Logger("5329570-城配-创建运输任务")
	@ResponseBody
	public ResDataBean<Resp5329570Bean> createCityTask(@RequestBody BaseBean<Req5329570Bean> iBean) throws Exception {
		return new ResDataBean<Resp5329570Bean>().success(transTaskService.firstProNotSupCreateCityTask(iBean.getData()));
	}

	@PostMapping(value = "batchQuery")
	@Logger("5329269-批量查询运输任务")
	public ResDataBean<List<Req5330269OBean>> batchQueryTransTaskData(@RequestBody BaseBean<Req5330269IBean> bean) throws Exception {
		return new ResDataBean<List<Req5330269OBean>>().success(businessOperationService.batchQueryTransTaskData(bean.getData()));
	}

	@PostMapping("check/beforeCreate")
	@Logger("5329604-任务-新建任务前置校验")
	public ResDataBean<CommonTipsBean> taskCreateCheck(@RequestBody BaseBean<TaskCreateCheckBean> baseBean)throws Exception {

		return new ResDataBean<CommonTipsBean>().success(transTaskMixService.taskCreateCheck(baseBean.getData()));
	}

	/**
	 * 5329639-运输任务-取消派车校验
	 * @param baseBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping("cancel/dispatched/check")
	@Logger("5329639-运输任务-取消派车校验")
	public ResDataBean<?> cancelDispatchedCheck(@RequestBody BaseBean<Req5329639Bean> baseBean) throws Exception {
		this.transTaskService.cancelDispatchedCheck(baseBean.getData());
		return new ResDataBean<>().success();
	}

	/**
	 * 5329640-运输任务-换车
	 * @param baseBean
	 * @return
	 * @throws Exception
	 */
	@PostMapping("change/driver")
	@Logger("5329640-运输任务-换车")
	public ResDataBean<?> changeDriver(@RequestBody BaseBean<Req5329640Bean> baseBean) throws Exception {
		this.transTaskService.changeDriver(baseBean.getData());
		return new ResDataBean<>().success();
	}


	public boolean isEnableShareToTingJie(String orgId) throws Exception {

		boolean isTingJieShareQRCode = FeatureProbeUtil.isTingJieShareQRCode(orgId);

		return isTingJieShareQRCode;
	}
}
