<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoTaskDetailMapper">

    <sql id="taskQueryWhereSql">
        <include refid="taskPageWhereSql"/>
        <!-- 异常数据 -->
        <choose>
            <when test="abnormalStateList != null and abnormalStateList.size()>0">
                AND EXISTS (
                SELECT 1 FROM T_BO_TRANS_NODE_ALARM a
                WHERE bt.BO_TRANS_TASK_ID = a.BO_TRANS_TASK_ID
                AND a.IS_DEL = 0
                AND a.NODE_DATA_TYPE IN
                <foreach collection="abnormalStateList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                AND a.ALARM_TYPE = 1
                AND a.ALARM_PROCESS_RESULT = 0)
            </when>
            <otherwise>
                <if test="taskAlarmList != null and taskAlarmList.size() > 0">
                    AND EXISTS (
                    SELECT 1 FROM T_BO_TRANS_NODE_ALARM BTNA
                    WHERE BTNA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                    AND BTNA.IS_DEL = 0
                    <foreach collection="taskAlarmList" item="taskAlarm" open="AND (" close=")" separator=" OR ">
                        (BTNA.NODE_DATA_TYPE = #{taskAlarm.nodeDataType} AND BTNA.ALARM_TYPE = #{taskAlarm.alarmType}
                        <if test="taskAlarm.alarmProcessResultList != null and taskAlarm.alarmProcessResultList.size() > 0">
                            AND BTNA.ALARM_PROCESS_RESULT IN
                            <foreach collection="taskAlarm.alarmProcessResultList" item="alarmProcessResult" open="(" separator="," close=")">
                                #{alarmProcessResult}
                            </foreach>
                        </if>
                        )
                    </foreach>
                    )
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="taskRightConditionExist">
        SELECT
         1
        FROM T_BO_TRANS_TASK_GROUP_REL TGR
        INNER JOIN T_BO_TRANS_TASK_USERS TU
        ON TU.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID AND TU.IS_DEL = 0
        <if test="hasTransTaskCusCondition">
            INNER JOIN T_BO_TASK_CUS_FIELD CUS
            ON CUS.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID
            AND CUS.IS_DEL = 0
        </if>
        <if test="hasShippingListCondition">
            LEFT JOIN T_BO_SHIPPING_LIST BSL
            ON BSL.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID AND BSL.IS_DEL = 0
        </if>
        WHERE TGR.IS_DEL = 0
        AND BT.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID
        <if test="supplierTypes != null and supplierTypes.size() > 0">
            AND TGR.SUPPLIER_TYPE IN
            <foreach collection="supplierTypes" item="supplierType" open="(" separator="," close=")">
                #{supplierType}
            </foreach>
        </if>
        <if test="transTaskPermissionList != null and transTaskPermissionList.size() > 0">
            AND
            <foreach collection="transTaskPermissionList" item="transTaskPermission" open="(" separator="OR" close=")">
                <foreach collection="transTaskPermission.scopeList" item="scope" open="(" separator="OR" close=")" index="index">
                    <if test="index == 0 and transTaskPermission.condition != null and transTaskPermission.condition.length > 0">
                        (
                    </if>
                    <choose>
                        <when test="scope.userId != null and scope.userId.length > 0">
                            (TGR.GROUP_ID = #{scope.groupId} AND TU.USER_ID =  #{scope.userId})
                        </when>
                        <otherwise>TGR.GROUP_ID = #{scope.groupId}</otherwise>
                    </choose>
                    <if test="index + 1 == transTaskPermission.scopeList.size() and transTaskPermission.condition != null and transTaskPermission.condition.length > 0">
                        ) AND ${transTaskPermission.condition}
                    </if>
                </foreach>
            </foreach>
        </if>

        <if test="shippingListPermissionList != null and shippingListPermissionList.size() > 0">
            AND (BSL.BO_SHIPPING_LIST_ID IS NULL OR
            <foreach collection="shippingListPermissionList" item="shippingListPermission" open="(" separator="OR" close=")">
                ${shippingListPermission.condition}
            </foreach>
            )
        </if>
        <if test="hasShippingListCondition and tabState == 9">
            AND BSL.PRINT_STATUS = 0
            AND BSL.RECEIPT_NO IS NOT NULL
            AND BSL.BO_SHIPPING_LIST_ID IS NOT NULL
        </if>
    </sql>

    <sql id="taskPageWhereSql">
        <choose>
            <when test="isDel != null and isDel == '1'.toString()">
                BT.IS_DEL = 1
            </when>
            <otherwise>
                BT.IS_DEL = 0
            </otherwise>
        </choose>
        <choose>
            <when test="boTransTaskIds !=null and boTransTaskIds.size()>0">
                AND (
                bt.BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIds" index="index" open="(" close=")" item="id" separator=",">
                    <if test="(index % 999) == 998"> NULL) OR bt.BO_TRANS_TASK_ID IN(</if>#{id}
                </foreach>
                )
            </when>
            <otherwise>
                <if test="noSupplier != null and noSupplier">
                    AND bt.ORG_ID = #{orgId}
                    AND EXISTS (
                    <include refid="taskRightConditionExist"></include>
                    )
                </if>
            </otherwise>
        </choose>
        <if test="capacityType != null and capacityType != ''">
            AND bt.CAPACITY_TYPE = #{capacityType}
            <if test="capacityTypeName != null and capacityTypeName != ''">
                AND bt.CAPACITY_TYPE_NAME = #{capacityTypeName}
            </if>
        </if>
        <if test="optSource==4 and tabState==1">
            AND (bt.TRANS_MODE IS NULL OR bt.TRANS_MODE IN (1,3,4))
        </if>
        <if test="searchKeyword !=null and searchKeyword != ''">
            <choose>
                <when test="searchKeywordsType==1">
                    AND bt.DRIVER_NAME LIKE '%' || #{searchKeyword} || '%'
                </when>
                <when test="searchKeywordsType==2">
                    <choose>
                        <when test="searchKeyword.length()>3">
                            AND bt.MOBILE_NO LIKE '%' || #{searchKeyword} || '%'
                        </when>
                        <otherwise>
                            AND 1=2
                        </otherwise>
                    </choose>
                </when>
                <when test="searchKeywordsType==3">
                    AND bt.CART_BADGE_NO LIKE '%' || #{searchKeyword} || '%'
                </when>
                <when test="searchKeywordsType==4">
                    AND bt.TAX_WAYBILL_NO LIKE '%' || #{searchKeyword} || '%'
                </when>
                <when test="searchKeywordsType==5">
                    AND (bt.START_PROVINCE_NAME || bt.START_CITY_NAME || bt.START_COUNTY_NAME || bte.LOADING_ADDRESS_NAME || BT.LOADING_PLACE_NAME) LIKE '%' || #{searchKeyword} || '%'
                </when>
                <when test="searchKeywordsType==6">
                    AND (bt.END_PROVINCE_NAME || bt.END_CITY_NAME || bt.END_COUNTY_NAME || bte.UNLOADING_ADDRESS_NAME || BT.UNLOADING_PLACE_NAME) LIKE '%' || #{searchKeyword} || '%'
                </when>
                <when test="searchKeywordsType==7">
                    AND INSTR(bt.WB_ITEM, #{searchKeyword}) > 0
                </when>
                <otherwise >
                    AND (bt.TAX_WAYBILL_NO LIKE '%' || #{searchKeyword} || '%'
                    OR bt.CART_BADGE_NO LIKE '%' || #{searchKeyword} || '%'
                    <if test="searchKeyword.length()>3">
                        OR bt.MOBILE_NO LIKE '%' || #{searchKeyword} || '%'
                    </if>
                    OR INSTR(bt.WB_ITEM, #{searchKeyword}) > 0
                    OR bt.DRIVER_NAME LIKE '%' || #{searchKeyword} || '%'
                    OR (bt.START_PROVINCE_NAME || bt.START_CITY_NAME || bt.START_COUNTY_NAME || bte.LOADING_ADDRESS_NAME || BT.LOADING_PLACE_NAME
                    || bt.END_PROVINCE_NAME || bt.END_CITY_NAME || bt.END_COUNTY_NAME || bte.UNLOADING_ADDRESS_NAME || BT.UNLOADING_PLACE_NAME) LIKE '%' || #{searchKeyword} || '%'
                    )
                </otherwise>
            </choose>
        </if>
        <if test="startProvinceName !=null and startProvinceName !='' ">
            AND bt.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName !=null and startCityName !='' ">
            AND bt.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName !=null and startCountyName !='' ">
            AND bt.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="loadingAddressName !=null and loadingAddressName !='' ">
            AND bte.LOADING_ADDRESS_NAME LIKE '%' || #{loadingAddressName} || '%'
        </if>
        <if test="loadingPlaceName !=null and loadingPlaceName !='' ">
            AND bt.LOADING_PLACE_NAME LIKE '%' || #{loadingPlaceName} || '%'
        </if>
        <if test="endProvinceName !=null and endProvinceName !='' ">
            AND bt.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName !=null and endCityName !='' ">
            AND bt.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName !=null and endCountyName !='' ">
            AND bt.END_COUNTY_NAME = #{endCountyName}
        </if>
        <if test="unloadingAddressName !=null and unloadingAddressName !='' ">
            AND bte.UNLOADING_ADDRESS_NAME LIKE '%' || #{unloadingAddressName} || '%'
        </if>
        <if test="unloadingPlaceName !=null and unloadingPlaceName !='' ">
            AND bt.UNLOADING_PLACE_NAME LIKE '%' || #{unloadingPlaceName} || '%'
        </if>
        <if test="taxWaybillId!=null and taxWaybillId!=''">
            AND BT.BO_TRANS_TASK_ID =
            (
            SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE IS_DEL = 0 AND TAX_WAYBILL_ID = #{taxWaybillId}
            UNION
            SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE IS_DEL = 0 AND TAX_WAYBILL_ID = #{taxWaybillId}
            )
        </if>
        <if test="prePayOilcard!=null and prePayOilcard!=''">
            <choose>
                <when test="prePayOilcard==0">
                    AND (A.PREPAYMENTS_OILCARD IS NULL OR A.PREPAYMENTS_OILCARD=0 or bt.PREPAYMENTS_OILCARD IS NULL OR bt.PREPAYMENTS_OILCARD=0)
                </when>
                <otherwise>
                    AND (A.PREPAYMENTS_OILCARD>0 or bt.PREPAYMENTS_OILCARD>0)
                </otherwise>
            </choose>
        </if>
        <if test="payState!=null and payState!=''">
            <choose>
                <when test="payState==0">
                    <![CDATA[
                    AND CASE WHEN bt.ORG_ID = #{orgId} THEN bt.PAY_STATE
                    ELSE A.PAY_STATE END = 0
                    ]]>

                    <if test="isOilSettleOrg == false">
                        <![CDATA[
                        AND CASE WHEN bt.ORG_ID = #{orgId} THEN bt.SETTLE_MODE
                        ELSE A.SETTLE_MODE END <> 2
                        ]]>
                    </if>
                </when>
                <when test="payState==2">
                    <![CDATA[
                    AND CASE WHEN bt.ORG_ID = #{orgId} THEN bt.PAY_STATE
                    ELSE A.PAY_STATE END = 2
                    ]]>
                </when>
                <otherwise>
                    <![CDATA[
                    AND CASE WHEN bt.ORG_ID = #{orgId} THEN bt.PAY_STATE
                    ELSE A.PAY_STATE END NOT IN (0,2)
                    ]]>
                </otherwise>
            </choose>
        </if>
        <include refid="tabStateQueryConditionApp" />
        <if test="backReceiptStatus!=null and backReceiptStatus!=''">
            <choose>
                <when test="backReceiptStatus==0">
                    AND bt.FIRST_RECEIPT_TIME IS NULL
                </when>
                <otherwise>
                    AND bt.FIRST_RECEIPT_TIME IS NOT NULL
                </otherwise>
            </choose>
        </if>
        <if test="taskBeginTime!=null and taskBeginTime!=''">
            AND bt.CREATED_TIME >= TO_DATE(#{taskBeginTime},'YYYY-MM-DD')
        </if>
        <if test="taskEndTime!=null and taskEndTime!=''">
            AND bt.CREATED_TIME &lt;= TO_DATE(#{taskEndTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="receiveState != null and receiveState != '' and receiveState != -1">
            <choose>
                <when test="receiveState == 0">
                    AND bte.HYB_RECEIVED_TIME IS NULL AND bte.DISPATCH_CAR_RECORD_ID IS NOT NULL
                </when>
                <when test="receiveState == 1">
                    AND bte.HYB_RECEIVED_TIME IS NOT NULL
                </when>
                <otherwise>
                    AND 1=1
                </otherwise>
            </choose>
        </if>
        <if test="frozenBackFeeState != null and frozenBackFeeState != ''">
            AND bt.FROZEN_BACK_FEE_STATE = #{frozenBackFeeState}
        </if>
        <if test="userFreightApprovalStatus != null and userFreightApprovalStatus != ''">
            AND bte.USER_FREIGHT_APPROVAL_STATUS = #{userFreightApprovalStatus}
        </if>
        <if test="settleMode != null and settleMode != '' and settleMode != -1">
            <![CDATA[
            AND CASE WHEN bt.ORG_ID = #{orgId} THEN bt.SETTLE_MODE
            ELSE A.SETTLE_MODE END = #{settleMode}
            ]]>
        </if>
        <if test="customerNameList != null and customerNameList.size() > 0">
            AND bt.WB_ITEM IN
            <foreach collection="customerNameList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="belongActSysList != null and belongActSysList.size() > 0">
            AND bt.WAYBILL_BELONG_ACT_SYS IN
            <foreach collection="belongActSysList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="boBusinessLineId != null and boBusinessLineId != ''">
            AND bt.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
        </if>
        <if test="isThirdInterface != null and isThirdInterface !=''">
            <choose>
                <when test="isThirdInterface==0">
                    AND (bte.IS_THIRD_INTERFACE = 0 OR bte.IS_THIRD_INTERFACE IS NULL)
                </when>
                <otherwise>
                    AND bte.IS_THIRD_INTERFACE = #{isThirdInterface}
                </otherwise>
            </choose>
        </if>
        <if test="goodsAmountOcrState !=null and goodsAmountOcrState.size()>0 ">
            AND bte.GOODS_AMOUNT_OCR_STATE IN
            <foreach collection="goodsAmountOcrState" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="confirmedArrivalStatus != null and confirmedArrivalStatus.length > 0">
            <choose>
                <when test="confirmedArrivalStatus == '0'.toString()">
                    AND BTE.DISPATCH_CAR_RECORD_ID IS NOT NULL
                    AND BTE.ARRIVE_CONFIRM_TIME IS NULL
                </when>
                <when test="confirmedArrivalStatus == '1'.toString()">
                    AND BTE.ARRIVE_CONFIRM_TIME IS NOT NULL
                </when>
            </choose>
        </if>
        <if test="contactDriverStatus != null and contactDriverStatus.length > 0">
            <choose>
                <when test="contactDriverStatus == '0'.toString()">
                    AND BTE.DISPATCH_CAR_RECORD_ID IS NOT NULL
                    AND BTE.CONTACT_DRIVER_TIME IS NULL
                </when>
                <when test="contactDriverStatus == '1'.toString()">
                    AND BTE.CONTACT_DRIVER_TIME IS NOT NULL
                </when>
            </choose>
        </if>
        <if test="downstreamInfo != null">
            <if test="downstreamInfo.payState != null and downstreamInfo.payState!=''">
                AND A.SETTLE_MODE = 1
                <choose>
                    <when test="downstreamInfo.payState==0">
                        AND NVL(A.PAY_STATE,0) = 0
                    </when>
                    <when test="downstreamInfo.payState==2">
                        AND A.PAY_STATE = 2
                    </when>
                    <when test="downstreamInfo.payState==5">
                        AND A.PAY_STATE = 5
                    </when>
                    <otherwise>
                        AND A.PAY_STATE NOT IN (0,2,5)
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="upstreamInfo != null">
            <if test="upstreamInfo.payState != null and upstreamInfo.payState!=''">
                AND bt.SETTLE_MODE = 1
                <choose>
                    <when test="upstreamInfo.payState==0">
                        AND NVL(bt.PAY_STATE,0) = 0
                    </when>
                    <when test="upstreamInfo.payState==2">
                        AND bt.PAY_STATE = 2
                    </when>
                    <when test="upstreamInfo.payState==5">
                        AND bt.PAY_STATE = 5
                    </when>
                    <otherwise>
                        AND bt.PAY_STATE NOT IN (0,2,5)
                    </otherwise>
                </choose>
            </if>
        </if>

        <if test="boVoucherCheckState !=null and boVoucherCheckState!=''">
            AND bte.BO_VOUCHER_CHECK_STATE = #{boVoucherCheckState}
        </if>

        <if test="beginStartTime !=null and beginStartTime!=''">
            AND bt.START_TIME &gt;= to_date(#{beginStartTime},'yyyy-MM-dd')
        </if>
        <if test="endStartTime !=null and endStartTime !=''">
            AND bt.START_TIME &lt;= to_date(#{endStartTime} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="beginEndTime !=null and beginEndTime!=''">
            AND bt.END_TIME &gt;= to_date(#{beginEndTime},'yyyy-MM-dd')
        </if>
        <if test="endEndTime !=null and endEndTime !=''">
            AND bt.END_TIME &lt;= to_date(#{endEndTime} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="businessType !=null and businessType !='' ">
            AND bte.BUSINESS_TYPE = #{businessType}
        </if>
        <if test="(dispatchCarUserIds !=null and dispatchCarUserIds.size()>0) or (groupIds !=null and groupIds.size()>0)">
            AND (
            1=0
            <if test="dispatchCarUserIds !=null and dispatchCarUserIds.size()>0">
                OR NR.USER_ID IN
                <foreach collection="dispatchCarUserIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="groupIds !=null and groupIds.size()>0">
                OR EXISTS (
                SELECT 1 FROM T_BO_TRANS_TASK_GROUP_REL tgr WHERE tgr.BO_TRANS_TASK_ID=bt.BO_TRANS_TASK_ID AND tgr.IS_DEL=0 AND tgr.GROUP_ID IN
                <foreach collection="groupIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            )
        </if>
        <if test="appointStatus != null and appointStatus.length > 0">
            AND NVL(BTE.APPOINT_STATUS, -1) = #{appointStatus}
            AND NVL(BTE.TRANS_TASK_FLAG, -1) = (CASE WHEN -1 = #{appointStatus} THEN -1 ELSE 1 END)
        </if>
        <if test="signinStatus != null and signinStatus.length > 0">
            AND NVL(BTE.SIGNIN_STATUS, -1) = #{signinStatus}
            AND NVL(BTE.TRANS_TASK_FLAG, -1) = (CASE WHEN -1 = #{signinStatus} THEN -1 ELSE 1 END)
        </if>
        <if test="cpdPoolGroupName !=null and cpdPoolGroupName !='' ">
            AND bte.CPD_POOL_GROUP_NAME = #{cpdPoolGroupName}
        </if>
        <if test="freezeStatus != null and freezeStatus.length > 0">
            <choose>
                <when test="freezeStatus == 0">
                    AND BTE.FREEZE_STATUS IS NULL
                </when>
                <otherwise>
                    AND BTE.FREEZE_STATUS = #{freezeStatus}
                </otherwise>
            </choose>
        </if>

        <if test="taxWaybillNoList != null and taxWaybillNoList.size() > 0">
            <choose>
                <when test="taxWaybillNoList.size() == 1">
                    AND BT.TAX_WAYBILL_NO LIKE #{taxWaybillNoList[0]} || '%'
                </when>
                <otherwise>
                    AND BT.TAX_WAYBILL_NO IN
                    <foreach collection="taxWaybillNoList" item="taxWaybillNo" open="(" close=")" separator=",">
                        #{taxWaybillNo}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        <if test="cusField != null and cusField.size() > 0">
            AND #{cusFieldSize} = (
                SELECT COUNT(*)
                FROM T_BO_TASK_CUS_FIELD CUS
                WHERE CUS.IS_DEL = 0
                AND CUS.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                <foreach collection="cusField" index="key" item="value" open="AND (" separator=" OR " close=")">
                    CUS.FIELD_KEY = #{key} AND CUS.FIELD_VALUE = #{value}
                </foreach>
            )
        </if>
        <if test="xcyUserId != null and xcyUserId.length > 0">
            AND BT.XCY_USER_ID = #{xcyUserId}
        </if>
        <if test="belongDispatcherId != null and belongDispatcherId.length > 0">
            AND BTE.BELONG_DISPATCHER_ID = #{belongDispatcherId}
        </if>
        <if test="transTaskFlag != null and transTaskFlag.length > 0">
            AND ((#{transTaskFlag} = '-1' AND (BTE.TRANS_TASK_FLAG IS NULL OR BTE.TRANS_TASK_FLAG NOT IN ('3','4'))) OR (#{transTaskFlag} IN (3, 4) AND BTE.TRANS_TASK_FLAG = #{transTaskFlag}))
        </if>
    </sql>

    <sql id="tabStateQueryConditionApp">
        <if test="tabState!=null and tabState!='' and tabState!=0">
            <choose>
                <when test="tabState==4">
                    AND bt.STATE = 1
                </when>
                <when test="tabState==5">
                    AND bt.STATE = 2
                    AND CASE WHEN bt.ORG_ID = #{orgId} THEN NVL(bt.PAY_STATE,0) ELSE NVL(A.PAY_STATE,0) END = 0
                </when>
                <when test="tabState==10">
                    AND bt.STATE = 2
                    AND CASE WHEN bt.ORG_ID = #{orgId} THEN bt.PAY_STATE ELSE A.PAY_STATE END NOT IN (0,2)
                </when>
                <when test="tabState==6">
                    AND bt.STATE = 2
                    AND CASE WHEN bt.ORG_ID = #{orgId} THEN bt.PAY_STATE ELSE A.PAY_STATE END = 2
                </when>
                <when test="tabState==13">
                </when>
                <when test="tabState==14">
                    AND BTE.TRANSPORT_NODE = 600
                    AND NOT (
                        (BT.STATE = 2 AND CASE WHEN bt.ORG_ID = #{orgId} THEN NVL(bt.PAY_STATE,0) ELSE NVL(A.PAY_STATE,0) END = 2)
                        OR (BT.STATE = 2 AND CASE WHEN bt.ORG_ID = #{orgId} THEN NVL(bt.PAY_STATE,0) ELSE NVL(A.PAY_STATE,0) END NOT IN (0, 2))
                    )
                </when>
                <when test="tabState==15">
                    AND BTE.TRANSPORT_NODE = 650
                    AND NOT (
                        (BT.STATE = 2 AND CASE WHEN bt.ORG_ID = #{orgId} THEN NVL(bt.PAY_STATE,0) ELSE NVL(A.PAY_STATE,0) END = 2)
                        OR (BT.STATE = 2 AND CASE WHEN bt.ORG_ID = #{orgId} THEN NVL(bt.PAY_STATE,0) ELSE NVL(A.PAY_STATE,0) END NOT IN (0, 2))
                    )
                </when>
                <when test="tabState==16">
                    AND BTE.TRANSPORT_NODE = 800
                    AND NOT (
                        (BT.STATE = 2 AND CASE WHEN bt.ORG_ID = #{orgId} THEN NVL(bt.PAY_STATE,0) ELSE NVL(A.PAY_STATE,0) END = 2)
                        OR (BT.STATE = 2 AND CASE WHEN bt.ORG_ID = #{orgId} THEN NVL(bt.PAY_STATE,0) ELSE NVL(A.PAY_STATE,0) END NOT IN (0, 2))
                    )
                </when>
                <otherwise>
                    AND bt.STATE = 0
                    <if test="nodeIdQueryList !=null and nodeIdQueryList.size()>0">
                        <![CDATA[
                            AND CASE WHEN bt.ORG_ID = #{orgId} THEN bt.NODE_ID ELSE A.NODE_ID END IN
                        ]]>
                        <foreach collection="nodeIdQueryList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="isNewTransportNode and (tabState == 2 or tabState == 3)">
                        AND BTE.TRANSPORT_NODE IS NULL
                    </if>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="queryDashboardTaskTableCondition">
        bt.IS_DEL =0
        AND bt.org_id = #{orgId}
        AND (
        <foreach item="item" index="index" collection="permissionList" separator=" OR ">
            (
            EXISTS (SELECT 1 FROM T_BO_TRANS_TASK_GROUP_REL tgr WHERE tgr.BO_TRANS_TASK_ID=bt.BO_TRANS_TASK_ID AND tgr.IS_DEL=0 AND tgr.GROUP_ID =#{item.groupId})
            )
        </foreach>
        )
        <if test="boBusinessLineId !=null and boBusinessLineId !='' ">
            AND bt.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
        </if>

        <if test="startTime !=null and startTime!=''">
            <![CDATA[ AND bt.CREATED_TIME >= to_date(#{startTime},'yyyy-MM-dd') ]]>
        </if>
        <if test="endTime !=null and endTime !=''">
            <![CDATA[ AND bt.CREATED_TIME <= to_date(#{endTime} || '23:59:59','yyyy-MM-dd Hh24:mi:ss') ]]>
        </if>
        <if test="status != null and status != ''">
            <choose>
                <when test="status == 7">
                    AND bte.DISPATCH_CAR_RECORD_ID IS NOT NULL
                </when>
                <when test="status == 8 or status == 9">
                    AND bt.STATE = 0
                </when>
                <when test="status == 10">
                    AND bt.STATE = 1
                </when>
                <when test="status == 11">
                    AND bt.STATE = 2
                    AND NVL(bt.PAY_STATE,0) = 0
                </when>
                <when test="status == 12">
                    AND bt.STATE = 2
                    AND bt.PAY_STATE NOT IN (0,2)
                </when>
                <when test="status == 13">
                    AND bt.STATE = 2
                    AND bt.PAY_STATE = 2
                </when>
            </choose>
        </if>

    </sql>

    <sql id="fromTableForPC">
        <include refid="taskRightTempTable"/> TEMP
        JOIN T_BO_TRANS_TASK bt
        ON TEMP.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID AND bt.IS_DEL =0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA bte ON
        bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
        AND bte.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE tta ON
        tta.BO_TRANS_TASK_ID =bt.BO_TRANS_TASK_ID
        AND tta.IS_DEL =0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR ON  NR.BO_TRANS_NODE_RECORD_ID = bte.DISPATCH_CAR_RECORD_ID AND NR.IS_DEL =0
        LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE tdg ON tdg.BO_TASK_DRIVER_GUARANTEE_ID =bte.BO_TASK_DRIVER_GUARANTEE_ID AND tdg.IS_DEL =0
    </sql>


    <sql id="fromTableForPCUnDel">
        <include refid="taskRightTempTableUnDel"/> TEMP
        JOIN T_BO_TRANS_TASK bt
        ON TEMP.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
        bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE tta ON
        tta.BO_TRANS_TASK_ID =bt.BO_TRANS_TASK_ID
        AND tta.IS_DEL =0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR ON  NR.BO_TRANS_NODE_RECORD_ID = bte.DISPATCH_CAR_RECORD_ID AND NR.IS_DEL =0
        LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE tdg ON tdg.BO_TASK_DRIVER_GUARANTEE_ID =bte.BO_TASK_DRIVER_GUARANTEE_ID AND tdg.IS_DEL =0
    </sql>


    <sql id="taskRightTempTable">
        (
            <include refid="taskRightCondition" />
        )
    </sql>

    <!-- 公共sql，接口过来对任务进行操作时做权限判断时也使用了该sql -->
    <sql id="taskRightCondition">
            SELECT
                DISTINCT TGR.BO_TRANS_TASK_ID
            FROM T_BO_TRANS_TASK_GROUP_REL TGR
            INNER JOIN T_BO_TRANS_TASK_USERS TU
                ON TU.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID AND TU.IS_DEL = 0
            <if test="hasTransTaskCondition">
                INNER JOIN T_BO_TRANS_TASK BT
                    ON BT.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID
            </if>
            <if test="hasTransTaskExtraCondition">
                INNER JOIN T_BO_TRANS_TASK_EXTRA BTE
                    ON BTE.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID AND BTE.IS_DEL = 0
            </if>
            <if test="hasTransTaskCusCondition">
                INNER JOIN T_BO_TASK_CUS_FIELD CUS
                ON CUS.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID
                AND CUS.IS_DEL = 0
            </if>
            <if test="hasShippingListCondition">
                LEFT JOIN T_BO_SHIPPING_LIST BSL
                    ON BSL.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID AND BSL.IS_DEL = 0
            </if>
            WHERE TGR.IS_DEL = 0
            <if test="taskBeginTime!=null and taskBeginTime!=''">
                AND TGR.CREATED_TIME >= TO_DATE(#{taskBeginTime},'YYYY-MM-DD')
            </if>
            <if test="supplierTypes != null and supplierTypes.size() > 0">
                AND TGR.SUPPLIER_TYPE IN
                <foreach collection="supplierTypes" item="supplierType" open="(" separator="," close=")">
                    #{supplierType}
                </foreach>
            </if>
            <if test="transTaskPermissionList != null and transTaskPermissionList.size() > 0">
                AND
                <foreach collection="transTaskPermissionList" item="transTaskPermission" open="(" separator="OR" close=")">
                    <foreach collection="transTaskPermission.scopeList" item="scope" open="(" separator="OR" close=")" index="index">
                        <if test="index == 0 and transTaskPermission.condition != null and transTaskPermission.condition.length > 0">
                            (
                        </if>
                        <choose>
                            <when test="scope.userId != null and scope.userId.length > 0">
                                (TGR.GROUP_ID = #{scope.groupId} AND TU.USER_ID =  #{scope.userId})
                            </when>
                            <otherwise>TGR.GROUP_ID = #{scope.groupId}</otherwise>
                        </choose>
                        <if test="index + 1 == transTaskPermission.scopeList.size() and transTaskPermission.condition != null and transTaskPermission.condition.length > 0">
                            ) AND ${transTaskPermission.condition}
                        </if>
                    </foreach>
                </foreach>
            </if>

            <if test="shippingListPermissionList != null and shippingListPermissionList.size() > 0">
                AND (BSL.BO_SHIPPING_LIST_ID IS NULL OR
                <foreach collection="shippingListPermissionList" item="shippingListPermission" open="(" separator="OR" close=")">
                    ${shippingListPermission.condition}
                </foreach>
                )
            </if>
            <if test="hasShippingListCondition and tabState == 9">
                AND BSL.PRINT_STATUS = 0
                AND BSL.RECEIPT_NO IS NOT NULL
                AND BSL.BO_SHIPPING_LIST_ID IS NOT NULL
            </if>
    </sql>


    <sql id="taskRightTempTableUnDel">
        (
            SELECT
                DISTINCT TGR.BO_TRANS_TASK_ID
            FROM T_BO_TRANS_TASK_GROUP_REL TGR
            INNER JOIN T_BO_TRANS_TASK_USERS TU
                ON TU.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID AND TU.IS_DEL = 0
            <if test="hasTransTaskCondition">
                INNER JOIN T_BO_TRANS_TASK BT
                    ON BT.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID
            </if>
            <if test="hasTransTaskExtraCondition">
                INNER JOIN T_BO_TRANS_TASK_EXTRA BTE
                    ON BTE.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID
            </if>
            <if test="hasShippingListCondition">
                LEFT JOIN T_BO_SHIPPING_LIST BSL
                    ON BSL.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID AND BSL.IS_DEL = 0
            </if>
            WHERE TGR.IS_DEL = 0
            <if test="supplierTypes != null and supplierTypes.size() > 0">
                AND TGR.SUPPLIER_TYPE IN
                <foreach collection="supplierTypes" item="supplierType" open="(" separator="," close=")">
                    #{supplierType}
                </foreach>
            </if>
            <if test="transTaskPermissionList != null and transTaskPermissionList.size() > 0">
                AND
                <foreach collection="transTaskPermissionList" item="transTaskPermission" open="(" separator="OR" close=")">
                    <foreach collection="transTaskPermission.scopeList" item="scope" open="(" separator="OR" close=")" index="index">
                        <if test="index == 0 and transTaskPermission.condition != null and transTaskPermission.condition.length > 0">
                            (
                        </if>
                        <choose>
                            <when test="scope.userId != null and scope.userId.length > 0">
                                (TGR.GROUP_ID = #{scope.groupId} AND TU.USER_ID =  #{scope.userId})
                            </when>
                            <otherwise>TGR.GROUP_ID = #{scope.groupId}</otherwise>
                        </choose>
                        <if test="index + 1 == transTaskPermission.scopeList.size() and transTaskPermission.condition != null and transTaskPermission.condition.length > 0">
                            ) AND ${transTaskPermission.condition}
                        </if>
                    </foreach>
                </foreach>
            </if>

            <if test="shippingListPermissionList != null and shippingListPermissionList.size() > 0">
                AND (BSL.BO_SHIPPING_LIST_ID IS NULL OR
                <foreach collection="shippingListPermissionList" item="shippingListPermission" open="(" separator="OR" close=")">
                    ${shippingListPermission.condition}
                </foreach>
                )
            </if>
            <if test="hasShippingListCondition and tabState == 9">
                AND BSL.PRINT_STATUS = 0
                AND BSL.RECEIPT_NO IS NOT NULL
                AND BSL.BO_SHIPPING_LIST_ID IS NOT NULL
            </if>
        )
    </sql>


    <sql id="queryTransportTaskTableCondition">
        bt.IS_DEL =0
        <include refid="queryTransportTaskTableConditionUnDel"/>
    </sql>


    <sql id="queryTransportTaskTableConditionUnDel">
        <if test="(dispatchCarUserIds !=null and dispatchCarUserIds.size()>0) or (groupIds !=null and groupIds.size()>0)">
            AND (
                1=0
                <if test="dispatchCarUserIds !=null and dispatchCarUserIds.size()>0">
                    OR NR.USER_ID IN
                    <foreach collection="dispatchCarUserIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="groupIds !=null and groupIds.size()>0">
                    OR EXISTS (
                    SELECT 1 FROM T_BO_TRANS_TASK_GROUP_REL tgr WHERE tgr.BO_TRANS_TASK_ID=bt.BO_TRANS_TASK_ID AND tgr.IS_DEL=0 AND tgr.GROUP_ID IN
                    <foreach collection="groupIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
            )
        </if>
        <if test="settleMode !=null and settleMode!=''">
            <choose>
                <when test="queryDispatch">AND tta.SETTLE_MODE = #{settleMode}</when>
                <otherwise>AND bt.SETTLE_MODE = #{settleMode}</otherwise>
            </choose>
        </if>
        <if test="createdTimeStart !=null and createdTimeStart!=''">
            AND bt.CREATED_TIME &gt;= to_date(#{createdTimeStart},'yyyy-MM-dd')
        </if>
        <if test="createdTimeEnd !=null and createdTimeEnd !=''">
            AND bt.CREATED_TIME &lt;= to_date(#{createdTimeEnd} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="startProvinceName !=null and startProvinceName !='' ">
            AND bt.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName !=null and startCityName !='' ">
            AND bt.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName !=null and startCountyName !='' ">
            AND bt.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="loadingAddressName !=null and loadingAddressName !='' ">
            AND bte.LOADING_ADDRESS_NAME LIKE '%' || #{loadingAddressName} || '%'
        </if>
        <if test="loadingPlaceName !=null and loadingPlaceName !='' ">
            AND bt.LOADING_PLACE_NAME LIKE '%' || #{loadingPlaceName} || '%'
        </if>
        <if test="endProvinceName !=null and endProvinceName !='' ">
            AND bt.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName !=null and endCityName !='' ">
            AND bt.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName !=null and endCountyName !='' ">
            AND bt.END_COUNTY_NAME = #{endCountyName}
        </if>
        <if test="unloadingAddressName !=null and unloadingAddressName !='' ">
            AND bte.UNLOADING_ADDRESS_NAME LIKE '%' || #{unloadingAddressName} || '%'
        </if>
        <if test="unloadingPlaceName !=null and unloadingPlaceName !='' ">
            AND bt.UNLOADING_PLACE_NAME LIKE '%' || #{unloadingPlaceName} || '%'
        </if>
        <if test="goodsName !=null and goodsName !='' ">
            AND bt.GOODS_NAME LIKE '%' || #{goodsName} || '%'
        </if>
        <if test="searchKeyword !=null and searchKeyword != ''">
            <choose>
                <when test="searchKeywordsType==1">
                    AND bt.DRIVER_NAME LIKE '%' || #{searchKeyword} || '%'
                </when>
                <when test="searchKeywordsType==2">
                    AND bt.MOBILE_NO = #{searchKeyword}
                </when>
                <when test="searchKeywordsType==3">
                    AND bt.CART_BADGE_NO LIKE '%' || #{searchKeyword} || '%'
                </when>
                <when test="searchKeywordsType==4">
                    AND bt.TAX_WAYBILL_NO LIKE '%' || #{searchKeyword} || '%'
                </when>
                <when test="searchKeywordsType==8">
                    AND EXISTS (SELECT 1 FROM T_BO_SHIPPING_LIST tbsl WHERE tbsl.BO_TRANS_TASK_ID=bt.BO_TRANS_TASK_ID AND tbsl.IS_DEL=0 AND tbsl.CUSTOMER_ORDER_NO =#{searchKeyword})
                </when>
                <otherwise >
                    AND (bt.TAX_WAYBILL_NO LIKE '%' || #{searchKeyword} || '%'  OR
                        bt.CART_BADGE_NO LIKE '%' || #{searchKeyword} || '%' OR
                        bt.DRIVER_NAME LIKE '%' || #{searchKeyword} || '%' OR
                        bt.MOBILE_NO = #{searchKeyword} OR
                        EXISTS (SELECT 1 FROM T_BO_SHIPPING_LIST tbsl WHERE tbsl.BO_TRANS_TASK_ID=bt.BO_TRANS_TASK_ID AND tbsl.IS_DEL=0 AND tbsl.CUSTOMER_ORDER_NO =#{searchKeyword})
                    )
                </otherwise>
            </choose>
        </if>
        <if test="taxWaybillNoList !=null and taxWaybillNoList.size()>0">
            AND bt.TAX_WAYBILL_NO IN
            <foreach collection="taxWaybillNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="electronicReceiptStatus != null and electronicReceiptStatus != ''">
            AND bte.ELECTRONIC_RECEIPT_STATUS = #{electronicReceiptStatus}
        </if>
        <if test="paperReceiptStatus != null and paperReceiptStatus != ''">
            AND bte.PAPER_RECEIPT_STATUS = #{paperReceiptStatus}
        </if>
        <if test="dispatchCarTimeStart != null and dispatchCarTimeStart != ''">
            AND bte.DISPATCH_CAR_TIME &gt;= TO_DATE(#{dispatchCarTimeStart},'yyyy-MM-dd')
        </if>
        <if test="dispatchCarTimeEnd != null and dispatchCarTimeEnd != ''">
            AND bte.DISPATCH_CAR_TIME &lt;= TO_DATE(#{dispatchCarTimeEnd} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="arriveEndTimeStart != null and arriveEndTimeStart != ''">
            AND bte.ARRIVE_END_TIME &gt;= TO_DATE(#{arriveEndTimeStart},'yyyy-MM-dd')
        </if>
        <if test="arriveEndTimeEnd != null and arriveEndTimeEnd != ''">
            AND bte.ARRIVE_END_TIME &lt;= TO_DATE(#{arriveEndTimeEnd} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="orderInfoSearch != null">
            <if test="orderInfoSearch.receiptNo != null and orderInfoSearch.receiptNo!=''">
                AND EXISTS (SELECT 1 FROM T_BO_SHIPPING_LIST tbsl WHERE tbsl.BO_TRANS_TASK_ID=bt.BO_TRANS_TASK_ID AND tbsl.IS_DEL=0 AND tbsl.RECEIPT_NO LIKE '%' || #{orderInfoSearch.receiptNo} || '%')
            </if>
        </if>

        <if test="thirdOrderNo != null and thirdOrderNo != ''">
            AND EXISTS (SELECT 1 FROM T_BO_SHIPPING_LIST sl WHERE sl.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID AND sl.IS_DEL = 0 AND sl.THIRD_ORDER_NO LIKE '%' || #{thirdOrderNo} || '%')
        </if>

        <if test="receiveState != null and receiveState != '' and receiveState != -1">
            <choose>
                <when test="receiveState == 0">
                    AND HYB_RECEIVED_TIME IS NULL AND bte.DISPATCH_CAR_RECORD_ID IS NOT NULL
                </when>
                <when test="receiveState == 1">
                    AND HYB_RECEIVED_TIME IS NOT NULL
                </when>
                <otherwise>
                    AND 1=1
                </otherwise>
            </choose>
        </if>
        <if test="driverGuaranteeState != null and driverGuaranteeState != ''">
            AND tdg.GUARANTEE_STATE = #{driverGuaranteeState}
        </if>
        <if test="driverGuaranteeChannel != null and driverGuaranteeChannel != ''">
            <choose>
                <when test="isAdminView">
                    AND tdg.GUARANTEE_CHANNEL LIKE '%' || #{driverGuaranteeChannel} || '%'
                </when>
                <otherwise>
                    AND tdg.GUARANTEE_CHANNEL = #{driverGuaranteeChannel}
                </otherwise>
            </choose>
        </if>
        <if test="frozenBackFeeState != null and frozenBackFeeState != ''">
            AND bt.FROZEN_BACK_FEE_STATE = #{frozenBackFeeState}
        </if>
        <if test="capacityType != null and capacityType != ''">
            AND bt.CAPACITY_TYPE = #{capacityType}
            <if test="capacityTypeName != null and capacityTypeName != ''">
                AND bt.CAPACITY_TYPE_NAME = #{capacityTypeName}
            </if>
        </if>
        <if test="wbItemList != null and wbItemList.size() > 0">
            AND bt.WB_ITEM IN
            <foreach collection="wbItemList" item="wbItem" open="(" separator="," close=")">
                #{wbItem}
            </foreach>
        </if>
        <if test="userFreightApprovalStatus != null and userFreightApprovalStatus != ''">
            AND bte.USER_FREIGHT_APPROVAL_STATUS = #{userFreightApprovalStatus}
        </if>
        <if test="operateFeeStatus != null and operateFeeStatus != ''">
            AND bte.OPERATE_FEE_STATUS = #{operateFeeStatus}
        </if>
        <if test="transportVoucherUploadStatus != null and transportVoucherUploadStatus != ''">
            <choose>
                <when test="transportVoucherUploadStatus == 1">
                    AND bt.FIRST_RECEIPT_TIME IS NULL
                </when>
                <when test="transportVoucherUploadStatus == 2">
                    AND bt.FIRST_RECEIPT_TIME IS NOT NULL
                </when>
            </choose>
        </if>
        <if test="payState != null and payState != ''">
            <choose>
                <when test="queryDispatch">AND (tta.SETTLE_MODE IS NULL OR tta.SETTLE_MODE=1) AND NVL(tta.PAY_STATE,0) = #{payState}</when>
                <otherwise>
                    AND (bt.SETTLE_MODE IS NULL OR bt.SETTLE_MODE=1 OR bt.SETTLE_TYPE =3)
                    AND NVL(bt.PAY_STATE,0) = #{payState}</otherwise>
            </choose>
        </if>
        <if test="isThirdInterface != null and isThirdInterface !=''">
            <choose>
                <when test="isThirdInterface==0">
                    AND (bte.IS_THIRD_INTERFACE = 0 OR bte.IS_THIRD_INTERFACE IS NULL)
                </when>
                <otherwise>
                    AND bte.IS_THIRD_INTERFACE = #{isThirdInterface}
                </otherwise>
            </choose>
        </if>
        <if test="boBusinessLineId !=null and boBusinessLineId !='' ">
            AND bt.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
        </if>
        <if test="goodsAmountOcrState !=null and goodsAmountOcrState.size()>0 ">
            AND bte.GOODS_AMOUNT_OCR_STATE IN
            <foreach collection="goodsAmountOcrState" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessType !=null and businessType !='' ">
            AND bte.BUSINESS_TYPE = #{businessType}
        </if>
        <if test="confirmedArrivalStatus != null and confirmedArrivalStatus.length > 0">
            <choose>
                <when test="confirmedArrivalStatus == '0'.toString()">
                    AND BTE.DISPATCH_CAR_RECORD_ID IS NOT NULL
                    AND BTE.ARRIVE_CONFIRM_TIME IS NULL
                </when>
                <when test="confirmedArrivalStatus == '1'.toString()">
                    AND BTE.ARRIVE_CONFIRM_TIME IS NOT NULL
                </when>
            </choose>
        </if>
        <if test="taskFeeVerifyState !=null and taskFeeVerifyState!=''">
            AND bt.SETTLE_MODE =2
            AND bte.TASK_FEE_VERIFY_STATE = #{taskFeeVerifyState}
        </if>
        <if test="downstreamInfo != null">
            <if test="downstreamInfo.payState != null and downstreamInfo.payState!=''">
                AND tta.SETTLE_MODE = 1
                <choose>
                    <when test="downstreamInfo.payState==0">
                        AND NVL(tta.PAY_STATE,0) = 0
                    </when>
                    <when test="downstreamInfo.payState==2">
                        AND tta.PAY_STATE = 2
                    </when>
                    <when test="downstreamInfo.payState==5">
                        AND tta.PAY_STATE = 5
                    </when>
                    <otherwise>
                        AND tta.PAY_STATE NOT IN (0,2,5)
                    </otherwise>
                </choose>
            </if>
            <if test="downstreamInfo.settleMode != null and downstreamInfo.settleMode!=''">
                AND tta.SETTLE_MODE = #{downstreamInfo.settleMode}
            </if>
        </if>
        <if test="upstreamInfo != null">
            <if test="upstreamInfo.payState != null and upstreamInfo.payState!=''">
                AND bt.SETTLE_MODE = 1
                <choose>
                    <when test="upstreamInfo.payState==0">
                        AND NVL(bt.PAY_STATE,0) = 0
                    </when>
                    <when test="upstreamInfo.payState==2">
                        AND bt.PAY_STATE = 2
                    </when>
                    <when test="upstreamInfo.payState==5">
                        AND bt.PAY_STATE = 5
                    </when>
                    <otherwise>
                        AND bt.PAY_STATE NOT IN (0,2,5)
                    </otherwise>
                </choose>
            </if>
        </if>


        <if test="boVoucherCheckState !=null and boVoucherCheckState!=''">
            AND bte.BO_VOUCHER_CHECK_STATE = #{boVoucherCheckState}
        </if>

        <if test="beginStartTime !=null and beginStartTime!=''">
            AND bt.START_TIME &gt;= to_date(#{beginStartTime},'yyyy-MM-dd')
        </if>
        <if test="endStartTime !=null and endStartTime !=''">
            AND bt.START_TIME &lt;= to_date(#{endStartTime} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="beginEndTime !=null and beginEndTime!=''">
            AND bt.END_TIME &gt;= to_date(#{beginEndTime},'yyyy-MM-dd')
        </if>
        <if test="endEndTime !=null and endEndTime !=''">
            AND bt.END_TIME &lt;= to_date(#{endEndTime} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="queryOrgIds != null and queryOrgIds.size() > 0">
            <choose>
                <when test="queryDispatch">
                    AND (TTA.ORG_ID IN
                    <foreach collection="queryOrgIds" index="index" item="orgId" open="(" separator="," close=")">
                        <if test="(index % 999) == 998"> NULL) OR TTA.ORG_ID IN(</if>#{orgId}
                    </foreach>
                    )
                </when>
                <otherwise>
                    AND (BT.ORG_ID IN
                    <foreach collection="queryOrgIds" index="index" item="orgId" open="(" separator="," close=")">
                        <if test="(index % 999) == 998"> NULL) OR BT.ORG_ID IN(</if>#{orgId}
                    </foreach>
                    )
                </otherwise>
            </choose>
        </if>
        <if test="serviceRequire !=null and serviceRequire !='' ">
            AND bte.SERVICE_REQUIRE LIKE '%' || #{serviceRequire} || '%'
        </if>
        <if test="cpdPoolGroupName !=null and cpdPoolGroupName !='' ">
            AND bte.CPD_POOL_GROUP_NAME LIKE '%' || #{cpdPoolGroupName} || '%'
        </if>
        <if test="hybReceivedTimeStart != null and hybReceivedTimeStart != ''">
            AND bte.HYB_RECEIVED_TIME &gt;= TO_DATE(#{hybReceivedTimeStart},'yyyy-MM-dd')
        </if>
        <if test="hybReceivedTimeEnd != null and hybReceivedTimeEnd != ''">
            AND bte.HYB_RECEIVED_TIME &lt;= TO_DATE(#{hybReceivedTimeEnd} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="wbItem !=null and wbItem !='' ">
            AND bt.WB_ITEM LIKE '%' || #{wbItem} || '%'
        </if>
    </sql>

    <update id="updateTaskFreightByTaskIdOrWaybillId">
        UPDATE
            T_BO_TRANS_TASK
        SET
            ALL_FREIGHT = #{allFreight},
            FREIGHT_INCR = #{freightIncr},
            LOSS_FEE = #{lossFee},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID = #{boTransTaskId}
            <if test="taxWaybillId != null and taxWaybillId !=''">
                AND TAX_WAYBILL_ID = #{taxWaybillId}
            </if>
    </update>
    <update id="updateTaskAllocateFreightByTaskIdOrWaybillId">
        UPDATE
            T_BO_TRANS_TASK_ALLOCATE
        SET
            ALL_FREIGHT = #{allFreight},
            FREIGHT_INCR = #{freightIncr},
            LOSS_FEE = #{lossFee},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID = #{boTransTaskId}
            <if test="taxWaybillId != null and taxWaybillId !=''">
                AND TAX_WAYBILL_ID = #{taxWaybillId}
            </if>
    </update>
    <update id="batchUpdateTransTask">
        UPDATE T_BO_TRANS_TASK T SET T.IS_DEL = #{delState}, T.MODIFY_USER_ID = #{operationUserId}, T.LAST_MODIFIED_TIME = SYSDATE, T.NOTE = #{note}
        <if test="operationUserRoleType != null and operationUserRoleType != ''">
            , T.MODIFY_SYS_ROLE_TYPE = #{operationUserRoleType}
        </if>
        WHERE T.BO_TRANS_TASK_ID IN
        <foreach collection="taskIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <![CDATA[ AND T.IS_DEL <> #{delState} ]]>
    </update>
    <update id="transferBelongActSys">
        UPDATE T_BO_TRANS_TASK
        <set>
            LAST_MODIFIED_TIME = SYSDATE,
            WAYBILL_BELONG_ACT_SYS = #{data.toBelongActSys}
        </set>
        <where>
            IS_DEL = 0
            AND WAYBILL_BELONG_ACT_SYS = #{data.fromBelongActSys}
            AND BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </update>

    <select id="getNewestXcyUserId" parameterType="Req1735216Bean" resultType="String">
        SELECT
            C.XCY_USER_ID
        FROM
            (
                SELECT
                    A.XCY_USER_ID
                FROM
                    T_BO_TRANS_SEND_CAR_RECORD A
                JOIN T_BO_TRANS_TASK B
                        ON
                    B.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
                WHERE
                    B.ORG_ID = #{orgId}
                    AND B.CREATED_USER_ID = #{userId}
                    AND A.TYPE = 1
                    AND A.IS_DEL = 0
                    AND B.IS_DEL = 0
                ORDER BY
                    A.CREATED_TIME DESC) C
        WHERE
        ROWNUM = 1
    </select>

    <select id="getRecentlyXcyUserId" resultType="String">
        SELECT
            C.XCY_USER_ID
        FROM
            (
                SELECT
                    A.XCY_USER_ID
                FROM
                    T_BO_TRANS_SEND_CAR_RECORD A
                JOIN T_BO_TRANS_TASK B
                        ON B.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
                WHERE
                    B.ORG_ID = #{orgId}
                    AND B.CREATED_USER_ID = #{userId}
                    AND A.TYPE = 1
                    AND A.IS_DEL = 0
                    AND B.IS_DEL = 0
                ORDER BY
                    A.CREATED_TIME DESC) C
        WHERE
        ROWNUM = 1
    </select>

    <sql id="taskDetailCommonField">
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.TAX_WAYBILL_ID taxWaybillId,
        T.TAX_WAYBILL_ID ownerTaxWaybillId,
        T.TAX_WAYBILL_NO taxWaybillNo,
        T.ORG_ID orgId,
        T.XCY_USER_ID xcyUserId,
        NVL(T.TRANSPORT_TYPE, 0) transportType,
        T.TRANS_PATTERN transPattern,
        T.TRANS_MODE transMode,
        T.MOBILE_NO mobileNo,
        T.DRIVER_NAME driverName,
        T.DRIVER_ID_CARD driverIdCard,
        T.CART_BADGE_NO cartBadgeNo,
        T.CART_BADGE_COLOR cartBadgeColor,
        T.START_PROVINCE_NAME startProvinceName,
        T.START_CITY_NAME startCityName,
        T.START_COUNTY_NAME startCountyName,
        T.END_PROVINCE_NAME endProvinceName,
        T.END_CITY_NAME endCityName,
        T.END_COUNTY_NAME endCountyName,
        T.START_LONGITUDE startLongitude,
        T.START_LATITUDE startLatitude,
        T.END_LONGITUDE endLongitude,
        T.END_LATITUDE endLatitude,
        T.LOADING_PLACE_NAME loadingPlaceName,
        T.UNLOADING_PLACE_NAME unloadingPlaceName,

        CASE
        WHEN T.LOADING_TONNAGE - TRUNC(T.LOADING_TONNAGE)= 0 THEN
        TO_CHAR(TRUNC(T.LOADING_TONNAGE), '*********90')
        ELSE
        TO_CHAR(T.LOADING_TONNAGE, '*********90.9999')
        END loadingTonnage,
        CASE
        WHEN T.UNLOADING_TONNAGE - TRUNC(T.UNLOADING_TONNAGE) = 0 THEN
        TO_CHAR(TRUNC(T.UNLOADING_TONNAGE), '*********90')
        ELSE
        TO_CHAR(T.UNLOADING_TONNAGE, '*********90.9999')
        END unloadingTonnage,
        T.MILEAGE mileage,
        T.GOODS_NAME goodsName,
        CASE
        WHEN T.GOODS_AMOUNT - TRUNC(T.GOODS_AMOUNT)= 0 THEN
        TO_CHAR(TRUNC(T.GOODS_AMOUNT), '*********90')
        ELSE
        TO_CHAR(T.GOODS_AMOUNT, '*********90.9999')
        END goodsAmount,
        T.GOODS_AMOUNT_TYPE goodsAmountType,
        TO_CHAR(T.PREPAYMENTS, '*********90.00') prepayments,
        TO_CHAR(T.PREPAYMENTS_OILCARD, '*********90.00') prepaymentsOilcard,
        TO_CHAR(T.PREPAYMENTS_GASCARD, '*********90.00') prepaymentsGascard,
        TO_CHAR(T.PREPAYMENTS_BUY_OIL, '*********90.00') prepaymentsBuyOil,
        TO_CHAR(T.PREPAYMENTS_BUY_GAS, '*********90.00') prepaymentsBuyGas,
        TO_CHAR(T.PREPAYMENTS_OILCARD, '*********90.00') taskPrepaymentsOilcard,
        TO_CHAR(T.PREPAYMENTS_GASCARD, '*********90.00') taskPrepaymentsGascard,
        TO_CHAR(T.ALL_FREIGHT, '*********90.00') allFreight,
        TO_CHAR(T.ALL_FREIGHT, '*********90.00') taskAllFreight,
        TO_CHAR(T.USER_FREIGHT, '*********90.00') userFreight,
        TO_CHAR(T.USER_FREIGHT, '*********90.00') taskUserFreight,
        TO_CHAR(T.SERVICE_FEE, '*********90.00') serviceFee,
        TO_CHAR(T.WITHHOLD_TAX_FEE, '*********90.00') withholdTaxFee,
        TO_CHAR(T.DATA_SERVICE_FEE, '*********90.00') dataServiceFee,
        TO_CHAR(T.BACK_FEE, '*********90.00') backFee,
        TO_CHAR(T.FREIGHT_INCR, '*********90.00') freightIncr,
        TO_CHAR(T.LOSS_FEE, '*********90.00') lossFee,
        TO_CHAR(T.ETC_AMOUNT, '*********90.00') etcAmount,
        CASE
            WHEN T.INS_STATE = 1 THEN TO_CHAR(NVL(T.ALL_FREIGHT, 0) + NVL(T.SERVICE_FEE, 0) + NVL(T.DATA_SERVICE_FEE, 0) + NVL(T.INS_FEE, 0)+ NVL(T.WITHHOLD_TAX_FEE, 0), '*********90.00')
            ELSE TO_CHAR(NVL(T.ALL_FREIGHT, 0) + NVL(T.SERVICE_FEE, 0) + NVL(T.DATA_SERVICE_FEE, 0) + NVL(T.WITHHOLD_TAX_FEE, 0), '*********90.00')
        END payableFreight,
        TO_CHAR(T.GOODS_COST, '*********90.00') goodsCost,
        TO_CHAR(T.GOODS_COST, '*********90.00') taskGoodsCost,
        CASE
            WHEN T.INS_STATE = 1 THEN TO_CHAR(NVL(T.INS_FEE, 0), '*********90.00')
            ELSE '0.00'
        END insFee,
        TO_CHAR(T.UNIT_PRICE, '*********90.00') unitPrice,
        TO_CHAR(T.UNIT_PRICE, '*********90.00') taskUnitPrice,
        TO_CHAR(T.FIRST_RECEIPT_TIME, 'YYYY-MM-DD HH24:MI:SS') firstReceiptTime,
        T.NODE_ID nodeId,
        A.NODE_ID allocateNodeId,
        TO_CHAR(T.NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
        TO_CHAR(T.START_TIME, 'YYYY-MM-DD HH24:MI:SS') startTime,
        TO_CHAR(T.END_TIME, 'YYYY-MM-DD HH24:MI:SS') endTime,
        T.TRANSPORT_LINE_ID transportLineId,
        T.BO_BUSINESS_LINE_ID boBusinessLineId,
        T.LOSS_ENSURE_STATE lossEnsureState,
        T.OFFER_TYPE offerType,
        T.HYB_STATE hybState,
        T.PAY_STATE payState,
        NVL(T.ADVANCE_PAY_STATE, '0') advancePayState,
        T.STATE,
        T.NOTE,
        T.WB_ITEM wbItem,
        T.ORDER_CREATE_TYPE orderCreateType,
        T.ORG_ID ownerOrgId,
        T.RECEIVER_MOBILE receiverMobile,
        NVL(T.SETTLE_MODE,'1') settleMode,
        NVL(T.SETTLE_MODE,'1') taskSettleMode,
        NVL(T.FROZEN_BACK_FEE_STATE,0) frozenBackFeeState,
        T.INST_ID instId,
        TE.CART_TYPE cartType,
        TE.CART_LENGTH cartLength,
        TE.CART_TONNAGE cartTonnage,
        TE.LOADING_ADDRESS_NAME loadingAddressName,
        TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
        TE.PAY_NAME payName,
        TE.PAY_ID_CARD payIdCard,
        TE.PAY_BANK_NO payBankNo,
        TE.PAY_BANK_NAME payBankName,
        TE.PROVINCE province,
        TE.CITY_NAME cityName,
        TE.PAY_MOBILE_NO payMobileNo,
        TE.PAY_TYPE payType,
        TO_CHAR(TE.FREIGHT_GUARANTEE, '*********90.00') freightGuarantee,
        TO_CHAR(TE.FREIGHT_GUARANTEE, '*********90.00') taskFreightGuarantee,
        TE.SERVICE_REQUIRE serviceRequire,
        TE.OIL_CARD_NO oilCardNo,
        TE.GAS_CARD_NO gasCardNo,
        TE.PAPER_RECEIPT_NEED_POST_TYPE paperReceiptNeedPostType,
        TO_CHAR(TE.LOSS_AMOUNT, '*********90.00') lossAmount,
        TO_CHAR(TE.ARRIVE_END_TIME, 'YYYY-MM-DD HH24:MI:SS') arriveEndTime,
        TO_CHAR(TE.UNLOAD_TIME, 'YYYY-MM-DD HH24:MI:SS') unloadTime,
        TO_CHAR(TE.ARRIVE_TIME, 'YYYY-MM-DD HH24:MI:SS') arriveTime,
        TE.EXTEND_JSON extendJson,
        TE.DISPATCH_CAR_RECORD_ID dispatchCarRecordId,
        TO_CHAR(TE.DISPATCH_CAR_TIME, 'YYYY-MM-DD HH24:MI:SS') dispatchCarTime,
        TE.TRANS_VOUCHER transVoucher,
        TE.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
        TE.UNLOAD_TIME_FORMAT unloadTimeFormat,
        TE.OPERATE_FEE_STATUS operateFeeStatus,
        TE.SALESMAN_NAME salesmanName,
        TE.EXPRESS_NUMBER expressNumber,
        TE.PAYER payer,
        TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
        NVL(TE.USER_FREIGHT_APPROVAL_STATUS,0) userFreightApprovalStatus,
        A.ORG_ID allocateOrgId,
        A.SETTLE_MODE allocateSettleMode,
        A.TAX_WAYBILL_ID allocateTaxWaybillId,
        TO_CHAR(A.UNIT_PRICE, '*********90.00') allocateUnitPrice,
        TO_CHAR(A.USER_FREIGHT, '*********90.00') allocateUserFreight,
        TO_CHAR(A.BACK_FEE, '*********90.00') allocateBackFee,
        TO_CHAR(A.PREPAYMENTS, '*********90.00') allocatePrepayments,
        TO_CHAR(A.FREIGHT_INCR, '*********90.00') allocateFreightIncr,
        TO_CHAR(A.LOSS_FEE, '*********90.00') allocateLossFee,
        TO_CHAR(A.PREPAYMENTS_GASCARD, '*********90.00') allocatePrepaymentsGascard,
        TO_CHAR(A.PREPAYMENTS_OILCARD, '*********90.00') allocatePrepaymentsOilcard,
        A.ALL_FREIGHT allocateAllFreight,
        TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
        NVL(T.FROZEN_BACK_FEE_STATE,0) frozenBackFeeState,
        NVL(A.TAX_WAYBILL_ID, T.TAX_WAYBILL_ID) createTaxWaybillId,
        NVL(T.TAX_WAYBILL_ID, A.TAX_WAYBILL_ID) locTaxWaybillId,
        NVL(T.ORG_ID, A.ORG_ID) locOrgId,
        T.CAPACITY_TYPE capacityType,
        T.CAPACITY_TYPE_NAME capacityTypeName,
        A.PAY_STATE allocatePayState,
        T.IS_PARTAKE_OPERATE isPartakeOperate,
        T.CUSTOMER_REMARK customerRemark,
        T.CREATED_USER_ID createdUserId,
        T.CREATED_USER_JOB_NAME createdUserJobName,
        CASE
        WHEN TE.LOADING_ROUGH_WEIGHT - TRUNC(TE.LOADING_ROUGH_WEIGHT)= 0 THEN
        TO_CHAR(TRUNC(TE.LOADING_ROUGH_WEIGHT), '*********90')
        ELSE
        TO_CHAR(TE.LOADING_ROUGH_WEIGHT, '*********90.9999')
        END loadingRoughWeight,
        CASE
        WHEN TE.UNLOADING_ROUGH_WEIGHT - TRUNC(TE.UNLOADING_ROUGH_WEIGHT) = 0 THEN
        TO_CHAR(TRUNC(TE.UNLOADING_ROUGH_WEIGHT), '*********90')
        ELSE
        TO_CHAR(TE.UNLOADING_ROUGH_WEIGHT, '*********90.9999')
        END unloadingRoughWeight,
        CASE
        WHEN TE.LOADING_TARE - TRUNC(TE.LOADING_TARE)= 0 THEN
        TO_CHAR(TRUNC(TE.LOADING_TARE), '*********90')
        ELSE
        TO_CHAR(TE.LOADING_TARE, '*********90.9999')
        END loadingTare,
        CASE
        WHEN TE.UNLOADING_TARE - TRUNC(TE.UNLOADING_TARE) = 0 THEN
        TO_CHAR(TRUNC(TE.UNLOADING_TARE), '*********90')
        ELSE
        TO_CHAR(TE.UNLOADING_TARE, '*********90.9999')
        END unloadingTare,
        TE.SETTLE_ISSUE settleIssue,
        TE.BO_LINE_ASSIGN_REL_ID boLineAssignRelId,
        TE.CPD_POOL_GROUP_ID cpdPoolGroupId,
        TE.CPD_POOL_GROUP_NAME cpdPoolGroupName,
        TE.CPD_SECOND_POOL_GROUP_NAME cpdSecondPoolGroupName,
        TE.CUSTOMIZE_NO customizeNo,
        TE.SHIPMENT_PHOTO shipmentPhoto,
        TE.RECEIPT_BZ_STATE receiptBzState,
        TE.TRANS_VOUCHER_EXPRESS_NUMBER transVoucherExpressNumber,
        TO_CHAR(TE.RECEIPT_RECEIVE_TIME, 'yyyy-mm-dd hh24:mi:ss') receiptReceiveTime,
        TE.TRAN_REQUIRE tranRequire,
        TE.CART_TONNAGE cartTonnage,
        TE.TRAILER_CART_BADGE_NO trailerCartBadgeNo,
        NVL(TE.IS_THIRD_INTERFACE,0) isThirdInterface,
        TO_CHAR(TE.ACTUAL_LOAD_TIME,'YYYY-MM-DD') actualLoadTime,
        TO_CHAR(TE.ACTUAL_UNLOAD_TIME,'YYYY-MM-DD') actualUnloadTime,
        BOX_NO boxNo,
        CASE
        WHEN TE.DEDUCT_TONNAGE - TRUNC(TE.DEDUCT_TONNAGE) = 0 THEN
        TO_CHAR(TRUNC(TE.DEDUCT_TONNAGE), '*********90')
        ELSE
        TO_CHAR(TE.DEDUCT_TONNAGE, '*********90.9999')
        END deductTonnage,
        TE.ORG_MAILING_ADDRESS_ID orgMailingAddressId,
        T.RECEIVER receiver,
        TO_CHAR(T.FIXED_COSTS, '*********90.00') fixedCosts,
        TO_CHAR(A.FIXED_COSTS, '*********90.00') allocateFixedCosts,
        T.SETTLE_TARGET settleTarget,
        T.SETTLE_TYPE originSettleType,
        NVL(T.SETTLE_TYPE, T.SETTLE_MODE) settleType,
        TE.BUSINESS_TYPE businessType,
        TE.LOAD_TYPE loadType,
        TO_CHAR(TE.LOADING_GOODS_TIME,'YYYY-MM-DD HH24:MI:SS') loadingGoodsTime,
        TE.IS_FREIGHT_SHOW isFreightShow,
        TE.CUSTOMER_ORDER_NO customerOrderNo,
        TE.PAPER_RECEIPT_STATUS paperReceiptStatus,
        TO_CHAR(TE.LOSS_UNIT_PRICE, '*********90.00') lossUnitPrice,
        CASE WHEN TE.LOSS_ACTUAL_TONNAGE = FLOOR(TE.LOSS_ACTUAL_TONNAGE) THEN  TO_CHAR(TE.LOSS_ACTUAL_TONNAGE, '*********90') ELSE TO_CHAR(TE.LOSS_ACTUAL_TONNAGE, '*********90.9999') END lossActualTonnage,
        CASE WHEN TE.ALLOW_LOSS_WEIGHT = FLOOR(TE.ALLOW_LOSS_WEIGHT) THEN  TO_CHAR(TE.ALLOW_LOSS_WEIGHT, '*********90') ELSE TO_CHAR(TE.ALLOW_LOSS_WEIGHT, '*********90.9999') END allowLossWeight,
        TE.ALLOW_GAIN_FLAG allowGainFlag,
        TE.LOADING_REMARK loadingRemark,
        TE.LOADING_CONTACT_NAME loadingContactName,
        TE.LOADING_CONTACT_MOBILE_NO loadingContactMobileNo,
        TE.UNLOADING_REMARK unloadingRemark,
        TE.UNLOADING_CONTACT_NAME unloadingContactName,
        TE.UNLOADING_CONTACT_MOBILE_NO unloadingContactMobileNo,
        TE.AUDIT_STATUS auditStatus,
        TE.DELETE_STATUS deleteStatus,
        TE.RECEIVED_NUMBER receivedNumber,
        TO_CHAR(TE.TRANSPORT_TIME, '*********90.09') transportTime,
        T.INST_ID instId,
        TE.SETTLE_FLAG settleFlag,
        TE.BELONG_DISPATCHER_ID belongDispatcherId,
        T.WAYBILL_BELONG_ACT_SYS belongActSys,
        TE.APPOINT_STATUS appointStatus,
        TE.SIGNIN_STATUS signinStatus,
        TE.THIRD_TASK_NO thirdTaskNo,
        TE.TRANS_TASK_FLAG transTaskFlag,
        TE.IS_REBATE isRebate,
        TE.CANCEL_STATE cancelState,
        TE.FREEZE_STATUS freezeStatus,
        TE.TRANSPORT_NODE transportNode,
        TE.TASK_FEE_VERIFY_STATE taskFeeVerifyState,
        T.IS_DEL isDel
    </sql>

    <sql id="queryTransTaskDetailTableSql">
        T_BO_TRANS_TASK T
        JOIN T_BO_TRANS_TASK_EXTRA TE
            ON TE.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
            AND TE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
            AND A.IS_DEL = 0
    </sql>
    <sql id="queryTransTaskDetailWhereSql">
        <where>
            <if test="isDel != null and isDel.length > 0">
                T.IS_DEL = #{isDel}
            </if>
            <if test="taxWaybillId != null and taxWaybillId != ''">
                AND T.BO_TRANS_TASK_ID = (
                    SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE TAX_WAYBILL_ID = #{taxWaybillId}
                    <if test="isDel != null and isDel.length > 0">
                        AND IS_DEL = #{isDel}
                    </if>
                    UNION
                    SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE IS_DEL =  0 AND TAX_WAYBILL_ID = #{taxWaybillId}
                )
            </if>
            <if test="boTransTaskId != null and boTransTaskId != ''">
                AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
            </if>
            <if test="taxWaybillNo != null and taxWaybillNo != ''">
                AND T.TAX_WAYBILL_NO = #{taxWaybillNo}
            </if>
            <if test="orgId != null and orgId != ''">
                AND (T.ORG_ID = #{orgId} OR A.ORG_ID = #{orgId})
            </if>
        </where>
    </sql>

    <select id="getTaskDetailById" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
        <include refid="taskDetailCommonField"></include>
        FROM
        T_BO_TRANS_TASK T
        JOIN T_BO_TRANS_TASK_EXTRA TE
        ON
        TE.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        AND TE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE
        <choose>
            <when test="boTransTaskId != null and boTransTaskId !=''">
                T.BO_TRANS_TASK_ID = #{boTransTaskId}
            </when>
            <otherwise>
                T.BO_TRANS_TASK_ID =
                (
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE IS_DEL =0 AND TAX_WAYBILL_ID = #{taxWaybillId}
                UNION
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE  TAX_WAYBILL_ID=#{taxWaybillId} AND IS_DEL = 0
                )
            </otherwise>
        </choose>
        AND T.IS_DEL = 0
    </select>

    <select id="findTransTaskInfoByTaskIdOrBillId" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.TAX_WAYBILL_ID ownerTaxWaybillId,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.GOODS_NAME goodsName,
            T.ORG_ID orgId,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(T.LOSS_FEE, 0), '*********90.00') lossFee,
            TO_CHAR(NVL(T.FREIGHT_INCR, 0), '*********90.00') freightIncr,
            TO_CHAR(NVL(T.UNIT_PRICE, 0), '*********90.00') unitPrice,
            TO_CHAR(NVL(T.LOADING_TONNAGE, 0), '*********90.0000') loadingTonnage,
            TO_CHAR(NVL(T.UNLOADING_TONNAGE, 0), '*********90.0000') unloadingTonnage,
            T.TRANSPORT_LINE_ID transportLineId,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            TO_CHAR(T.START_TIME, 'YYYY-MM-DD HH24:MI:SS') startTime,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            TE.LOADING_ADDRESS_NAME loadingAddressName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.XCY_USER_ID xcyUserId,
            T.CREATED_USER_SYS_ROLE_TYPE sysRoleType,
            T.ORDER_CREATE_TYPE orderCreateType,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.DRIVER_NAME driverName,
            T.CREATED_USER_ID createdUserId,
            TE.CART_LENGTH cartLength,
            TE.CART_TYPE cartType,
            T.HYB_STATE hybState,
            T.TRANS_PATTERN transPattern,
            T.TRANS_MODE transMode,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TRANSPORT_TYPE transportType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            TO_CHAR(NVL(T.ALL_FREIGHT, 0), '*********90.00') allFreight,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            T.PAY_STATE payState,
            A.BO_TRANS_TASK_ALLOCATE_ID boTransTaskAllocateId,
            A.ORG_ID allocateOrgId,
            A.SETTLE_MODE allocateSettleMode,
            A.TAX_WAYBILL_ID allocateTaxWaybillId,
            T.RECEIVER_MOBILE receiverMobile,
            T.RECEIVER receiver,
            TO_CHAR(TE.ARRIVE_END_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveEndTime,
            TO_CHAR(TE.ARRIVE_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveTime,
            TE.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
            TE.UNLOAD_TIME_FORMAT unloadTimeFormat,
            NVL(T.TAX_WAYBILL_ID, A.TAX_WAYBILL_ID) locTaxWaybillId,
            NVL(A.TAX_WAYBILL_ID, T.TAX_WAYBILL_ID) createTaxWaybillId,
            case when A.TAX_WAYBILL_ID is not NULL then A.ORG_ID ELSE T.ORG_ID END createOrgId,
            TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            NVL(TE.USER_FREIGHT_APPROVAL_STATUS,0) userFreightApprovalStatus,
            case when T.TAX_WAYBILL_ID is not null
                then T.ORG_ID
                ELSE A.ORG_ID END locOrgId,
            T.OPERATE_SCHEME operateScheme,
            T.CAPACITY_TYPE capacityType,
            T.CAPACITY_TYPE_NAME capacityTypeName,
            T.IS_PARTAKE_OPERATE isPartakeOperate,
            TO_CHAR(TE.DISPATCH_CAR_TIME, 'yyyy-mm-dd hh24:mi:ss') dispatchCarTime,
            TE.DRIVER_ID driverId,
            TE.HYB_USER_ID hybUserId,
            TE.RECEIVED_NUMBER receivedNumber,
            TE.CART_TONNAGE cartTonnage,
            TO_CHAR(TE.TRANSPORT_TIME, '*********90.09') transportTime,
            TE.LOAD_TYPE loadType,
            TE.CPD_POOL_GROUP_ID cpdPoolGroupId,
            TE.TRANS_TASK_FLAG transTaskFlag,
            TE.VOUCHER_CONFIG_TYPE voucherConfigType,
            T.WB_ITEM wbItem
        FROM
            T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TE ON
            T.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON
            T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE
            T.IS_DEL = 0
        <choose>
            <when test="boTransTaskId != null and boTransTaskId !=''">
                AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
            </when>
            <when test="taxWaybillId != null and taxWaybillId !=''">
                AND T.TAX_WAYBILL_ID = #{taxWaybillId}
            </when>
            <otherwise>
                AND 1 = 0
            </otherwise>
        </choose>
    </select>

    <select id="getTaskDetailByTransTaskId" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(T.LOSS_FEE, 0), '*********90.00') lossFee,
            TO_CHAR(NVL(T.FREIGHT_INCR, 0), '*********90.00') freightIncr,
            TO_CHAR(NVL(T.UNIT_PRICE, 0), '*********90.00') unitPrice,
            TO_CHAR(NVL(T.LOADING_TONNAGE, 0), '*********90.0000') loadingTonnage,
            TO_CHAR(NVL(T.UNLOADING_TONNAGE, 0), '*********90.0000') unloadingTonnage,
            T.TRANSPORT_LINE_ID transportLineId,
            TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.XCY_USER_ID xcyUserId,
            T.CREATED_USER_SYS_ROLE_TYPE sysRoleType,
            T.ORDER_CREATE_TYPE orderCreateType,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.DRIVER_NAME driverName,
            T.DRIVER_ID_CARD driverIdCard,
            T.CREATED_USER_ID createdUserId,
            T.HYB_STATE hybState,
            TO_CHAR(TE.HYB_RECEIVED_TIME, 'YYYY-MM-DD HH24:MI:SS') hybReceivedTime,
            T.STATE state,
            T.TRANS_PATTERN transPattern,
            T.TRANS_MODE transMode,
            T.TRANSPORT_TYPE transportType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            T.WB_ITEM wbItem,
            TO_CHAR(NVL(T.ALL_FREIGHT, 0), '*********90.00') allFreight,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            T.RECEIVER_MOBILE receiverMobile,
            T.RECEIVER receiver,
            T.IS_DEL isDel,
            T.OPERATE_SCHEME operateScheme,
            T.IS_PARTAKE_OPERATE IsPartakeOperate,
            TO_CHAR(T.START_TIME, 'yyyy-mm-dd hh24:mi:ss') startTime,
            TO_CHAR(T.END_TIME, 'yyyy-mm-dd hh24:mi:ss') endTime,
            TO_CHAR(T.FIRST_RECEIPT_TIME, 'yyyy-mm-dd hh24:mi:ss') firstReceiptTime,
            NVL(T.TAX_WAYBILL_ID, A.TAX_WAYBILL_ID) locTaxWaybillId,
            T.GOODS_NAME goodsName,
            T.GOODS_AMOUNT goodsAmount,
            T.GOODS_AMOUNT_TYPE goodsAmountType,
            T.FIXED_COSTS fixedCosts,
            A.FIXED_COSTS allocateFixedCosts,
            NVL(A.TAX_WAYBILL_ID, T.TAX_WAYBILL_ID) createTaxWaybillId,
            A.BO_TRANS_TASK_ALLOCATE_ID boTransTaskAllocateId,
            A.ORG_ID allocateOrgId,
            A.SETTLE_MODE allocateSettleMode,
            A.TAX_WAYBILL_ID allocateTaxWaybillId,
            TO_CHAR(NVL(A.UNIT_PRICE,0), '*********90.00') allocateUnitPrice,
            TO_CHAR(NVL(A.USER_FREIGHT, 0), '*********90.00') allocateUserFreight,
            TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            TE.LOADING_ADDRESS_NAME loadingAddressName,
            TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
            TE.CART_LENGTH cartLength,
            TE.CART_TYPE cartType,
            TE.DISPATCH_CAR_RECORD_ID dispatchCarRecordId,
            TO_CHAR(TE.DISPATCH_CAR_TIME, 'yyyy-mm-dd hh24:mi:ss') dispatchCarTime,
            TO_CHAR(TE.ARRIVE_TIME, 'yyyy-mm-dd hh24:mi:ss') arriveTime,
            TE.DRIVER_ID driverId,
            TE.TRANS_VOUCHER transVoucher,
            TE.TRAILER_CART_BADGE_NO trailerCartBadgeNo,
            TE.PAPER_RECEIPT_NEED_POST_TYPE paperReceiptNeedPostType,
            TE.APPOINT_STATUS appointStatus,
            TE.TRANS_TASK_FLAG transTaskFlag,
            TE.LOAD_TYPE loadType,
            TO_CHAR(TE.ARRIVE_END_TIME, 'YYYY-MM-DD HH24:MI:SS') arriveEndTime,
            TO_CHAR(TE.UNLOAD_TIME, 'YYYY-MM-DD HH24:MI:SS') unloadTime,
            TE.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
            TE.UNLOAD_TIME_FORMAT unloadTimeFormat,
            TO_CHAR(TE.TRANSPORT_TIME, '*********90.09') transportTime,
            T.WAYBILL_BELONG_ACT_SYS waybillBelongActSys,
            TE.ELECTRONIC_RECEIPT_STATUS electronicReceiptStatus
        FROM T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TE
            ON T.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE T.IS_DEL = 0
        <choose>
            <when test="boTransTaskId != null and boTransTaskId.length > 0">
                AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
            </when>
            <otherwise>
                AND T.BO_TRANS_TASK_ID = -1
            </otherwise>
        </choose>
    </select>

    <select id="getAllTaskDetailByTransTaskIdList" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(T.LOSS_FEE, 0), '*********90.00') lossFee,
            TO_CHAR(NVL(T.FREIGHT_INCR, 0), '*********90.00') freightIncr,
            TO_CHAR(NVL(T.UNIT_PRICE, 0), '*********90.00') unitPrice,
            TO_CHAR(NVL(T.LOADING_TONNAGE, 0), '*********90.0000') loadingTonnage,
            TO_CHAR(NVL(T.UNLOADING_TONNAGE, 0), '*********90.0000') unloadingTonnage,
            T.TRANSPORT_LINE_ID transportLineId,
            TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.XCY_USER_ID xcyUserId,
            T.CREATED_USER_SYS_ROLE_TYPE sysRoleType,
            T.ORDER_CREATE_TYPE orderCreateType,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.DRIVER_NAME driverName,
            T.CREATED_USER_ID createdUserId,
            T.HYB_STATE hybState,
            T.STATE state,
            T.TRANS_PATTERN transPattern,
            T.TRANS_MODE transMode,
            T.TRANSPORT_TYPE transportType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            TO_CHAR(NVL(T.ALL_FREIGHT, 0), '*********90.00') allFreight,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            T.RECEIVER_MOBILE receiverMobile,
            T.RECEIVER receiver,
            T.IS_DEL isDel,
            T.OPERATE_SCHEME operateScheme,
            T.IS_PARTAKE_OPERATE IsPartakeOperate,
            TO_CHAR(T.START_TIME, 'yyyy-mm-dd hh24:mi:ss') startTime,
            TO_CHAR(T.END_TIME, 'yyyy-mm-dd hh24:mi:ss') endTime,
            TO_CHAR(T.FIRST_RECEIPT_TIME, 'yyyy-mm-dd hh24:mi:ss') firstReceiptTime,
            NVL(T.TAX_WAYBILL_ID, A.TAX_WAYBILL_ID) locTaxWaybillId,
            T.GOODS_NAME goodsName,
            T.GOODS_AMOUNT goodsAmount,
            T.GOODS_AMOUNT_TYPE goodsAmountType,
            NVL(A.TAX_WAYBILL_ID, T.TAX_WAYBILL_ID) createTaxWaybillId,
            A.BO_TRANS_TASK_ALLOCATE_ID boTransTaskAllocateId,
            A.ORG_ID allocateOrgId,
            A.SETTLE_MODE allocateSettleMode,
            A.TAX_WAYBILL_ID allocateTaxWaybillId,
            TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            TE.LOADING_ADDRESS_NAME loadingAddressName,
            TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
            TE.CART_LENGTH cartLength,
            TE.CART_TYPE cartType,
            TE.DISPATCH_CAR_RECORD_ID dispatchCarRecordId,
            TO_CHAR(TE.DISPATCH_CAR_TIME, 'yyyy-mm-dd hh24:mi:ss') dispatchCarTime,
            TO_CHAR(TE.ARRIVE_TIME, 'yyyy-mm-dd hh24:mi:ss') arriveTime,
            TO_CHAR(TE.HYB_RECEIVED_TIME, 'yyyy-mm-dd hh24:mi:ss') hybReceivedTime
        FROM T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TE
            ON T.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE
        <choose>
            <when test="boTransTaskIdList != null and boTransTaskIdList.size() > 0">
                T.BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
                    #{boTransTaskId}
                </foreach>
            </when>
            <otherwise>
                T.BO_TRANS_TASK_ID = -1
            </otherwise>
        </choose>
    </select>

    <select id="getDeletedTaskDetailByTransTaskId" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(T.LOSS_FEE, 0), '*********90.00') lossFee,
            TO_CHAR(NVL(T.FREIGHT_INCR, 0), '*********90.00') freightIncr,
            TO_CHAR(NVL(T.UNIT_PRICE, 0), '*********90.00') unitPrice,
            TO_CHAR(NVL(T.LOADING_TONNAGE, 0), '*********90.0000') loadingTonnage,
            TO_CHAR(NVL(T.UNLOADING_TONNAGE, 0), '*********90.0000') unloadingTonnage,
            T.TRANSPORT_LINE_ID transportLineId,
            TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.XCY_USER_ID xcyUserId,
            T.CREATED_USER_SYS_ROLE_TYPE sysRoleType,
            T.ORDER_CREATE_TYPE orderCreateType,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.DRIVER_NAME driverName,
            T.CREATED_USER_ID createdUserId,
            T.HYB_STATE hybState,
            T.STATE state,
            T.TRANS_PATTERN transPattern,
            T.TRANS_MODE transMode,
            T.TRANSPORT_TYPE transportType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            TO_CHAR(NVL(T.ALL_FREIGHT, 0), '*********90.00') allFreight,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            T.RECEIVER_MOBILE receiverMobile,
            T.RECEIVER receiver,
            T.IS_DEL isDel,
            T.OPERATE_SCHEME operateScheme,
            T.IS_PARTAKE_OPERATE IsPartakeOperate,
            TO_CHAR(T.START_TIME, 'yyyy-mm-dd hh24:mi:ss') startTime,
            TO_CHAR(T.FIRST_RECEIPT_TIME, 'yyyy-mm-dd hh24:mi:ss') firstReceiptTime,
            NVL(T.TAX_WAYBILL_ID, A.TAX_WAYBILL_ID) locTaxWaybillId,
            NVL(A.TAX_WAYBILL_ID, T.TAX_WAYBILL_ID) createTaxWaybillId,
            A.BO_TRANS_TASK_ALLOCATE_ID boTransTaskAllocateId,
            A.ORG_ID allocateOrgId,
            A.SETTLE_MODE allocateSettleMode,
            A.TAX_WAYBILL_ID allocateTaxWaybillId,
            TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            TE.LOADING_ADDRESS_NAME loadingAddressName,
            TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
            TE.CART_LENGTH cartLength,
            TE.CART_TYPE cartType,
            TE.DELETE_STATUS deleteStatus,
            TE.DISPATCH_CAR_RECORD_ID dispatchCarRecordId,
            TO_CHAR(TE.DISPATCH_CAR_TIME, 'yyyy-mm-dd hh24:mi:ss') dispatchCarTime,
            TE.LOAD_TYPE loadType,
            TO_CHAR(TE.ARRIVE_TIME, 'yyyy-mm-dd hh24:mi:ss') arriveTime
        FROM T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TE
            ON T.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE T.IS_DEL = 1
        <choose>
            <when test="boTransTaskId != null and boTransTaskId.length > 0">
                AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
            </when>
            <otherwise>
                AND T.BO_TRANS_TASK_ID = -1
            </otherwise>
        </choose>
    </select>

    <select id="queryTransTaskListCollect" parameterType="com.wtyt.bo.bean.request.Req1735209Bean" resultType="BoCollectBean">
        SELECT
            COUNT(*) transTaskCount,
            SUM(NVL(BT.ALL_FREIGHT, 0)) allFreight,
            SUM(NVL(BT.SERVICE_FEE, 0)) serviceFee,
            SUM(NVL(BT.DATA_SERVICE_FEE, 0)) dataServiceFee,
            SUM(CASE
                    WHEN BT.INS_STATE = 1
                    THEN NVL(BT.INS_FEE, 0)
                    ELSE 0
                END
            ) insFee,
            BT.GOODS_AMOUNT_TYPE goodsAmountType,
            SUM(CASE
                    WHEN BT.TRANSPORT_TYPE = 1 or BT.LOADING_TONNAGE>0 or BT.UNLOADING_TONNAGE>0
                    THEN 0
                    ELSE NVL(BT.GOODS_AMOUNT, 0)
                END
            ) goodsAmount,
            SUM(NVL(BT.LOADING_TONNAGE, 0)) loadingTonnage,
            SUM(NVL(BT.UNLOADING_TONNAGE, 0)) unloadingTonnage,
            COUNT(CASE
                    WHEN BT.TRANSPORT_TYPE = 1
                    THEN 1
                    ELSE NULL
                END
            ) dzCount
        FROM
        <if test="tabState!=13">
            <include refid="fromTableForPC"/>
        </if>
        <if test="tabState==13">
            <include refid="fromTableForPCUnDel"/>
        </if>
        <where>
            <!--已删除列表-->
            <if test="tabState!=13">
                <include refid="queryTransportTaskTableCondition" />
            </if>
            <if test="tabState==13">
                AND bt.IS_DEL = 1
                <include refid="queryTransportTaskTableConditionUnDel" />
            </if>
            <include refid="tabStateQueryCondition" />
        </where>
        GROUP BY BT.GOODS_AMOUNT_TYPE
    </select>

    <select id="queryDispatchTransTaskListCollect" parameterType="com.wtyt.bo.bean.request.Req1735209Bean" resultType="BoCollectBean">
        SELECT
            COUNT(*) transTaskCount,
            SUM(NVL(TTA.ALL_FREIGHT, 0)) allFreight,
            SUM(CASE
                    WHEN TTA.INS_STATE = 1
                    THEN NVL(TTA.INS_FEE, 0)
                    ELSE 0
                END
            ) insFee,
            BT.GOODS_AMOUNT_TYPE goodsAmountType,
            SUM(CASE
                    WHEN BT.TRANSPORT_TYPE = 1 or BT.LOADING_TONNAGE>0 or BT.UNLOADING_TONNAGE>0
                    THEN 0
                    ELSE NVL(BT.GOODS_AMOUNT, 0)
                END
            ) goodsAmount,
            SUM(NVL(BT.LOADING_TONNAGE, 0)) loadingTonnage,
            SUM(NVL(BT.UNLOADING_TONNAGE, 0)) unloadingTonnage,
            COUNT(CASE
                    WHEN BT.TRANSPORT_TYPE = 1
                    THEN 1
                    ELSE NULL
                END
            ) dzCount
        FROM
        <include refid="taskRightTempTable"/> TEMP
        JOIN T_BO_TRANS_TASK BT
            ON TEMP.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND BT.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA BTE
            ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND BTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TTA
            ON TTA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND TTA.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR
            ON NR.BO_TRANS_NODE_RECORD_ID = BTE.DISPATCH_CAR_RECORD_ID
            AND NR.IS_DEL = 0
        LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE TDG
            ON TDG.BO_TASK_DRIVER_GUARANTEE_ID = BTE.BO_TASK_DRIVER_GUARANTEE_ID
            AND TDG.IS_DEL = 0
        WHERE
        <include refid="queryTransportTaskTableCondition" />
        <include refid="tabStateQueryConditionForDispatch" />
        GROUP BY BT.GOODS_AMOUNT_TYPE
    </select>

    <sql id="orderByCondition">
        <choose>
            <when test="tabState == 3">
                ORDER BY bte.ARRIVE_TIME ASC,bt.CREATED_TIME DESC
            </when>
            <when test="tabState == 11">
                ORDER BY DECODE(bte.USER_FREIGHT_APPROVAL_STATUS,1,1,2,2,3,4,4,3) ASC,bt.CREATED_TIME DESC
            </when>
            <otherwise>
                ORDER BY bt.CREATED_TIME DESC
            </otherwise>
        </choose>
    </sql>

    <sql id="tabStateQueryCondition" >
        <if test="tabState!=0">
            <choose>
                <!-- 异常数据 -->
                <when test="tabState==7">
                    AND (
                        EXISTS (
                            SELECT 1 FROM T_BO_TRANS_NODE_ALARM a
                            WHERE bt.BO_TRANS_TASK_ID =a.BO_TRANS_TASK_ID
                            AND A.IS_DEL=0
                            AND A.NODE_DATA_TYPE IN (
                                SELECT NODE_DATA_TYPE
                                FROM T_BO_ROLE_ALARM_TYPE_REL
                                WHERE ROLE_ID = 12000
                                AND IS_DEL = 0
                            )
                            AND A.ALARM_TYPE =1
                        )
                        OR
                            EXISTS (
                                SELECT 1 FROM T_BO_TRANS_NODE_RECORD R
                                WHERE bt.BO_TRANS_TASK_ID =R.BO_TRANS_TASK_ID
                                AND R.IS_DEL=0
                                AND R.OVER_TIME >0
                        )
                    )
                </when>
                <when test="tabState==11">
                    AND bte.USER_FREIGHT_APPROVAL_STATUS != 0
                </when>
                <when test="tabState==4">
                    AND bt.STATE = 1
                </when>
                <when test="tabState==5">
                    AND bt.STATE = 2
                    AND NVL(bt.PAY_STATE,0) = 0
                </when>
                <when test="tabState==10">
                    AND bt.STATE = 2
                    AND bt.PAY_STATE NOT IN (0,2)
                </when>
                <when test="tabState==6">
                    AND bt.STATE = 2
                    AND bt.PAY_STATE = 2
                </when>
                <when test="tabState==12">
                    AND EXISTS (
                        SELECT
                            1
                        FROM
                            T_BO_TRANS_NODE_ALARM tna
                        WHERE
                            tna.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                            AND tna.IS_DEL = 0
                            <if test="trackStopType!=null and trackStopType!='' and trackStopType==1">
                                AND tna.ALARM_PROCESS_RESULT = 0
                            </if>
                            AND tna.NODE_DATA_TYPE = 14
                    )
                    <if test="trackStopType!=null and trackStopType!='' and trackStopType==0">
                        AND NOT EXISTS (
                            SELECT
                                1
                            FROM
                            T_BO_TRANS_NODE_ALARM tna
                            WHERE
                            tna.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                            AND tna.IS_DEL = 0
                            AND tna.ALARM_PROCESS_RESULT = 0
                            AND tna.NODE_DATA_TYPE = 14
                        )
                    </if>
                </when>
                <when test="tabState==9">
                    <if test="nodeIdQueryList !=null and nodeIdQueryList.size()>0">
                        AND bt.NODE_ID IN
                        <foreach collection="nodeIdQueryList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                </when>
                <when test="tabState==13">
                </when>
                <otherwise>
                    AND bt.STATE = 0
                    <if test="nodeIdQueryList !=null and nodeIdQueryList.size()>0">
                        AND bt.NODE_ID IN
                        <foreach collection="nodeIdQueryList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="tabStateQueryConditionForDispatch" >
        <if test="tabState!=0">
            <choose>
                <!-- 异常数据 -->
                <when test="tabState==7">
                    AND (
                    EXISTS (
                    SELECT 1 FROM T_BO_TRANS_NODE_ALARM a
                    WHERE bt.BO_TRANS_TASK_ID =a.BO_TRANS_TASK_ID
                    AND A.IS_DEL=0
                    AND A.NODE_DATA_TYPE IN (
                    SELECT NODE_DATA_TYPE
                    FROM T_BO_ROLE_ALARM_TYPE_REL
                    WHERE ROLE_ID = 12000
                    AND IS_DEL = 0
                    )
                    AND A.ALARM_TYPE =1
                    )
                    OR
                    EXISTS (
                    SELECT 1 FROM T_BO_TRANS_NODE_RECORD R
                    WHERE bt.BO_TRANS_TASK_ID =R.BO_TRANS_TASK_ID
                    AND R.IS_DEL=0
                    AND R.OVER_TIME >0
                    )
                    )
                </when>
                <when test="tabState==4">
                    AND bt.STATE = 1
                </when>
                <when test="tabState==5">
                    AND bt.STATE = 2
                    AND NVL(tta.PAY_STATE,0) = 0
                </when>
                <when test="tabState==10">
                    AND bt.STATE = 2
                    AND tta.PAY_STATE NOT IN (0,2)
                </when>
                <when test="tabState==6">
                    AND bt.STATE = 2
                    AND tta.PAY_STATE = 2
                </when>
                <otherwise>
                    AND bt.STATE = 0
                    <if test="nodeIdQueryList !=null and nodeIdQueryList.size()>0">
                        AND tta.NODE_ID IN
                        <foreach collection="nodeIdQueryList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="taskExportField">
        NVL(bt.TAX_WAYBILL_ID,tta.TAX_WAYBILL_ID) taxWaybillId,
        bt.TAX_WAYBILL_NO taxWaybillNo,
        bt.BO_TRANS_TASK_ID boTransTaskId,
        bt.ORG_ID orgId,
        bt.TRANS_MODE transMode,
        bt.LOADING_PLACE_NAME loadingPlaceName,
        bt.UNLOADING_PLACE_NAME unloadingPlaceName,
        bt.MILEAGE mileage,
        bt.GOODS_NAME goodsName,
        bt.GOODS_AMOUNT goodsAmount,
        bt.GOODS_AMOUNT_TYPE goodsAmountType,
        bt.XCY_USER_ID xcyUserId,
        bt.DRIVER_NAME driverName,
        bt.MOBILE_NO mobileNo,
        bt.CART_BADGE_NO cartBadgeNo,
        bt.CART_BADGE_COLOR cartBadgeColor,
        bt.CAPACITY_TYPE_NAME capacityTypeName,
        TO_CHAR(bt.START_TIME, 'yyyy-MM-dd Hh24:mi:ss') startTime,
        TO_CHAR(bt.END_TIME, 'yyyy-MM-dd Hh24:mi:ss') endTime,
        TO_CHAR(NVL(bt.ALL_FREIGHT, 0), '*********90.00') allFreight,
        TO_CHAR(NVL(bt.SERVICE_FEE, 0), '*********90.00') serviceFee,
        TO_CHAR(NVL(bt.DATA_SERVICE_FEE, 0), '*********90.00') dataServiceFee,
        TO_CHAR(NVL(bt.USER_FREIGHT, 0), '*********90.00') userFreight,
        TO_CHAR(NVL(bt.PREPAYMENTS, 0) , '*********90.00') prepayments,
        TO_CHAR(NVL(bt.BACK_FEE, 0) , '*********90.00') backFee,
        CASE
        WHEN BT.INS_STATE = 1
        THEN TO_CHAR(NVL(bt.ALL_FREIGHT, 0) + NVL(bt.SERVICE_FEE, 0) + NVL(bt.DATA_SERVICE_FEE, 0) + NVL(bt.INS_FEE, 0), '*********90.00')
        ELSE TO_CHAR(NVL(bt.ALL_FREIGHT, 0) + NVL(bt.SERVICE_FEE, 0) + NVL(bt.DATA_SERVICE_FEE, 0), '*********90.00')
        END payableFreight,
        TO_CHAR(NVL(bt.PREPAYMENTS_OILCARD , 0), '*********90.00') prepaymentsOilcard,
        TO_CHAR(NVL(bt.PREPAYMENTS_GASCARD , 0), '*********90.00') prepaymentsGascard,
        TO_CHAR(bt.PREPAYMENTS_BUY_OIL, '*********90.00') prepaymentsBuyOil,
        TO_CHAR(bt.PREPAYMENTS_BUY_GAS, '*********90.00') prepaymentsBuyGas,
        NVL(bt.PAY_STATE,0) payState,
        TO_CHAR(bt.UNIT_PRICE, '*********90.00') unitPrice,
        NVL(bt.ADVANCE_PAY_STATE, 0) advancePayState,
        TO_CHAR(NVL(bt.FREIGHT_INCR , 0), '*********90.00') freightIncr,
        TO_CHAR(NVL(bt.LOSS_FEE , 0), '*********90.00') lossFee,
        TO_CHAR(bt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') createdTime,
        bt.WB_ITEM wbItem,
        TO_CHAR(NVL(bt.GOODS_COST, 0), '*********90.00') goodsCost,
        bt.START_PROVINCE_NAME startProvinceName,
        bt.START_CITY_NAME startCityName,
        bt.START_COUNTY_NAME startCountyName,
        bt.END_PROVINCE_NAME endProvinceName,
        bt.END_CITY_NAME endCityName,
        bt.END_COUNTY_NAME endCountyName,
        bt.TRANSPORT_TYPE transportType,
        bt.NODE_ID nodeId,
        bt.state state,
        bt.SETTLE_MODE settleMode,
        NVL(bt.TRANS_PATTERN, 1) transPattern,
        bt.BO_BUSINESS_LINE_ID boBusinessLineId,
        bte.CART_TYPE cartType,
        bte.CART_LENGTH cartLength,
        bte.LOADING_ADDRESS_NAME loadingAddressName,
        bte.UNLOADING_ADDRESS_NAME unloadingAddressName,
        TO_CHAR(bte.ARRIVE_END_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveEndTime,
        TO_CHAR(bte.HYB_RECEIVED_TIME, 'yyyy-MM-dd Hh24:mi:ss') hybReceivedTime,
        bte.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
        bte.UNLOAD_TIME_FORMAT unloadTimeFormat,
        TO_CHAR(C1.DEAD_LINE_TIME, 'YYYY-MM-DD HH24:MI') unLoadTime,
        TO_CHAR(bte.ARRIVE_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveTime,
        bte.PAPER_RECEIPT_STATUS paperReceiptStatus,
        TO_CHAR(bte.DISPATCH_CAR_TIME, 'yyyy-MM-dd Hh24:mi:ss') dispatchCarTime,
        bte.SERVICE_REQUIRE serviceRequire,
        bte.TRANS_VOUCHER transVoucher,
        bte.SALESMAN_NAME salesmanName,
        bte.SETTLE_ISSUE settleIssue,
        bte.BO_VOUCHER_CHECK_STATE boVoucherCheckState,
        NR.USER_ID dispatchCarUserId,
        CASE
        WHEN bt.LOADING_TONNAGE - TRUNC(bt.LOADING_TONNAGE) = 0 THEN
        TO_CHAR(TRUNC(bt.LOADING_TONNAGE), '*********90')
        ELSE
        TO_CHAR(bt.LOADING_TONNAGE, '*********90.9999')
        END loadingTonnage,
        CASE
        WHEN bt.UNLOADING_TONNAGE - TRUNC(bt.UNLOADING_TONNAGE) = 0 THEN
        TO_CHAR(TRUNC(bt.UNLOADING_TONNAGE), '*********90')
        ELSE
        TO_CHAR(bt.UNLOADING_TONNAGE, '*********90.9999')
        END unloadingTonnage,
        TO_CHAR(bte.LOSS_AMOUNT, '*********90.00') lossAmount,
        CASE
        WHEN tdg.GUARANTEE_AMOUNT - TRUNC(tdg.GUARANTEE_AMOUNT) = 0 THEN
        TO_CHAR(TRUNC(tdg.GUARANTEE_AMOUNT), '*********90')
        ELSE
        TO_CHAR(tdg.GUARANTEE_AMOUNT, '*********90.9999')
        END driverGuaranteeAmount,
        tdg.GUARANTEE_STATE driverGuaranteeState,
        tdg.GUARANTEE_CHANNEL driverGuaranteeChannel,
        NVL(bt.SETTLE_TYPE, bt.SETTLE_MODE) settleType,
        BT.RECEIVER receiver,
        BT.RECEIVER_MOBILE receiverMobile,
        TO_CHAR(NVL(BT.INS_FEE , 0), '*********90.00') insFee,
        BT.TASK_NOTE taskNote,
        BT.CUSTOMER_REMARK customerRemark,
        BTE.CUSTOMER_ORDER_NO customerOrderNo,
        TO_CHAR(BTE.PAPER_ESTIMATED_DELIVERY_TIME, 'yyyy-MM-dd Hh24:mi:ss') paperEstimatedDeliveryTime,
        TO_CHAR(BTE.ELECTRONIC_RECEIPT_AUDIT_TIME, 'yyyy-MM-dd Hh24:mi:ss') electronicReceiptAuditTime,
        BTE.LINE_TIME_REQUIRE lineTimeRequire,
        BTE.PAY_NAME payName,
        BTE.PAY_BANK_NAME payBankName,
        BTE.PROVINCE province,
        BTE.CITY_NAME cityName,
        BTE.PAY_MOBILE_NO payMobileNo,
        BTE.PAY_ID_CARD payIdCard,
        BTE.TRAN_REQUIRE tranRequire,
        BTE.CART_TONNAGE cartTonnage,
        BTE.OIL_CARD_NO oilCardNo,
        BTE.CUSTOMIZE_NO customizeNo,
        TO_CHAR(BTE.RECEIPT_RECEIVE_TIME, 'yyyy-MM-dd Hh24:mi:ss') receiptReceiveTime,
        TO_CHAR(BTE.USER_FREIGHT_APPROVAL_TIME, 'yyyy-MM-dd Hh24:mi:ss') userFreightApprovalTime,
        BTE.EXPRESS_NUMBER expressNumber,
        BTE.PAYER payer,
        BTE.LOADING_ROUGH_WEIGHT loadingRoughWeight,
        BTE.UNLOADING_ROUGH_WEIGHT unloadingRoughWeight,
        BTE.LOADING_TARE loadingTare,
        BTE.UNLOADING_TARE unloadingTare,
        TO_CHAR(BTE.ACTUAL_LOAD_TIME, 'yyyy-MM-dd Hh24:mi:ss') actualLoadTime,
        TO_CHAR(BTE.ACTUAL_UNLOAD_TIME, 'yyyy-MM-dd Hh24:mi:ss') actualUnloadTime,
        BTE.BOX_NO boxNo,
        BTE.DEDUCT_TONNAGE deductTonnage,
        TO_CHAR(BTE.LOADING_GOODS_TIME, 'yyyy-MM-dd Hh24:mi:ss') loadingGoodsTime,
        TO_CHAR(BTE.ARRIVE_CONFIRM_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveConfirmTime,
        TO_CHAR(BTE.CONTACT_DRIVER_TIME, 'yyyy-MM-dd Hh24:mi:ss') contactDriverTime,
        TO_CHAR(bte.LOSS_UNIT_PRICE, '*********90.00') lossUnitPrice,
        CASE WHEN bte.LOSS_ACTUAL_TONNAGE = FLOOR(bte.LOSS_ACTUAL_TONNAGE) THEN  TO_CHAR(bte.LOSS_ACTUAL_TONNAGE, '*********90') ELSE TO_CHAR(bte.LOSS_ACTUAL_TONNAGE, '*********90.9999') END lossActualTonnage,
        CASE WHEN bte.ALLOW_LOSS_WEIGHT = FLOOR(bte.ALLOW_LOSS_WEIGHT) THEN  TO_CHAR(bte.ALLOW_LOSS_WEIGHT, '*********90') ELSE TO_CHAR(bte.ALLOW_LOSS_WEIGHT, '*********90.9999') END allowLossWeight,
        bte.LOSS_CONFIG_VALUE lossConfigValue,
        bte.ALLOW_GAIN_FLAG allowGainFlag,
        bte.CPD_POOL_GROUP_NAME cpdPoolGroupName,
        tta.PAY_STATE diffPayState,
        tta.SETTLE_MODE diffSettleMode,
        TO_CHAR(BT.PAY_OVER_TIME, 'yyyy-MM-dd Hh24:mi:ss') payOverTime,
        BTE.BELONG_DISPATCHER_ID belongDispatcherId,
        BTE.TRAILER_CART_BADGE_NO trailerCartBadgeNo
    </sql>

    <select id="countTransportTaskNodeIdGroupNoYc" resultType="com.wtyt.tt.bean.TaskTabCountBean"
            parameterType="com.wtyt.bo.bean.request.Req1735210Bean">
        SELECT STATE state,PAY_STATE payState,NODE_ID nodeId,COUNT(*) count
            FROM
        (
            SELECT
                <![CDATA[
                    bt.STATE,CASE WHEN bt.STATE IN (0,1) THEN NULL WHEN bt.ORG_ID = #{orgId} THEN NVL(bt.PAY_STATE,0)
                    ELSE NVL(a.PAY_STATE,0) END PAY_STATE,CASE WHEN bt.STATE IN (1,2) THEN NULL WHEN bt.ORG_ID = #{orgId} THEN bt.NODE_ID
                    ELSE a.NODE_ID END NODE_ID
                ]]>
            FROM
                <include refid="fromTableForApp"></include>
            WHERE
                <include refid="taskQueryWhereSql"/>
                AND (bt.TRANS_MODE!=2 OR bte.DISPATCH_CAR_RECORD_ID IS NOT NULL)
        )
        GROUP BY STATE,PAY_STATE,NODE_ID
    </select>

    <select id="countTransportTaskNodeIdGroupNoYc_new" resultType="com.wtyt.tt.bean.TaskTabCountBean"
            parameterType="com.wtyt.bo.bean.request.Req1735210Bean">
        SELECT TRANSPORT_NODE transportNode, STATE state,PAY_STATE payState,NODE_ID nodeId,COUNT(*) count
            FROM
        (
            SELECT
                <![CDATA[
                    BTE.TRANSPORT_NODE,bt.STATE,CASE WHEN bt.STATE IN (0,1) THEN NULL WHEN bt.ORG_ID = #{orgId} THEN NVL(bt.PAY_STATE,0)
                    ELSE NVL(a.PAY_STATE,0) END PAY_STATE,CASE WHEN bt.STATE IN (1,2) THEN NULL WHEN bt.ORG_ID = #{orgId} THEN bt.NODE_ID
                    ELSE a.NODE_ID END NODE_ID
                ]]>
            FROM
                <include refid="fromTableForApp"></include>
            WHERE
                <include refid="taskQueryWhereSql"/>
                AND (bt.TRANS_MODE!=2 OR bte.DISPATCH_CAR_RECORD_ID IS NOT NULL)
        )
        GROUP BY TRANSPORT_NODE, STATE,PAY_STATE,NODE_ID
    </select>

    <select id="queryYcNotDispatchTaskIds" resultType="java.lang.String" parameterType="com.wtyt.bo.bean.request.Req1735210Bean">
        SELECT
            bt.BO_TRANS_TASK_ID
        FROM
            <include refid="fromTableForApp"></include>
        WHERE
            <include refid="taskQueryWhereSql"/>
            AND bt.TRANS_MODE=2 AND bte.DISPATCH_CAR_RECORD_ID IS NULL
    </select>

    <select id="countTransportTaskNodeIdGroup" resultType="com.wtyt.tt.bean.TaskTabCountBean" parameterType="com.wtyt.bo.bean.request.Req1735210Bean">
        SELECT
            bt.STATE state,
            bt.PAY_STATE payState,
            bt.NODE_ID nodeId,
            COUNT(*) count
        FROM
            <include refid="fromTableForPC"></include>
        WHERE
            <include refid="queryTransportTaskTableCondition"/>
        GROUP BY bt.STATE,bt.PAY_STATE,bt.NODE_ID
    </select>

    <select id="countTransportTaskNotPrint" resultType="java.lang.Integer" parameterType="com.wtyt.bo.bean.request.Req1735210Bean">
        SELECT
            COUNT(*)
        FROM
            <include refid="fromTableForPC"></include>
        WHERE
        <include refid="queryTransportTaskTableCondition"/>
        <if test="queryNodeIdList != null and queryNodeIdList.size()>0">
            AND bt.NODE_ID IN
            <foreach collection="queryNodeIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        AND bt.NODE_ID IS NOT null
    </select>

    <select id="countTrackStop" resultType="java.lang.Integer" parameterType="com.wtyt.bo.bean.request.Req1735210Bean">
        SELECT
            COUNT(*)
        FROM
        <include refid="fromTableForPC"></include>
        WHERE
        <include refid="queryTransportTaskTableCondition"/>
        AND EXISTS (
            SELECT
                1
            FROM
                T_BO_TRANS_NODE_ALARM tna
            WHERE
                tna.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                AND tna.IS_DEL = 0
                <if test="trackStopType!=null and trackStopType!='' and trackStopType==1">
                    AND tna.ALARM_PROCESS_RESULT = 0
                </if>
                AND tna.NODE_DATA_TYPE = 14
        )
    </select>

    <select id="countTaskUserFreightStatus" resultType="com.wtyt.commons.bean.StringIntegerPair" parameterType="com.wtyt.bo.bean.request.Req1735210Bean">
        SELECT
            bte.USER_FREIGHT_APPROVAL_STATUS key,COUNT(*) value
        FROM
            <include refid="fromTableForPC"></include>
        WHERE
        <include refid="queryTransportTaskTableCondition"/>
        AND bte.USER_FREIGHT_APPROVAL_STATUS != 0
        GROUP BY bte.USER_FREIGHT_APPROVAL_STATUS
    </select>

    <select id="countErrorTransportTask" parameterType="com.wtyt.bo.bean.request.Req1735210Bean" resultType="int">
        SELECT
            count(*)
        FROM
            <include refid="fromTableForPC"></include>
        WHERE
        <include refid="queryTransportTaskTableCondition" />
        AND (
            EXISTS (
                SELECT 1 FROM T_BO_TRANS_NODE_ALARM a
                WHERE bt.BO_TRANS_TASK_ID =a.BO_TRANS_TASK_ID
                AND A.IS_DEL=0
                AND A.NODE_DATA_TYPE IN (
                    SELECT NODE_DATA_TYPE
                    FROM T_BO_ROLE_ALARM_TYPE_REL
                    WHERE ROLE_ID = 12000
                    AND IS_DEL = 0
                )
                AND A.ALARM_TYPE =1
            )
            OR
            EXISTS (
                SELECT 1 FROM T_BO_TRANS_NODE_RECORD R
                WHERE bt.BO_TRANS_TASK_ID =R.BO_TRANS_TASK_ID
                AND R.IS_DEL=0
                AND R.OVER_TIME >0
            )
        )
    </select>

    <select id="countDelTransportTask" parameterType="com.wtyt.bo.bean.request.Req1735210Bean" resultType="int">
        SELECT
            count(*)
        FROM
            <include refid="fromTableForPCUnDel"></include>
        <where>
            bt.IS_DEL = 1
            <include refid="queryTransportTaskTableConditionUnDel" />
        </where>
    </select>

    <select id="queryDetailByBoTransTaskId" resultType="com.wtyt.bo.bean.response.Resp5329452Bean"
            parameterType="java.lang.String">
        WITH RECORD_CTE AS (
                 SELECT
                     C2.BO_TRANS_TASK_ID,
                     C2.CREATED_TIME,
                     ROW_NUMBER() OVER(PARTITION BY C2.BO_TRANS_TASK_ID ORDER BY C2.CREATED_TIME DESC) AS RN
                 FROM
                     T_BO_TRANS_NODE_RECORD C2
                 WHERE
                     C2.NODE_ID = 500
                   AND C2.IS_DEL = 0
             )
        SELECT
            bt.TAX_WAYBILL_ID taxWaybillId,
            bt.TAX_WAYBILL_NO taxWaybillNo,
            TO_CHAR(bte.ARRIVE_END_TIME, 'MM-DD HH24:MI') arriveEndTime,
            TO_CHAR(C2.CREATED_TIME, 'MM-DD HH24:MI') actualPresentTime,
            bt.GOODS_AMOUNT_TYPE goodsAmountType,
            bt.TRANSPORT_TYPE transportType,
            bt.NODE_ID nodeId,
            bt.TRANS_PATTERN transPattern,
            CASE
                WHEN bt.TRANSPORT_TYPE = 1 THEN
                    (CASE
                         WHEN bt.UNLOADING_TONNAGE - TRUNC(bt.UNLOADING_TONNAGE) = 0 THEN
                             TO_CHAR(TRUNC(bt.UNLOADING_TONNAGE), '*********90')
                         ELSE
                             TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM TO_CHAR(bt.UNLOADING_TONNAGE, '*********90.9999')))
                        END)
                ELSE
                    (CASE
                         WHEN bt.GOODS_AMOUNT - TRUNC(bt.GOODS_AMOUNT)= 0 THEN
                             TO_CHAR(TRUNC(bt.GOODS_AMOUNT), '*********90')
                         ELSE
                             TRIM(TRAILING '.' FROM TRIM(TRAILING '0' FROM TO_CHAR(bt.GOODS_AMOUNT, '*********90.9999')))
                        END)
                END
                AS goodsAmount,
            bt.GOODS_NAME goodsName,
            CASE
                WHEN bt.TRANSPORT_TYPE = 1 THEN
                    bt.LOADING_PLACE_NAME
                ELSE
                    bt.START_CITY_NAME || bt.START_COUNTY_NAME
                END startPlace,
            CASE
                WHEN bt.TRANSPORT_TYPE = 1 THEN
                    bt.UNLOADING_PLACE_NAME
                ELSE
                    bt.END_CITY_NAME || bt.END_COUNTY_NAME
                END endPlace
        FROM
            T_BO_TRANS_TASK bt
                LEFT JOIN
            T_BO_TRANS_TASK_EXTRA bte
            ON bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID AND bte.IS_DEL = 0
                LEFT JOIN
            RECORD_CTE C2
            ON
                        C2.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                    AND C2.RN = 1
        WHERE
            bt.IS_DEL = 0
          AND
            bt.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="queryDashboardTaskPage" resultType="com.wtyt.bo.bean.response.Resp5329455SubBean"
            parameterType="com.wtyt.bo.bean.request.Req5329455Bean">
        SELECT
        bt.TAX_WAYBILL_ID taxWaybillId,
        bt.TAX_WAYBILL_NO taxWaybillNo,
        bt.ORG_ID orgId,
        bt.PAY_STATE payState,
        bt.NODE_ID nodeId,
        bt.HYB_STATE hybState,
        bt.STATE state,
        TO_CHAR(bt.CREATED_TIME,'yyyy-MM-dd Hh24:mi:ss') createdTime,
        bt.DRIVER_NAME driverName,
        bt.MOBILE_NO mobileNo,
        bt.CART_BADGE_NO cartBadgeNo,
        bt.LOADING_PLACE_NAME loadingAddressName,
        bt.UNLOADING_PLACE_NAME unLoadingAddressName,
        TO_CHAR(NVL(bt.USER_FREIGHT,0),'*********90.00') userFreight,
        TO_CHAR(NVL(bt.ALL_FREIGHT,0),'*********90.00') allFreight,
        TO_CHAR(NVL(bt.PREPAYMENTS,0),'*********90.00') prepayments,
        TO_CHAR(NVL(tf.CONFIG_VALUE, 0) , '*********90.00') useOilFreight,
        TO_CHAR(NVL(bt.BACK_FEE,0),'*********90.00') backFee,
        TO_CHAR(NVL(bt.PREPAYMENTS_OILCARD ,0),'*********90.00') prepaymentsOilCrad,
        TO_CHAR(NVL(bt.PREPAYMENTS_GASCARD ,0),'*********90.00') prepaymentsGasCard,
        bte.TRANS_VOUCHER transVoucher,
        NVL(bt.TRANS_PATTERN,1) transPattern,
        bt.BO_TRANS_TASK_ID boTransTaskId,
        CASE
        WHEN bt.LOADING_TONNAGE - TRUNC(bt.LOADING_TONNAGE) = 0 THEN
        TO_CHAR(TRUNC(bt.LOADING_TONNAGE), '*********90')
        ELSE
        TO_CHAR(bt.LOADING_TONNAGE, '*********90.9999')
        END loadingTonnage,
        CASE
        WHEN bt.UNLOADING_TONNAGE - TRUNC(bt.UNLOADING_TONNAGE) = 0 THEN
        TO_CHAR(TRUNC(bt.UNLOADING_TONNAGE), '*********90')
        ELSE
        TO_CHAR(bt.UNLOADING_TONNAGE, '*********90.9999')
        END unloadingTonnage,
        bt.GOODS_AMOUNT_TYPE goodsAmountType,
        bt.GOODS_NAME goodsName,
        bte.SETTLE_ISSUE settleIssue,
        bt.SETTLE_MODE settleMode,
        bt.TRANSPORT_TYPE transportType,
        bt.CAPACITY_TYPE capacityType,
        bt.CAPACITY_TYPE_NAME capacityTypeName,
        <![CDATA[
        CASE
		WHEN (bt.UNLOADING_TONNAGE IS NULL OR bt.UNLOADING_TONNAGE = 0) THEN 0
		ELSE
			CASE
				WHEN bt.LOADING_TONNAGE IS NOT NULL AND bt.UNLOADING_TONNAGE IS NOT NULL THEN
					CASE
						WHEN (bt.LOADING_TONNAGE - bt.UNLOADING_TONNAGE) < 0 THEN (bt.UNLOADING_TONNAGE - bt.LOADING_TONNAGE)
						ELSE (bt.LOADING_TONNAGE - bt.UNLOADING_TONNAGE)
					END
				ELSE
					NULL
			END
	    END AS damageTonnage
        ]]>
        FROM
        T_BO_TRANS_TASK bt LEFT JOIN T_BO_TRANS_TASK_EXTRA bte ON bte.BO_TRANS_TASK_ID =bt.BO_TRANS_TASK_ID AND bte.IS_DEL =0
        LEFT JOIN T_BO_TRANS_TASK_FEE tf ON tf.BO_TRANS_TASK_ID =bt.BO_TRANS_TASK_ID AND tf.IS_DEL =0 AND tf.CONFIG_KEY ='fuelCostFee'
        WHERE
        <include refid="queryDashboardTaskTableCondition" />
        <if test="nodeIdQueryList !=null and nodeIdQueryList.size()>0">
            AND (bt.NODE_ID IN
            <foreach collection="nodeIdQueryList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="transPattern1 != null and transPattern1 != ''">
                AND bt.TRANS_PATTERN = #{transPattern1}
            </if>
            <if test="orNodeIdQueryList != null and orNodeIdQueryList.size() > 0">
                OR bt.NODE_ID IN
                <foreach collection="orNodeIdQueryList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                <if test="transPattern2 != null and transPattern2 != ''">
                    AND bt.TRANS_PATTERN = #{transPattern2}
                </if>
            </if>
            )
        </if>
        <if test="filterNodeIdList !=null and filterNodeIdList.size()>0">
            AND bt.NODE_ID NOT IN
            <foreach collection="filterNodeIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="receiveTimeTag != null and receiveTimeTag != '' and receiveTimeTag == 0">
            AND bte.HYB_RECEIVED_TIME is not null
        </if>
        <if test="firstReceiptTimeTag != null and firstReceiptTimeTag != '' and firstReceiptTimeTag == 0">
            AND bt.FIRST_RECEIPT_TIME is not null
        </if>
        <if test="firstReceiptTimeTag != null and firstReceiptTimeTag != '' and firstReceiptTimeTag == 1">
            AND bt.FIRST_RECEIPT_TIME is null
        </if>
        <!-- 异常数据 -->
        <if test="abnormalStateList != null and abnormalStateList.size()>0">
            AND EXISTS
            (
            SELECT 1 FROM T_BO_TRANS_NODE_ALARM a
            WHERE bt.BO_TRANS_TASK_ID = a.BO_TRANS_TASK_ID
            AND a.IS_DEL = 0
            AND a.NODE_DATA_TYPE IN
            <foreach collection="abnormalStateList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND a.ALARM_TYPE = 1
            AND (
            (a.NODE_DATA_TYPE = 14)
            OR
            (a.NODE_DATA_TYPE != 14 AND a.ALARM_PROCESS_RESULT = 0)
            )

            )
        </if>
        ORDER BY bt.CREATED_TIME DESC
    </select>

    <select id="queryTransportTaskPageAppV2" parameterType="com.wtyt.bo.bean.request.Req1735209Bean" resultType="com.wtyt.bo.bean.response.Resp5329410ItemBean">
        <include refid="queryTaskTransPortSql"/>
        <if test="tabState != null and tabState != ''">
            <if test="tabState == 0">
                ORDER BY bt.CREATED_TIME DESC
            </if>
            <if test="tabState == 1">
                ORDER BY bt.CREATED_TIME DESC
            </if>
            <if test="tabState == 2">
                ORDER BY CASE WHEN bte.ARRIVE_END_TIME IS NULL THEN 1 ELSE 0 END, bte.ARRIVE_END_TIME
            </if>
            <if test="tabState == 3">
                ORDER BY CASE WHEN bte.ARRIVE_TIME IS NULL THEN 1 ELSE 0 END, bte.ARRIVE_TIME
            </if>
            <if test="tabState == 4">
                ORDER BY CASE WHEN bt.START_TIME IS NULL THEN 1 ELSE 0 END, bt.START_TIME
            </if>
            <if test="tabState == 5 or tabState == 10">
                ORDER BY CASE WHEN bt.START_TIME IS NULL THEN 1 ELSE 0 END, bt.START_TIME DESC
            </if>
            <if test="tabState == 6">
                ORDER BY CASE WHEN bt.PAY_OVER_TIME IS NULL THEN 1 ELSE 0 END, bt.PAY_OVER_TIME DESC
            </if>
            <if test="tabState == 13">
                ORDER BY BT.CREATED_TIME DESC
            </if>
            <if test="tabState == 14">
                ORDER BY CASE WHEN BTE.TRANSPORT_NODE_TIME IS NULL THEN 1 ELSE 0 END, BTE.TRANSPORT_NODE_TIME
            </if>
            <if test="tabState == 15 or tabState == 16">
                ORDER BY CASE WHEN BTE.TRANSPORT_NODE_TIME IS NULL THEN 1 ELSE 0 END, BTE.TRANSPORT_NODE_TIME DESC
            </if>
        </if>
    </select>

    <select id="queryOtherTransportTaskCount" resultType="java.lang.Integer"
            parameterType="com.wtyt.bo.bean.request.Req1735209Bean">
        select count(*)
        FROM
        <include refid="taskListTableApp"></include>
        WHERE
        <include refid="taskQueryWhereSql"/>
        <if test="tabState != null and tabState != ''">
            <if test="tabState == 2">
                AND bte.ARRIVE_END_TIME IS NULL
            </if>
            <if test="tabState == 3">
                AND bte.ARRIVE_TIME IS NULL
            </if>
            <if test="tabState == 4">
                AND bt.START_TIME IS NULL
            </if>
            <if test="tabState == 5 or tabState == 10">
                AND bt.START_TIME IS NULL
            </if>
            <if test="tabState == 6">
                AND bt.PAY_OVER_TIME IS NULL
            </if>
            <if test="tabState == 14 or tabState == 15 or tabState == 16">
                AND BTE.TRANSPORT_NODE_TIME IS NULL
            </if>
        </if>
    </select>
    <select id="queryTaxWaybillIdsPageByPermission" parameterType="com.wtyt.bo.bean.request.Req1735209Bean" resultType="com.wtyt.common.rpc.bean.TaskBean">
        select * from (
            SELECT
            case when bt.OWNER_ORG_ID=#{orgId} then
              bt.TAX_WAYBILL_ID
            else tta.TAX_WAYBILL_ID end taxWaybillId
            FROM
            <include refid="taskRightTempTable"/> TEMP
            JOIN T_BO_TRANS_TASK bt
            ON TEMP.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE tta ON tta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID AND tta.IS_DEL = 0
            WHERE bt.IS_DEL = 0 AND (bt.tax_waybill_id is not null or tta.TAX_WAYBILL_ID is not null)
            <if test="lastModifiedTimeStart!=null and lastModifiedTimeStart!=''">
                AND BT.LAST_MODIFIED_TIME >= TO_DATE(#{lastModifiedTimeStart},'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="taxWaybillId != null and taxWaybillId != ''">
                AND (bt.TAX_WAYBILL_ID > #{taxWaybillId} or tta.TAX_WAYBILL_ID>#{taxWaybillId})
            </if>
        ) btmp where btmp.taxWaybillId is not null
    </select>

    <select id="queryTaxWaybillIdsByPermission" parameterType="com.wtyt.bo.bean.request.Req1735209Bean" resultType="com.wtyt.common.rpc.bean.TaskBean" fetchSize="10000">
        SELECT
            <choose>
                <when test="noSupplier!=null and noSupplier">
                    bt.TAX_WAYBILL_ID
                </when>
                <otherwise>
                    case when tta.ORG_ID=#{orgId} then
                    tta.TAX_WAYBILL_ID
                    else bt.TAX_WAYBILL_ID end
                </otherwise>
            </choose>
            taxWaybillId
        FROM
        <choose>
            <when test="noSupplier!=null and noSupplier">
                T_BO_TRANS_TASK bt
            </when>
            <otherwise>
                <include refid="taskRightTempTable"/> TEMP
                JOIN T_BO_TRANS_TASK bt
                ON TEMP.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            </otherwise>
        </choose>
        JOIN T_BO_TRANS_TASK_EXTRA bte ON bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID AND bte.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE tta ON tta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID AND tta.IS_DEL = 0
        WHERE bt.IS_DEL = 0
        <choose>
            <when test="noSupplier!=null and noSupplier">
                AND bt.ORG_ID = #{orgId}
                AND EXISTS (
                <include refid="taskRightConditionExist"></include>
                )
                AND  bt.TAX_WAYBILL_ID IS NOT NULL
            </when>
            <otherwise>
                AND (case when tta.ORG_ID = #{orgId} then tta.TAX_WAYBILL_ID else bt.TAX_WAYBILL_ID END IS NOT NULL)
            </otherwise>
        </choose>
        <if test="lastModifiedTimeStart!=null and lastModifiedTimeStart!=''">
            AND BT.LAST_MODIFIED_TIME >= TO_DATE(#{lastModifiedTimeStart},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="excludeTaskIds!=null and excludeTaskIds.size()>0">
            AND (
            bt.BO_TRANS_TASK_ID NOT IN
            <foreach collection="excludeTaskIds" index="index" open="(" close=")" item="id" separator=",">
                <if test="(index % 999) == 998"> -1) AND bt.BO_TRANS_TASK_ID NOT IN(</if>#{id}
            </foreach>
            )
        </if>
    </select>

    <select id="queryLatestTaxWaybillIdsByPermission" resultType="com.wtyt.common.rpc.bean.TaskBean">
        select * from (
            SELECT
                <choose>
                    <when test="noSupplier!=null and noSupplier">
                        bt.TAX_WAYBILL_ID
                    </when>
                    <otherwise>
                        case when tta.ORG_ID=#{orgId} then
                        tta.TAX_WAYBILL_ID
                        else bt.TAX_WAYBILL_ID end
                    </otherwise>
                </choose> taxWaybillId
                ,bt.BO_TRANS_TASK_ID boTransTaskId
                ,TO_CHAR(bt.CREATED_TIME,'yyyy-MM-dd') createdTime
            FROM
            <choose>
                <when test="noSupplier!=null and noSupplier">
                    T_BO_TRANS_TASK bt
                </when>
                <otherwise>
                    <include refid="taskRightTempTable"/> TEMP
                    JOIN T_BO_TRANS_TASK bt
                    ON TEMP.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                </otherwise>
            </choose>
            JOIN T_BO_TRANS_TASK_EXTRA bte ON bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID AND bte.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE tta ON tta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID AND tta.IS_DEL = 0
            WHERE bt.IS_DEL = 0
            <choose>
                <when test="noSupplier!=null and noSupplier">
                    AND bt.ORG_ID = #{orgId}
                    AND EXISTS (
                    <include refid="taskRightConditionExist"></include>
                    )
                    AND  bt.TAX_WAYBILL_ID IS NOT NULL
                </when>
                <otherwise>
                    AND (case when tta.ORG_ID = #{orgId} then tta.TAX_WAYBILL_ID else bt.TAX_WAYBILL_ID END IS NOT NULL)
                </otherwise>
            </choose>
            <if test="lastModifiedTimeStart!=null and lastModifiedTimeStart!=''">
                AND BT.LAST_MODIFIED_TIME >= TO_DATE(#{lastModifiedTimeStart},'YYYY-MM-DD HH24:MI:SS')
            </if>
            ORDER BY bt.CREATED_TIME DESC
        ) temp WHERE ROWNUM &lt;=#{pageSize}
    </select>

    <select id="findTransTaskInfoById" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.GOODS_NAME goodsName,
            T.ORG_ID orgId,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(T.LOSS_FEE, 0), '*********90.00') lossFee,
            TO_CHAR(NVL(T.FREIGHT_INCR, 0), '*********90.00') freightIncr,
            TO_CHAR(NVL(T.UNIT_PRICE, 0), '*********90.00') unitPrice,
            TO_CHAR(NVL(T.LOADING_TONNAGE, 0), '*********90.0000') loadingTonnage,
            TO_CHAR(NVL(T.UNLOADING_TONNAGE, 0), '*********90.0000') unloadingTonnage,
            T.TRANSPORT_LINE_ID transportLineId,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            TE.LOADING_ADDRESS_NAME loadingAddressName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.XCY_USER_ID xcyUserId,
            T.CREATED_USER_SYS_ROLE_TYPE sysRoleType,
            T.ORDER_CREATE_TYPE orderCreateType,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.DRIVER_NAME driverName,
            T.CREATED_USER_ID createdUserId,
            TE.CART_LENGTH cartLength,
            TE.CART_TYPE cartType,
            T.HYB_STATE hybState,
            T.TRANS_PATTERN transPattern,
            T.TRANS_MODE transMode,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TRANSPORT_TYPE transportType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            TO_CHAR(NVL(T.ALL_FREIGHT, 0), '*********90.00') allFreight,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            A.BO_TRANS_TASK_ALLOCATE_ID boTransTaskAllocateId,
            A.ORG_ID allocateOrgId,
            A.SETTLE_MODE allocateSettleMode,
            A.TAX_WAYBILL_ID allocateTaxWaybillId,
            T.RECEIVER_MOBILE receiverMobile,
            T.RECEIVER receiver,
            TE.TRANS_VOUCHER transVoucher,
            TO_CHAR(TE.ARRIVE_END_TIME, 'YYYY-MM-DD HH24:MI:SS') arriveEndTime,
            TE.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
            NVL(T.TAX_WAYBILL_ID, A.TAX_WAYBILL_ID) locTaxWaybillId,
            TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            T.CAPACITY_TYPE capacityType,
            T.CAPACITY_TYPE_NAME capacityTypeName,
            T.OPERATE_SCHEME operateScheme,
            T.IS_PARTAKE_OPERATE isPartakeOperate
        FROM
            T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TE ON
            T.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE
        <if test="isDel != null and isDel !=''">
            T.IS_DEL = #{isDel} AND
        </if>
        <choose>
            <when test="boTransTaskId != null and boTransTaskId !=''">
                T.BO_TRANS_TASK_ID = #{boTransTaskId}
            </when>
            <when test="taxWaybillId != null and taxWaybillId !=''">
                T.BO_TRANS_TASK_ID =
                (
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE TAX_WAYBILL_ID = #{taxWaybillId}
                UNION
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE  TAX_WAYBILL_ID=#{taxWaybillId} AND IS_DEL = 0
                )
            </when>
            <otherwise>
                1 = 0
            </otherwise>
        </choose>
    </select>

    <select id="getHistoryTemplate" resultType="com.wtyt.bo.bean.BoTransTaskHistoryBean">
        SELECT * FROM (
            SELECT * FROM (
                SELECT
                    T1.START_PROVINCE_NAME startProvinceName,
                    T1.START_CITY_NAME startCityName,
                    T1.START_COUNTY_NAME startCountyName,
                    T1.START_PROVINCE_CODE startProvinceCode,
                    T1.START_CITY_CODE startCityCode,
                    T1.START_COUNTY_CODE startCountyCode,
                    T1.START_LATITUDE startLatitude,
                    T1.START_LONGITUDE startLongitude,
                    T1.END_PROVINCE_NAME endProvinceName,
                    T1.END_CITY_NAME endCityName,
                    T1.END_COUNTY_NAME endCountyName,
                    T1.END_PROVINCE_CODE endProvinceCode,
                    T1.END_CITY_CODE endCityCode,
                    T1.END_COUNTY_CODE endCountyCode,
                    T1.END_LATITUDE endLatitude,
                    T1.END_LONGITUDE endLongitude,
                    T1.GOODS_AMOUNT goodsAmount,
                    T1.GOODS_AMOUNT_TYPE goodsAmountType,
                    T1.GOODS_NAME goodsName,
                    T2.LOADING_ADDRESS_NAME startAddressName,
                    T2.UNLOADING_ADDRESS_NAME endAddressName,
                    T1.CREATED_TIME createTime,
                    T1.WB_ITEM wbItem,
                    T1.CUSTOMER_REMARK customerRemark,
                    <if test="retrievalType == 2">
                        COUNT(1) OVER(PARTITION BY T1.START_PROVINCE_NAME, T1.START_CITY_NAME, T1.START_COUNTY_NAME, T2.LOADING_ADDRESS_NAME, T1.END_PROVINCE_NAME, T1.END_CITY_NAME, T1.END_COUNTY_NAME, T2.UNLOADING_ADDRESS_NAME, T1.GOODS_NAME) NUM,
                    </if>
                    ROW_NUMBER() OVER(PARTITION BY T1.START_PROVINCE_NAME, T1.START_CITY_NAME, T1.START_COUNTY_NAME, T2.LOADING_ADDRESS_NAME, T1.END_PROVINCE_NAME, T1.END_CITY_NAME, T1.END_COUNTY_NAME, T2.UNLOADING_ADDRESS_NAME, T1.GOODS_NAME ORDER BY T1.CREATED_TIME DESC) RN
                FROM T_BO_TRANS_TASK T1
                JOIN T_BO_TRANS_TASK_EXTRA T2
                    ON T2.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID AND T2.IS_DEL = 0
                WHERE T1.IS_DEL = 0
                <choose>
                    <when test="retrievalValue != null and retrievalValue.length > 0">
                        AND (
                            INSTR(T1.GOODS_NAME, #{retrievalValue}) > 0
                            OR INSTR(T1.START_PROVINCE_NAME || T1.START_CITY_NAME || T1.START_COUNTY_NAME || T2.LOADING_ADDRESS_NAME, #{retrievalValue}) > 0
                            OR INSTR(T1.END_PROVINCE_NAME || T1.END_CITY_NAME || T1.END_COUNTY_NAME || T2.UNLOADING_ADDRESS_NAME, #{retrievalValue}) > 0
                            OR INSTR(T1.WB_ITEM, #{retrievalValue}) > 0
                        )
                    </when>
                    <otherwise>
                        AND T1.START_PROVINCE_NAME IS NOT NULL
                        AND T1.START_CITY_NAME IS NOT NULL
                        AND T2.LOADING_ADDRESS_NAME IS NOT NULL
                        AND T1.END_PROVINCE_NAME IS NOT NULL
                        AND T1.END_CITY_NAME IS NOT NULL
                        AND T2.UNLOADING_ADDRESS_NAME IS NOT NULL
                        AND T1.GOODS_NAME IS NOT NULL
                    </otherwise>
                </choose>
                AND T1.START_PROVINCE_CODE IS NOT NULL
                AND T1.START_CITY_CODE IS NOT NULL
                AND T1.START_LATITUDE IS NOT NULL
                AND T1.START_LONGITUDE IS NOT NULL
                AND T1.END_PROVINCE_CODE IS NOT NULL
                AND T1.END_CITY_CODE IS NOT NULL
                AND T1.END_LONGITUDE IS NOT NULL
                AND T1.END_LATITUDE IS NOT NULL
                AND T1.CREATED_TIME > SYSDATE - 180
                AND T1.ORG_ID = #{orgId}
                <if test="retrievalType == 3">
                    AND T1.CREATED_USER_ID = #{userId}
                </if>
            ) T
            WHERE T.RN = 1
            ORDER BY
            <choose>
                <when test="retrievalType == 2">
                    T.NUM DESC
                </when>
                <otherwise>
                    T.CREATETIME DESC
                </otherwise>
            </choose>
        ) WHERE ROWNUM &lt;= 100
    </select>

    <select id="getChengYunInfoByTaskId" resultType="com.wtyt.bo.bean.response.Resp1735111Bean">
        SELECT
            NVL(T3.TAX_WAYBILL_ID, T1.TAX_WAYBILL_ID) taxWaybillId,
            T1.DRIVER_NAME driverName,
            T1.MOBILE_NO mobileNo,
            T1.CART_BADGE_NO cartBadgeNo,
            T2.CART_LENGTH cartLength,
            T2.CART_TYPE cartType
        FROM
            T_BO_TRANS_TASK T1
        LEFT JOIN T_BO_TRANS_TASK_EXTRA T2 ON
            T1.BO_TRANS_TASK_ID = T2.BO_TRANS_TASK_ID
            AND T2.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE T3 ON
            T3.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
            AND T3.IS_DEL = 0
        WHERE
            T1.IS_DEL = 0
            AND T1.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <sql id="getRecentlyCooperationDriverListCommonQuery">
        BT.IS_DEL = 0
        AND BTE.DISPATCH_CAR_RECORD_ID IS NOT NULL
        AND BT.CREATED_TIME > SYSDATE - 360
        AND BT.MOBILE_NO IS NOT NULL
        AND BT.CART_BADGE_NO IS NOT NULL
        <if test="retrievalValue != null and retrievalValue.length > 0">
            <choose>
                <when test="retrievalKey != null and retrievalKey == '1'.toString()">
                    AND INSTR(BT.DRIVER_NAME, #{retrievalValue}) > 0
                </when>
                <when test="retrievalKey != null and retrievalKey == '2'.toString()">
                    AND INSTR(BT.MOBILE_NO, #{retrievalValue}) > 0
                </when>
                <when test="retrievalKey != null and retrievalKey == '3'.toString()">
                    AND INSTR(BT.CART_BADGE_NO, #{retrievalValue}) > 0
                </when>
                <otherwise>
                    AND (
                    INSTR(BT.MOBILE_NO, #{retrievalValue}) > 0
                    OR INSTR(BT.DRIVER_NAME, #{retrievalValue}) > 0
                    OR INSTR(BT.CART_BADGE_NO, UPPER(#{retrievalValue})) > 0
                    )
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="getRecentlyCooperationDriverList" resultType="com.wtyt.dao.bean.syf.BoDriverInfoBean">
        SELECT
            DRIVER_NAME driverName,
            MOBILE_NO mobileNo,
            CART_BADGE_NO cartBadgeNo,
            CART_BADGE_COLOR cartBadgeColor,
            CART_LENGTH cartLength,
            CART_TYPE cartType
        FROM (
            SELECT
                ST1.*
            FROM (
                SELECT ST2.*,
                    ROW_NUMBER() OVER (PARTITION BY MOBILE_NO, DRIVER_NAME, CART_BADGE_NO ORDER BY CREATED_TIME DESC) AS RN
                FROM (
                    SELECT
                        BT.DRIVER_NAME,
                        BT.MOBILE_NO,
                        BT.CART_BADGE_NO,
                        BT.CART_BADGE_COLOR,
                        BTE.CART_LENGTH,
                        BTE.CART_TYPE,
                        BT.CREATED_TIME
                    FROM T_BO_TRANS_TASK BT
                    LEFT JOIN T_BO_TRANS_TASK_ALLOCATE BTA
                        ON BT.BO_TRANS_TASK_ID = BTA.BO_TRANS_TASK_ID AND BTA.IS_DEL = 0
                    JOIN T_BO_TRANS_TASK_EXTRA BTE
                        ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                    WHERE
                        <include refid="getRecentlyCooperationDriverListCommonQuery" />
                        AND (
                            (BTA.BO_TRANS_TASK_ALLOCATE_ID IS NULL AND ((BT.SETTLE_MODE = 1 AND BT.PAY_STATE IN (2, 5)) OR BT.SETTLE_MODE = 2))
                            OR
                            (BTA.BO_TRANS_TASK_ALLOCATE_ID IS NOT NULL AND (((BT.SETTLE_MODE = 1 AND BT.PAY_STATE IN (2, 5)) OR (BTA.SETTLE_MODE = 1 AND BTA.PAY_STATE IN (2, 5))) OR BTA.SETTLE_MODE = 2))
                        )
                        AND BT.ORG_ID IN
                        <choose>
                            <when test="orgIdList != null and orgIdList.size() > 0">
                                <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
                                    #{orgId}
                                </foreach>
                            </when>
                            <otherwise>(-1)</otherwise>
                        </choose>

                    UNION ALL

                    SELECT
                        BT.DRIVER_NAME,
                        BT.MOBILE_NO,
                        BT.CART_BADGE_NO,
                        BT.CART_BADGE_COLOR,
                        BTE.CART_LENGTH,
                        BTE.CART_TYPE,
                        BT.CREATED_TIME
                    FROM T_BO_TRANS_TASK BT
                    JOIN T_BO_TRANS_TASK_ALLOCATE BTA
                    ON BT.BO_TRANS_TASK_ID = BTA.BO_TRANS_TASK_ID AND BTA.IS_DEL = 0
                    JOIN T_BO_TRANS_TASK_EXTRA BTE
                    ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                    WHERE
                        <include refid="getRecentlyCooperationDriverListCommonQuery" />
                        AND (((BT.SETTLE_MODE = 1 AND BT.PAY_STATE IN (2, 5)) OR (BTA.SETTLE_MODE = 1 AND BTA.PAY_STATE IN (2, 5))) OR BTA.SETTLE_MODE = 2)
                        AND BTA.ORG_ID IN
                        <choose>
                            <when test="orgIdList != null and orgIdList.size() > 0">
                                <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
                                    #{orgId}
                                </foreach>
                            </when>
                            <otherwise>(-1)</otherwise>
                        </choose>

                    UNION

                    SELECT
                        BDC.DRIVER_NAME,
                        TO_CHAR(BDC.MOBILE_NO) MOBILE_NO,
                        BDC.CART_BADGE_NO,
                        BDC.CART_BADGE_COLOR,
                        BDC.CART_LENGTH,
                        BDC.CART_TYPE,
                        BDC.LAST_MODIFIED_TIME CREATED_TIME
                    FROM T_BO_DRIVER_COOPERATED BDC
                    WHERE BDC.IS_DEL = 0
                    AND BDC.TYPE = 0
                    <if test="retrievalValue != null and retrievalValue.length > 0">
                        <choose>
                            <when test="retrievalKey != null and retrievalKey == '1'.toString()">
                                AND INSTR(BDC.DRIVER_NAME, #{retrievalValue}) > 0
                            </when>
                            <when test="retrievalKey != null and retrievalKey == '2'.toString()">
                                AND INSTR(BDC.MOBILE_NO, #{retrievalValue}) > 0
                            </when>
                            <when test="retrievalKey != null and retrievalKey == '3'.toString()">
                                AND INSTR(BDC.CART_BADGE_NO, #{retrievalValue}) > 0
                            </when>
                            <otherwise>
                                AND (
                                    INSTR(BDC.MOBILE_NO, #{retrievalValue}) > 0
                                    OR INSTR(BDC.DRIVER_NAME, #{retrievalValue}) > 0
                                    OR INSTR(BDC.CART_BADGE_NO, UPPER(#{retrievalValue})) > 0
                                )
                            </otherwise>
                        </choose>
                    </if>
                    AND (BDC.COMPANY_ID = #{companyId} OR BDC.COMPANY_ID = -1)
                    )ST2
                )ST1
                WHERE ST1.RN = 1
                AND NOT EXISTS (
                    SELECT 1 FROM T_BO_DRIVER_COOPERATED BDC
                    WHERE BDC.IS_DEL = 0
                    AND BDC.TYPE = 1
                    AND ((BDC.DRIVER_NAME = ST1.DRIVER_NAME) OR (BDC.DRIVER_NAME IS NULL AND ST1.DRIVER_NAME IS NULL))
                    AND BDC.MOBILE_NO = ST1.MOBILE_NO
                    AND BDC.CART_BADGE_NO = ST1.CART_BADGE_NO
                )
                ORDER BY ST1.CREATED_TIME DESC
        )
        <if test="limit > 0">
            WHERE ROWNUM <![CDATA[<=]]> #{limit}
        </if>
    </select>

    <select id="getRecentlyCooperationDriverListOld" resultType="com.wtyt.dao.bean.syf.BoDriverInfoBean">
        SELECT
            DRIVER_NAME driverName,
            MOBILE_NO mobileNo,
            CART_BADGE_NO cartBadgeNo,
            CART_BADGE_COLOR cartBadgeColor,
            CART_LENGTH cartLength,
            CART_TYPE cartType
        FROM (
            SELECT
                ST1.*
            FROM (
                SELECT ST2.*,
                    ROW_NUMBER() OVER (PARTITION BY MOBILE_NO, DRIVER_NAME, CART_BADGE_NO ORDER BY CREATED_TIME DESC) AS RN
                FROM (
                    SELECT
                        BT.DRIVER_NAME,
                        BT.MOBILE_NO,
                        BT.CART_BADGE_NO,
                        BT.CART_BADGE_COLOR,
                        BTE.CART_LENGTH,
                        BTE.CART_TYPE,
                        BT.CREATED_TIME
                    FROM T_BO_TRANS_TASK BT
                    LEFT JOIN T_BO_TRANS_TASK_ALLOCATE BTA
                        ON BT.BO_TRANS_TASK_ID = BTA.BO_TRANS_TASK_ID AND BTA.IS_DEL = 0
                    JOIN T_BO_TRANS_TASK_EXTRA BTE
                        ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                    WHERE
                        <include refid="getRecentlyCooperationDriverListCommonQuery" />
                        AND BT.ORG_ID IN
                        <choose>
                            <when test="orgIdList != null and orgIdList.size() > 0">
                                <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
                                    #{orgId}
                                </foreach>
                            </when>
                            <otherwise>(-1)</otherwise>
                        </choose>

                    UNION ALL
                    SELECT
                        BT.DRIVER_NAME,
                        BT.MOBILE_NO,
                        BT.CART_BADGE_NO,
                        BT.CART_BADGE_COLOR,
                        BTE.CART_LENGTH,
                        BTE.CART_TYPE,
                        BT.CREATED_TIME
                        FROM T_BO_TRANS_TASK BT
                        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE BTA
                        ON BT.BO_TRANS_TASK_ID = BTA.BO_TRANS_TASK_ID AND BTA.IS_DEL = 0
                        JOIN T_BO_TRANS_TASK_EXTRA BTE
                        ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                    WHERE
                        <include refid="getRecentlyCooperationDriverListCommonQuery" />
                        AND BTA.ORG_ID IN
                        <choose>
                            <when test="orgIdList != null and orgIdList.size() > 0">
                                <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
                                    #{orgId}
                                </foreach>
                            </when>
                            <otherwise>(-1)</otherwise>
                        </choose>

                    UNION

                    SELECT
                        BDC.DRIVER_NAME,
                        TO_CHAR(BDC.MOBILE_NO) MOBILE_NO,
                        BDC.CART_BADGE_NO,
                        BDC.CART_BADGE_COLOR,
                        BDC.CART_LENGTH,
                        BDC.CART_TYPE,
                        BDC.LAST_MODIFIED_TIME CREATED_TIME
                    FROM T_BO_DRIVER_COOPERATED BDC
                    WHERE BDC.IS_DEL = 0
                    AND BDC.TYPE = 0
                    <if test="retrievalValue != null and retrievalValue.length > 0">
                        <choose>
                            <when test="retrievalKey != null and retrievalKey == '1'.toString()">
                                AND INSTR(BDC.DRIVER_NAME, #{retrievalValue}) > 0
                            </when>
                            <when test="retrievalKey != null and retrievalKey == '2'.toString()">
                                AND INSTR(BDC.MOBILE_NO, #{retrievalValue}) > 0
                            </when>
                            <when test="retrievalKey != null and retrievalKey == '3'.toString()">
                                AND INSTR(BDC.CART_BADGE_NO, #{retrievalValue}) > 0
                            </when>
                            <otherwise>
                                AND (
                                    INSTR(BDC.MOBILE_NO, #{retrievalValue}) > 0
                                    OR INSTR(BDC.DRIVER_NAME, #{retrievalValue}) > 0
                                    OR INSTR(BDC.CART_BADGE_NO, UPPER(#{retrievalValue})) > 0
                                )
                            </otherwise>
                        </choose>
                    </if>
                    AND (BDC.COMPANY_ID = #{companyId} OR BDC.COMPANY_ID = -1)
                    )ST2
                )ST1
                WHERE ST1.RN = 1
                AND NOT EXISTS (
                    SELECT 1 FROM T_BO_DRIVER_COOPERATED BDC
                    WHERE BDC.IS_DEL = 0
                    AND BDC.TYPE = 1
                    AND ((BDC.DRIVER_NAME = ST1.DRIVER_NAME) OR (BDC.DRIVER_NAME IS NULL AND ST1.DRIVER_NAME IS NULL))
                    AND BDC.MOBILE_NO = ST1.MOBILE_NO
                    AND BDC.CART_BADGE_NO = ST1.CART_BADGE_NO
                )
                ORDER BY ST1.CREATED_TIME DESC
        )
        <if test="limit > 0">
            WHERE ROWNUM <![CDATA[<=]]> #{limit}
        </if>
    </select>

    <!--    对账列表的where条件-->
    <sql id="whereDzPageCondition">
        AND T.IS_DEL = 0
        AND T.ORG_ID = #{orgId}
        AND E.PAPER_RECEIPT_STATUS IN (1,2,3,4,5)
        <if test="tabStatus != null and tabStatus != ''">
            <if test="tabStatus == 3">
                AND E.PAPER_RECEIPT_STATUS = 3
            </if>
            <if test="tabStatus == 2">
                AND E.PAPER_RECEIPT_STATUS IN (2,1)
            </if>
            <if test="tabStatus == 9">
                AND E.PAPER_RECEIPT_STATUS IN (4,5)
            </if>
        </if>

        <if test="startProvinceName != null and startProvinceName != ''">
            AND T.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="endProvinceName != null and endProvinceName != ''">
            AND T.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="startCityName != null and startCityName != ''">
            AND T.START_CITY_NAME = #{startCityName}
        </if>
        <if test="endCityName != null and endCityName != ''">
            AND T.END_CITY_NAME = #{endCityName}
        </if>
        <if test="keywords != null and keywords != ''">
            AND (T.TAX_WAYBILL_NO = #{keywords}  OR
            T.CART_BADGE_NO = #{keywords} )
        </if>
    </sql>

    <select id="queryAllByLimit" resultType="com.wtyt.bo.bean.response.Resp5329430Bean"
            parameterType="com.wtyt.bo.bean.request.Req5329430Bean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            TO_CHAR(T.ALL_FREIGHT, '*********90.00') allFreight,
            T.GOODS_NAME goodsName,
            TO_CHAR(T.GOODS_AMOUNT, '*********90.0000') goodsAmount,
            T.GOODS_AMOUNT_TYPE goodsAmountType,
            T.START_CITY_NAME startCityName,
            T.END_CITY_NAME endCityName,
            T.START_PROVINCE_NAME || T.START_CITY_NAME loadingAddressName,
            T.END_PROVINCE_NAME || T.END_CITY_NAME unloadingAddressName,
            E.CART_TYPE cartType,
            E.CART_LENGTH cartLength,
            to_char(E.PAPER_ESTIMATED_DELIVERY_TIME,'MM-dd') paperEstimatedDeliveryTime,
            E.CUSTOMER_ORDER_NO customerOrderNo,
            E.PAPER_RECEIPT_STATUS paperReceiptStatus,
            n1.CREATED_TIME AS arrivePlaceTime,
            n2.CREATED_TIME AS estimatedDeliveryTime
        FROM
            T_BO_TRANS_TASK T
        JOIN T_BO_TRANS_TASK_EXTRA E ON
            T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
            AND E.IS_DEL = 0
        LEFT JOIN (
            SELECT BO_TRANS_TASK_ID, CREATED_TIME FROM (SELECT BO_TRANS_TASK_ID, CREATED_TIME, ROW_NUMBER() OVER (PARTITION BY BO_TRANS_TASK_ID ORDER BY CREATED_TIME DESC) AS rn FROM T_BO_TRANS_NODE_RECORD WHERE NODE_ID = 500 AND IS_DEL = 0 ) WHERE rn = 1
            ) n1 ON T.BO_TRANS_TASK_ID = n1.BO_TRANS_TASK_ID
            LEFT JOIN (
            SELECT BO_TRANS_TASK_ID, CREATED_TIME FROM (SELECT BO_TRANS_TASK_ID, CREATED_TIME, ROW_NUMBER() OVER (PARTITION BY BO_TRANS_TASK_ID ORDER BY CREATED_TIME DESC) AS rn FROM T_BO_TRANS_NODE_RECORD WHERE NODE_ID = 800 AND IS_DEL = 0 ) WHERE rn = 1
            ) n2 ON T.BO_TRANS_TASK_ID = n2.BO_TRANS_TASK_ID
        WHERE 1=1
            <if test="arrivePlaceTimeStart != null and arrivePlaceTimeStart != ''">
                <![CDATA[ AND n1.CREATED_TIME >= to_date(#{arrivePlaceTimeStart},'yyyy-MM-dd') ]]>
            </if>
            <if test="arrivePlaceTimeEnd != null and arrivePlaceTimeEnd != ''">
                <![CDATA[ AND n1.CREATED_TIME <= to_date(#{arrivePlaceTimeEnd} || '23:59:59','yyyy-MM-dd Hh24:mi:ss') ]]>
            </if>
            <include refid="whereDzPageCondition"/>
    </select>
    <select id="queryPaperReceiptTabStatusCount" resultType="com.wtyt.commons.bean.StringIntegerPair"
            parameterType="com.wtyt.bo.bean.request.Req5329431Bean">
        SELECT
            E.PAPER_RECEIPT_STATUS key,
            count(*) value
        FROM
            T_BO_TRANS_TASK T
        JOIN T_BO_TRANS_TASK_EXTRA E ON
            T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
            AND E.IS_DEL = 0
        WHERE 1=1
            <include refid="whereDzPageCondition"/>
            GROUP BY E.PAPER_RECEIPT_STATUS
    </select>
    <select id="queryBoTransTaskByWaybillIds" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.TAX_WAYBILL_ID taxWaybillId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            NVL(T.TRANS_MODE,1) transMode,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.SETTLE_MODE settleMode,
            T.RECEIVER_MOBILE receiverMobile,
            T.WB_ITEM wbItem,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(T.LOSS_FEE, 0), '*********90.00') lossFee,
            TO_CHAR(NVL(T.FREIGHT_INCR, 0), '*********90.00') freightIncr
          FROM T_BO_TRANS_TASK T
         WHERE T.IS_DEL=0 AND T.TAX_WAYBILL_ID IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        UNION
        SELECT
            TA.TAX_WAYBILL_ID taxWaybillId,
            TA.BO_TRANS_TASK_ID boTransTaskId,
            TA.ORG_ID orgId,
            null transMode,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            TA.SETTLE_MODE settleMode,
            T.RECEIVER_MOBILE receiverMobile,
            T.WB_ITEM wbItem,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(T.LOSS_FEE, 0), '*********90.00') lossFee,
            TO_CHAR(NVL(T.FREIGHT_INCR, 0), '*********90.00') freightIncr
          FROM T_BO_TRANS_TASK_ALLOCATE TA,T_BO_TRANS_TASK T
         WHERE TA.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID and TA.IS_DEL = 0 AND T.IS_DEL=0
          and TA.TAX_WAYBILL_ID IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>

    <select id="queryBoTransTaskByBoTransTaskIds" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean"
            parameterType="java.util.List">
        SELECT
        T.TAX_WAYBILL_ID taxWaybillId,
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.ORG_ID orgId,
        T.TRANS_MODE transMode,
        T.MOBILE_NO mobileNo,
        T.SETTLE_MODE settleMode
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL=0
        AND (T.BO_TRANS_TASK_ID, T.ORG_ID) IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            (#{item.boTransTaskId},#{item.orgId})
        </foreach>
        UNION
        SELECT
        TA.TAX_WAYBILL_ID taxWaybillId,
        TA.BO_TRANS_TASK_ID boTransTaskId,
        TA.ORG_ID orgId,
        null transMode,
        T.MOBILE_NO mobileNo,
        TA.SETTLE_MODE settleMode
        FROM T_BO_TRANS_TASK_ALLOCATE TA,T_BO_TRANS_TASK T
        WHERE TA.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID and TA.IS_DEL = 0 AND T.IS_DEL=0
        AND (TA.BO_TRANS_TASK_ID, TA.ORG_ID) IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            (#{item.boTransTaskId},#{item.orgId})
        </foreach>
    </select>

    <select id="findTransTaskAllocateInfoByTaskIdOrBillId" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.TAX_WAYBILL_ID ownerTaxWaybillId,
            A.TAX_WAYBILL_ID allocateTaxWaybillId,
            A.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            A.ORG_ID orgId,
            TO_CHAR(NVL(A.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(A.LOSS_FEE, 0), '*********90.00') lossFee,
            TO_CHAR(NVL(A.FREIGHT_INCR, 0), '*********90.00') freightIncr,
            TO_CHAR(NVL(A.UNIT_PRICE, 0), '*********90.00') unitPrice,
            TO_CHAR(NVL(T.LOADING_TONNAGE, 0), '*********90.0000') loadingTonnage,
            TO_CHAR(NVL(T.UNLOADING_TONNAGE, 0), '*********90.0000') unloadingTonnage,
            T.TRANSPORT_LINE_ID transportLineId,
            TO_CHAR(A.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            TE.LOADING_ADDRESS_NAME loadingAddressName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.XCY_USER_ID xcyUserId,
            T.CREATED_USER_SYS_ROLE_TYPE sysRoleType,
            T.ORDER_CREATE_TYPE orderCreateType,
            A.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.DRIVER_NAME driverName,
            T.CREATED_USER_ID createdUserId,
            TE.CART_LENGTH cartLength,
            TE.CART_TYPE cartType,
            T.HYB_STATE hybState,
            T.TRANS_PATTERN transPattern,
            T.TRANS_MODE transMode,
            A.BO_TRANS_TASK_ID boTransTaskId,
            T.TRANSPORT_TYPE transportType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            TO_CHAR(NVL(A.ALL_FREIGHT, 0), '*********90.00') allFreight,
            T.SETTLE_MODE settleMode,
            A.PAY_STATE payState,
            A.SETTLE_MODE allocateSettleMode,
            T.OWNER_ORG_ID ownerOrgId,
            NVL(T.TAX_WAYBILL_ID, A.TAX_WAYBILL_ID) locTaxWaybillId,
            NVL(A.TAX_WAYBILL_ID, T.TAX_WAYBILL_ID) createTaxWaybillId,
            case when A.TAX_WAYBILL_ID is not NULL then A.ORG_ID ELSE T.ORG_ID END createOrgId,
            TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            case when T.TAX_WAYBILL_ID is not null
                then T.ORG_ID
                ELSE A.ORG_ID END locOrgId,
            T.OPERATE_SCHEME operateScheme,
            T.CAPACITY_TYPE capacityType,
            T.CAPACITY_TYPE_NAME capacityTypeName,
            T.IS_PARTAKE_OPERATE isPartakeOperate,
            TE.DRIVER_ID driverId,
            TE.HYB_USER_ID hybUserId,
            TO_CHAR(TE.TRANSPORT_TIME, '*********90.09') transportTime,
            TE.LOAD_TYPE loadType,
            TE.TRANS_TASK_FLAG transTaskFlag,
            TE.VOUCHER_CONFIG_TYPE voucherConfigType,
            TO_CHAR(TE.ARRIVE_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveTime,
            TO_CHAR(TE.DISPATCH_CAR_TIME, 'yyyy-mm-dd hh24:mi:ss') dispatchCarTime
        FROM T_BO_TRANS_TASK_ALLOCATE A
        LEFT JOIN T_BO_TRANS_TASK T ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TE ON
            T.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
        WHERE
        	A.IS_DEL = 0
        	AND T.IS_DEL = 0
            <choose>
                <when test="boTransTaskId != null and boTransTaskId !=''">
                    AND A.BO_TRANS_TASK_ID = #{boTransTaskId}
                </when>
                <when test="taxWaybillId != null and taxWaybillId !=''">
                    AND A.TAX_WAYBILL_ID = #{taxWaybillId}
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
    </select>

    <sql id="queryTaskTransPortSql">
        SELECT
            bt.TAX_WAYBILL_ID taxWaybillId,
            bt.TAX_WAYBILL_NO taxWaybillNo,
            bt.BO_TRANS_TASK_ID boTransTaskId,
            bt.OWNER_ORG_ID ownerOrgId,
            bt.SETTLE_MODE settleMode,
            bt.SETTLE_MODE taskSettleMode,
            bt.ORG_ID taskOrgId,
            bt.NODE_ID taskNodeId,
            bt.TAX_WAYBILL_ID taskTaxWaybillId,
            bt.TRANS_MODE transMode,
            TO_CHAR(bt.UNIT_PRICE, '*********90.00') unitPrice,
            bt.PAY_STATE payState,
            bt.ORG_ID orgId,
            bt.NODE_ID nodeId,
            bt.HYB_STATE hybState,
            TO_CHAR(bt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') createdTime,
            TO_CHAR(bt.LAST_MODIFIED_TIME, 'yyyy-MM-dd Hh24:mi:ss') lastModifiedTime,
            bt.DRIVER_NAME driverName,
            bt.MOBILE_NO mobileNo,
            bt.CART_BADGE_NO cartBadgeNo,
            bt.CART_BADGE_COLOR cartBadgeColor,
            bt.START_PROVINCE_NAME startProvinceName,
            bt.START_CITY_NAME startCityName,
            bt.START_COUNTY_NAME startCountyName,
            bt.START_LATITUDE startLatitude,
            bt.START_LONGITUDE startLongitude,
            bt.END_PROVINCE_NAME endProvinceName,
            bt.END_CITY_NAME endCityName,
            bt.END_COUNTY_NAME endCountyName,
            TO_CHAR(NVL(A.ALL_FREIGHT, 0), '*********90.00') allocateAllFreight,
            TO_CHAR(NVL(A.USER_FREIGHT, 0), '*********90.00') allocateUserFreight,
            TO_CHAR(NVL(A.PREPAYMENTS, 0) , '*********90.00') allocatePrepayments,
            TO_CHAR(NVL(A.BACK_FEE, 0) , '*********90.00') allocateBackFee,
            TO_CHAR(NVL(A.PREPAYMENTS_OILCARD , 0), '*********90.00') allocatePrepaymentsOilcard,
            TO_CHAR(NVL(A.PREPAYMENTS_GASCARD , 0), '*********90.00') allocatePrepaymentsGasCard,
            TO_CHAR(NVL(bt.ALL_FREIGHT, 0), '*********90.00') allFreight,
            TO_CHAR(NVL(bt.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(bt.SERVICE_FEE, 0), '*********90.00') serviceFee,
            TO_CHAR(NVL(bt.DATA_SERVICE_FEE, 0), '*********90.00') dataServiceFee,
            TO_CHAR(NVL(bt.PREPAYMENTS, 0) , '*********90.00') prepayments,
            TO_CHAR(NVL(bt.BACK_FEE, 0) , '*********90.00') backFee,
            TO_CHAR(NVL(bt.PREPAYMENTS_OILCARD , 0), '*********90.00') prepaymentsOilcard,
            TO_CHAR(NVL(bt.PREPAYMENTS_GASCARD , 0), '*********90.00') prepaymentsGascard,
            NVL(bte.RECEIPT_BZ_STATE, 0) receiptBzState,
            bte.TRANS_VOUCHER transVoucher,
            NVL(bt.ADVANCE_PAY_STATE, 0) advancePayState,
            NVL(bt.TRANS_PATTERN, 1) transPattern,
            bt.GOODS_NAME goodsName,
            bt.GOODS_AMOUNT goodsAmount,
            bt.GOODS_AMOUNT_TYPE goodsAmountType,
            bte.CART_TYPE cartType,
            bte.CART_LENGTH cartLength,
            bte.SERVICE_REQUIRE serviceRequire,
            TO_CHAR(bt.START_TIME,'YYYY-MM-DD HH24:MI') startRunTime,
            TO_CHAR(bte.ARRIVE_END_TIME, 'YYYY-MM-DD HH24:MI') promisedArriveTime,
            bte.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
            bte.UNLOAD_TIME_FORMAT unloadTimeFormat,
            TO_CHAR(bte.UNLOAD_TIME, 'YYYY-MM-DD HH24:MI') unLoadTime,
            TO_CHAR(A.PAY_OVER_TIME, 'YYYY-MM-DD HH24:MI') allocatePayOverTime,
            TO_CHAR(bt.PAY_OVER_TIME, 'YYYY-MM-DD HH24:MI') payOverTime,
            NVL(bt.TRANSPORT_TYPE,0) transportType,
            bte.LOADING_ADDRESS_NAME loadingAddressName,
            bte.UNLOADING_ADDRESS_NAME unloadingAddressName,
            bt.LOADING_PLACE_NAME loadingPlaceName,
            bt.UNLOADING_PLACE_NAME unloadingPlaceName,
            bt.LOADING_TONNAGE loadingTonnage,
            bt.UNLOADING_TONNAGE unloadingTonnage,
            TO_CHAR(bte.ARRIVE_TIME, 'YYYY-MM-DD HH24:MI') actualArriveTime,
            CASE WHEN bt.FIRST_RECEIPT_TIME IS NULL THEN 0 ELSE 1 END unLoadingScaleStatus,
            A.ORG_ID allocateOrgId,
            A.TAX_WAYBILL_ID allocateTaxWaybillId,
            A.SETTLE_MODE allocateSettleMode,
            A.PAY_STATE allocatePayState,
            A.NODE_ID allocateNodeId,
            TO_CHAR(A.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') allocateCreatedTime,
            bt.NOTE note,
            TO_CHAR(A.UNIT_PRICE, '*********90.00') allocateUnitPrice,
            bt.TASK_NOTE taskNote,
            bt.CUSTOMER_REMARK customerRemark,
            NVL(bt.FROZEN_BACK_FEE_STATE,0) frozenBackFeeState,
            bt.STATE state,
            bt.CAPACITY_TYPE capacityType,
            bt.CAPACITY_TYPE_NAME capacityTypeName,
            bt.IS_PARTAKE_OPERATE isPartakeOperate,
            bt.WB_ITEM wbItem,
            NVL(bte.IS_THIRD_INTERFACE,0) isThirdInterface,
            TO_CHAR(bt.PREPAYMENTS_OILCARD, '*********90.00') taskPrepaymentsOilcard,
            TO_CHAR(bt.PREPAYMENTS_GASCARD, '*********90.00') taskPrepaymentsGascard,
            bte.GOODS_AMOUNT_OCR_STATE goodsAmountOcrState,
            bte.SETTLE_ISSUE settleIssue,
            NVL(bt.SETTLE_TYPE, bt.SETTLE_MODE) settleType,
            bt.SETTLE_TARGET settleTarget,
            TO_CHAR(BTE.ARRIVE_CONFIRM_TIME, 'yyyy-mm-dd hh24:mi:ss') arriveConfirmTime,
            TO_CHAR(BTE.ARRIVE_TIME, 'yyyy-mm-dd hh24:mi:ss') arriveTime,
            bte.BUSINESS_TYPE businessType,
            bte.BO_VOUCHER_CHECK_STATE boVoucherCheckState,
            bte.TRANS_TASK_FLAG transTaskFlag,
            bte.CPD_POOL_GROUP_NAME cpdPoolGroupName,
            bte.THIRD_TASK_NO thirdTaskNo,
            bte.FREEZE_STATUS freezeStatus,
            BTE.TRANSPORT_NODE transportNode,
            TO_CHAR(BTE.TRANSPORT_NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') transportNodeTime,
            bte.DISPATCH_CAR_RECORD_ID dispatchCarRecordId
        FROM
            <include refid="taskListTableApp"/>
        WHERE
            <include refid="taskQueryWhereSql"/>
    </sql>

    <sql id="taskListTableApp">
        <choose>
            <when test="boTransTaskIds !=null and boTransTaskIds.size()>0">
                T_BO_TRANS_TASK bt
            </when>
            <when test="noSupplier!=null and noSupplier">
                T_BO_TRANS_TASK bt
            </when>
            <otherwise>
                <include refid="taskRightTempTable"/> TEMP
                JOIN T_BO_TRANS_TASK bt
                ON TEMP.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                <choose>
                    <when test="isDel != null and isDel == '1'.toString()">
                        AND BT.IS_DEL = 1
                    </when>
                    <otherwise>
                        AND BT.IS_DEL = 0
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
        JOIN T_BO_TRANS_TASK_EXTRA bte ON bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID AND bte.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON bt.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        <if test="dispatchCarUserIds !=null and dispatchCarUserIds.size()>0">
            LEFT JOIN T_BO_TRANS_NODE_RECORD NR
            ON NR.BO_TRANS_NODE_RECORD_ID = BTE.DISPATCH_CAR_RECORD_ID
            AND NR.IS_DEL = 0
        </if>
    </sql>

    <sql id="queryTaskCountWhereSql">
        <if test="tabState != null and tabState != ''">
            <if test="tabState == 0 || tabState == 1 || tabState == 13">
                <if test="sortDateStart!=null and sortDateStart!=''">
                    AND bt.CREATED_TIME >= TO_DATE(#{sortDateStart},'YYYY-MM-DD')
                </if>
                <if test="sortDateEnd!=null and sortDateEnd!=''">
                    AND bt.CREATED_TIME &lt;= TO_DATE(#{sortDateEnd} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
                </if>
            </if>
            <if test="tabState == 2">
                <if test="sortDateStart!=null and sortDateStart!=''">
                    AND bte.ARRIVE_END_TIME >= TO_DATE(#{sortDateStart},'YYYY-MM-DD')
                </if>
                <if test="sortDateEnd!=null and sortDateEnd!=''">
                    AND bte.ARRIVE_END_TIME &lt;= TO_DATE(#{sortDateEnd} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
                </if>
            </if>
            <if test="tabState == 3">
                <if test="sortDateStart!=null and sortDateStart!=''">
                    AND bte.ARRIVE_TIME >= TO_DATE(#{sortDateStart},'YYYY-MM-DD')
                </if>
                <if test="sortDateEnd!=null and sortDateEnd!=''">
                    AND bte.ARRIVE_TIME &lt;= TO_DATE(#{sortDateEnd} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
                </if>
            </if>
            <if test="tabState == 4">
                <if test="sortDateStart!=null and sortDateStart!=''">
                    AND bt.START_TIME >= TO_DATE(#{sortDateStart},'YYYY-MM-DD')
                </if>
                <if test="sortDateEnd!=null and sortDateEnd!=''">
                    AND bt.START_TIME &lt;= TO_DATE(#{sortDateEnd} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
                </if>
            </if>
            <if test="tabState == 5 or tabState == 10">
                <if test="sortDateStart!=null and sortDateStart!=''">
                    AND bt.START_TIME >= TO_DATE(#{sortDateStart},'YYYY-MM-DD')
                </if>
                <if test="sortDateEnd!=null and sortDateEnd!=''">
                    AND bt.START_TIME &lt;= TO_DATE(#{sortDateEnd} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
                </if>
            </if>
            <if test="tabState == 6">
                <if test="sortDateStart!=null and sortDateStart!=''">
                    AND bt.PAY_OVER_TIME >= TO_DATE(#{sortDateStart},'YYYY-MM-DD')
                </if>
                <if test="sortDateEnd!=null and sortDateEnd!=''">
                    AND bt.PAY_OVER_TIME &lt;= TO_DATE(#{sortDateEnd} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
                </if>
            </if>
            <if test="tabState == 14 or tabState == 15 or tabState == 16">
                <if test="sortDateStart!=null and sortDateStart!=''">
                    AND BTE.TRANSPORT_NODE_TIME >= TO_DATE(#{sortDateStart},'YYYY-MM-DD')
                </if>
                <if test="sortDateEnd!=null and sortDateEnd!=''">
                    AND BTE.TRANSPORT_NODE_TIME &lt;= TO_DATE(#{sortDateEnd} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
                </if>
            </if>
        </if>
    </sql>

    <!--<sql id="queryTaskCountWhereSql">
        <if test="tabState != null and tabState != '' and lastDateOfSortTab!=null and lastDateOfSortTab!=''">
            <choose>
                <when test="tabState == 2">
                    AND bte.ARRIVE_END_TIME = TO_DATE(#{lastDateOfSortTab},'YYYY-MM-DD')
                </when>
                <when test="tabState == 3">
                    AND bte.ARRIVE_TIME = TO_DATE(#{lastDateOfSortTab},'YYYY-MM-DD')
                </when>
                <when test="tabState == 4">
                    AND bt.START_TIME = TO_DATE(#{lastDateOfSortTab},'YYYY-MM-DD')
                </when>
                <when test="tabState == 5 or tabState == 10">
                    AND bt.START_TIME = TO_DATE(#{lastDateOfSortTab},'YYYY-MM-DD')
                </when>
                <when test="tabState == 6">
                    AND bt.PAY_OVER_TIME = TO_DATE(#{lastDateOfSortTab},'YYYY-MM-DD')
                </when>
                <otherwise>
                    AND bt.CREATED_TIME = TO_DATE(#{lastDateOfSortTab},'YYYY-MM-DD')
                </otherwise>
            </choose>
        </if>
    </sql>-->

    <select id="queryTransportTaskCount" resultType="java.lang.Integer"
            parameterType="com.wtyt.bo.bean.request.Req1735209Bean">
        select count(*) from
        <include refid="taskListTableApp"/>
        WHERE
        <include refid="taskQueryWhereSql"/>
        <include refid="queryTaskCountWhereSql"/>
    </select>

    <sql id="queryBoTaskDetailBeanByWaybillIdsCommonField">
        T.TAX_WAYBILL_ID taxWaybillId,
        NVL(T.TAX_WAYBILL_ID,A.TAX_WAYBILL_ID) locTaxWaybillId,
        NVL(A.TAX_WAYBILL_ID, T.TAX_WAYBILL_ID) createTaxWaybillId,
        T.TAX_WAYBILL_NO taxWaybillNo,
        case when A.TAX_WAYBILL_ID is not NULL then A.ORG_ID ELSE T.ORG_ID END createOrgId,
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.ORG_ID orgId,
        A.ORG_ID allocateOrgId,
        A.TAX_WAYBILL_ID allocateTaxWaybillId,
        T.TRANS_MODE transMode,
        T.SETTLE_MODE settleMode,
        A.SETTLE_MODE allocateSettleMode,
        T.NODE_ID nodeId,
        A.NODE_ID allocateNodeId,
        T.PAY_STATE payState,
        A.PAY_STATE allocatePayState,
        T.BO_BUSINESS_LINE_ID boBusinessLineId,
        TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
        T.ORDER_CREATE_TYPE orderCreateType,
        T.CREATED_USER_ID createdUserId,
        T.MOBILE_NO mobileNo,
        T.DRIVER_NAME driverName,
        T.CART_BADGE_NO cartBadgeNo,
        T.SETTLE_TYPE settleType,
        TE.BO_LINE_ASSIGN_REL_ID boLineAssignRelId,
        TE.CART_TONNAGE cartTonnage,
        TE.TRANS_TASK_FLAG transTaskFlag,
        TE.THIRD_TASK_NO thirdTaskNo,
        TE.CART_LENGTH cartLength,
        TE.RECEIVED_NUMBER receivedNumber
    </sql>

    <select id="queryBoTaskDetailBeanByWaybillIds" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            <include refid="queryBoTaskDetailBeanByWaybillIdsCommonField"/>
        FROM T_BO_TRANS_TASK T
        JOIN T_BO_TRANS_TASK_EXTRA TE
            ON TE.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
            AND A.IS_DEL = 0
        WHERE T.IS_DEL = 0
        AND T.TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        UNION
        SELECT
            <include refid="queryBoTaskDetailBeanByWaybillIdsCommonField"/>
        FROM T_BO_TRANS_TASK T
        JOIN T_BO_TRANS_TASK_EXTRA TE
            ON TE.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND
            A.IS_DEL = 0
        WHERE T.IS_DEL = 0
        AND A.TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryBoTaskDetailBeanByTaskIdList" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.TAX_WAYBILL_ID taxWaybillId,
            NVL(T.TAX_WAYBILL_ID,A.TAX_WAYBILL_ID) locTaxWaybillId,
            NVL(A.TAX_WAYBILL_ID, T.TAX_WAYBILL_ID) createTaxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            case when A.TAX_WAYBILL_ID is not NULL then A.ORG_ID ELSE T.ORG_ID END createOrgId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.OWNER_ORG_ID ownerOrgId,
            T.TRANS_MODE transMode,
            T.SETTLE_MODE settleMode,
            A.SETTLE_MODE allocateSettleMode,
            A.ORG_ID allocateOrgId,
            A.TAX_WAYBILL_ID allocateTaxWaybillId,
            T.NODE_ID nodeId,
            A.NODE_ID allocateNodeId,
            T.PAY_STATE payState,
            A.PAY_STATE allocatePayState,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            T.ORDER_CREATE_TYPE orderCreateType,
            T.CREATED_USER_ID createdUserId,
            T.MOBILE_NO mobileNo,
            T.DRIVER_NAME driverName,
            T.CART_BADGE_NO cartBadgeNo,
            T.SETTLE_TYPE settleType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            TE.BO_LINE_ASSIGN_REL_ID boLineAssignRelId,
            TE.BO_LINE_ASSIGN_REL_ID boLineAssignRelId,
            TE.CART_TONNAGE cartTonnage,
            TE.TRANS_TASK_FLAG transTaskFlag,
            TE.THIRD_TASK_NO thirdTaskNo,
            TE.CART_LENGTH cartLength,
            TE.RECEIVED_NUMBER receivedNumber
        FROM T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TE
            ON TE.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryTaskDetailByBoTransTaskId" resultType="com.wtyt.bo.bean.BoTaskDetailBean"
            parameterType="java.lang.String">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            A.BO_TRANS_TASK_ALLOCATE_ID boTransTaskAllocateId,
            T.ORG_ID orgId,
            T.TRANSPORT_TYPE transportType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.TRANSPORT_LINE_ID transportLineId,
            T.TRANS_MODE transMode,
            T.OFFER_TYPE offerType,
            T.CREATED_USER_ID createdUserId,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.XCY_USER_ID xcyUserId,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            TO_CHAR(T.FIRST_RECEIPT_TIME, 'yyyy-mm-dd hh24:mi:ss') firstReceiptTime,
            T.TRANS_PATTERN transPattern,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            T.CREATED_USER_JOB_NAME createdUserJobName,
            T.MODIFY_USER_JOB_NAME modifyUserJobName,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(T.PREPAYMENTS, 0) , '*********90.00') prepayments,
            TO_CHAR(NVL(T.BACK_FEE, 0) , '*********90.00') backFee,
            TO_CHAR(NVL(T.PREPAYMENTS_OILCARD , 0), '*********90.00') prepaymentsOilcard,
            TO_CHAR(NVL(T.FREIGHT_INCR , 0), '*********90.00') freightIncr,
            TO_CHAR(NVL(T.LOSS_FEE , 0), '*********90.00') lossFee,
            TO_CHAR(NVL(T.GOODS_COST , 0), '*********90.00') goodsCost,
            NVL(t.ADVANCE_PAY_STATE, 0) advancePayState,
            T.LOSS_ENSURE_STATE lossEnsureState,
            T.HYB_STATE hybState,
            T.TRANS_PATTERN transPattern,
            T.OFFER_TYPE offerType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            T.WB_ITEM wbItem,
            T.PAY_STATE payState,
            A.PAY_STATE allocatePayState,
            T.STATE state,
            A.TAX_WAYBILL_ID allocateTaxWaybillId,
            A.ORG_ID allocateOrgId,
            A.SETTLE_MODE allocateSettleMode,
            A.NODE_ID allocateNodeId,
            TE.LOADING_ADDRESS_NAME loadingAddressName,
            TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
            T.GOODS_NAME goodsName,
            T.GOODS_AMOUNT_TYPE goodsAmountType,
            T.FROZEN_BACK_FEE_STATE frozenBackFeeState,
            TE.DISPATCH_CAR_RECORD_ID dispatchCarRecordId,
            TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            TE.CART_TYPE cartType,
            TE.CART_LENGTH cartLength,
            TE.PAPER_RECEIPT_STATUS paperReceiptStatus,
            TE.DELETE_STATUS deleteStatus,
            TE.AUDIT_STATUS auditStatus,
            TE.TRANS_TASK_FLAG transTaskFlag,
            TE.THIRD_TASK_NO thirdTaskNo,
            TO_CHAR(TE.ARRIVE_TIME, 'yyyy-mm-dd hh24:mi:ss') arriveTime,
            TO_CHAR(TE.ARRIVE_END_TIME, 'yyyy-mm-dd hh24:mi:ss') arriveEndTime,
            TO_CHAR(TE.DISPATCH_CAR_TIME, 'yyyy-mm-dd hh24:mi:ss') dispatchCarTime,
            TE.VOUCHER_CONFIG_TYPE voucherConfigType,
            TE.CANCEL_STATE cancelState,
            TE.CPD_POOL_GROUP_ID cpdPoolGroupId
        FROM
            T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TE
            ON TE.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
            AND A.IS_DEL = 0
        WHERE
            T.is_del = 0
          AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>
    <select id="queryDeletedTaskDetailByBoTransTaskId" resultType="com.wtyt.bo.bean.BoTaskDetailBean"
            parameterType="java.lang.String">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TRANSPORT_TYPE transportType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.TRANSPORT_LINE_ID transportLineId,
            T.TRANS_MODE transMode,
            T.OFFER_TYPE offerType,
            T.CREATED_USER_ID createdUserId,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.XCY_USER_ID xcyUserId,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            T.TRANS_PATTERN transPattern,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            T.CREATED_USER_JOB_NAME createdUserJobName,
            T.MODIFY_USER_JOB_NAME modifyUserJobName,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(T.PREPAYMENTS, 0) , '*********90.00') prepayments,
            TO_CHAR(NVL(T.BACK_FEE, 0) , '*********90.00') backFee,
            TO_CHAR(NVL(T.PREPAYMENTS_OILCARD , 0), '*********90.00') prepaymentsOilcard,
            TO_CHAR(NVL(T.FREIGHT_INCR , 0), '*********90.00') freightIncr,
            TO_CHAR(NVL(T.LOSS_FEE , 0), '*********90.00') lossFee,
            TO_CHAR(NVL(T.GOODS_COST , 0), '*********90.00') goodsCost,
            NVL(t.ADVANCE_PAY_STATE, 0) advancePayState,
            T.LOSS_ENSURE_STATE lossEnsureState,
            T.HYB_STATE hybState,
            T.TRANS_PATTERN transPattern,
            T.OFFER_TYPE offerType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            A.TAX_WAYBILL_ID allocateTaxWaybillId,
            A.ORG_ID allocateOrgId,
            A.SETTLE_MODE allocateSettleMode,
            A.NODE_ID allocateNodeId,
            TE.DISPATCH_CAR_RECORD_ID dispatchCarRecordId,
            TE.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId
        FROM
            T_BO_TRANS_TASK T LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND A.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TE ON TE.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND TE.IS_DEL = 0
        WHERE
            T.is_del = 1
          AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="queryDeletedTaskByBoTransTaskIdList" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.SETTLE_MODE taskSettleMode,
            A.SETTLE_MODE allocateSettleMode
        FROM
            T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE
            T.is_del = 1
          AND T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryBoTaskDetailBeanMainInAllocate" resultType="com.wtyt.bo.bean.BoTaskDetailBean"
            parameterType="java.lang.String">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TRANSPORT_TYPE transportType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.TRANSPORT_LINE_ID transportLineId,
            T.TRANS_MODE transMode,
            T.OFFER_TYPE offerType,
            T.CREATED_USER_ID createdUserId,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.XCY_USER_ID xcyUserId,
            TO_CHAR(T.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            T.TRANS_PATTERN transPattern,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            T.CREATED_USER_JOB_NAME createdUserJobName,
            T.MODIFY_USER_JOB_NAME modifyUserJobName,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
            TO_CHAR(NVL(A.USER_FREIGHT, 0), '*********90.00') userFreight,
            TO_CHAR(NVL(A.PREPAYMENTS, 0) , '*********90.00') prepayments,
            TO_CHAR(NVL(A.BACK_FEE, 0) , '*********90.00') backFee,
            TO_CHAR(NVL(A.PREPAYMENTS_OILCARD , 0), '*********90.00') prepaymentsOilcard,
            TO_CHAR(NVL(A.FREIGHT_INCR , 0), '*********90.00') freightIncr,
            TO_CHAR(NVL(A.LOSS_FEE , 0), '*********90.00') lossFee,
            TO_CHAR(NVL(A.UNIT_PRICE, 0), '*********90.00') unitPrice,
            TO_CHAR(NVL(T.GOODS_COST , 0), '*********90.00') goodsCost,
            NVL(t.ADVANCE_PAY_STATE, 0) advancePayState,
            NVL(A.TAX_WAYBILL_ID, T.TAX_WAYBILL_ID) createTaxWaybillId,
            case when A.TAX_WAYBILL_ID is not NULL then A.ORG_ID ELSE T.ORG_ID END createOrgId,
            T.LOSS_ENSURE_STATE lossEnsureState,
            T.HYB_STATE hybState,
            T.TRANS_PATTERN transPattern,
            T.OFFER_TYPE offerType,
            T.OWNER_ORG_ID ownerOrgId,
            A.ORG_ID orgId,
            A.NODE_ID nodeId,
            A.TAX_WAYBILL_ID taxWaybillId,
            A.SETTLE_MODE settleMode,
            TO_CHAR(TE.ARRIVE_END_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveEndTime,
            TE.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
            TE.UNLOAD_TIME_FORMAT unloadTimeFormat,
            TE.DISPATCH_CAR_RECORD_ID dispatchCarRecordId,
            TE.CPD_POOL_GROUP_ID cpdPoolGroupId,
            T.WB_ITEM wbItem
        FROM
            T_BO_TRANS_TASK_ALLOCATE A
            LEFT JOIN T_BO_TRANS_TASK T ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND A.IS_DEL = 0
                              LEFT JOIN T_BO_TRANS_TASK_EXTRA TE ON TE.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND TE.IS_DEL = 0
        WHERE
            T.is_del = 0
          AND A.TAX_WAYBILL_ID = #{taxWaybillId}
        <if test="boTransTaskId != null and boTransTaskId != ''">
            AND A.BO_TRANS_TASK_ID = #{boTransTaskId}
        </if>
    </select>
    <select id="queryDetailMainInTaskByTaskIdAndTaxWaybillId" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
        T.TAX_WAYBILL_ID taxWaybillId,
        T.TAX_WAYBILL_NO taxWaybillNo,
        T.ORG_ID orgId,
        TO_CHAR(NVL(T.USER_FREIGHT, 0), '*********90.00') userFreight,
        TO_CHAR(NVL(T.LOSS_FEE, 0), '*********90.00') lossFee,
        TO_CHAR(NVL(T.FREIGHT_INCR, 0), '*********90.00') freightIncr,
        TO_CHAR(NVL(T.UNIT_PRICE, 0), '*********90.00') unitPrice,
        TO_CHAR(NVL(T.LOADING_TONNAGE, 0), '*********90.0000') loadingTonnage,
        TO_CHAR(NVL(T.UNLOADING_TONNAGE, 0), '*********90.0000') unloadingTonnage,
        T.TRANSPORT_LINE_ID transportLineId,
        TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        T.START_PROVINCE_NAME startProvinceName,
        T.START_CITY_NAME startCityName,
        T.START_COUNTY_NAME startCountyName,
        TE.LOADING_ADDRESS_NAME loadingAddressName,
        T.START_LONGITUDE startLongitude,
        T.START_LATITUDE startLatitude,
        T.END_PROVINCE_NAME endProvinceName,
        T.END_CITY_NAME endCityName,
        T.END_COUNTY_NAME endCountyName,
        TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
        T.END_LONGITUDE endLongitude,
        T.END_LATITUDE endLatitude,
        T.XCY_USER_ID xcyUserId,
        T.CREATED_USER_SYS_ROLE_TYPE sysRoleType,
        T.ORDER_CREATE_TYPE orderCreateType,
        T.NODE_ID nodeId,
        TO_CHAR(T.NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
        T.MOBILE_NO mobileNo,
        T.CART_BADGE_NO cartBadgeNo,
        T.CART_BADGE_COLOR cartBadgeColor,
        T.DRIVER_NAME driverName,
        T.CREATED_USER_ID createdUserId,
        TE.CART_LENGTH cartLength,
        TE.CART_TYPE cartType,
        T.HYB_STATE hybState,
        T.TRANS_PATTERN transPattern,
        T.TRANS_MODE transMode,
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.TRANSPORT_TYPE transportType,
        T.BO_BUSINESS_LINE_ID boBusinessLineId,
        T.LOADING_PLACE_NAME loadingPlaceName,
        T.UNLOADING_PLACE_NAME unloadingPlaceName,
        T.MODIFY_USER_ID modifyUserId,
        T.MODIFY_DRIVER_ID modifyDriverId,
        T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
        TO_CHAR(NVL(T.ALL_FREIGHT, 0), '*********90.00') allFreight,
        T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
        T.OWNER_ORG_ID ownerOrgId,
        T.SETTLE_MODE settleMode,
        A.BO_TRANS_TASK_ALLOCATE_ID boTransTaskAllocateId,
        A.ORG_ID allocateOrgId,
        A.SETTLE_MODE allocateSettleMode,
        A.TAX_WAYBILL_ID allocateTaxWaybillId,
        T.OWNER_ORG_ID ownerOrgId,
        T.RECEIVER_MOBILE receiverMobile,
        T.RECEIVER receiver,
        NVL(T.TAX_WAYBILL_ID, A.TAX_WAYBILL_ID) locTaxWaybillId,
        NVL(A.TAX_WAYBILL_ID, T.TAX_WAYBILL_ID) createTaxWaybillId,
        case when A.TAX_WAYBILL_ID is not NULL then A.ORG_ID ELSE T.ORG_ID END createOrgId,
        T.RECEIVER receiver,
        T.OWNER_ORG_ID ownerOrgId,
        T.SETTLE_MODE settleMode,
        TO_CHAR(TE.ARRIVE_END_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveEndTime,
        TE.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
        TE.UNLOAD_TIME_FORMAT unloadTimeFormat,
        A.ORG_ID allocateOrgId,
        A.SETTLE_MODE allocateSettleMode,
        TE.CPD_POOL_GROUP_ID cpdPoolGroupId,
        T.WB_ITEM wbItem
        FROM
        T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TE ON
        T.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON
        T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE
        T.IS_DEL = 0
          <if test="boTransTaskId != null and boTransTaskId != ''">
                AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
          </if>
        AND T.TAX_WAYBILL_ID = #{taxWaybillId}
    </select>

    <select id="getTaxWaybillId" resultType="java.lang.String">
        SELECT
            NVL(T.TAX_WAYBILL_ID,TA.TAX_WAYBILL_ID) taxWaybillId
        FROM
        T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TA ON
        TA.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        AND TA.IS_DEL = 0
        WHERE
        T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.IS_DEL = 0
    </select>
    <select id="getTaxWaybillIdByTaskIdAndOrgId" resultType="java.lang.String">
        SELECT
            CASE WHEN T.ORG_ID = #{orgId} THEN  T.TAX_WAYBILL_ID
            ELSE TA.TAX_WAYBILL_ID END taxWaybillId
        FROM
            T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TA ON
            TA.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
            AND TA.IS_DEL = 0
        WHERE
            T.BO_TRANS_TASK_ID = #{boTransTaskId}
            AND T.IS_DEL = 0
    </select>
    <select id="getTaxWaybillIdPriorityAllocate" resultType="java.lang.String">
        SELECT
        NVL(TA.TAX_WAYBILL_ID,T.TAX_WAYBILL_ID) taxWaybillId
        FROM
        T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TA ON
        TA.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        AND TA.IS_DEL = 0
        WHERE
        T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.IS_DEL = 0
    </select>

    <select id="listBaseTaskDetail" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T1.BO_TRANS_TASK_ID boTransTaskId,
            T1.TAX_WAYBILL_ID taxWaybillId,
            T1.TAX_WAYBILL_NO taxWaybillNo,
            T1.ORG_ID ownerOrgId,
            T1.TAX_WAYBILL_ID ownerTaxWaybillId,
            T1.STATE state,
            T3.ORG_ID allocateOrgId,
            T3.TAX_WAYBILL_ID allocateTaxWaybillId,
            T2.BO_LINE_ASSIGN_REL_ID boLineAssignRelId
        FROM T_BO_TRANS_TASK T1
        JOIN T_BO_TRANS_TASK_EXTRA T2
            ON T2.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
            AND T2.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE T3
            ON T3.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
            AND T3.IS_DEL = 0
        WHERE
        T1.BO_TRANS_TASK_ID IN
        <choose>
            <when test="boTransTaskIds!=null and boTransTaskIds.size()>0">
                <foreach collection="boTransTaskIds"     item="transTaskId" open="(" separator="," close=")">
                    #{transTaskId}
                </foreach>
            </when>
            <when test="taxWaybillIds!=null and taxWaybillIds.size()>0">
                (
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE TAX_WAYBILL_ID IN
                <foreach collection="taxWaybillIds" item="waybillId" open="(" separator="," close=")">
                    #{waybillId}
                </foreach>
                UNION
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE  WHERE TAX_WAYBILL_ID IN
                <foreach collection="taxWaybillIds" item="waybillId" open="(" separator="," close=")">
                    #{waybillId}
                </foreach>
                AND IS_DEL =0
                )
            </when>
            <otherwise>
                (
                SELECT BO_TRANS_TASK_ID  FROM T_BO_TRANS_TASK WHERE ORG_ID = #{orgId} AND TAX_WAYBILL_NO IN
                <foreach collection="taxWaybillNos" item="taxWaybillNo" open="(" separator="," close=")">
                    #{taxWaybillNo}
                </foreach>
                UNION
                SELECT t.BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK t JOIN T_BO_TRANS_TASK_ALLOCATE a ON t.BO_TRANS_TASK_ID=a.BO_TRANS_TASK_ID AND a.IS_DEL=0 WHERE a.ORG_ID = #{orgId} AND t.TAX_WAYBILL_NO IN
                <foreach collection="taxWaybillNos" item="taxWaybillNo" open="(" separator="," close=")">
                    #{taxWaybillNo}
                </foreach>
                )
            </otherwise>
        </choose>
        <choose>
            <when test="queryStateList != null and queryStateList.size() > 0">
                AND T1.IS_DEL IN
                <foreach collection="queryStateList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND T1.IS_DEL = 0
            </otherwise>
        </choose>
    </select>

    <select id="queryBaseTaskDetail" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
        T1.BO_TRANS_TASK_ID boTransTaskId,
        T1.TAX_WAYBILL_ID taxWaybillId,
        T1.TAX_WAYBILL_NO taxWaybillNo,
        T1.TRANS_MODE transMode,
        T1.ORG_ID orgId,
        T1.SETTLE_MODE settleMode,
        T3.ORG_ID allocateOrgId,
        T3.TAX_WAYBILL_ID allocateTaxWaybillId,
        T3.SETTLE_MODE allocateSettleMode,
        T1.HYB_STATE hybState,
        T2.BO_LINE_ASSIGN_REL_ID boLineAssignRelId
        FROM T_BO_TRANS_TASK T1
        JOIN T_BO_TRANS_TASK_EXTRA T2
        ON T2.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
        AND T2.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE T3
        ON T3.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
        AND T3.IS_DEL = 0
        <where>
            <if test="isDel != null and isDel.length > 0">
                T1.IS_DEL = #{isDel}
            </if>
            AND T1.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIds"     item="transTaskId" open="(" separator="," close=")">
                #{transTaskId}
            </foreach>
        </where>
    </select>

    <select id="querySendCarCountWithMonth" resultType="com.wtyt.tt.bean.Resp5330236Bean$DispatchInfo">
        SELECT TO_CHAR(t.START_TIME, 'YYYYMM') dispatchCarDate,COUNT(1) dispatchCarCount FROM
            (SELECT
            bt.BO_TRANS_TASK_ID
            FROM
            T_BO_TRANS_TASK bt
            WHERE
                <include refid="querySendCarCountWithMonth_conmmonCondition"/>
                <if test="orgIdList != null and orgIdList.size() > 0">
                    AND bt.ORG_ID IN
                    <foreach collection="orgIdList" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="orgInfos != null and orgInfos.size() > 0">
                    AND
                    <foreach collection="orgInfos" index="index" item="orgInfo" open="(" close=")" separator="or">
                        (
                        bt.ORG_ID = #{orgInfo.orgId}
                        <if test="orgInfo.wbItemList != null and orgInfo.wbItemList.size() > 0">
                            AND bt.WB_ITEM IN
                            <foreach collection="orgInfo.wbItemList" index="index" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                        </if>
                        )
                    </foreach>
                </if>
            UNION
            SELECT
            bt.BO_TRANS_TASK_ID
            FROM
            T_BO_TRANS_TASK bt
            JOIN T_BO_TRANS_TASK_ALLOCATE btta ON
            btta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND btta.IS_DEL = 0
            WHERE
                <include refid="querySendCarCountWithMonth_conmmonCondition"/>
                <if test="orgIdList != null and orgIdList.size() > 0">
                    AND btta.ORG_ID IN
                    <foreach collection="orgIdList" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="orgInfos != null and orgInfos.size() > 0">
                    AND
                    <foreach collection="orgInfos" index="index" item="orgInfo" open="(" close=")" separator="or">
                        (
                        btta.ORG_ID = #{orgInfo.orgId}
                        <if test="orgInfo.wbItemList != null and orgInfo.wbItemList.size() > 0">
                            AND bt.WB_ITEM IN
                            <foreach collection="orgInfo.wbItemList" index="index" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                        </if>
                        )
                    </foreach>
                </if>
            ) temp
        JOIN T_BO_TRANS_TASK t ON t.BO_TRANS_TASK_ID=temp.BO_TRANS_TASK_ID
        GROUP BY
        TO_CHAR(t.START_TIME, 'YYYYMM')
        ORDER BY
        dispatchCarDate ASC
    </select>

    <sql id="querySendCarCountWithMonth_conmmonCondition">
        bt.IS_DEL = 0
        <if test="startProvinceName != null and startProvinceName != ''">
            AND bt.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName != null and startCityName != ''">
            AND bt.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName != null and startCountyName != ''">
            AND bt.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="endProvinceName != null and endProvinceName != ''">
            AND bt.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName != null and endCityName != ''">
            AND bt.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName != null and endCountyName != ''">
            AND bt.END_COUNTY_NAME = #{endCountyName}
        </if>
        <if test="wbItemList != null and wbItemList.size() > 0">
            AND bt.WB_ITEM IN
            <foreach collection="wbItemList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        AND bt.START_TIME >= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        AND bt.START_TIME &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
    </sql>

    <select id="queryTransTaskPageForOwnerDashboard" resultType="com.wtyt.tt.bean.Resp5330235Bean"
            parameterType="com.wtyt.tt.bean.Req5330235Bean">
        SELECT
            bt.TAX_WAYBILL_ID taxWaybillId,
            btta.TAX_WAYBILL_ID allocateTaxWaybillId,
            bt.SETTLE_MODE settleMode,
            btta.SETTLE_MODE allocateSettleMode,
            bt.NODE_ID nodeId,
            btta.NODE_ID allocateNodeId,
            bt.ORG_ID orgId,
            btta.ORG_ID allocateOrgId,
            bt.TAX_WAYBILL_NO taxWaybillNo,
            bt.DRIVER_NAME driverName,
            TO_CHAR(bt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') createdTime,
            TO_CHAR(bt.START_TIME, 'yyyy-mm-dd hh24:mi:ss') startTime,
            bt.MOBILE_NO mobileNo,
            bt.CART_BADGE_NO cartBadgeNo,
            bt.CART_BADGE_COLOR cartBadgeColor,
            bte.CART_TYPE cartType,
            bte.CART_LENGTH cartLength,
            bte.HYB_USER_ID hybUserId,
            bte.DRIVER_ID driverId,
            TO_CHAR(bte.HYB_RECEIVED_TIME, 'yyyy-MM-dd Hh24:mi:ss') hybReceivedTime,
            bt.HYB_STATE hybState,
            bt.STATE state,
            bt.PAY_STATE payState,
            bt.BO_TRANS_TASK_ID boTransTaskId,
            bt.START_CITY_NAME || bt.START_COUNTY_NAME loadingAddressName,
            bt.END_CITY_NAME || bt.END_COUNTY_NAME unloadingAddressName,
            bt.START_LONGITUDE startLongitude,
            bt.START_LATITUDE startLatitude,
            bt.END_LONGITUDE endLongitude,
            bt.END_LATITUDE endLatitude,
            bt.START_PROVINCE_NAME startProvinceName,
            bt.START_PROVINCE_CODE startProvinceCode,
            bt.START_CITY_NAME startCityName,
            bt.START_CITY_CODE startCityCode,
            bt.START_COUNTY_NAME startCountyName,
            bt.START_COUNTY_CODE startCountyCode,
            bt.END_PROVINCE_NAME endProvinceName,
            bt.END_PROVINCE_CODE endProvinceCode,
            bt.END_CITY_NAME endCityName,
            bt.END_CITY_CODE endCityCode,
            bt.END_COUNTY_NAME endCountyName,
            bt.END_COUNTY_CODE endCountyCode,
            bt.GOODS_NAME goodsName,
            bt.GOODS_AMOUNT goodsAmount,
            bt.GOODS_AMOUNT_TYPE goodsAmountType,
            BTE.TRANSPORT_NODE transportNode,
            NVL(bte.CPD_POOL_GROUP_NAME,NVL(bt.CAPACITY_TYPE_NAME,'社会临调车')) capacityTypeName
        FROM
            T_BO_TRANS_TASK bt
            <if test="boTransTaskIdList == null or boTransTaskIdList.isEmpty()">
                JOIN (
                    SELECT BT.BO_TRANS_TASK_ID
                    FROM
                    T_BO_TRANS_TASK bt
                    JOIN T_BO_TRANS_TASK_EXTRA bte ON
                    bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                    WHERE
                    <include refid="ownerDashboardCommonQuerySql"/>
                    <if test="orgIdList != null and orgIdList.size() > 0">
                        AND bt.ORG_ID IN
                        <foreach collection="orgIdList" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="orgInfos != null and orgInfos.size() > 0">
                        AND
                        <foreach collection="orgInfos" index="index" item="orgInfo" open="(" close=")" separator="or">
                            (
                            bt.ORG_ID = #{orgInfo.orgId}
                            <if test="orgInfo.wbItemList != null and orgInfo.wbItemList.size() > 0">
                                AND bt.WB_ITEM IN
                                <foreach collection="orgInfo.wbItemList" index="index" item="item" open="(" close=")" separator=",">
                                    #{item}
                                </foreach>
                            </if>
                            )
                        </foreach>
                    </if>
                    UNION
                    SELECT BT.BO_TRANS_TASK_ID
                    FROM
                    T_BO_TRANS_TASK bt
                    JOIN T_BO_TRANS_TASK_EXTRA bte ON
                    bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                    JOIN T_BO_TRANS_TASK_ALLOCATE btta ON
                    btta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                    AND btta.IS_DEL = 0
                    WHERE
                    <include refid="ownerDashboardCommonQuerySql"/>
                    <if test="orgIdList != null and orgIdList.size() > 0">
                        AND btta.ORG_ID IN
                        <foreach collection="orgIdList" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="orgInfos != null and orgInfos.size() > 0">
                        AND
                        <foreach collection="orgInfos" index="index" item="orgInfo" open="(" close=")" separator="or">
                            (
                            btta.ORG_ID = #{orgInfo.orgId}
                            <if test="orgInfo.wbItemList != null and orgInfo.wbItemList.size() > 0">
                                AND bt.WB_ITEM IN
                                <foreach collection="orgInfo.wbItemList" index="index" item="item" open="(" close=")" separator=",">
                                    #{item}
                                </foreach>
                            </if>
                            )
                        </foreach>
                    </if>
                ) TEMP ON bt.BO_TRANS_TASK_ID = TEMP.BO_TRANS_TASK_ID
            </if>
            JOIN T_BO_TRANS_TASK_EXTRA bte ON
            bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE btta ON
            btta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND btta.IS_DEL = 0
            <if test="boTransTaskIdList != null and boTransTaskIdList.size() > 0">
                WHERE
                <include refid="ownerDashboardCommonQuerySql"/>
                AND BT.BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
                    #{boTransTaskId}
                </foreach>
            </if>
        ORDER BY BT.CREATED_TIME DESC
    </select>

    <!--公共的查询条件，如果要带分配表，需要注意应用的地方是否都关联了分配表 -->
    <sql id="ownerDashboardCommonQuerySql">
        bt.IS_DEL =0
        <if test="startDate != null and startDate !=''">
            AND bt.CREATED_TIME &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate !=''">
            AND bt.CREATED_TIME &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startProvinceName != null and startProvinceName != ''">
            AND bt.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName != null and startCityName != ''">
            AND bt.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName != null and startCountyName != ''">
            AND bt.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="endProvinceName != null and endProvinceName != ''">
            AND bt.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName != null and endCityName != ''">
            AND bt.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName != null and endCountyName != ''">
            AND bt.END_COUNTY_NAME = #{endCountyName}
        </if>
        <if test="searchKeyword !=null and searchKeyword != ''">
            <choose>
                <when test="searchKeywordType == '1'.toString()">
                    AND BT.DRIVER_NAME = #{searchKeyword}
                </when>
                <when test="searchKeywordType == '2'.toString()">
                    AND BT.MOBILE_NO = #{searchKeyword}
                </when>
                <when test="searchKeywordType == '3'.toString()">
                    AND BT.CART_BADGE_NO = #{searchKeyword}
                </when>
                <when test="searchKeywordType == '4'.toString()">
                    AND BT.TAX_WAYBILL_NO = #{searchKeyword}
                </when>
                <when test="searchKeywordType == '8'.toString()">
                    AND EXISTS (
                    SELECT 1
                    FROM T_BO_SHIPPING_LIST SL
                    WHERE SL.IS_DEL = 0
                    AND BT.BO_TRANS_TASK_ID = SL.BO_TRANS_TASK_ID
                    AND SL.CUSTOMER_ORDER_NO = #{searchKeyword}
                    )
                </when>
                <otherwise>
                    AND (
                    bt.TAX_WAYBILL_NO = #{searchKeyword} OR
                    bt.CART_BADGE_NO = #{searchKeyword} OR
                    bt.MOBILE_NO = #{searchKeyword} OR
                    bt.DRIVER_NAME = #{searchKeyword}
                    OR EXISTS (
                    SELECT 1
                    FROM T_BO_SHIPPING_LIST SL
                    WHERE SL.IS_DEL = 0
                    AND BT.BO_TRANS_TASK_ID = SL.BO_TRANS_TASK_ID
                    AND SL.CUSTOMER_ORDER_NO = #{searchKeyword}
                    )
                    )
                </otherwise>
            </choose>
        </if>
        <if test="beginStartTime != null and beginStartTime.length > 0">
            AND bt.START_TIME >= TO_DATE(#{beginStartTime}, 'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="endStartTime != null and endStartTime.length > 0">
            AND bt.START_TIME <![CDATA[<=]]> TO_DATE(#{endStartTime}, 'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="userId != null and userId !=''">
            AND EXISTS(SELECT 1 FROM T_BO_TRANS_TASK_USERS TU WHERE TU.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID AND TU.IS_DEL = 0 AND TU.USER_ID = #{userId})
        </if>
        <choose>
            <when test="tabStatusList != null and tabStatusList.size() > 0">
                <foreach collection="tabStatusList" item="tabStatus" open="AND (" separator=" OR " close=")">
                    <choose>
                        <when test="tabStatus == 0">
                            BTE.DISPATCH_CAR_RECORD_ID IS NOT NULL
                        </when>
                        <when test="tabStatus == 2">
                            (BTE.DISPATCH_CAR_RECORD_ID IS NOT NULL AND BTE.ARRIVE_TIME IS NULL AND BTE.TRANSPORT_NODE IS NULL)
                        </when>
                        <when test="tabStatus == 3">
                            (BTE.ARRIVE_TIME IS NOT NULL AND BT.STATE = 0 AND BTE.TRANSPORT_NODE IS NULL)
                        </when>
                        <when test="tabStatus == 6">
                            (BT.STATE = 2 AND BT.PAY_STATE = 2)
                        </when>
                        <when test="tabStatus == 10">
                            (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) NOT IN (0, 2))
                        </when>
                        <when test="tabStatus == 14">
                            (BTE.TRANSPORT_NODE = 600
                                AND NOT (
                                    (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) = 2) OR (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) NOT IN (0, 2))
                                )
                            )
                        </when>
                        <when test="tabStatus == 15">
                            (BTE.TRANSPORT_NODE = 650
                                AND NOT (
                                    (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) = 2) OR (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) NOT IN (0, 2))
                                )
                            )
                        </when>
                        <when test="tabStatus == 16">
                            (BTE.TRANSPORT_NODE = 800
                                AND NOT (
                                    (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) = 2) OR (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) NOT IN (0, 2))
                                )
                            )
                        </when>
                        <otherwise>
                            BT.BO_TRANS_TASK_ID = -1
                        </otherwise>
                    </choose>
                </foreach>
            </when>
            <otherwise>
                <choose>
                    <when test="queryOverStatusTask and nodeIdQueryList != null and nodeIdQueryList.size() > 0">
                        AND (BT.NODE_ID IN
                        <foreach collection="nodeIdQueryList" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                        OR BT.STATE = 2)
                    </when>
                    <otherwise>
                        <if test="nodeIdQueryList != null and nodeIdQueryList.size() > 0">
                            AND BT.NODE_ID IN
                            <foreach collection="nodeIdQueryList" index="index" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                        </if>
                        <if test="queryOverStatusTask">
                            AND BT.STATE = 2
                        </if>
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </sql>

    <select id="listTransTaskByWaybillId" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            BT.BO_TRANS_TASK_ID boTransTaskId,
            BT.ORG_ID orgId,
            tta.ORG_ID allocateOrgId,
            bt.ORDER_CREATE_TYPE orderCreateType,
            bte.AUDIT_STATUS auditStatus,
            BT.TAX_WAYBILL_ID taxWayBillId,
            bt.TAX_WAYBILL_NO taxWayBillNo,
            TTA.TAX_WAYBILL_ID allocateTaxWaybillId,
            NVL(BTE.USER_FREIGHT_APPROVAL_STATUS,0) userFreightApprovalStatus,
            BTE.TRANS_VOUCHER_AUDIT_STATUS transVoucherAuditStatus
        FROM T_BO_TRANS_TASK BT
        JOIN T_BO_TRANS_TASK_EXTRA BTE
            ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND BTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TTA
            ON TTA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND TTA.IS_DEL = 0
        WHERE BT.IS_DEL = 0
        AND BT.BO_TRANS_TASK_ID IN
        (
        SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE IS_DEL = 0 AND TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        UNION
        SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE IS_DEL = 0 AND TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        )
    </select>

    <select id="listBaseByWaybillId" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            BT.BO_TRANS_TASK_ID boTransTaskId,
            BT.ORG_ID orgId,
            tta.ORG_ID allocateOrgId,
            BT.TAX_WAYBILL_ID taxWayBillId,
            TTA.TAX_WAYBILL_ID allocateTaxWaybillId,
            BT.IS_DEL isDel
        FROM T_BO_TRANS_TASK BT
        JOIN T_BO_TRANS_TASK_EXTRA BTE
        ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
        AND BTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TTA
        ON TTA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
        AND TTA.IS_DEL = 0
        WHERE
        BT.BO_TRANS_TASK_ID IN
        (
        SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        UNION
        SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE IS_DEL = 0 AND TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        )
        <if test="isDel!=null and isDel!=''">
            AND BT.IS_DEL = #{isDel}
        </if>
    </select>

    <select id="listTaskDetailByTaskId" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            BT.TAX_WAYBILL_ID taxWayBillId,
            BT.BO_TRANS_TASK_ID boTransTaskId,
            BT.TAX_WAYBILL_NO taxWaybillNo,
            BT.ORG_ID orgId,
            BT.OWNER_ORG_ID ownerOrgId,
            BT.ORDER_CREATE_TYPE orderCreateType,
            BT.TRANS_PATTERN transPattern,
            BT.NODE_ID nodeId,
            BT.SETTLE_MODE settleMode,
            BT.MOBILE_NO mobileNo,
            BT.START_PROVINCE_NAME startProvinceName,
            BT.START_PROVINCE_CODE startProvinceCode,
            BT.START_CITY_NAME startCityName,
            BT.START_CITY_CODE startCityCode,
            BT.START_COUNTY_NAME startCountyName,
            BT.START_COUNTY_CODE startCountyCode,
            BT.START_LONGITUDE startLongitude,
            BT.START_LATITUDE startLatitude,
            BT.END_PROVINCE_NAME endProvinceName,
            BT.END_PROVINCE_CODE endProvinceCode,
            BT.END_CITY_NAME endCityName,
            BT.END_CITY_CODE endCityCode,
            BT.END_COUNTY_NAME endCountyName,
            BT.END_COUNTY_CODE endCountyCode,
            BT.END_LONGITUDE endLongitude,
            BT.END_LATITUDE endLatitude,
            TO_CHAR(BT.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
            BTE.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
            BTE.UNLOAD_TIME_FORMAT unloadTimeFormat,
            BTE.AUDIT_STATUS auditStatus,
            BTE.LOADING_ADDRESS_NAME loadingAddressName,
            BTE.UNLOADING_ADDRESS_NAME unloadingAddressName,
            BTE.TRANS_TASK_FLAG transTaskFlag,
            BTE.APPOINT_STATUS appointStatus,
            NVL(BTE.USER_FREIGHT_APPROVAL_STATUS, 0) userFreightApprovalStatus,
            TTA.ORG_ID allocateOrgId,
            TTA.SETTLE_MODE allocateSettleMode,
            TTA.TAX_WAYBILL_ID allocateTaxWaybillId,
            NVL(TTA.TAX_WAYBILL_ID, BT.TAX_WAYBILL_ID) createTaxWaybillId,
            BT.DRIVER_NAME driverName,
            BT.CART_BADGE_NO cartBadgeNo,
            BT.CART_BADGE_COLOR cartBadgeColor,
            BT.DRIVER_ID_CARD driverIdCard,
            NVL(BT.TAX_WAYBILL_ID, TTA.TAX_WAYBILL_ID) locTaxWaybillId
        FROM T_BO_TRANS_TASK BT
        JOIN T_BO_TRANS_TASK_EXTRA BTE ON
            BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND BTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TTA ON
            TTA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND TTA.IS_DEL = 0
        WHERE BT.IS_DEL = 0
            AND BT.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <sql id="queryTransTaskTabCountWhereSql">
        <if test="boTransTaskIdList != null and boTransTaskIdList.size() > 0">
            AND BT.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
                #{boTransTaskId}
            </foreach>
        </if>
        <if test="startDate != null and startDate !=''">
            AND BT.CREATED_TIME &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate !=''">
            AND BT.CREATED_TIME &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startProvinceName != null and startProvinceName != ''">
            AND BT.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName != null and startCityName != ''">
            AND BT.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName != null and startCountyName != ''">
            AND BT.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="groupType == '2'.toString() and loadingAddressName != null and loadingAddressName != ''">
            AND BTE.LOADING_ADDRESS_NAME = #{loadingAddressName}
        </if>
        <if test="endProvinceName != null and endProvinceName != ''">
            AND BT.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName != null and endCityName != ''">
            AND BT.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName != null and endCountyName != ''">
            AND BT.END_COUNTY_NAME = #{endCountyName}
        </if>
        <if test="groupType == '2'.toString() and unloadingAddressName != null and unloadingAddressName != ''">
            AND BTE.UNLOADING_ADDRESS_NAME = #{unloadingAddressName}
        </if>
    </sql>

    <sql id="queryTransTaskTabStatusCountWhereSql">
        <if test="orgInfos != null and orgInfos.size() > 0">
            AND
            <foreach collection="orgInfos" index="index" item="orgInfo" open="(" close=")" separator="or">
                (
                (bt.ORG_ID = #{orgInfo.orgId} OR btta.ORG_ID = #{orgInfo.orgId})
                <if test="orgInfo.wbItemList != null and orgInfo.wbItemList.size() > 0">
                    AND bt.WB_ITEM IN
                    <foreach collection="orgInfo.wbItemList" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                )
            </foreach>
        </if>
        <if test="startDate != null and startDate !=''">
            AND bt.CREATED_TIME &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate !=''">
            AND bt.CREATED_TIME &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>

        <if test="startProvinceName != null and startProvinceName != ''">
            AND bt.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName != null and startCityName != ''">
            AND bt.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName != null and startCountyName != ''">
            AND bt.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="endProvinceName != null and endProvinceName != ''">
            AND bt.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName != null and endCityName != ''">
            AND bt.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName != null and endCountyName != ''">
            AND bt.END_COUNTY_NAME = #{endCountyName}
        </if>
    </sql>

    <select id="queryTransTaskTableCount" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            <if test="groupType == '2'.toString()">
                T.START_COUNTY_NAME startCountyName,
                T.LOADING_ADDRESS_NAME loadingAddressName,
            </if>
            COUNT(CASE WHEN T.DISPATCH_CAR_RECORD_ID IS NULL THEN 1 ELSE NULL END) AS waitDispatcherCarCount,
            COUNT(CASE WHEN T.DISPATCH_CAR_RECORD_ID IS NOT NULL AND T.ARRIVE_TIME IS NULL AND T.TRANSPORT_NODE IS NULL THEN 1 ELSE NULL END) AS waitArrivalCount,
            COUNT(CASE WHEN T.ARRIVE_TIME IS NOT NULL AND T.STATE = 0 AND T.TRANSPORT_NODE IS NULL THEN 1 ELSE NULL END) AS waitSendCarCount,
            COUNT(CASE WHEN T.STATE = 1 THEN 1 ELSE NULL END) AS inTransitCount,
            COUNT(
                CASE
                    WHEN
                        T.TRANSPORT_NODE = 600
                        AND NOT (
                            (T.STATE = 2 AND NVL(T.PAY_STATE, 0) = 2) OR (T.STATE = 2 AND NVL(T.PAY_STATE, 0) NOT IN (0, 2))
                        ) THEN 1
                    ELSE NULL
                END
            ) AS inTransportCount,
            COUNT(CASE WHEN T.NODE_ID IN (700) THEN 1 ELSE NULL END) AS transportCompletedCount,
            COUNT(CASE WHEN T.STATE = 2 THEN 1 ELSE NULL END) AS completedCount,
            COUNT(
                CASE
                    WHEN
                        T.TRANSPORT_NODE = 650
                        AND NOT (
                            (T.STATE = 2 AND NVL(T.PAY_STATE, 0) = 2) OR (T.STATE = 2 AND NVL(T.PAY_STATE, 0) NOT IN (0, 2))
                        ) THEN 1
                    ELSE NULL
                END
            ) AS arrivedEndPlaceCount,
            COUNT(
                CASE
                    WHEN
                        T.TRANSPORT_NODE = 800
                        AND NOT (
                            (T.STATE = 2 AND NVL(T.PAY_STATE, 0) = 2) OR (T.STATE = 2 AND NVL(T.PAY_STATE, 0) NOT IN (0, 2))
                        ) THEN 1
                    ELSE NULL
                END
            ) AS unloadReceiptCount,
            COUNT(
                CASE
                    WHEN T.TRANSPORT_NODE = 800 THEN 1
                    ELSE NULL
                END
            ) AS historyUnloadReceiptCount,
            COUNT(*) AS taskCount
        FROM (
            SELECT
                <if test="groupType == '2'.toString()">
                    BT.START_COUNTY_NAME,
                    BTE.LOADING_ADDRESS_NAME,
                </if>
                BT.BO_TRANS_TASK_ID,
                BT.MOBILE_NO,
                BT.NODE_ID,
                BT.STATE,
                BT.PAY_STATE,
                BT.START_PROVINCE_NAME,
                BT.START_CITY_NAME,
                BTE.ARRIVE_TIME,
                BTE.TRANSPORT_NODE,
                BTE.DISPATCH_CAR_RECORD_ID
            FROM T_BO_TRANS_TASK BT
            JOIN T_BO_TRANS_TASK_EXTRA BTE
                ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            WHERE BT.IS_DEL = 0
            <if test="orgInfos != null and orgInfos.size() > 0">
                AND
                <foreach collection="orgInfos" index="index" item="orgInfo" open="(" close=")" separator="or">
                    (BT.ORG_ID = #{orgInfo.orgId}
                    <if test="orgInfo.wbItemList != null and orgInfo.wbItemList.size() > 0">
                        AND BT.WB_ITEM IN
                        <foreach collection="orgInfo.wbItemList" index="index" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>)
                </foreach>
            </if>
            <include refid="queryTransTaskTabCountWhereSql"/>

            <if test="orgInfos != null and orgInfos.size() > 0">
                UNION

                SELECT
                    <if test="groupType == '2'.toString()">
                        BT.START_COUNTY_NAME,
                        BTE.LOADING_ADDRESS_NAME,
                    </if>
                    BT.BO_TRANS_TASK_ID,
                    BT.MOBILE_NO,
                    BT.NODE_ID,
                    BT.STATE,
                    BT.PAY_STATE,
                    BT.START_PROVINCE_NAME,
                    BT.START_CITY_NAME,
                    BTE.ARRIVE_TIME,
                    BTE.TRANSPORT_NODE,
                    BTE.DISPATCH_CAR_RECORD_ID
                FROM T_BO_TRANS_TASK_ALLOCATE BTA
                JOIN T_BO_TRANS_TASK BT
                    ON BT.BO_TRANS_TASK_ID = BTA.BO_TRANS_TASK_ID
                    AND BTA.IS_DEL = 0
                JOIN T_BO_TRANS_TASK_EXTRA BTE
                    ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                WHERE BTA.IS_DEL = 0
                <if test="orgInfos != null and orgInfos.size() > 0">
                    AND
                    <foreach collection="orgInfos" index="index" item="orgInfo" open="(" close=")" separator="or">
                        (BTA.ORG_ID = #{orgInfo.orgId}
                        <if test="orgInfo.wbItemList != null and orgInfo.wbItemList.size() > 0">
                            AND BT.WB_ITEM IN
                            <foreach collection="orgInfo.wbItemList" index="index" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                        </if>)
                    </foreach>
                </if>
                <include refid="queryTransTaskTabCountWhereSql"/>
            </if>
        )T
        GROUP BY
        <choose>
            <when test="groupType == '2'.toString()">
                T.START_PROVINCE_NAME,
                T.START_CITY_NAME,
                T.START_COUNTY_NAME,
                T.LOADING_ADDRESS_NAME
            </when>
            <otherwise>
                T.START_PROVINCE_NAME,
                T.START_CITY_NAME
            </otherwise>
        </choose>
    </select>

    <select id="batchQueryTaxWaybill" resultType="com.wtyt.tt.bean.Resp5330239Bean"
            parameterType="java.util.List">
        SELECT
        <![CDATA[
        CASE WHEN bt.trans_mode = 3 AND bt.OWNER_ORG_ID = bt.ORG_ID THEN A.TAX_WAYBILL_ID
        WHEN bt.trans_mode = 4 AND bt.OWNER_ORG_ID = bt.ORG_ID THEN A.TAX_WAYBILL_ID
        WHEN bt.trans_mode = 3 AND bt.OWNER_ORG_ID <> bt.ORG_ID THEN bt.TAX_WAYBILL_ID
        WHEN bt.trans_mode = 4 AND bt.OWNER_ORG_ID <> bt.ORG_ID THEN bt.TAX_WAYBILL_ID
        ELSE bt.TAX_WAYBILL_ID END taxWaybillId,
        CASE WHEN bt.trans_mode = 3 AND bt.OWNER_ORG_ID = bt.ORG_ID THEN A.NODE_ID
        WHEN bt.trans_mode = 4 AND bt.OWNER_ORG_ID = bt.ORG_ID THEN A.NODE_ID
        WHEN bt.trans_mode = 3 AND bt.OWNER_ORG_ID <> bt.ORG_ID THEN bt.NODE_ID
        WHEN bt.trans_mode = 4 AND bt.OWNER_ORG_ID <> bt.ORG_ID THEN bt.NODE_ID
        ELSE bt.NODE_ID END nodeId,
        ]]>
        bt.BO_TRANS_TASK_ID boTransTaskId
        FROM T_BO_TRANS_TASK bt LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON A.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE bt.IS_DEL = 0 AND bt.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="countDispatchTransportTaskNodeIdGroup" resultType="com.wtyt.tt.bean.TaskTabCountBean">
            SELECT
                bt.STATE state,
                tta.PAY_STATE payState,
                tta.NODE_ID nodeId,
                COUNT(*) count
            FROM
                <include refid="fromTableForPC"></include>
            WHERE
                <include refid="queryTransportTaskTableCondition"/>
                GROUP BY bt.STATE,tta.PAY_STATE,tta.NODE_ID
    </select>

    <select id="queryNoHybUserTask" resultType="com.wtyt.bo.bean.BoTaskDetailBean" fetchSize="100">
         SELECT
            bt.BO_TRANS_TASK_ID boTransTaskId,
            bt.TAX_WAYBILL_NO taxWaybillNo,
            bt.ORG_ID orgId,
            bt.DRIVER_NAME driverName,
            TO_CHAR(bt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') createdTime,
            bt.MOBILE_NO mobileNo,
            bt.CART_BADGE_NO cartBadgeNo,
            bte.CART_TYPE cartType,
            bte.CART_LENGTH cartLength,
            bte.HYB_USER_ID hybUserId,
            bte.DRIVER_ID driverId
        FROM
        T_BO_TRANS_TASK bt
        LEFT JOIN T_BO_TRANS_TASK_EXTRA bte ON
        bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
        AND bte.IS_DEL = 0
        WHERE
        bt.IS_DEL =0
        and bte.DISPATCH_CAR_RECORD_ID is not null
        and bte.HYB_USER_ID is null
        and bte.DRIVER_ID is null
        and bt.CART_BADGE_NO is not null
        and bt.MOBILE_NO is not null
        and bte.DISPATCH_CAR_TIME >= sysdate - #{beforeDays}
    </select>

    <select id="queryDzCompleteList" resultType="com.wtyt.board.bean.BoardCompleteBean">
        SELECT
            BO_BUSINESS_LINE_ID boBusinessLineId,
            TO_CHAR(START_TIME, 'YYYY-MM-DD') completeDate,
            nvl(GOODS_AMOUNT_TYPE,0) goodsAmountType,
            SUM(LOADING_TONNAGE) loadingTonnage
        FROM
            t_bo_trans_task t
        WHERE
            t.is_del = 0 AND t.TRANSPORT_TYPE =1 AND t.START_TIME >= TO_DATE(#{completeDate}||'00:00:00','YYYY-MM-DD HH24:MI:SS') AND T.LOADING_TONNAGE > 0
            AND BO_BUSINESS_LINE_ID IS NOT NULL AND START_TIME IS NOT NULL
        GROUP BY
            BO_BUSINESS_LINE_ID,TO_CHAR(START_TIME, 'YYYY-MM-DD'),nvl(GOODS_AMOUNT_TYPE,0)
    </select>
    <select id="queryTaskAppLabel" resultType="com.wtyt.bo.bean.BoTaskDetailBean" fetchSize="500">
        SELECT
            bt.BO_TRANS_TASK_ID boTransTaskId,
            bt.TRANSPORT_TYPE transportType,
            CASE
            WHEN bt.ORG_ID = #{orgId} THEN bt.TAX_WAYBILL_ID
            ELSE A.TAX_WAYBILL_ID
            END taxWaybillId,
            CASE
            WHEN bt.ORG_ID = #{orgId} THEN bt.PAY_STATE
            ELSE A.PAY_STATE
            END payState
        FROM
            <include refid="fromTableForApp" />
        WHERE bt.IS_DEL =0
            <if test="taskBeginTime!=null and taskBeginTime!=''">
                AND bt.CREATED_TIME >= TO_DATE(#{taskBeginTime},'YYYY-MM-DD')
            </if>
            <if test="taskEndTime!=null and taskEndTime!=''">
                AND bt.CREATED_TIME &lt;= TO_DATE(#{taskEndTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
            </if>
        <include refid="tabStateQueryConditionApp" />

    </select>

    <sql id="fromTableForApp">
        <choose>
            <when test="noSupplier!=null and noSupplier">
                T_BO_TRANS_TASK bt
            </when>
            <otherwise>
                <include refid="taskRightTempTable"/> TEMP
                JOIN T_BO_TRANS_TASK bt
                ON TEMP.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            </otherwise>
        </choose>
        JOIN T_BO_TRANS_TASK_EXTRA bte ON bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID AND bte.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON bt.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        <if test="dispatchCarUserIds !=null and dispatchCarUserIds.size()>0">
            LEFT JOIN T_BO_TRANS_NODE_RECORD NR
            ON NR.BO_TRANS_NODE_RECORD_ID = BTE.DISPATCH_CAR_RECORD_ID
            AND NR.IS_DEL = 0
        </if>
    </sql>

    <select id="countNotAccepted" resultType="java.lang.String">
        SELECT
            bt.BO_TRANS_TASK_ID
        FROM
            <include refid="fromTableForApp" />
        WHERE bt.IS_DEL =0
        <include refid="tabStateQueryConditionApp" />
        AND NVL(bt.HYB_STATE,0) = 0
        AND bt.IS_PARTAKE_OPERATE =1
    </select>

    <select id="queryPlaceHisInfos" parameterType="com.wtyt.bo.bean.request.Req1735209Bean"
            fetchSize="100" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            bt.BO_TRANS_TASK_ID boTransTaskId,
            bt.OWNER_ORG_ID ownerOrgId,
            bt.START_PROVINCE_NAME startProvinceName,
            bt.START_CITY_NAME startCityName,
            bt.START_COUNTY_NAME startCountyName,
            bt.END_PROVINCE_NAME endProvinceName,
            bt.END_CITY_NAME endCityName,
            bt.END_COUNTY_NAME endCountyName,
            bt.TRANSPORT_TYPE transportType,
            bt.LOADING_PLACE_NAME loadingPlaceName,
            bt.UNLOADING_PLACE_NAME unloadingPlaceName,
            bte.LOADING_ADDRESS_NAME loadingAddressName,
            bte.UNLOADING_ADDRESS_NAME unloadingAddressName,
            TO_CHAR(bt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') createdTime
        FROM
        <include refid="taskRightTempTable"/>
        TEMP
        JOIN T_BO_TRANS_TASK bt
        ON TEMP.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID AND bt.IS_DEL =0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA bte ON bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID AND bte.IS_DEL = 0
        WHERE bt.IS_DEL =0
        <choose>
            <when test="searchKeywordsType == 1">
             and   INSTR(bt.START_PROVINCE_NAME||bt.START_CITY_NAME||bt.START_COUNTY_NAME||bt.LOADING_PLACE_NAME||bte.LOADING_ADDRESS_NAME,#{searchKeyword})
                > 0
            </when>
            <when test="searchKeywordsType == 2">
                and   INSTR(bt.END_PROVINCE_NAME||bt.END_CITY_NAME||bt.END_COUNTY_NAME||bt.UNLOADING_PLACE_NAME||bte.UNLOADING_ADDRESS_NAME,#{searchKeyword})
                > 0
            </when>
            <otherwise>
                and  1 = 0
            </otherwise>
        </choose>
        order by bt.CREATED_TIME desc
    </select>
    <select id="queryTaskAllFreight" resultType="com.wtyt.commons.bean.StringBigDecimalPair">
        SELECT
            BO_TRANS_TASK_ID key,
            NVL(ALL_FREIGHT, 0) + NVL(SERVICE_FEE, 0) -NVL(PREPAYMENTS_OILCARD, 0)  value
        FROM
            T_BO_TRANS_TASK tbtt
        WHERE
            BO_TRANS_TASK_ID IN
        <foreach collection="list" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </select>
    <select id="queryWbItemWithPermission" resultType="java.lang.String"
            parameterType="com.wtyt.bo.bean.request.Req1735209Bean">
        SELECT
            WB_ITEM
        FROM
            (
                SELECT
                ST.WB_ITEM,
                ROW_NUMBER() OVER(PARTITION BY ST.WB_ITEM ORDER BY ST.CREATED_TIME DESC) RN
                FROM
                <include refid="taskRightTempTable"/> TEMP
                JOIN T_BO_TRANS_TASK ST
                ON
                TEMP.BO_TRANS_TASK_ID = ST.BO_TRANS_TASK_ID
                AND ST.IS_DEL = 0
                WHERE
                ST.WB_ITEM IS NOT NULL
                <if test="createdTimeStart !=null and createdTimeStart!=''">
                    AND ST.CREATED_TIME &gt;= to_date(#{createdTimeStart},'yyyy-MM-dd')
                </if>
            )
        WHERE
            RN = 1
    </select>

    <select id="queryPrepaymentsOilcard" resultType="com.wtyt.commons.bean.StringBigDecimalPair">
        SELECT
        BO_TRANS_TASK_ID key,
        NVL(PREPAYMENTS_OILCARD, 0) value
        FROM
        T_BO_TRANS_TASK tbtt
        WHERE
        BO_TRANS_TASK_ID IN
        <foreach collection="list" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </select>
    <select id="countDispatchedNotSettlementTask" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM
        T_BO_TRANS_TASK bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
        bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
        AND bte.IS_DEL = 0
        WHERE
        bt.IS_DEL = 0
        AND ORG_ID = #{orgId}
        AND bt.SETTLE_MODE = 2
        AND (
            bt.MOBILE_NO = #{mobileNo}
            OR bt.CART_BADGE_NO  = #{cartBadgeNo}
        )
        AND bt.STATE != 2
        AND bte.DISPATCH_CAR_RECORD_ID IS NOT NULL
    </select>
    
    
    
    
    <select id="getTaskDetailByNosOrIds" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
        <include refid="taskDetailCommonField"></include>
        FROM
        T_BO_TRANS_TASK T
        inner JOIN T_BO_TRANS_TASK_EXTRA TE
        ON
        TE.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        AND TE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE
        T.IS_DEL = 0
        <if test="orgId != null and orgId !=''">
            AND T.ORG_ID = #{orgId}
        </if>
        <if test="boTransTaskIdList!=null and boTransTaskIdList.size>0">
            AND T.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="taxWaybillNos!=null and taxWaybillNos.size > 0">
            AND T.TAX_WAYBILL_NO IN
            <foreach collection="taxWaybillNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <sql id="getOrgSelfTaskDetailByNosOrIds_commonQuery">
        <if test="boTransTaskIdList!=null and boTransTaskIdList.size>0">
            AND T.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="taxWaybillNos!=null and taxWaybillNos.size > 0">
            AND T.TAX_WAYBILL_NO IN
            <foreach collection="taxWaybillNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </sql>
    <select id="getOrgSelfTaskDetailByNosOrIds" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_ID taxWaybillId
        FROM
            T_BO_TRANS_TASK T
        WHERE
            T.IS_DEL = 0
            AND T.ORG_ID = #{orgId}
            <include refid="getOrgSelfTaskDetailByNosOrIds_commonQuery"/>
        UNION ALL
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            A.TAX_WAYBILL_ID taxWaybillId
        FROM
            T_BO_TRANS_TASK T
        JOIN T_BO_TRANS_TASK_ALLOCATE A ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE
            A.ORG_ID = #{orgId}
            <include refid="getOrgSelfTaskDetailByNosOrIds_commonQuery"/>
    </select>

    <select id="queryDispatcherUserId" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT
            r.USER_ID
        FROM
            T_BO_TRANS_TASK t
                LEFT JOIN T_BO_TRANS_TASK_EXTRA e ON
                        t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
                    AND e.IS_DEL = 0
                INNER JOIN T_BO_TRANS_NODE_RECORD r ON
                        r.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
                    AND r.BO_TRANS_NODE_RECORD_ID = e.DISPATCH_CAR_RECORD_ID
                    AND r.IS_DEL = 0
        WHERE
            t.IS_DEL = 0
          AND t.BO_TRANS_TASK_ID = #{boTransTaskId}
          AND ROWNUM = 1
        ORDER BY
            r.CREATED_TIME DESC
    </select>

    <select id="queryTransTaskDetail" resultType="com.wtyt.bo.bean.BoTaskDetailBean"
            parameterType="com.wtyt.bo.bean.request.Req1735216Bean">
        SELECT
        <include refid="taskDetailCommonField"/>
        FROM
        <include refid="queryTransTaskDetailTableSql"/>
        <include refid="queryTransTaskDetailWhereSql"/>
    </select>

    <select id="queryHasRightTaskId" resultType="java.lang.String">
        <include refid="taskRightCondition"></include>
        AND BT.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="queryLastTaskWithinMonthByStartTime" resultType="com.wtyt.bo.bean.BoTaskDetailBean"
            parameterType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            BO_TRANS_TASK_ID AS boTransTaskId
        FROM
            (
                SELECT
                    t.BO_TRANS_TASK_ID
                FROM
                    T_BO_TRANS_TASK t
                        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE a ON
                                t.BO_TRANS_TASK_ID = a.BO_TRANS_TASK_ID
                            AND a.IS_DEL = 0
                WHERE
                  t.IS_DEL = 0
                  AND t.ORG_ID = #{orgId}
                  AND <![CDATA[ t.BO_TRANS_TASK_ID <> #{boTransTaskId} ]]>
                  AND t.START_TIME >= ADD_MONTHS(SYSDATE, -1)
                  AND nvl(t.SETTLE_MODE, 0) != 2
		          AND nvl(a.SETTLE_MODE, 0) != 2
                  AND t.MOBILE_NO = #{mobileNo}
                ORDER BY
                    t.START_TIME DESC
            )
        WHERE
            ROWNUM = 1
    </select>
    <select id="queryUpLoadingOrReceiptNodeRecordCount" resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
            T_BO_TRANS_TASK t
                INNER JOIN T_BO_TRANS_NODE_RECORD r ON
                        t.BO_TRANS_TASK_ID = r.BO_TRANS_TASK_ID
                    AND r.IS_DEL = 0
                    AND r.NODE_ID IN (510, 800)
        WHERE
            t.IS_DEL = 0
          AND r.LATITUDE IS NULL
          AND r.LONGITUDE IS NULL
          AND r.DRIVER_ID IS NOT NULL
    </select>
    <select id="queryUpLoadingOrReceiptNodeRecord" resultType="com.wtyt.dao.bean.syf.BoTransNodeRecordBean">
        SELECT
            r.BO_TRANS_NODE_RECORD_ID boTransNodeRecordId,
            r.BO_TRANS_TASK_ID boTransTaskId,
            r.NODE_ID nodeId,
            NVL(A.TAX_WAYBILL_ID, t.TAX_WAYBILL_ID) taxWaybillId
        FROM
            T_BO_TRANS_TASK t
                LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON
                        t.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
                    AND A.IS_DEL = 0
                INNER JOIN T_BO_TRANS_NODE_RECORD r ON
                        t.BO_TRANS_TASK_ID = r.BO_TRANS_TASK_ID
                    AND r.IS_DEL = 0
                    AND r.NODE_ID IN (510, 800)
        WHERE
            t.IS_DEL = 0
          AND r.LATITUDE IS NULL
          AND r.LONGITUDE IS NULL
          AND r.DRIVER_ID IS NOT NULL
    </select>
    <select id="getTaskSettleAndStateListByTaskIds" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.STATE state,
        T.SETTLE_MODE settleMode,
        A.SETTLE_MODE allocateSettleMode
        FROM T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
        ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="queryTransTaskTabStatusCount" resultType="com.wtyt.tt.bean.StaticQuantityObj"
            parameterType="com.wtyt.tt.bean.Req5330266Bean">
        SELECT
            NVL(SUM(CASE WHEN bte.DISPATCH_CAR_RECORD_ID IS NOT NULL THEN 1 ELSE 0 END), 0) AS dispatchCount,
            NVL(SUM(CASE WHEN bt.START_TIME IS NOT NULL THEN 1 ELSE 0 END), 0) AS departureCount,
            NVL(SUM(CASE WHEN bt.state = 2 THEN 1 ELSE 0 END), 0) AS arrivedCount,
            NVL(COUNT(
                CASE
                WHEN
                    BTE.TRANSPORT_NODE = 800 THEN 1
                    ELSE NULL
                END
            ), 0) AS historyUnloadReceiptCount
        FROM
            t_bo_trans_task bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
            bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID
            AND bte.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE btta ON
            btta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND btta.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
            <include refid="queryTransTaskTabStatusCountWhereSql"/>
    </select>
    <select id="queryLoadingTonnageList" resultType="com.wtyt.tt.bean.LoadingTonnageList"
            parameterType="com.wtyt.tt.bean.Req5330266Bean">
        SELECT
            goodsAmountType,
            CASE
            WHEN loadingTonnageCount - TRUNC(loadingTonnageCount) = 0 THEN
            TO_CHAR(TRUNC(loadingTonnageCount), '*********90')
            ELSE
            TO_CHAR(loadingTonnageCount, '*********90.9999')
            END AS loadingTonnage
        FROM
        (
        SELECT
            bt.GOODS_AMOUNT_TYPE AS goodsAmountType,
            SUM(
                case bt.TRANSPORT_TYPE when 1 then NVL(bt.LOADING_TONNAGE,0) ELSE NVL(bt.GOODS_AMOUNT,0) end
                ) AS loadingTonnageCount
        FROM
            T_BO_TRANS_TASK bt
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE btta ON
            btta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND btta.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
            AND bt.GOODS_AMOUNT_TYPE IS NOT NULL
            <include refid="queryTransTaskTabStatusCountWhereSql"/>
        GROUP BY
            bt.GOODS_AMOUNT_TYPE
        ) sub
        ORDER BY
            goodsAmountType
    </select>
    <select id="queryVehicleSourceProportionList" resultType="com.wtyt.tt.bean.CapacityProportionList"
            parameterType="com.wtyt.tt.bean.Req5330266Bean">
        SELECT
            NVL(bte.CPD_POOL_GROUP_NAME,NVL(bt.CAPACITY_TYPE_NAME, '社会临调车')) AS capacityTypeName,
            count(*) AS taskCount,
            CASE
            WHEN ROUND((count(*) * 100.0) / SUM(count(*)) OVER (), 2) = TRUNC((count(*) * 100.0) / SUM(count(*)) OVER ())
            THEN TO_CHAR(TRUNC((count(*) * 100.0) / SUM(count(*)) OVER ()), 'FM990')
            ELSE TO_CHAR(ROUND((count(*) * 100.0) / SUM(count(*)) OVER (), 2), 'FM990.99')
            END AS percentage
        FROM
            T_BO_TRANS_TASK bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON bte.BO_TRANS_TASK_ID=bt.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE btta ON
            btta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND btta.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
        <include refid="queryTransTaskTabStatusCountWhereSql"/>
        GROUP BY
            NVL(bte.CPD_POOL_GROUP_NAME,NVL(bt.CAPACITY_TYPE_NAME, '社会临调车'))
        ORDER BY
            taskCount DESC
    </select>
    <select id="queryTaskCountByReq5330266Bean" resultType="java.lang.Long"
            parameterType="com.wtyt.tt.bean.Req5330266Bean">
        SELECT
            count(*) AS taskCount
        FROM
            T_BO_TRANS_TASK bt
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE btta ON
            btta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND btta.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
        <include refid="queryTransTaskTabStatusCountWhereSql"/>
    </select>

    <select id="queryLineOperationRankingList" resultType="com.wtyt.tt.bean.LineOperationRanking" parameterType="com.wtyt.tt.bean.Req5330266Bean">
        SELECT
            <choose>
                <when test="groupType == '2'.toString()">
                    BTE.LOADING_ADDRESS_NAME loadingPlaceName,
                    BTE.UNLOADING_ADDRESS_NAME unloadingPlaceName,
                </when>
                <otherwise>
                    BT.START_CITY_NAME loadingPlaceName,
                    BT.END_CITY_NAME unloadingPlaceName,
                </otherwise>
            </choose>
            COUNT(BT.BO_TRANS_TASK_ID) taskCount
        FROM T_BO_TRANS_TASK BT
        JOIN T_BO_TRANS_TASK_EXTRA BTE
            ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE BTTA
            ON BTTA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND BTTA.IS_DEL = 0
        WHERE BT.IS_DEL = 0
        <include refid="queryTransTaskTabStatusCountWhereSql"/>
        AND BT.START_PROVINCE_NAME IS NOT NULL
        AND BT.START_CITY_NAME IS NOT NULL
        AND BT.END_PROVINCE_NAME IS NOT NULL
        AND BT.END_CITY_NAME IS NOT NULL
        <if test="groupType == '2'.toString()">
            AND BTE.LOADING_ADDRESS_NAME IS NOT NULL
            AND BTE.UNLOADING_ADDRESS_NAME IS NOT NULL
        </if>
        GROUP BY
        <choose>
            <when test="groupType == '2'.toString()">
                BT.START_PROVINCE_NAME,
                BT.START_CITY_NAME,
                BT.START_COUNTY_NAME,
                BTE.LOADING_ADDRESS_NAME,
                BT.END_PROVINCE_NAME,
                BT.END_CITY_NAME,
                BT.END_COUNTY_NAME,
                BTE.UNLOADING_ADDRESS_NAME
            </when>
            <otherwise>
                BT.START_PROVINCE_NAME,
                BT.START_CITY_NAME,
                BT.END_PROVINCE_NAME,
                BT.END_CITY_NAME
            </otherwise>
        </choose>
        ORDER BY taskCount DESC
    </select>
    <select id="queryLoadingDataList" resultType="com.wtyt.tt.bean.LoadingDataInfo" parameterType="com.wtyt.tt.bean.Req5330266Bean">
        SELECT *
        FROM (
            SELECT
                T.LOADING_PLACE_NAME loadingPlaceName,
                T.START_PROVINCE_NAME startProvinceName,
                T.START_CITY_NAME startCityName,
                <if test="groupType == '2'.toString()">
                    T.START_COUNTY_NAME,
                    T.LOADING_ADDRESS_NAME,
                </if>
                T.CAPACITY_TYPE_NAME capacityTypeName,
                MAX(RN) taskCount,
                MAX(CASE WHEN rn = 1 THEN START_LATITUDE END) startLatitude,
                MAX(CASE WHEN rn = 1 THEN START_LONGITUDE END) startLongitude
            FROM (
                SELECT
                    <choose>
                        <when test="groupType == '2'.toString()">
                            BTE.LOADING_ADDRESS_NAME LOADING_PLACE_NAME,
                        </when>
                        <otherwise>
                            BT.START_CITY_NAME LOADING_PLACE_NAME,
                        </otherwise>
                    </choose>
                    BT.START_PROVINCE_NAME,
                    BT.START_CITY_NAME,
                    BT.START_COUNTY_NAME,
                    BT.START_LATITUDE,
                    BT.START_LONGITUDE,
                    BTE.LOADING_ADDRESS_NAME,
                    NVL(BTE.CPD_POOL_GROUP_NAME, NVL(BT.CAPACITY_TYPE_NAME, '社会临调车')) CAPACITY_TYPE_NAME,
                    <choose>
                        <when test="groupType == '2'.toString()">
                            ROW_NUMBER() OVER (PARTITION BY
                                BT.START_PROVINCE_NAME,
                                BT.START_CITY_NAME,
                                BT.START_COUNTY_NAME,
                                BTE.LOADING_ADDRESS_NAME,
                                NVL(BTE.CPD_POOL_GROUP_NAME, NVL(BT.CAPACITY_TYPE_NAME, '社会临调车'))
                                ORDER BY BT.BO_TRANS_TASK_ID) RN
                        </when>
                        <otherwise>
                            ROW_NUMBER() OVER (PARTITION BY
                                BT.START_PROVINCE_NAME,
                                BT.START_CITY_NAME,
                                NVL(BTE.CPD_POOL_GROUP_NAME, NVL(BT.CAPACITY_TYPE_NAME, '社会临调车'))
                                ORDER BY BT.BO_TRANS_TASK_ID) RN
                        </otherwise>
                    </choose>
                FROM T_BO_TRANS_TASK BT
                JOIN T_BO_TRANS_TASK_EXTRA BTE
                    ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                LEFT JOIN T_BO_TRANS_TASK_ALLOCATE BTTA
                    ON BTTA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                    AND BTTA.IS_DEL = 0
                WHERE BT.IS_DEL = 0
                    AND BT.START_PROVINCE_NAME IS NOT NULL
                    AND BT.START_CITY_NAME IS NOT NULL
                    <if test="groupType == '2'.toString()">
                        AND BTE.LOADING_ADDRESS_NAME IS NOT NULL
                    </if>
                    <include refid="queryTransTaskTabStatusCountWhereSql"/>
            )T
            GROUP BY
            <choose>
                <when test="groupType == '2'.toString()">
                    T.START_PROVINCE_NAME,
                    T.START_CITY_NAME,
                    T.START_COUNTY_NAME,
                    T.LOADING_ADDRESS_NAME,
                    T.CAPACITY_TYPE_NAME
                </when>
                <otherwise>
                    T.START_PROVINCE_NAME,
                    T.START_CITY_NAME,
                    T.CAPACITY_TYPE_NAME
                </otherwise>
            </choose>
        )ORDER BY TASKCOUNT DESC
    </select>

    <select id="queryLineDataInfoList" resultType="com.wtyt.tt.bean.LineDataInfo" parameterType="com.wtyt.tt.bean.Req5330266Bean">
        SELECT *
        FROM (
            SELECT
                T.START_CITY_NAME loadingPlaceName,
                T.START_PROVINCE_NAME startProvinceName,
                MAX(CASE WHEN rn = 1 THEN START_LATITUDE END) startLatitude,
                MAX(CASE WHEN rn = 1 THEN START_LONGITUDE END) startLongitude,
                T.END_CITY_NAME unloadingPlaceName,
                T.END_PROVINCE_NAME endProvinceName,
                MAX(CASE WHEN rn = 1 THEN END_LATITUDE END) endLatitude,
                MAX(CASE WHEN rn = 1 THEN END_LONGITUDE END) endLongitude,
                T.CAPACITY_TYPE_NAME capacityTypeName,
                MAX(RN) taskCount
            FROM (
                SELECT
                    BT.START_PROVINCE_NAME,
                    BT.START_CITY_NAME,
                    BT.START_LATITUDE,
                    BT.START_LONGITUDE,
                    BT.END_PROVINCE_NAME,
                    BT.END_CITY_NAME,
                    BT.END_LATITUDE,
                    BT.END_LONGITUDE,
                    NVL(BTE.CPD_POOL_GROUP_NAME, NVL(BT.CAPACITY_TYPE_NAME, '社会临调车')) CAPACITY_TYPE_NAME,
                    ROW_NUMBER() OVER (PARTITION BY
                            BT.START_PROVINCE_NAME,
                            BT.START_CITY_NAME,
                            BT.END_PROVINCE_NAME,
                            BT.END_CITY_NAME,
                            NVL(BTE.CPD_POOL_GROUP_NAME, NVL(BT.CAPACITY_TYPE_NAME, '社会临调车'))
                            ORDER BY BT.BO_TRANS_TASK_ID) RN
                FROM T_BO_TRANS_TASK BT
                JOIN T_BO_TRANS_TASK_EXTRA BTE
                    ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                LEFT JOIN T_BO_TRANS_TASK_ALLOCATE BTTA
                    ON BTTA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                    AND BTTA.IS_DEL = 0
                WHERE BT.IS_DEL = 0
                AND BT.START_PROVINCE_NAME IS NOT NULL
                AND BT.START_CITY_NAME IS NOT NULL
                AND BT.END_PROVINCE_NAME IS NOT NULL
                AND BT.END_CITY_NAME IS NOT NULL
                <include refid="queryTransTaskTabStatusCountWhereSql"/>
            ) T
            GROUP BY
                T.START_PROVINCE_NAME,
                T.START_CITY_NAME,
                T.END_PROVINCE_NAME,
                T.END_CITY_NAME,
                T.CAPACITY_TYPE_NAME
        )ORDER BY TASKCOUNT
    </select>

    <select id="batchQueryByTaskIdsOrTaxWaybillIds" resultType="com.wtyt.tt.bean.DpTaskResultBean"
            parameterType="com.wtyt.bo.bean.request.Req5330269IBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            CASE WHEN (#{orgId} = T.ORG_ID OR #{orgId} = -1) THEN T.ORG_ID ELSE A.ORG_ID END orgId,
            T.MOBILE_NO mobileNo,
            T.DRIVER_NAME driverName,
            T.CART_BADGE_NO cartBadgeNo,
            T.TRANSPORT_TYPE transportType,
            CASE
                WHEN (#{orgId} = T.ORG_ID OR #{orgId} = -1 ) THEN T.TAX_WAYBILL_ID
                ELSE A.TAX_WAYBILL_ID
                END taxWaybillId,
            CASE
                WHEN T.TRANSPORT_TYPE = 1 THEN T.LOADING_PLACE_NAME
                ELSE E.LOADING_ADDRESS_NAME
                END loadingAddressName,
            CASE
                WHEN T.TRANSPORT_TYPE = 1 THEN T.UNLOADING_PLACE_NAME
                ELSE E.UNLOADING_ADDRESS_NAME
                END unloadingAddressName,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            T.HYB_STATE hybState,
            T.WB_ITEM wbItem,
            TO_CHAR(T.FIRST_RECEIPT_TIME, 'YYYY-MM-DD HH24:MI:SS') firstReceiptTime,
            CASE
                WHEN (#{orgId} = T.ORG_ID OR #{orgId} = -1 ) THEN T.NODE_ID
                ELSE A.NODE_ID
                END nodeId,
            T.STATE state,
            CASE
                WHEN (#{orgId} = T.ORG_ID OR #{orgId} = -1 ) THEN NVL(T.PAY_STATE,0)
                ELSE NVL(A.PAY_STATE,0)
                END payState,
            E.TRANS_VOUCHER transVoucher,
            NR.USER_ID dispatchCarUserId,
            CASE
                WHEN (#{orgId} = T.ORG_ID OR #{orgId} = -1 ) THEN TO_CHAR(T.ALL_FREIGHT, '*********90.00')
                ELSE TO_CHAR(A.ALL_FREIGHT, '*********90.00')
                END allFreight,
            CASE
                WHEN (#{orgId} = T.ORG_ID OR #{orgId} = -1 ) THEN TO_CHAR(T.USER_FREIGHT, '*********90.00')
                ELSE TO_CHAR(A.USER_FREIGHT, '*********90.00')
                END userFreight,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            T.INST_ID instId,
            T.SETTLE_TARGET settleTarget,
            CASE
            WHEN (#{orgId} = A.ORG_ID) THEN A.SETTLE_MODE
            ELSE T.SETTLE_MODE
            END settleMode,
            CASE
                WHEN (#{orgId} = T.ORG_ID OR #{orgId} = -1 ) THEN TO_CHAR(T.PREPAYMENTS, '*********90.00')
                ELSE TO_CHAR(A.PREPAYMENTS, '*********90.00')
            END prepayments,
            CASE
                WHEN (#{orgId} = T.ORG_ID OR #{orgId} = -1 ) THEN TO_CHAR(T.BACK_FEE, '*********90.00')
                ELSE TO_CHAR(A.BACK_FEE, '*********90.00')
            END backFee,
            E.GAS_CARD_NO gasCardNo,
            E.IS_REBATE isRebate,
            E.CPD_POOL_GROUP_ID cpdPoolGroupId,
            E.CPD_POOL_GROUP_NAME cpdPoolGroupName,
            E.CPD_SECOND_POOL_GROUP_NAME cpdSecondPoolGroupName,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            E.OIL_CARD_NO oilCardNo
        FROM T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
              ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA E
              ON E.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND E.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR ON
            NR.BO_TRANS_NODE_RECORD_ID = E.DISPATCH_CAR_RECORD_ID AND NR.IS_DEL = 0
        WHERE T.IS_DEL = 0
            <if test="boTransTaskIds != null and boTransTaskIds.size() > 0">
                AND T.BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIds" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="taxWaybillIds != null and taxWaybillIds.size() > 0">
                AND T.BO_TRANS_TASK_ID IN
                (
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE IS_DEL = 0 AND TAX_WAYBILL_ID IN
                <foreach collection="taxWaybillIds" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
                UNION
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE IS_DEL = 0 AND TAX_WAYBILL_ID IN
                <foreach collection="taxWaybillIds" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
                )
            </if>
    </select>
    <select id="batchQueryTransTask" resultType="com.wtyt.tt.bean.DpTaskResultBean"
            parameterType="java.util.List">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.ORG_ID orgId,
            T.MOBILE_NO mobileNo,
            T.DRIVER_NAME driverName,
            T.CART_BADGE_NO cartBadgeNo,
            T.TRANSPORT_TYPE transportType,
            T.TAX_WAYBILL_ID taxWaybillId,
            CASE
            WHEN T.TRANSPORT_TYPE = 1 THEN T.LOADING_PLACE_NAME
            ELSE E.LOADING_ADDRESS_NAME
            END loadingAddressName,
            CASE
            WHEN T.TRANSPORT_TYPE = 1 THEN T.UNLOADING_PLACE_NAME
            ELSE E.UNLOADING_ADDRESS_NAME
            END unloadingAddressName,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            T.NODE_ID nodeId,
            T.STATE state,
            T.HYB_STATE hybState,
            T.WB_ITEM wbItem,
            TO_CHAR(T.FIRST_RECEIPT_TIME, 'YYYY-MM-DD HH24:MI:SS') firstReceiptTime,
            NVL(T.PAY_STATE,0) payState,
            E.TRANS_VOUCHER transVoucher,
            NR.USER_ID dispatchCarUserId,
            TO_CHAR(T.ALL_FREIGHT, '*********90.00') allFreight,
            TO_CHAR(T.USER_FREIGHT, '*********90.00') userFreight,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            T.INST_ID instId,
            T.SETTLE_TARGET settleTarget,
            T.SETTLE_MODE settleMode,
            TO_CHAR(T.PREPAYMENTS, '*********90.00') prepayments,
            TO_CHAR(T.BACK_FEE, '*********90.00') backFee,
            E.GAS_CARD_NO gasCardNo,
            E.IS_REBATE isRebate,
            E.CPD_POOL_GROUP_ID cpdPoolGroupId,
            E.CPD_POOL_GROUP_NAME cpdPoolGroupName,
            E.CPD_SECOND_POOL_GROUP_NAME cpdSecondPoolGroupName,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            E.OIL_CARD_NO oilCardNo
        FROM T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_EXTRA E
            ON E.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND E.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR
            ON NR.BO_TRANS_NODE_RECORD_ID = E.DISPATCH_CAR_RECORD_ID AND NR.IS_DEL = 0
        WHERE T.IS_DEL = 0
        AND T.TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="batchQueryTransTaskAllocate" resultType="com.wtyt.tt.bean.DpTaskResultBean"
            parameterType="java.util.List">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            A.ORG_ID orgId,
            T.MOBILE_NO mobileNo,
            T.DRIVER_NAME driverName,
            T.CART_BADGE_NO cartBadgeNo,
            T.TRANSPORT_TYPE transportType,
            A.TAX_WAYBILL_ID taxWaybillId,
            CASE
            WHEN T.TRANSPORT_TYPE = 1 THEN T.LOADING_PLACE_NAME
            ELSE E.LOADING_ADDRESS_NAME
            END loadingAddressName,
            CASE
            WHEN T.TRANSPORT_TYPE = 1 THEN T.UNLOADING_PLACE_NAME
            ELSE E.UNLOADING_ADDRESS_NAME
            END unloadingAddressName,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            A.NODE_ID nodeId,
            T.STATE state,
            T.HYB_STATE hybState,
            T.WB_ITEM wbItem,
            TO_CHAR(T.FIRST_RECEIPT_TIME, 'YYYY-MM-DD HH24:MI:SS') firstReceiptTime,
            NVL(A.PAY_STATE,0) payState,
            E.TRANS_VOUCHER transVoucher,
            NR.USER_ID dispatchCarUserId,
            TO_CHAR(T.ALL_FREIGHT, '*********90.00') allFreight,
            TO_CHAR(T.USER_FREIGHT, '*********90.00') userFreight,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            T.INST_ID instId,
            T.SETTLE_TARGET settleTarget,
            T.SETTLE_MODE settleMode,
            TO_CHAR(T.PREPAYMENTS, '*********90.00') prepayments,
            TO_CHAR(T.BACK_FEE, '*********90.00') backFee,
            E.GAS_CARD_NO gasCardNo,
            E.IS_REBATE isRebate,
            E.CPD_POOL_GROUP_ID cpdPoolGroupId,
            E.CPD_POOL_GROUP_NAME cpdPoolGroupName,
            E.CPD_SECOND_POOL_GROUP_NAME cpdSecondPoolGroupName,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            E.OIL_CARD_NO oilCardNo
        FROM T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA E
            ON E.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND E.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR
            ON NR.BO_TRANS_NODE_RECORD_ID = E.DISPATCH_CAR_RECORD_ID AND NR.IS_DEL = 0
        WHERE T.IS_DEL = 0
        AND A.TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="queryAllPayStateByTaskId" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            tt.PAY_STATE payState,
            tt.ORG_ID orgId,
            tt.TAX_WAYBILL_ID taxWaybillId,
            tt.TRANS_PATTERN transPattern,
            tta.PAY_STATE allocatePayState,
            tta.TAX_WAYBILL_ID allocateTaxWaybillId,
            tta.ORG_ID allocateOrgId
        FROM
        T_BO_TRANS_TASK tt
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE tta ON
        tt.BO_TRANS_TASK_ID = tta.BO_TRANS_TASK_ID
        AND tta.IS_DEL = 0
        WHERE
        tt.IS_DEL = 0
        AND tt.BO_TRANS_TASK_ID = #{taskId}
    </select>

    <select id="queryBaseOperationProportion" resultType="com.wtyt.tt.bean.BaseOperationProportion"
            parameterType="com.wtyt.tt.bean.Req5330270Bean">
        SELECT
            bt.START_CITY_NAME loadingPlaceName,
            NVL(count(*),0) AS taskCount,
            CASE
                WHEN ROUND((count(*) * 100.0) / SUM(count(*)) OVER (), 2) = TRUNC((count(*) * 100.0) / SUM(count(*)) OVER ())
                    THEN TO_CHAR(TRUNC((count(*) * 100.0) / SUM(count(*)) OVER ()), 'FM990')
                ELSE TO_CHAR(ROUND((count(*) * 100.0) / SUM(count(*)) OVER (), 2), 'FM990.99')
                END AS percentage
        FROM
            T_BO_TRANS_TASK bt
                LEFT JOIN T_BO_TRANS_TASK_ALLOCATE btta ON
                        btta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                    AND btta.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
        <include refid="queryTransTaskTabStatusCountWhereSql"/>
        AND bt.START_CITY_NAME IS NOT NULL
        GROUP BY
            bt.START_CITY_NAME
        ORDER BY
            taskCount DESC
    </select>
    <select id="queryTaskListByBo" resultType="com.wtyt.bo.bean.BoTaskDetailBean"
            parameterType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            bt.BO_TRANS_TASK_ID boTransTaskId,
            bt.TAX_WAYBILL_ID taxWaybillId,
            btta.TAX_WAYBILL_ID allocateTaxWaybillId,
            bt.TAX_WAYBILL_NO taxWaybillNo,
            bt.MOBILE_NO mobileNo,
            bt.DRIVER_NAME driverName,
            bt.CART_BADGE_NO cartBadgeNo,
            bte.CART_TYPE cartType,
            bte.CART_LENGTH cartLength,
            bt.CART_BADGE_COLOR cartBadgeColor,
            bt.ORG_ID orgId,
            btta.ORG_ID allocateOrgId
        FROM
            T_BO_TRANS_TASK bt
                LEFT JOIN T_BO_TRANS_TASK_ALLOCATE btta ON
                        btta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                    AND btta.IS_DEL = 0
                LEFT JOIN T_BO_TRANS_TASK_EXTRA bte ON
                    bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                    AND bte.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
          <if test="orgIds != null and orgIds.size() > 0">
              AND (bt.ORG_ID IN
              <foreach collection="orgIds" index="index" item="item"  open="(" close=")" separator=",">
                  <if test="(index % 999) == 998"> NULL) OR bt.ORG_ID IN(</if>#{item}
              </foreach>

              OR
              btta.ORG_ID IN
              <foreach collection="orgIds" index="index" item="item"  open="(" close=")" separator=",">
                  <if test="(index % 999) == 998"> NULL) OR btta.ORG_ID IN(</if>#{item}
              </foreach>
              )
          </if>
          AND bt.MOBILE_NO = #{mobileNo}
          <if test="isUploadReceipt != null and isUploadReceipt != ''">
              <if test="isUploadReceipt == 0">
                AND bt.FIRST_RECEIPT_TIME IS NULL
              </if>
              <if test="isUploadReceipt == 1">
                AND bt.FIRST_RECEIPT_TIME IS NOT NULL
              </if>
          </if>
        ORDER BY bt.CREATED_TIME DESC
    </select>
    <select id="queryDispatcherCarNodeRecord" resultType="com.wtyt.dao.bean.syf.BoTransNodeRecordBean">
        SELECT
            c.USER_ID userId,
            c.USER_NAME userName
        FROM
            T_BO_TRANS_TASK a
            JOIN T_BO_TRANS_TASK_EXTRA b ON
            a.BO_TRANS_TASK_ID = b.BO_TRANS_TASK_ID
            AND b.IS_DEL = 0
            JOIN T_BO_TRANS_NODE_RECORD c ON
            c.BO_TRANS_NODE_RECORD_ID = b.DISPATCH_CAR_RECORD_ID
            AND c.IS_DEL = 0
            WHERE
                a.IS_DEL = 0
                AND a.BO_TRANS_TASK_ID = #{taskId}
    </select>
    <select id="queryCanTransferTask" resultType="com.wtyt.bo.bean.BoTaskDetailBean"
            parameterType="java.util.List">
        SELECT
            temp.BO_TRANS_TASK_ID boTransTaskId,
            temp.TAX_WAYBILL_ID taxWaybillId,
            temp.TAX_WAYBILL_NO taxWaybillNo,
            temp.ORG_ID orgId,
            temp.CREATED_USER_ID createdUserId,
            temp.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType
        FROM
            (
                SELECT
                    bt.BO_TRANS_TASK_ID,
                    bt.TAX_WAYBILL_ID,
                    bt.TAX_WAYBILL_NO,
                    bt.ORG_ID,
                    bt.CREATED_USER_ID,
                    bt.CREATED_USER_SYS_ROLE_TYPE
        FROM
                    T_BO_TRANS_TASK bt
                WHERE
                    bt.IS_DEL = 0
                    <choose>
                        <when test="data.requestType == '0'.toString()">
                            AND BT.SETTLE_MODE = 1
                        </when>
                        <otherwise>
                            AND BT.SETTLE_MODE = 2
                        </otherwise>
                    </choose>
                  <if test="data.fromOrgId != null and data.fromOrgId != '' and data.toOrgId != null and data.toOrgId != '' and data.fromOrgId != data.toOrgId">
                      AND bt.TRANSPORT_TYPE = 0
                      AND bt.BO_BUSINESS_LINE_ID IS NULL
                      AND bt.TRANSPORT_LINE_ID IS NULL
                      AND bt.TRANS_MODE = 1
                      AND bt.ORG_ID = #{data.fromOrgId}
                  </if>
                  <if test="data.fromBelongActSys != null and data.fromBelongActSys != ''">
                      AND bt.WAYBILL_BELONG_ACT_SYS = #{data.fromBelongActSys}
                  </if>
                    AND (
                    <if test="data.taxWaybillIds != null and data.taxWaybillIds.size() > 0">
                        bt.TAX_WAYBILL_ID IN
                        <foreach collection="data.taxWaybillIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="data.taxWaybillNos != null and data.taxWaybillNos.size() > 0">
                        <if test="data.taxWaybillIds != null and data.taxWaybillIds.size() > 0">
                            OR
                        </if>
                        bt.TAX_WAYBILL_NO IN
                        <foreach collection="data.taxWaybillNos" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="data.boTransTaskIds != null and data.boTransTaskIds.size() > 0">
                        <if test="(data.taxWaybillIds != null and data.taxWaybillIds.size() > 0) or (data.taxWaybillNos != null and data.taxWaybillNos.size() > 0)">
                            OR
                        </if>
                        bt.BO_TRANS_TASK_ID IN
                        <foreach collection="data.boTransTaskIds" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    )
            ) temp

        <where>
            <if test="data.getFromOrgId != null and data.getFromOrgId != '' and data.getToOrgId != null and data.getToOrgId != '' and data.getFromOrgId != data.getToOrgId">
                NOT EXISTS (
                    SELECT
                        1
                    FROM
                        T_BO_TRANS_ORDER_REL tor
                    WHERE
                        tor.BO_TRANS_TASK_ID = temp.BO_TRANS_TASK_ID
                      AND tor.IS_DEL = 0
                )
            </if>
        </where>
    </select>
    <select id="queryCanTransferUpstreamDownTask" resultType="com.wtyt.bo.bean.BoTaskDetailBean"
            parameterType="java.util.List">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.ORG_ID orgId,
            T.CREATED_USER_ID createdUserId,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType
        FROM
            T_BO_TRANS_TASK T
                LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON
                        T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
                    AND A.IS_DEL = 0
        WHERE
            T.IS_DEL = 0
        AND (T.ORG_ID = #{data.fromOrgId} OR A.ORG_ID = #{data.fromOrgId})
        AND T.WAYBILL_BELONG_ACT_SYS = #{data.fromBelongActSys}
        AND (
        <if test="data.taxWaybillIds != null and data.taxWaybillIds.size() > 0">
            T.TAX_WAYBILL_ID IN
            <foreach collection="data.taxWaybillIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="data.taxWaybillNos != null and data.taxWaybillNos.size() > 0">
            <if test="data.taxWaybillIds != null and data.taxWaybillIds.size() > 0">
                OR
            </if>
            T.TAX_WAYBILL_NO IN
            <foreach collection="data.taxWaybillNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="data.boTransTaskIds != null and data.boTransTaskIds.size() > 0">
            <if test="(data.taxWaybillIds != null and data.taxWaybillIds.size() > 0) or (data.taxWaybillNos != null and data.taxWaybillNos.size() > 0)">
                OR
            </if>
            T.BO_TRANS_TASK_ID IN
            <foreach collection="data.boTransTaskIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )
    </select>

    <update id="batchUpdateTaskByBoBusinessLineIds">
        DECLARE
            CNT NUMBER(10);
        BEGIN
            CNT := 0;
        FOR TEMP IN (
            SELECT BO_TRANS_TASK_ID AS PRIMARY_KEY FROM T_BO_TRANS_TASK WHERE BO_BUSINESS_LINE_ID IN
            <foreach collection="boBusinessLineIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND (WB_ITEM IS NULL OR WB_ITEM!=#{wbItem})
        )
        LOOP
            UPDATE
            T_BO_TRANS_TASK T
            SET
            T.WB_ITEM = #{wbItem},
            T.LAST_MODIFIED_TIME = sysdate
            WHERE
            T.BO_TRANS_TASK_ID = TEMP.PRIMARY_KEY;
        CNT := CNT + 1;
        IF (MOD(CNT, 1000) = 0) THEN
        COMMIT;
        END IF;
        END LOOP;
        COMMIT;
        END;
    </update>

    <select id="countDriverFinishTaskCount" resultType="int">
        SELECT COUNT(*) FROM T_BO_TRANS_TASK WHERE IS_DEL =0 AND ORG_ID =#{orgId} AND STATE =2 AND MOBILE_NO = #{mobileNo}
    </select>

    <select id="getTaskIdByAllocateTaxWaybillId" resultType="java.lang.String">
        SELECT t.BO_TRANS_TASK_ID  FROM T_BO_TRANS_TASK t JOIN T_BO_TRANS_TASK_ALLOCATE a ON a.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND a.IS_DEL =0 WHERE a.TAX_WAYBILL_ID =#{taxWaybillId}
        <if test="isDel!=null and isDel!=''">
            AND t.IS_DEL =0
        </if>
    </select>

    <select id="getTransTaskDetailByParams" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T1.BO_TRANS_TASK_ID boTransTaskId,
            T1.TAX_WAYBILL_ID taxWaybillId,
            T1.TAX_WAYBILL_ID ownerTaxWaybillId,
            T1.TAX_WAYBILL_NO taxWaybillNo,
            T1.ORG_ID orgId,
            T1.ORG_ID ownerOrgId,
            T1.XCY_USER_ID xcyUserId,
            NVL(T1.TRANSPORT_TYPE, 0) transportType,
            T1.TRANS_PATTERN transPattern,
            T1.TRANS_MODE transMode,
            T1.MOBILE_NO mobileNo,
            T1.DRIVER_NAME driverName,
            T1.CART_BADGE_NO cartBadgeNo,
            T1.CART_BADGE_COLOR cartBadgeColor,
            T1.DRIVER_ID_CARD driverIdCard,
            T1.START_PROVINCE_NAME startProvinceName,
            T1.START_CITY_NAME startCityName,
            T1.START_COUNTY_NAME startCountyName,
            T1.END_PROVINCE_NAME endProvinceName,
            T1.END_CITY_NAME endCityName,
            T1.END_COUNTY_NAME endCountyName,
            T1.START_LONGITUDE startLongitude,
            T1.START_LATITUDE startLatitude,
            T1.END_LONGITUDE endLongitude,
            T1.END_LATITUDE endLatitude,
            T1.LOADING_PLACE_NAME loadingPlaceName,
            T1.UNLOADING_PLACE_NAME unloadingPlaceName,
            CASE
                WHEN T1.LOADING_TONNAGE - TRUNC(T1.LOADING_TONNAGE) = 0 THEN TO_CHAR(TRUNC(T1.LOADING_TONNAGE), '*********90')
                ELSE TO_CHAR(T1.LOADING_TONNAGE, '*********90.9999')
            END loadingTonnage,
            CASE
                WHEN T1.UNLOADING_TONNAGE - TRUNC(T1.UNLOADING_TONNAGE) = 0 THEN TO_CHAR(TRUNC(T1.UNLOADING_TONNAGE), '*********90')
                ELSE TO_CHAR(T1.UNLOADING_TONNAGE, '*********90.9999')
            END unloadingTonnage,
            T1.MILEAGE mileage,
            T1.GOODS_NAME goodsName,
            CASE
                WHEN T1.GOODS_AMOUNT - TRUNC(T1.GOODS_AMOUNT) = 0 THEN TO_CHAR(TRUNC(T1.GOODS_AMOUNT), '*********90')
                ELSE TO_CHAR(T1.GOODS_AMOUNT, '*********90.9999')
            END goodsAmount,
            T1.GOODS_AMOUNT_TYPE goodsAmountType,
            TO_CHAR(T1.PREPAYMENTS, '*********90.00') prepayments,
            TO_CHAR(T1.PREPAYMENTS_OILCARD, '*********90.00') prepaymentsOilcard,
            TO_CHAR(T1.PREPAYMENTS_GASCARD, '*********90.00') prepaymentsGascard,
            TO_CHAR(T1.ALL_FREIGHT, '*********90.00') allFreight,
            TO_CHAR(T1.USER_FREIGHT, '*********90.00') userFreight,
            TO_CHAR(T1.SERVICE_FEE, '*********90.00') serviceFee,
            TO_CHAR(T1.DATA_SERVICE_FEE, '*********90.00') dataServiceFee,
            TO_CHAR(T1.BACK_FEE, '*********90.00') backFee,
            TO_CHAR(T1.FREIGHT_INCR, '*********90.00') freightIncr,
            TO_CHAR(T1.LOSS_FEE, '*********90.00') lossFee,
            CASE
                WHEN T1.INS_STATE = 1 THEN TO_CHAR(NVL(T1.ALL_FREIGHT, 0) + NVL(T1.SERVICE_FEE, 0) + NVL(T1.DATA_SERVICE_FEE, 0) + NVL(T1.INS_FEE, 0), '*********90.00')
                ELSE TO_CHAR(NVL(T1.ALL_FREIGHT, 0) + NVL(T1.SERVICE_FEE, 0) + NVL(T1.DATA_SERVICE_FEE, 0) , '*********90.00')
            END payableFreight,
            TO_CHAR(T1.GOODS_COST, '*********90.00') goodsCost,
            TO_CHAR(T1.GOODS_COST, '*********90.00') taskGoodsCost,
            CASE
                WHEN T1.INS_STATE = 1 THEN TO_CHAR(NVL(T1.INS_FEE, 0), '*********90.00')
                ELSE '0.00'
            END insFee,
            TO_CHAR(T1.UNIT_PRICE, '*********90.00') unitPrice,
            T1.NODE_ID nodeId,
            T1.TRANSPORT_LINE_ID transportLineId,
            T1.BO_BUSINESS_LINE_ID boBusinessLineId,
            T1.LOSS_ENSURE_STATE lossEnsureState,
            T1.OFFER_TYPE offerType,
            T1.HYB_STATE hybState,
            T1.PAY_STATE payState,
            NVL(T1.ADVANCE_PAY_STATE, '0') advancePayState,
            T1.STATE,
            T1.NOTE,
            T1.WB_ITEM wbItem,
            T1.ORDER_CREATE_TYPE orderCreateType,
            T1.RECEIVER_MOBILE receiverMobile,
            NVL(T1.SETTLE_MODE,'1') settleMode,
            T2.SETTLE_FLAG settleFlag,
            NVL(T1.FROZEN_BACK_FEE_STATE,0) frozenBackFeeState,
            T1.SETTLE_TYPE settleType,
            T1.SETTLE_TARGET settleTarget,
            T1.INST_ID instId,
            NVL(T1.FROZEN_BACK_FEE_STATE,0) frozenBackFeeState,
            T1.CAPACITY_TYPE capacityType,
            T1.CAPACITY_TYPE_NAME capacityTypeName,
            T1.IS_PARTAKE_OPERATE isPartakeOperate,
            T1.CUSTOMER_REMARK customerRemark,
            T1.CREATED_USER_ID createdUserId,
            T1.CREATED_USER_JOB_NAME createdUserJobName,
            T1.INST_ID instId,
            T1.RECEIVER receiver,
            TO_CHAR(T1.FIXED_COSTS, '*********90.00') fixedCosts,
            T1.SETTLE_TARGET settleTarget,
            T1.SETTLE_TYPE settleType,
            T1.WAYBILL_BELONG_ACT_SYS belongActSys,
            TO_CHAR(T1.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
            TO_CHAR(T1.FIRST_RECEIPT_TIME, 'yyyy-mm-dd hh24:mi:ss') firstReceiptTime,
            TO_CHAR(T1.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            TO_CHAR(T1.START_TIME, 'yyyy-mm-dd hh24:mi:ss') startTime,
            TO_CHAR(T1.END_TIME, 'yyyy-mm-dd hh24:mi:ss') endTime,
            T2.CART_TYPE cartType,
            T2.CART_LENGTH cartLength,
            T2.LOADING_ADDRESS_NAME loadingAddressName,
            T2.UNLOADING_ADDRESS_NAME unloadingAddressName,
            T2.PAY_NAME payName,
            T2.PAY_ID_CARD payIdCard,
            T2.PAY_BANK_NO payBankNo,
            T2.PAY_BANK_NAME payBankName,
            T2.PROVINCE province,
            T2.CITY_NAME cityName,
            T2.PAY_MOBILE_NO payMobileNo,
            T2.PAY_TYPE payType,
            TO_CHAR(T2.FREIGHT_GUARANTEE, '*********90.00') freightGuarantee,
            T2.SERVICE_REQUIRE serviceRequire,
            T2.OIL_CARD_NO oilCardNo,
            T2.GAS_CARD_NO gasCardNo,
            T2.PAPER_RECEIPT_NEED_POST_TYPE paperReceiptNeedPostType,
            TO_CHAR(T2.LOSS_AMOUNT, '*********90.00') lossAmount,
            T2.EXTEND_JSON extendJson,
            T2.DISPATCH_CAR_RECORD_ID dispatchCarRecordId,
            T2.TRANS_VOUCHER transVoucher,
            T2.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
            T2.UNLOAD_TIME_FORMAT unloadTimeFormat,
            T2.OPERATE_FEE_STATUS operateFeeStatus,
            T2.SALESMAN_NAME salesmanName,
            T2.EXPRESS_NUMBER expressNumber,
            T2.PAYER payer,
            T2.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            T2.BOX_NO boxNo,
            NVL(T2.USER_FREIGHT_APPROVAL_STATUS, 0) userFreightApprovalStatus,
            CASE
                WHEN T2.LOADING_ROUGH_WEIGHT - TRUNC(T2.LOADING_ROUGH_WEIGHT) = 0 THEN TO_CHAR(TRUNC(T2.LOADING_ROUGH_WEIGHT), '*********90')
                ELSE TO_CHAR(T2.LOADING_ROUGH_WEIGHT, '*********90.9999')
            END loadingRoughWeight,
            CASE
                WHEN T2.UNLOADING_ROUGH_WEIGHT - TRUNC(T2.UNLOADING_ROUGH_WEIGHT) = 0 THEN TO_CHAR(TRUNC(T2.UNLOADING_ROUGH_WEIGHT), '*********90')
                ELSE TO_CHAR(T2.UNLOADING_ROUGH_WEIGHT, '*********90.9999')
            END unloadingRoughWeight,
            CASE
                WHEN T2.LOADING_TARE - TRUNC(T2.LOADING_TARE) = 0 THEN TO_CHAR(TRUNC(T2.LOADING_TARE), '*********90')
                ELSE TO_CHAR(T2.LOADING_TARE, '*********90.9999')
            END loadingTare,
            CASE
                WHEN T2.UNLOADING_TARE - TRUNC(T2.UNLOADING_TARE) = 0 THEN TO_CHAR(TRUNC(T2.UNLOADING_TARE), '*********90')
                ELSE TO_CHAR(T2.UNLOADING_TARE, '*********90.9999')
            END unloadingTare,
            T2.SETTLE_ISSUE settleIssue,
            T2.BO_LINE_ASSIGN_REL_ID boLineAssignRelId,
            T2.CPD_POOL_GROUP_ID cpdPoolGroupId,
            T2.CPD_POOL_GROUP_NAME cpdPoolGroupName,
            T2.CPD_SECOND_POOL_GROUP_NAME cpdSecondPoolGroupName,
            T2.CUSTOMIZE_NO customizeNo,
            T2.SHIPMENT_PHOTO shipmentPhoto,
            T2.RECEIPT_BZ_STATE receiptBzState,
            T2.TRANS_VOUCHER_EXPRESS_NUMBER transVoucherExpressNumber,
            T2.TRAN_REQUIRE tranRequire,
            T2.CART_TONNAGE cartTonnage,
            T2.TRAILER_CART_BADGE_NO trailerCartBadgeNo,
            T2.APPOINT_STATUS appointStatus,
            T2.SIGNIN_STATUS signinStatus,
            T2.THIRD_TASK_NO thirdTaskNo,
            T2.TRANS_TASK_FLAG transTaskFlag,
            TO_CHAR(T2.HYB_RECEIVED_TIME, 'yyyy-mm-dd hh24:mi:ss') hybReceivedTime,
            NVL(T2.IS_THIRD_INTERFACE, 0) isThirdInterface,
            CASE
                WHEN T2.DEDUCT_TONNAGE - TRUNC(T2.DEDUCT_TONNAGE) = 0 THEN TO_CHAR(TRUNC(T2.DEDUCT_TONNAGE), '*********90')
                ELSE TO_CHAR(T2.DEDUCT_TONNAGE, '*********90.9999')
            END deductTonnage,
            T2.ORG_MAILING_ADDRESS_ID orgMailingAddressId,
            T2.BUSINESS_TYPE businessType,
            T2.LOAD_TYPE loadType,
            T2.IS_FREIGHT_SHOW isFreightShow,
            T2.CUSTOMER_ORDER_NO customerOrderNo,
            T2.PAPER_RECEIPT_STATUS paperReceiptStatus,
            TO_CHAR(T2.LOSS_UNIT_PRICE, '*********90.00') lossUnitPrice,
            CASE
                WHEN T2.LOSS_ACTUAL_TONNAGE = FLOOR(T2.LOSS_ACTUAL_TONNAGE) THEN TO_CHAR(T2.LOSS_ACTUAL_TONNAGE, '*********90')
                ELSE TO_CHAR(T2.LOSS_ACTUAL_TONNAGE, '*********90.9999')
            END lossActualTonnage,
            CASE
                WHEN T2.ALLOW_LOSS_WEIGHT = FLOOR(T2.ALLOW_LOSS_WEIGHT) THEN TO_CHAR(T2.ALLOW_LOSS_WEIGHT, '*********90')
                ELSE TO_CHAR(T2.ALLOW_LOSS_WEIGHT, '*********90.9999')
            END allowLossWeight,
            T2.ALLOW_GAIN_FLAG allowGainFlag,
            TO_CHAR(T2.ACTUAL_LOAD_TIME, 'yyyy-mm-dd') actualLoadTime,
            TO_CHAR(T2.ACTUAL_UNLOAD_TIME, 'yyyy-mm-dd') actualUnloadTime,
            TO_CHAR(T2.LOADING_GOODS_TIME, 'yyyy-mm-dd hh24:mi:ss') loadingGoodsTime,
            TO_CHAR(T2.RECEIPT_RECEIVE_TIME, 'yyyy-mm-dd hh24:mi:ss') receiptReceiveTime,
            TO_CHAR(T2.ARRIVE_END_TIME, 'yyyy-mm-dd hh24:mi:ss') arriveEndTime,
            TO_CHAR(T2.ARRIVE_TIME, 'yyyy-mm-dd hh24:mi:ss') arriveTime,
            T1.IS_DEL isDel,
            T2.VOUCHER_CONFIG_TYPE voucherConfigType,
            T2.AUDIT_STATUS auditStatus,
            TO_CHAR(T2.DISPATCH_CAR_TIME, 'yyyy-mm-dd hh24:mi:ss') dispatchCarTime
        FROM T_BO_TRANS_TASK T1
        INNER JOIN T_BO_TRANS_TASK_EXTRA T2
            ON T2.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE T3
            ON T3.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
            AND T3.IS_DEL = 0
        <where>
            <if test="isDel != null and isDel.length > 0">
                T1.IS_DEL = #{isDel}
            </if>
            <if test="boTransTaskId != null and boTransTaskId.length > 0">
                AND T1.BO_TRANS_TASK_ID = #{boTransTaskId}
            </if>
            <if test="taxWaybillNo != null and taxWaybillNo.length > 0">
                AND T1.TAX_WAYBILL_NO = #{taxWaybillNo}
            </if>
            <if test="taxWaybillId != null and taxWaybillId.length > 0">
                AND T1.BO_TRANS_TASK_ID = (
                    SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE TAX_WAYBILL_ID = #{taxWaybillId}
                    <if test="isDel != null and isDel.length > 0">
                        AND IS_DEL = #{isDel}
                    </if>
                    UNION
                    SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE IS_DEL = 0 AND TAX_WAYBILL_ID = #{taxWaybillId}
                )
            </if>
            <if test="orgId != null and orgId.length > 0">
                AND (T1.ORG_ID = #{orgId} OR T3.ORG_ID = #{orgId})
            </if>
        </where>
    </select>

    <select id="getOrgTransTaskListByParams" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.STATE state,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo
        FROM (
            SELECT
                T1.BO_TRANS_TASK_ID,
                T1.TAX_WAYBILL_NO,
                T1.STATE,
                T1.DRIVER_NAME,
                T1.MOBILE_NO,
                T1.CART_BADGE_NO,
                T1.CREATED_TIME
            FROM T_BO_TRANS_TASK T1
            INNER JOIN T_BO_TRANS_TASK_EXTRA T2
                ON T2.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
            WHERE T1.IS_DEL = 0
                AND T2.DISPATCH_CAR_RECORD_ID IS NOT NULL
            <choose>
                <when test="orgId != null and orgId.length > 0">
                    AND T1.ORG_ID = #{orgId}
                </when>
                <otherwise>
                    AND T1.ORG_ID = -1
                </otherwise>
            </choose>
            <if test="startCreatedTime != null and startCreatedTime.length > 0">
                AND T1.CREATED_TIME <![CDATA[>=]]> TO_DATE(#{startCreatedTime}, 'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="endCreatedTime != null and endCreatedTime.length > 0">
                AND T1.CREATED_TIME <![CDATA[<]]> TO_DATE(#{endCreatedTime}, 'yyyy-mm-dd hh24:mi:ss')
            </if>

            UNION

            SELECT
                T4.BO_TRANS_TASK_ID,
                T4.TAX_WAYBILL_NO,
                T4.STATE,
                T4.DRIVER_NAME,
                T4.MOBILE_NO,
                T4.CART_BADGE_NO,
                T4.CREATED_TIME
            FROM T_BO_TRANS_TASK T4
            INNER JOIN T_BO_TRANS_TASK_EXTRA T5
                ON T5.BO_TRANS_TASK_ID = T4.BO_TRANS_TASK_ID
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE T6
                ON T6.BO_TRANS_TASK_ID = T4.BO_TRANS_TASK_ID
                AND T6.IS_DEL = 0
            WHERE T4.IS_DEL = 0
                AND T5.DISPATCH_CAR_RECORD_ID IS NOT NULL
            <choose>
                <when test="orgId != null and orgId.length > 0">
                    AND T6.ORG_ID = #{orgId}
                </when>
                <otherwise>
                    AND T6.ORG_ID = -1
                </otherwise>
            </choose>
            <if test="startCreatedTime != null and startCreatedTime.length > 0">
                AND T4.CREATED_TIME <![CDATA[>=]]> TO_DATE(#{startCreatedTime}, 'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="endCreatedTime != null and endCreatedTime.length > 0">
                AND T4.CREATED_TIME <![CDATA[<]]> TO_DATE(#{endCreatedTime}, 'yyyy-mm-dd hh24:mi:ss')
            </if>
        )T
        ORDER BY T.CREATED_TIME DESC
    </select>
    
    
    <select id="getTaskFullInfoByIds" resultType="com.wtyt.bo.bean.response.BoTransTaskSubBean">
        SELECT
            bt.TAX_WAYBILL_ID taxWaybillId,
            bt.TAX_WAYBILL_NO taxWaybillNo,
            bt.BO_TRANS_TASK_ID boTransTaskId,
            bt.ORG_ID orgId,
            bt.OWNER_ORG_ID ownerOrgId,
            NVL(bt.TRANS_MODE,1)  transMode,
            tta.TAX_WAYBILL_ID allocateTaxWaybillId,
            TO_CHAR(bt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') createdTime,
            bt.MOBILE_NO mobileNo,
            bt.DRIVER_NAME driverName,
            bt.CART_BADGE_NO cartBadgeNo,
            bt.SETTLE_TYPE settleType,
            bt.INST_ID instId,
            bt.RECEIVER_MOBILE receiverMobile,
            NVL(bt.TRANSPORT_TYPE, 0) transportType,
            bt.WB_ITEM wbItem,
            NR.USER_ID dispatchCarUserId,
            bte.TRANS_VOUCHER transVoucher,
            bte.SETTLE_ISSUE settleIssue
        FROM T_BO_TRANS_TASK BT
        LEFT JOIN T_BO_TRANS_TASK_EXTRA BTE
        ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID AND BTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TTA
        ON TTA.BO_TRANS_TASK_ID =BT.BO_TRANS_TASK_ID AND TTA.IS_DEL =0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR ON  NR.BO_TRANS_NODE_RECORD_ID = bte.DISPATCH_CAR_RECORD_ID AND NR.IS_DEL =0
        WHERE BT.IS_DEL = 0
        <choose>
            <when test="boTransTaskIdList != null and boTransTaskIdList.size() > 0">
                AND BT.BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
                    #{boTransTaskId}
                </foreach>
            </when>
            <otherwise>
                AND BT.BO_TRANS_TASK_ID = -1
            </otherwise>
        </choose>
    </select>

    <select id="getFullTransTaskListByTaskIdList" resultType="com.wtyt.bo.bean.response.BoTransTaskSubBean">
        SELECT
            bt.TAX_WAYBILL_ID taxWaybillId,
            bt.TAX_WAYBILL_NO taxWaybillNo,
            bt.BO_TRANS_TASK_ID boTransTaskId,
            bt.ORG_ID orgId,
            bt.OWNER_ORG_ID ownerOrgId,
            NVL(bt.TRANS_MODE,1)  transMode,
            tta.TAX_WAYBILL_ID allocateTaxWaybillId,
            TO_CHAR(bt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') createdTime,
            bt.MOBILE_NO mobileNo,
            bt.DRIVER_NAME driverName,
            bt.CART_BADGE_NO cartBadgeNo,
            bt.SETTLE_TYPE settleType,
            bt.INST_ID instId,
            bt.RECEIVER_MOBILE receiverMobile,
            NVL(bt.TRANSPORT_TYPE, 0) transportType,
            bt.WB_ITEM wbItem,
            NR.USER_ID dispatchCarUserId,
            bte.TRANS_VOUCHER transVoucher,
            bte.LOAD_TYPE loadType,
            bte.VOUCHER_CONFIG_TYPE voucherConfigType,
            TO_CHAR(bte.HYB_RECEIVED_TIME, 'yyyy-MM-dd Hh24:mi:ss') hybReceivedTime,
            bte.SETTLE_ISSUE settleIssue
        FROM T_BO_TRANS_TASK BT
        LEFT JOIN T_BO_TRANS_TASK_EXTRA BTE
        ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID AND BTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TTA
        ON TTA.BO_TRANS_TASK_ID =BT.BO_TRANS_TASK_ID AND TTA.IS_DEL =0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR ON  NR.BO_TRANS_NODE_RECORD_ID = bte.DISPATCH_CAR_RECORD_ID AND NR.IS_DEL =0
        WHERE
        <choose>
            <when test="isDel != null and isDel == '1'.toString()">
                BT.IS_DEL = 1
            </when>
            <otherwise>
                BT.IS_DEL = 0
            </otherwise>
        </choose>
        <choose>
            <when test="taskIdList != null and taskIdList.size() > 0">
                AND BT.BO_TRANS_TASK_ID IN
                <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            </when>
            <otherwise>
                AND BT.BO_TRANS_TASK_ID = -1
            </otherwise>
        </choose>
    </select>

    <select id="getNoStatusTaskDetailByTaskId" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            BT.BO_TRANS_TASK_ID boTransTaskId,
            BT.TAX_WAYBILL_ID taxWaybillId,
            BT.TAX_WAYBILL_NO taxWaybillNo,
            BT.ORG_ID orgId,
            BT.NODE_ID nodeId,
            BT.CREATED_USER_ID createdUserId,
            TTA.ORG_ID allocateOrgId,
            TTA.NODE_ID allocateNodeId
        FROM T_BO_TRANS_TASK BT
            LEFT JOIN T_BO_TRANS_TASK_EXTRA BTE
            ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TTA
            ON TTA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND TTA.IS_DEL = 0
        WHERE BT.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="getTaskIdByAllocateOrgIdAndTaxWaybillNo" resultType="java.lang.String">
        SELECT t.BO_TRANS_TASK_ID  FROM T_BO_TRANS_TASK t JOIN T_BO_TRANS_TASK_ALLOCATE a ON a.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND a.IS_DEL =0 WHERE t.IS_DEL =0 AND a.ORG_ID =#{orgId} AND t.TAX_WAYBILL_NO =#{taxWaybillNo}
    </select>

    <select id="queryLatestTransportTime" resultType="java.lang.String">
        SELECT TO_CHAR(TRANSPORT_TIME, '*********90.09') transportTime FROM
        (
            SELECT TRANSPORT_TIME FROM
                (
                    SELECT
                    e.TRANSPORT_TIME,t.CREATED_TIME
                    FROM
                    T_BO_TRANS_TASK t
                    JOIN T_BO_TRANS_TASK_EXTRA e ON e.BO_TRANS_TASK_ID =t.BO_TRANS_TASK_ID
                    WHERE
                    t.IS_DEL = 0
                    AND t.ORG_ID = #{orgId}
                    AND t.CREATED_TIME > SYSDATE - 90
                    AND t.START_PROVINCE_NAME = #{loadingProvinceName}
                    AND t.START_CITY_NAME = #{loadingCityName}
                    <if test="loadingCountyName!=null and loadingCountyName!=''">
                        AND t.START_COUNTY_NAME = #{loadingCountyName}
                    </if>
                    AND t.END_PROVINCE_NAME = #{unloadingProvinceName}
                    AND t.END_CITY_NAME = #{unloadingCityName}
                    <if test="unloadingCountyName!=null and unloadingCountyName!=''">
                        AND t.END_COUNTY_NAME = #{unloadingCountyName}
                    </if>
                    UNION ALL
                    SELECT
                    e.TRANSPORT_TIME,t.CREATED_TIME
                    FROM
                    T_BO_TRANS_TASK t
                    JOIN T_BO_TRANS_TASK_EXTRA e ON e.BO_TRANS_TASK_ID =t.BO_TRANS_TASK_ID
                    JOIN T_BO_TRANS_TASK_ALLOCATE a ON a.BO_TRANS_TASK_ID =t.BO_TRANS_TASK_ID AND a.IS_DEL =0
                    WHERE
                    t.IS_DEL = 0
                    AND a.ORG_ID = #{orgId}
                    AND t.CREATED_TIME > SYSDATE - 90
                    AND t.START_PROVINCE_NAME = #{loadingProvinceName}
                    AND t.START_CITY_NAME = #{loadingCityName}
                    <if test="loadingCountyName!=null and loadingCountyName!=''">
                        AND t.START_COUNTY_NAME = #{loadingCountyName}
                    </if>
                    AND t.END_PROVINCE_NAME = #{unloadingProvinceName}
                    AND t.END_CITY_NAME = #{unloadingCityName}
                    <if test="unloadingCountyName!=null and unloadingCountyName!=''">
                        AND t.END_COUNTY_NAME = #{unloadingCountyName}
                    </if>
                ) temp
            ORDER BY
                CREATED_TIME DESC
        ) WHERE ROWNUM=1
    </select>

    <select id="queryCpdPoolGroupNameByTaskOrgId" resultType="java.lang.String">
        SELECT
        DISTINCT CPD_POOL_GROUP_NAME
        FROM
        T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA e ON
        t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
        WHERE
        t.ORG_ID = #{orgId}
        AND t.IS_DEL = 0
        AND e.CPD_POOL_GROUP_NAME IS NOT NULL
    </select>

    <select id="queryCpdSecondPoolGroupNameByTaskOrgId" resultType="java.lang.String">
        SELECT
        DISTINCT CPD_SECOND_POOL_GROUP_NAME
        FROM
        T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA e ON
        t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
        WHERE
        t.ORG_ID = #{orgId}
        AND t.IS_DEL = 0
        AND e.CPD_SECOND_POOL_GROUP_NAME IS NOT NULL
    </select>

    <select id="queryCpdPoolGroupNameByAllocateOrgId" resultType="java.lang.String">
        SELECT
        DISTINCT CPD_POOL_GROUP_NAME
        FROM
        T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA e ON
        t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_TASK_ALLOCATE a ON
        a.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        AND a.IS_DEL = 0
        WHERE
        a.ORG_ID = #{orgId}
        AND t.IS_DEL = 0
        AND e.CPD_POOL_GROUP_NAME IS NOT NULL
    </select>

    <select id="queryCpdSecondPoolGroupNameByAllocateOrgId" resultType="java.lang.String">
        SELECT
        DISTINCT CPD_SECOND_POOL_GROUP_NAME
        FROM
        T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA e ON
        t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_TASK_ALLOCATE a ON
        a.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        AND a.IS_DEL = 0
        WHERE
        a.ORG_ID = #{orgId}
        AND t.IS_DEL = 0
        AND e.CPD_SECOND_POOL_GROUP_NAME IS NOT NULL
    </select>

    <select id="getBaseTaskDetail" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            T1.BO_TRANS_TASK_ID boTransTaskId,
            T1.TAX_WAYBILL_ID taxWaybillId,
            T1.TAX_WAYBILL_NO taxWaybillNo,
            T1.TRANS_MODE transMode,
            T1.ORG_ID orgId,
            T1.SETTLE_MODE settleMode,
            T3.ORG_ID allocateOrgId,
            T3.TAX_WAYBILL_ID allocateTaxWaybillId,
            T3.SETTLE_MODE allocateSettleMode,
            T2.BO_LINE_ASSIGN_REL_ID boLineAssignRelId
        FROM T_BO_TRANS_TASK T1
            JOIN T_BO_TRANS_TASK_EXTRA T2
        ON T2.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
            AND T2.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE T3
            ON T3.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
            AND T3.IS_DEL = 0
        WHERE
            T1.IS_DEL = 0
            AND T1.BO_TRANS_TASK_ID = #{taskId}
    </select>
    
    <resultMap id="nodeRecordListMap" type="com.wtyt.bo.bean.response.NodeInfo">
            <id property="boTransNodeRecordId" column="boTransNodeRecordId" />
            <result property="boTransTaskId" column="boTransTaskId" />
            <result property="nodeId" column="nodeId" />
            <result property="boTransNodeRecordId" column="boTransNodeRecordId"/>
            <result property="optSource" column="optSource"/>
            <result property="userId" column="userId"/>
            <result property="driverId" column="driverId"/>
            <result property="overTime" column="overTime"/>
            <result property="nodeTime" column="nodeTime"/>
            <result property="lastModifiedTime" column="lastModifiedTime"/>
            <result property="reason" column="reason"/>
            <result property="sysRoleType" column="sysRoleType"/>
            <result property="orgId" column="orgId"/>
            <result property="groupId" column="groupId"/>
            <result property="jobName" column="jobName"/>
            <result property="address" column="address"/>
            <result property="nodeSubObjectType" column="nodeSubObjectType"/>
            <result property="nodeSubObjectValue" column="nodeSubObjectValue"/>
            <collection property="alarmList" ofType="com.wtyt.dao.bean.syf.BoTransNodeAlarmBean">
	            <id property="boTransNodeAlarmId" column="boTransNodeAlarmId" />
	            <result property="extend" column="extend"/>
	            <result property="nodeDataType" column="nodeDataType"/>
	            <result property="alarmType" column="alarmType"/>
	            <result property="overTime" column="overTime"/>
	            <result property="alarmEndTime" column="alarmEndTime"/>
	            <result property="alarmStartTime" column="alarmStartTime"/>
	            <result property="createdTime" column="alarmCreatedTime"/>
	            <result property="lastModifiedTime" column="alarmLastModifiedTime"/>
	            <result property="alarmProcessResult" column="alarmAlarmProcessResult"/>
	        </collection>
    </resultMap>
    
     <!-- 获取对应任务的非支付结算节点产生的节点及异常 -->
    <select id="getNoPayNodeRecordList" resultMap="nodeRecordListMap">
        SELECT
            A.BO_TRANS_TASK_ID boTransTaskId,
            A.BO_TRANS_NODE_RECORD_ID boTransNodeRecordId,
            A.NODE_ID nodeId,
            A.OPT_SOURCE optSource,
            A.USER_ID userId,
            A.DRIVER_ID driverId,
            A.OVER_TIME overTime,
            A.TAX_POS_INFO_ID taxPosInfoId,
            TO_CHAR(A.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            TO_CHAR(A.LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI:SS') lastModifiedTime,
            A.REASON reason,
            A.SYS_ROLE_TYPE sysRoleType,
            A.ORG_ID orgId,
            A.GROUP_ID groupId,
            A.JOB_NAME jobName,
            A.ADDRESS address,
            A.NODE_SUB_OBJECT_TYPE nodeSubObjectType,
        	A.NODE_SUB_OBJECT_VALUE nodeSubObjectValue,
        	B.BO_TRANS_NODE_ALARM_ID boTransNodeAlarmId,
            B.EXTEND extend,
            B.NODE_DATA_TYPE nodeDataType,
            B.ALARM_TYPE alarmType,
            B.OVER_TIME overTime,
            TO_CHAR(B.ALARM_END_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmEndTime,
            TO_CHAR(B.ALARM_START_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmStartTime,
            TO_CHAR(B.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmCreatedTime,
            TO_CHAR(B.LAST_MODIFIED_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmLastModifiedTime,
            B.ALARM_PROCESS_RESULT alarmAlarmProcessResult
        FROM T_BO_TRANS_NODE_RECORD A
        LEFT JOIN T_BO_TRANS_NODE_ALARM B ON A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID
        								  AND B.IS_DEL = 0
        								  AND B.NODE_ID = A.NODE_ID
       WHERE A.IS_DEL = 0
         AND A.BO_TRANS_TASK_ID = #{boTransTaskId}
         AND A.NODE_ID NOT IN (900,910,1200)
         <choose>
            <when test="sortFlag == 1">
                ORDER BY A.CREATED_TIME DESC, A.LAST_MODIFIED_TIME DESC, A.NODE_ID DESC
            </when>
            <otherwise>
                ORDER BY A.CREATED_TIME, A.LAST_MODIFIED_TIME, A.NODE_ID
            </otherwise>
        </choose>
    </select>
    
    <!-- 获取对应任务对应企业的结算节点产生的节点及异常 -->
    <select id="getSettleNodeRecordList" resultMap="nodeRecordListMap">
        SELECT
        	A.BO_TRANS_TASK_ID boTransTaskId,
            A.BO_TRANS_NODE_RECORD_ID boTransNodeRecordId,
            A.NODE_ID nodeId,
            A.OPT_SOURCE optSource,
            A.USER_ID userId,
            A.DRIVER_ID driverId,
            A.OVER_TIME overTime,
            A.TAX_POS_INFO_ID taxPosInfoId,
            TO_CHAR(A.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            TO_CHAR(A.LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI:SS') lastModifiedTime,
            A.REASON reason,
            A.SYS_ROLE_TYPE sysRoleType,
            A.ORG_ID orgId,
            A.GROUP_ID groupId,
            A.JOB_NAME jobName,
            A.ADDRESS address,
            A.NODE_SUB_OBJECT_TYPE nodeSubObjectType,
            A.NODE_SUB_OBJECT_VALUE nodeSubObjectValue,
            B.BO_TRANS_NODE_ALARM_ID boTransNodeAlarmId,
            B.EXTEND extend,
            B.NODE_DATA_TYPE nodeDataType,
            B.ALARM_TYPE alarmType,
            TO_CHAR(B.ALARM_END_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmEndTime,
            TO_CHAR(B.ALARM_START_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmStartTime,
            TO_CHAR(B.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmCreatedTime,
            TO_CHAR(B.LAST_MODIFIED_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmLastModifiedTime,
            B.ALARM_PROCESS_RESULT alarmAlarmProcessResult
         FROM T_BO_TRANS_NODE_RECORD A
         LEFT JOIN T_BO_TRANS_NODE_ALARM B ON A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID
         								   AND B.IS_DEL = 0
         								   AND B.NODE_ID = A.NODE_ID
       WHERE A.IS_DEL = 0
         AND A.BO_TRANS_TASK_ID = #{boTransTaskId}
         AND A.ORG_ID = #{orgId}
         AND A.NODE_ID IN (1200)
         <choose>
            <when test="sortFlag == 1">
                ORDER BY A.CREATED_TIME DESC, A.NODE_ID DESC
            </when>
            <otherwise>
                ORDER BY A.CREATED_TIME, A.NODE_ID
            </otherwise>
        </choose>
    </select>
    
     <!-- 查询有异常信息没有节点信息的，不包含900,910,1200（如预警） -->
    <select id="getAlarmNoPayNodeRecordList" resultType="com.wtyt.dao.bean.syf.BoTransNodeAlarmBean">
        SELECT
            A.NODE_ID nodeId,
            A.EXTEND extend,
            A.NODE_DATA_TYPE nodeDataType,
            A.ALARM_TYPE alarmType,
            A.OVER_TIME overTime,
            TO_CHAR(A.ALARM_END_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmEndTime,
            TO_CHAR(A.ALARM_START_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmStartTime,
            TO_CHAR(A.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
            TO_CHAR(A.LAST_MODIFIED_TIME, 'yyyy-mm-dd hh24:mi:ss') lastModifiedTime,
            A.ALARM_PROCESS_RESULT alarmProcessResult
        FROM T_BO_TRANS_NODE_ALARM A
       WHERE A.IS_DEL = 0
       	 AND A.BO_TRANS_TASK_ID = #{boTransTaskId}
         AND A.NODE_ID NOT IN (900,910,1200)
         AND NOT EXISTS(
         	SELECT 1 FROM T_BO_TRANS_NODE_RECORD B
         	        WHERE B.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
         	          AND B.NODE_ID = A.NODE_ID
         )
         ORDER BY A.CREATED_TIME
    </select>
    
    <!-- 查询有异常信息没有节点信息的（如预警），只包含1200 -->
    <select id="getAlarmSettleNodeRecordList" resultType="com.wtyt.dao.bean.syf.BoTransNodeAlarmBean">
        SELECT
            A.NODE_ID nodeId,
            A.EXTEND extend,
            A.NODE_DATA_TYPE nodeDataType,
            A.ALARM_TYPE alarmType,
            A.OVER_TIME overTime,
            TO_CHAR(A.ALARM_END_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmEndTime,
            TO_CHAR(A.ALARM_START_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmStartTime,
            TO_CHAR(A.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
            TO_CHAR(A.LAST_MODIFIED_TIME, 'yyyy-mm-dd hh24:mi:ss') lastModifiedTime,
            A.ALARM_PROCESS_RESULT alarmProcessResult
        FROM T_BO_TRANS_NODE_ALARM A
       WHERE A.IS_DEL = 0
       	 AND A.BO_TRANS_TASK_ID = #{boTransTaskId}
         AND A.ORG_ID = #{orgId}
         AND A.NODE_ID IN (1200)
         AND NOT EXISTS(
         	SELECT 1 FROM T_BO_TRANS_NODE_RECORD B
         	        WHERE B.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
         	          AND B.NODE_ID = A.NODE_ID
         )
         ORDER BY A.CREATED_TIME
    </select>

    <select id="queryOperateFeeStatusByTaskIds" resultType="com.wtyt.bo.bean.BoTaskDetailBean">
        SELECT
            t.BO_TRANS_TASK_ID boTransTaskId,
            NVL(te.OPERATE_FEE_STATUS,0) operateFeeStatus
        FROM
        T_bo_trans_task t
        JOIN T_BO_TRANS_TASK_EXTRA te ON
        te.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        WHERE
        t.IS_DEL =0
        AND t.BO_TRANS_TASK_ID IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateHybState">
        UPDATE T_BO_TRANS_TASK T
        SET T.HYB_STATE = #{hybState},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.HYB_STATE != #{hybState}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <select id="getAppTransTaskListDeletedCount" resultType="int">
        SELECT NVL(COUNT(*), 0)
        FROM <include refid="fromTableForApp"/>
        WHERE <include refid="taskQueryWhereSql"/>
    </select>

    <select id="getTransTaskIdByTaxWaybillId" resultType="java.lang.String">
        SELECT
            T1.BO_TRANS_TASK_ID
        FROM T_BO_TRANS_TASK T1
        WHERE T1.IS_DEL = 0
            AND T1.TAX_WAYBILL_ID = #{taxWaybillId}

        UNION

        SELECT
            T2.BO_TRANS_TASK_ID
        FROM T_BO_TRANS_TASK_ALLOCATE T2
        JOIN T_BO_TRANS_TASK T3
            ON T3.BO_TRANS_TASK_ID = T2.BO_TRANS_TASK_ID
        WHERE T2.IS_DEL = 0
            AND T3.IS_DEL = 0
            AND T2.TAX_WAYBILL_ID = #{taxWaybillId}
    </select>

    <select id="getBoTransTaskIdByIdList" resultType="java.lang.String">
        SELECT
            BO_TRANS_TASK_ID
        FROM T_BO_TRANS_TASK
        WHERE IS_DEL = 0
        AND BO_TRANS_TASK_ID IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>




</mapper>
