package com.wtyt.inquire.bean;

import com.wtyt.common.rpc.bean.base.BoTokenBean;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @createTime 2023/04/05 10:26:00
 */
public class Req5329124IBean extends BoTokenBean {

    private List<String> orderIdList;

    private String taxWaybillId;

    private String boTransTaskId;

    private String askType;

    private String mqFlag;

    /**
     * 1：超时未询价
     * 2：超时未报价
     * 3：超时未审批
     **/
    private String overTimeType;

    public List<String> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<String> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public String getTaxWaybillId() {
        return taxWaybillId;
    }

    public void setTaxWaybillId(String taxWaybillId) {
        this.taxWaybillId = taxWaybillId;
    }

    public String getBoTransTaskId() {
        return boTransTaskId;
    }

    public void setBoTransTaskId(String boTransTaskId) {
        this.boTransTaskId = boTransTaskId;
    }

    public String getAskType() {
        return askType;
    }

    public void setAskType(String askType) {
        this.askType = askType;
    }

    public String getMqFlag() {
        return mqFlag;
    }

    public void setMqFlag(String mqFlag) {
        this.mqFlag = mqFlag;
    }

    public String getOverTimeType() {
        return overTimeType;
    }

    public void setOverTimeType(String overTimeType) {
        this.overTimeType = overTimeType;
    }
}
