<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.sl.mapper.syf.ThirdSynShippingListMapper">

    <select id="getShippingListByOrderId" resultType="com.wtyt.dao.bean.syf.BoShippingListBean">
        select sl.BO_SHIPPING_LIST_ID boShippingListId,
               sl.BO_TRANS_TASK_ID    boTransTaskId,
               sl.BO_TRANS_ORDER_ID   boTransOrderId,
               sl.RECEIPT_NO          receiptNo
        from T_BO_SHIPPING_LIST sl, T_BO_TRANS_ORDER_REL tor
        where sl.BO_TRANS_ORDER_ID = tor.BO_TRANS_ORDER_ID
          and sl.BO_TRANS_TASK_ID = tor.BO_TRANS_TASK_ID
          and sl.BO_TRANS_ORDER_ID = #{boTransOrderId}
          and sl.is_del = 0
          and tor.is_del = 0
    </select>

</mapper>

