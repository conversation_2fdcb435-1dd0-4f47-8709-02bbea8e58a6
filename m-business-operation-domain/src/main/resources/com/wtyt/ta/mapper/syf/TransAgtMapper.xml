<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.ta.mapper.syf.TransAgtMapper">

    <select id="getAgreementInfoData" resultType="com.wtyt.dao.bean.syf.BoTransAgreementInfoBean">
        SELECT
            T.BO_TRANS_TASK_ID  boTransTaskId,
            T.CART_BADGE_NO plateNo,
            E.CART_LENGTH cartLength,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.END_CITY_NAME arriveCity,
            T.END_COUNTY_NAME endCountyName,
            E.PAY_NAME payee,
            E.PAY_BANK_NAME bankName,
            E.PAY_BANK_NO bankCard,
            T.GOODS_COST goodsValue,
            T.GOODS_AMOUNT_TYPE goodsAmountType,
            TO_CHAR(NVL(T.ALL_FREIGHT, 0) , 'FM999999990.00')  balance,
            TO_CHAR(NVL(T.USER_FREIGHT, 0) , 'FM999999990.00')  userFreight,
            TO_CHAR(NVL(TF.CONFIG_VALUE, 0) , 'FM999999990.00') oilCard,
            TF.REASON oilCardNo,
            OH.CONFIG_VALUE expenses,
            TF.REASON oilCardNo
        FROM T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_EXTRA E ON E.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND E.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE TF ON TF.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND TF.IS_DEL = 0 AND TF.CONFIG_KEY = 'fuelCostFee'
        LEFT JOIN T_BO_TRANS_TASK_FEE OH ON OH.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND OH.IS_DEL = 0 AND OH.CONFIG_KEY = 'overhead'
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

</mapper>
