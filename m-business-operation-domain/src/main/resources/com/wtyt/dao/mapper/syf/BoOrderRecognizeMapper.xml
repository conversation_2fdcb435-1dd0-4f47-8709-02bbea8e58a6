<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoOrderRecognizeMapper">

    <insert id="insertOrderRecognize">
        INSERT INTO T_BO_ORDER_RECOGNIZE
            (BO_ORDER_RECOGNIZE_ID,
             STICK_CONTENT,
             STICK_CONTENT_MD5,
             OPT_USER_ID,
             RECOGNIZE_PROGRESS,
             RECOGNIZE_START_TIME,
             RECOGNIZE_SIZE,
             FROM_SOURCE)
        VALUES
            (#{boOrderRecognizeId},
             #{stickContent},
             #{stickContentMd5},
             #{optUserId},
             #{recognizeProcess},
             #{recognizeStartTime},
             #{recognizeSize},
             #{fromSource})
    </insert>

    <update id="updateOrderRecognize">
        UPDATE T_BO_ORDER_RECOGNIZE
        SET RECOGNIZE_PROGRESS = #{recognizeProcess},
            RECOGNIZE_SIZE = #{recognizeSize},
            RECOGNIZE_END_TIME = #{recognizeEndTime},
            RECOGNIZE_RESULT = #{recognizeSplitResult1,javaType=string},
            RECOGNIZE_RESULT1 = #{recognizeSplitResult2,javaType=string},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_ORDER_RECOGNIZE_ID = #{boOrderRecognizeId}
    </update>

    <select id="selectById" resultType="com.wtyt.dao.bean.syf.BoOrderRecognizeBean">
        SELECT BO_ORDER_RECOGNIZE_ID boOrderRecognizeId,
               RECOGNIZE_PROGRESS recognizeProcess,
               RECOGNIZE_RESULT recognizeResult,
               RECOGNIZE_RESULT1 recognizeResult1,
               RECOGNIZE_START_TIME recognizeStartTime
        FROM T_BO_ORDER_RECOGNIZE
        WHERE BO_ORDER_RECOGNIZE_ID = #{boToFhRecognizeId}
    </select>

    <select id="countFailedTimesByRecognizeContentRecent10Min" resultType="java.lang.Integer">
        SELECT count(BO_ORDER_RECOGNIZE_ID)
        FROM T_BO_ORDER_RECOGNIZE
        WHERE STICK_CONTENT_MD5 = #{contentMd5}
        AND CREATED_TIME BETWEEN sysdate - (10/24/60) AND sysdate
        AND RECOGNIZE_SIZE = 0
    </select>
</mapper>
