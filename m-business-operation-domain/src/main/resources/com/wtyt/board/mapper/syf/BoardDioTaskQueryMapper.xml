<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardDioTaskQueryMapper">

    <sql id="dioQuery">
        AND T.ORG_ID IN
        <foreach collection="queryOrgIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        <if test="wbItems != null and wbItems.size() > 0">
            AND T.WB_ITEM IN
            <foreach collection="wbItems" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startPlace != null and startPlace.size() > 0">
            AND (
            <foreach item="item" index="index" collection="startPlace" separator="or">
                (
                    <if test="item.provinceName != null and item.provinceName != ''">
                        t.START_PROVINCE_NAME = #{item.provinceName}
                    </if>
                    <if test="item.cityName != null and item.cityName != ''">
                        and t.START_CITY_NAME = #{item.cityName}
                    </if>
                    <if test="item.countyName != null and item.countyName != ''">
                        and t.START_COUNTY_NAME = #{item.countyName}
                    </if>
                )
            </foreach>)
        </if>
        <if test="endPlace != null and endPlace.size() > 0">
            AND (
            <foreach item="item" index="index" collection="endPlace" separator="or">
                (
                    <if test="item.provinceName != null and item.provinceName != ''">
                        t.END_PROVINCE_NAME = #{item.provinceName}
                    </if>
                    <if test="item.cityName != null and item.cityName != ''">
                        and t.END_CITY_NAME = #{item.cityName}
                    </if>
                    <if test="item.countyName != null and item.countyName != ''">
                        and t.END_COUNTY_NAME = #{item.countyName}
                    </if>
                )
            </foreach>)
        </if>
        <if test="belongDispatcherIds != null and belongDispatcherIds.size() > 0">
            AND e.BELONG_DISPATCHER_ID IN
            <foreach collection="belongDispatcherIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cartTypeList != null and cartTypeList.size() > 0">
            AND e.CART_TYPE IN
            <foreach collection="cartTypeList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cartLengthList != null and cartLengthList.size() > 0">
            AND e.CART_LENGTH IN
            <foreach collection="cartLengthList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>

    </sql>


     <select id="arriveNum" resultType="com.wtyt.board.bean.TaskArrivedCountBean" parameterType="com.wtyt.board.bean.request.BoardDioQueryBean">
        SELECT NVL(sum(overtimeArrive) ,0)   overtimeArriveNum,
               NVL(sum(arrive),0)  arriveNum
        FROM (SELECT
             CASE WHEN  NVL(E.ARRIVE_TIME,T.START_TIME) IS NULL OR NVL(E.ARRIVE_TIME,T.START_TIME) > E.ARRIVE_END_TIME  THEN 1 ELSE 0 END AS overtimeArrive ,
             CASE WHEN  NVL(E.ARRIVE_TIME,T.START_TIME) &lt;= E.ARRIVE_END_TIME THEN 1 ELSE 0 END AS arrive
        FROM T_BO_TRANS_TASK T
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON e.IS_DEL = 0 AND e.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        WHERE T.IS_DEL = 0
        <include refid="dioQuery" />
         <!--任务已到场-->
        AND (E.ARRIVE_TIME IS NOT NULL OR T.STATE in (1,2))
         <!--约定到场时间startTime-->
         <if test="startTime != null and startTime !=''">
            AND  e.ARRIVE_END_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
         </if>
         <!--约定到场时间endTime-->
         <if test="endTime != null and endTime !=''">
            AND e .ARRIVE_END_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
         </if>
        )
    </select>


    <select id="predictArriveNum" resultType="com.wtyt.board.bean.TaskArrivedCountBean" parameterType="com.wtyt.board.bean.request.BoardDioQueryBean">
        SELECT NVL(sum(predictArrive), 0) predictArriveNum,
              NVL(sum(predictOvertimeArrive), 0) predictOvertimeArriveNum
        FROM (SELECT
             CASE WHEN  na.BO_TRANS_TASK_ID IS NULL  AND  na1.BO_TRANS_TASK_ID  is null  THEN 1 ELSE 0 END AS predictArrive ,
             CASE WHEN  na.BO_TRANS_TASK_ID IS NOT NULL  THEN 1  WHEN na1.BO_TRANS_TASK_ID is NOT null then 1  ELSE 0 END AS predictOvertimeArrive
        FROM T_BO_TRANS_TASK t
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON e.IS_DEL = 0 AND e.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        <!--预计装货地迟到异常标记-->
        LEFT  JOIN T_BO_TRANS_NODE_ALARM na ON t .BO_TRANS_TASK_ID =na.BO_TRANS_TASK_ID AND na.NODE_DATA_TYPE =2 AND NA.IS_DEL=0 AND NA.ALARM_PROCESS_RESULT =0 and NA.ALARM_TYPE=0
        LEFT  JOIN T_BO_TRANS_NODE_ALARM na1 ON t .BO_TRANS_TASK_ID =na1.BO_TRANS_TASK_ID AND na1.NODE_DATA_TYPE =2 AND NA1.IS_DEL=0  and NA1.ALARM_TYPE=1
        WHERE t.IS_DEL = 0
         <include refid="dioQuery" />
        <!--任务已派车未到场-->
        AND  E.DISPATCH_CAR_RECORD_ID IS NOT NULL AND E.ARRIVE_TIME IS NULL AND T.STATE = 0
        <!--约定到场时间startTime-->
        <if test="startTime != null and startTime !=''">
            AND e.ARRIVE_END_TIME  &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!--约定到场时间endTime-->
        <if test="endTime != null and endTime !=''">
            AND e.ARRIVE_END_TIME  &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        )
    </select>

    <select id="deliveryNum" resultType="com.wtyt.board.bean.TaskDeliveryCountBean" parameterType="com.wtyt.board.bean.request.BoardDioQueryBean">
        SELECT NVL(sum(overtimeDelivery) ,0)   overtimeDeliveryNum,
               NVL(sum(delivery),0)  deliveryNum
        FROM (SELECT
             CASE WHEN  NVL(R.CREATED_TIME,T.END_TIME) IS NULL  OR NVL(R.CREATED_TIME,T.END_TIME) > dl.DEAD_LINE_TIME  THEN 1 ELSE 0 END AS overtimeDelivery ,
             CASE WHEN  NVL(R.CREATED_TIME,T.END_TIME)  &lt;= dl.DEAD_LINE_TIME  THEN 1 ELSE 0 END AS delivery
        FROM T_BO_TRANS_TASK T
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON e.IS_DEL = 0 AND e.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        INNER JOIN T_BO_TRANS_NODE_DEAD_LINE dl ON T .BO_TRANS_TASK_ID =dl.BO_TRANS_TASK_ID AND dl.NODE_ID =800
        LEFT  JOIN T_BO_TRANS_NODE_RECORD R ON T .BO_TRANS_TASK_ID =R.BO_TRANS_TASK_ID AND R.NODE_ID =650 AND r.is_del =0
        WHERE T.IS_DEL = 0
        <include refid="dioQuery" />
            <!--到达卸货地-->
            AND (T.NODE_ID >=650 OR T.STATE = 2)  AND DL .IS_DEL =0
        <!--约定送达时间startTime-->
        <if test="startTime != null and startTime !=''">
            AND dl .DEAD_LINE_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!--约定送达时间endTime-->
        <if test="endTime != null and endTime !=''">
            AND dl .DEAD_LINE_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        )
    </select>

    <select id="predictDeliveryNum" resultType="com.wtyt.board.bean.TaskDeliveryCountBean" parameterType="com.wtyt.board.bean.request.BoardDioQueryBean">
        SELECT NVL(sum(predictDelivery), 0) predictDeliveryNum,
              NVL(sum(predictOvertimeDelivery), 0) predictOvertimeDeliveryNum
        FROM (SELECT
             CASE WHEN  na.BO_TRANS_TASK_ID IS NULL  AND  na1.BO_TRANS_TASK_ID  is null  THEN 1 ELSE 0 END AS predictDelivery ,
             CASE WHEN  na.BO_TRANS_TASK_ID IS NOT NULL  THEN 1  WHEN na1.BO_TRANS_TASK_ID is NOT null then 1  ELSE 0 END AS predictOvertimeDelivery
        FROM T_BO_TRANS_TASK t
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON e.IS_DEL = 0 AND e.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        INNER JOIN T_BO_TRANS_NODE_DEAD_LINE dl ON t .BO_TRANS_TASK_ID =dl.BO_TRANS_TASK_ID AND dl.NODE_ID =800
        <!--预计卸货地迟到异常标记-->
        LEFT  JOIN T_BO_TRANS_NODE_ALARM na ON t .BO_TRANS_TASK_ID =na.BO_TRANS_TASK_ID AND na.NODE_DATA_TYPE =16 AND NA.IS_DEL=0 AND NA.ALARM_PROCESS_RESULT =0 and NA.ALARM_TYPE=0
        LEFT  JOIN T_BO_TRANS_NODE_ALARM na1 ON t .BO_TRANS_TASK_ID =na1.BO_TRANS_TASK_ID AND na1.NODE_DATA_TYPE =16 AND NA1.IS_DEL=0  and NA1.ALARM_TYPE=1
        WHERE t.IS_DEL = 0
         <include refid="dioQuery" />
        <!--任务已发车未到达卸货地-->
        AND T.STATE =1  AND DL .IS_DEL =0
        <!--约定送达时间startTime-->
        <if test="startTime != null and startTime !=''">
            AND dl .DEAD_LINE_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!--约定送达时间endTime-->
        <if test="endTime != null and endTime !=''">
            AND dl .DEAD_LINE_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        )
    </select>

    <select id="longTermStayNum" resultType="com.wtyt.board.bean.TaskDeliveryCountBean" parameterType="com.wtyt.board.bean.request.BoardDioQueryBean">
        SELECT
              NVL(sum(longTermStay), 0) longTermStayNum
        FROM (SELECT
             CASE WHEN  na.BO_TRANS_TASK_ID IS NOT NULL  THEN 1 ELSE 0 END AS longTermStay
        FROM T_BO_TRANS_TASK t
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON e.IS_DEL = 0 AND e.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        INNER JOIN T_BO_TRANS_NODE_DEAD_LINE dl ON t .BO_TRANS_TASK_ID =dl.BO_TRANS_TASK_ID AND dl.NODE_ID =800
            <!--长时间停留异常标记-->
        LEFT  JOIN T_BO_TRANS_NODE_ALARM na ON t .BO_TRANS_TASK_ID =na.BO_TRANS_TASK_ID AND na.NODE_DATA_TYPE =14 AND NA.IS_DEL=0 AND NA.ALARM_PROCESS_RESULT =0 and NA.ALARM_TYPE=1
        WHERE t.IS_DEL = 0
         <include refid="dioQuery" />
            <!--约定送达时间startTime-->
        <if test="startTime != null and startTime !=''">
            AND dl .DEAD_LINE_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
            <!--约定送达时间endTime-->
        <if test="endTime != null and endTime !=''">
            AND dl .DEAD_LINE_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
         </if>
        )
    </select>


    <select id="dispatcherDetailList" resultType="com.wtyt.board.bean.DispatcherTaskInfoBean" parameterType="com.wtyt.board.bean.request.BoardDioQueryBean">
       SELECT
        nvl(e.BELONG_DISPATCHER_ID,'-1') belongDispatcherId,
        T.WB_ITEM wbItem,
        T.START_CITY_NAME || T.START_COUNTY_NAME||'→'||t.END_CITY_NAME||t.END_COUNTY_NAME lineName,
        COUNT(t.BO_TRANS_TASK_ID) taskTotalNum,
        COUNT(DISTINCT (F.BO_TRANS_TASK_ID || F1.BO_TRANS_TASK_ID)) taskAlarmNum,
        NVL(SUM(F.CONFIG_VALUE),0) taskDamageFreight,
        COUNT(F1.BO_TRANS_TASK_ID) taskEscortNum,
        NVL(SUM(F1.CONFIG_VALUE),0) taskEscortFreight
        FROM T_BO_TRANS_TASK t
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON e.IS_DEL = 0 AND e.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_FEE F ON t .BO_TRANS_TASK_ID =F.BO_TRANS_TASK_ID AND F.CONFIG_NAME ='货损金额'  AND F.CONFIG_VALUE > 0 AND F.IS_DEL=0
        LEFT JOIN T_BO_TRANS_TASK_FEE F1 ON t .BO_TRANS_TASK_ID =F1.BO_TRANS_TASK_ID AND F1.CONFIG_NAME ='压车费' AND F1.CONFIG_VALUE > 0 AND F1.IS_DEL=0
        WHERE t.IS_DEL = 0
        <include refid="dioQuery" />
            <!--任务创建时间startTime-->
        <if test="startTime != null and startTime !=''">
            AND T .CREATED_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
            <!--任务时间endTime-->
        <if test="endTime != null and endTime !=''">
            AND T .CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
       GROUP BY e.BELONG_DISPATCHER_ID, T.WB_ITEM ,T.START_CITY_NAME,T.START_COUNTY_NAME,t.END_CITY_NAME,t.END_COUNTY_NAME

    </select>
    <select id="queryMostGoodsNames" resultType="java.lang.String">
        SELECT GOODS_NAME FROM (
        SELECT
            *
        FROM
            (
            SELECT
                GOODS_NAME,
                count(1) num
            FROM
                T_BO_TRANS_TASK
            WHERE
                IS_DEL = 0
                AND CREATED_TIME > SYSDATE - 180
                AND ORG_ID IN
                <foreach collection="list" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
                AND GOODS_NAME IS NOT NULL
            GROUP BY
                GOODS_NAME
            )
        ORDER BY
            num DESC
            )  WHERE rownum &lt; 4
    </select>



</mapper>
