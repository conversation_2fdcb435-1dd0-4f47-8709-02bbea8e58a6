<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransNodeDeadLineMapper">

    <select id="detailByBoTransNodeDeadLine" resultType="BoTransNodeDeadLineBean">
        SELECT
        BO_TRANS_NODE_DEAD_LINE_ID boTransNodeDeadLineId,
        TAX_WAYBILL_ID taxWaybillId,
        BO_TRANS_TASK_ID boTransTaskId,
        NODE_ID nodeId,
        to_char(DEAD_LINE_TIME,'YYYY-MM-DD HH24:MI:SS') deadLineTime,
        NOTE note,
        to_char(KPI_DEAL_LINE_TIME,'YYYY-MM-DD HH24:MI:SS') kpiDealLineTime
        FROM T_BO_TRANS_NODE_DEAD_LINE
        <where>
            IS_DEL = 0
            <if test="boTransNodeDeadLineId != null and boTransNodeDeadLineId !=''">
                and BO_TRANS_NODE_DEAD_LINE_ID = #{boTransNodeDeadLineId}
            </if>
            <if test="boTransTaskId != null and boTransTaskId !=''">
                and BO_TRANS_TASK_ID = #{boTransTaskId}
            </if>
            <if test="nodeId != null and nodeId !=''">
                and NODE_ID = #{nodeId}
            </if>
        </where>
    </select>

    <insert id="insertBoTransNodeDeadLine" parameterType="BoTransNodeDeadLineBean">
        INSERT INTO T_BO_TRANS_NODE_DEAD_LINE(
        BO_TRANS_NODE_DEAD_LINE_ID,
        <if test="taxWaybillId != null and taxWaybillId !=''">
            TAX_WAYBILL_ID,
        </if>
        <if test="boTransTaskId != null and boTransTaskId !=''">
            BO_TRANS_TASK_ID,
        </if>
        NODE_ID,
        <if test="deadLineTime != null and deadLineTime !=''">
            DEAD_LINE_TIME,
        </if>
        <if test="note != null and note !=''">
            NOTE,
        </if>
        <if test="kpiDealLineTime != null and kpiDealLineTime !=''">
            KPI_DEAL_LINE_TIME,
        </if>
        CREATED_TIME
        )values(
        ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},
        <if test="taxWaybillId != null and taxWaybillId !=''">
            #{taxWaybillId},
        </if>
        <if test="boTransTaskId != null and boTransTaskId !=''">
            #{boTransTaskId},
        </if>
        #{nodeId},
        <if test="deadLineTime != null and deadLineTime !=''">
            to_date( #{deadLineTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
        <if test="note != null and note !=''">
            #{note},
        </if>
        <if test="kpiDealLineTime != null and kpiDealLineTime !=''">
            to_date( #{kpiDealLineTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
        SYSDATE

        )
    </insert>

    <update id="updateBoTransNodeDeadLine" parameterType="BoTransNodeDeadLineBean">
        update T_BO_TRANS_NODE_DEAD_LINE SET
        <if test="deadLineTime != null and deadLineTime !=''">
            DEAD_LINE_TIME = to_date( #{deadLineTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
        <if test="note != null and note !=''">
            NOTE = #{note},
        </if>
        <if test="kpiDealLineTime != null and kpiDealLineTime !=''">
            KPI_DEAL_LINE_TIME = to_date(#{kpiDealLineTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        <where>
            IS_DEL = 0
            <if test="boTransNodeDeadLineId != null and boTransNodeDeadLineId !=''">
                and BO_TRANS_NODE_DEAD_LINE_ID = #{boTransNodeDeadLineId}
            </if>
            <if test="boTransTaskId != null and boTransTaskId !=''">
                and BO_TRANS_TASK_ID = #{boTransTaskId}
            </if>
            <if test="taxWaybillId != null and taxWaybillId !=''">
                and TAX_WAYBILL_ID = #{taxWaybillId}
            </if>
            <if test="nodeId != null and nodeId !=''">
                and NODE_ID = #{nodeId}
            </if>
        </where>
    </update>
    <update id="clearNodeDeadLineTime">
            UPDATE T_BO_TRANS_NODE_DEAD_LINE SET DEAD_LINE_TIME = NULL,LAST_MODIFIED_TIME = SYSDATE  WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID = #{boTransTaskId} AND NODE_ID IN
            <foreach collection="nodeIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
    </update>

    <select id="getDeadLineByBoTransTaskIdAndNodeId" resultType="BoTransNodeDeadLineBean">
        SELECT
            BO_TRANS_NODE_DEAD_LINE_ID boTransNodeDeadLineId,
            TAX_WAYBILL_ID taxWaybillId,
            NODE_ID nodeId,
            to_char(DEAD_LINE_TIME,'YYYY-MM-DD HH24:MI:SS') deadLineTime,
            NOTE note,
            to_char(KPI_DEAL_LINE_TIME,'YYYY-MM-DD HH24:MI:SS') kpiDealLineTime
        FROM T_BO_TRANS_NODE_DEAD_LINE T
        WHERE T.BO_TRANS_TASK_ID = #{boTransTaskId}
          AND T.NODE_ID = #{nodeId}
          AND T.IS_DEL = 0
    </select>

    <select id="getDeadLineByTransTaskIdAndNodeId" resultType="BoTransNodeDeadLineBean">
        SELECT
            T.BO_TRANS_NODE_DEAD_LINE_ID boTransNodeDeadLineId,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.NODE_ID nodeId,
            TO_CHAR(T.DEAD_LINE_TIME, 'yyyy-mm-dd hh24:mi:ss') deadLineTime,
            TO_CHAR(T.KPI_DEAL_LINE_TIME, 'yyyy-mm-dd hh24:mi:ss') kpiDealLineTime,
            T.NOTE note
        FROM T_BO_TRANS_NODE_DEAD_LINE T
        WHERE T.IS_DEL = 0
        AND T.NODE_ID = #{nodeId}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="getDeadLineByTaskIdAndNodeIds" resultType="BoTransNodeDeadLineBean">
        SELECT
        T.BO_TRANS_NODE_DEAD_LINE_ID boTransNodeDeadLineId,
        T.NODE_ID nodeId,
        TO_CHAR(T.DEAD_LINE_TIME, 'yyyy-mm-dd hh24:mi:ss') deadLineTime,
        TO_CHAR(T.KPI_DEAL_LINE_TIME, 'yyyy-mm-dd hh24:mi:ss') kpiDealLineTime
        FROM T_BO_TRANS_NODE_DEAD_LINE T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.NODE_ID IN
        <foreach collection="nodeIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <delete id="delBoTransNodeDeadLine" parameterType="BoTransNodeDeadLineBean">
        delete T_BO_TRANS_NODE_DEAD_LINE
        where BO_TRANS_TASK_ID = #{boTransTaskId} and NODE_ID = #{nodeId}
    </delete>
</mapper>
