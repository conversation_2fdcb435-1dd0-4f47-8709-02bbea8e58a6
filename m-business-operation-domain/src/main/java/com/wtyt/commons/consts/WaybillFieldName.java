package com.wtyt.commons.consts;

import java.util.Set;

import com.google.common.collect.Sets;

/**
 * 
 * @ClassName: WaybillFieldName
 * @Description:运单字段名（修改记录用）
 * <AUTHOR>
 * @date 2023年2月10日
 *
 */
public class WaybillFieldName {
	public final static String StartAddress="始发城市";
	public final static String EndAddress="目的城市";
	public final static String ExLoadingAddress="始发详细地址";
	public final static String ExUnloadingAddress="目的详细地址";
	public final static String StartLatitude="起始地纬度";
	public final static String StartLongitude="起始地经度";
	public final static String EndLatitude="目的地纬度";
	public final static String EndLongitude="目的地经度";
	public final static String PrepaymentsBuyOil="线下油";
	public final static String PrepaymentsBuyGas="线下气";
	public final static String UnitPrice="运输单价";
	
	/**
	 * 
	 * @Title: getEndAddressInfoFields
	 * <AUTHOR>
	 * @Description:获取目的地信息相关字段
	 * @return
	 * @return Set<String> 返回类型
	 * @throws
	 */
	public static Set<String> getEndAddressInfoFields(){
	    	return Sets.newHashSet(WaybillFieldName.EndAddress,WaybillFieldName.EndLatitude,WaybillFieldName.EndLongitude,WaybillFieldName.ExUnloadingAddress);
	}
	
	
}
