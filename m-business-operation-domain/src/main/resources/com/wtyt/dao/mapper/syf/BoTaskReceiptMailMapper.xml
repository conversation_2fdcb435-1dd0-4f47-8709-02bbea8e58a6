<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskReceiptMailMapper">
    <resultMap type="com.wtyt.dao.bean.syf.BoTaskReceiptMailBean" id="BoTaskReceiptMailMap">
        <result property="boTaskReceiptMailId" column="BO_TASK_RECEIPT_MAIL_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="receiptReceiveTime" column="RECEIPT_RECEIVE_TIME" jdbcType="VARCHAR"/>
        <result property="transVoucherExpressNumber" column="TRANS_VOUCHER_EXPRESS_NUMBER" jdbcType="VARCHAR"/>
        <result property="optUserId" column="OPT_USER_ID" jdbcType="VARCHAR"/>
        <result property="optUserName" column="OPT_USER_NAME" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="save">
        INSERT INTO T_BO_TASK_RECEIPT_MAIL
        (BO_TASK_RECEIPT_MAIL_ID, BO_TRANS_TASK_ID, RECEIPT_RECEIVE_TIME, TRANS_VOUCHER_EXPRESS_NUMBER,OPT_USER_ID, OPT_USER_NAME)
        VALUES(#{boTaskReceiptMailId}, #{boTransTaskId}, TO_DATE(#{receiptReceiveTime}, 'YYYY-MM-DD HH24:MI:SS'), #{transVoucherExpressNumber},#{optUserId},#{optUserName})
    </insert>

    <select id="queryByTaskId" resultMap="BoTaskReceiptMailMap">
        SELECT
            BO_TASK_RECEIPT_MAIL_ID,
            BO_TRANS_TASK_ID,
            TO_CHAR(RECEIPT_RECEIVE_TIME, 'YYYY-MM-DD') RECEIPT_RECEIVE_TIME,
            TRANS_VOUCHER_EXPRESS_NUMBER,
            OPT_USER_ID,
            OPT_USER_NAME,
            TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') CREATED_TIME
        FROM
            T_BO_TASK_RECEIPT_MAIL
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID = #{taskId}
    </select>


</mapper>