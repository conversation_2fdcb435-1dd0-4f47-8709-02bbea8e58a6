<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoOrderHierarchyRelMapper">

    <sql id="Base_Column_List">
        BO_ORDER_HIERARCHY_REL_ID boOrderHierarchyRelId,BO_TRANS_ORDER_ID boTransOrderId,
        SUB_BO_TRANS_ORDER_ID subBoTransOrderId,
        BO_ORDER_TRANSIT_SNAP_ID boOrderTransitSnapId,SEGMENT_OP_TYPE segmentOpType
    </sql>


    <select id="queryByParentOrderId" resultType="com.wtyt.dao.bean.syf.BoOrderHierarchyRelBean">
        select
        <include refid="Base_Column_List" />
        from T_BO_ORDER_HIERARCHY_REL
        where BO_TRANS_ORDER_ID = #{boTransOrderId} AND IS_DEL=0
    </select>


    <update id="deleteByParentOrderIdList" >
        update T_BO_ORDER_HIERARCHY_REL
        set IS_DEL = 1,LAST_MODIFIED_TIME = SYSDATE
        where IS_DEL=0 AND BO_TRANS_ORDER_ID IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>




</mapper>
