package com.wtyt.inquire.bean;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @createTime 2023/04/05 20:58:00
 */
public class InquireNodeBean {
    private String listFlag;//节点结构是否有子数组
    private String nodeName;//节点名称
    private String nodeContent;//节点内容
    private String nodeTime;//节点时间
    private List<InquireNodeBean> childList;

    public String getListFlag() {
        return listFlag;
    }

    public void setListFlag(String listFlag) {
        this.listFlag = listFlag;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getNodeContent() {
        return nodeContent;
    }

    public void setNodeContent(String nodeContent) {
        this.nodeContent = nodeContent;
    }

    public String getNodeTime() {
        return nodeTime;
    }

    public void setNodeTime(String nodeTime) {
        this.nodeTime = nodeTime;
    }

    public List<InquireNodeBean> getChildList() {
        return childList;
    }

    public void setChildList(List<InquireNodeBean> childList) {
        this.childList = childList;
    }
}
