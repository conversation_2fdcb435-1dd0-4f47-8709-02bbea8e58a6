<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardWithdrawMapper">

    <select id="queryCanWithDrawGuaranteeByOrgIdList" resultType="com.wtyt.dao.bean.syf.BoTaskDriverGuaranteeBean">
        SELECT
            BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
            GUARANTEE_PAY_AMOUNT guaranteePayAmount
        FROM
        T_BO_TASK_DRIVER_GUARANTEE T
        LEFT JOIN T_BO_TRANS_TASK E ON T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        WHERE
        T.IS_DEL = 0
        AND T.GUARANTEE_STATE = 4
        AND T.GUARANTEE_WITHDRAW_STATE = 0
        AND T.CAPITAL_FLOW_CHANNEL IS NULL
        AND T.GUARANTEE_DEAL_TIME &lt; (SYSDATE - #{freezeDays})
        AND E.ORG_ID IN
        <foreach collection="orgIdList" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </select>
</mapper>
