package com.wtyt.dhconfig.service.impl;

import com.wtyt.common.toolkits.GsonToolkit;
import com.wtyt.common.toolkits.StringToolkit;
import com.wtyt.dao.bean.syf.*;
import com.wtyt.dao.mapper.syf.*;
import com.wtyt.dhconfig.bean.Req5330317IBean;
import com.wtyt.dhconfig.bean.Req5330317OBean;
import com.wtyt.dhconfig.service.DynamicHeadConsumerService;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class DynamicHeadConsumerServiceImpl implements DynamicHeadConsumerService {

    private final static Logger log = LoggerFactory.getLogger(DynamicHeadConsumerServiceImpl.class);


    @Autowired
    private BoDhConfigMapper boDhConfigMapper;
    @Autowired
    private BoDhFieldDictMapper boDhFieldDictMapper;
    @Autowired
    private BoDhHeaderImportMapper boDhHeaderImportMapper;

    @Autowired
    private BoDhHeaderColumnMapper boDhHeaderColumnMapper;
    @Autowired
    private BoDhHeaderShowMapper boDhHeaderShowMapper;

    @Autowired
    private BoDhHeaderDataCfgMapper boDhHeaderDataCfgMapper;
    @Override
    public Req5330317OBean queryDhConfig(Req5330317IBean data) {
        checkParam(data);

        BoDhConfigBean dbQueryBean = new BoDhConfigBean();
        dbQueryBean.setBusinessType(data.getBusinessType());
        dbQueryBean.setGroupType(data.getGroupType());
        dbQueryBean.setGroupCode(data.getGroupCode());
        //查询GroupCode 自己的
        BoDhConfigBean boDhConfigBean = boDhConfigMapper.queryDhConfigByGroupCode(dbQueryBean);
        //查询默认的
        if (boDhConfigBean == null) {
            //模版不存在的时候，如果不查询默认模版
            if ("0".equals(data.getDefaultConfig())) {
                throw new UnifiedBusinessException("动态表头该组织Code模版不存在！");
            }
            dbQueryBean.setGroupCode("-1");
            boDhConfigBean = boDhConfigMapper.queryDhConfigByGroupCode(dbQueryBean);
            if (boDhConfigBean == null) {
                throw new UnifiedBusinessException("动态表头默认模版不存在！");
            }
        }
        Req5330317OBean result = new Req5330317OBean();
        result.setHeaderRowNum(boDhConfigBean.getHeaderRowNum());
        result.setDataStartRowNum(boDhConfigBean.getDataStartRowNum());
        result.setConfigId(boDhConfigBean.getBoDhConfigId());
        if ("-1".equals(boDhConfigBean.getGroupCode())) {
            result.setIsDefault("1");
        } else {
            result.setIsDefault("0");
        }
        //查询组织Code 的 配置字典数据
        BoDhFiledDictBean dictBean = new BoDhFiledDictBean();
        dictBean.setBusinessType(data.getBusinessType());
        dictBean.setGroupType(data.getGroupType());
        dictBean.setGroupCode(data.getGroupCode());
        List<BoDhFiledDictBean> dhTaskFieldDictList = boDhFieldDictMapper.queryBoDhFieldDictList(dictBean);
        dhTaskFieldDictList.forEach(dictBean1 -> {
            dictBean1.setSearchConfig(GsonToolkit.jsonToMap(dictBean1.getSearchConfigStr()));
            dictBean1.setHeaderConfig(GsonToolkit.jsonToMap(dictBean1.getHeaderConfigStr()));
            dictBean1.setInputConfig(GsonToolkit.jsonToMap(dictBean1.getInputConfigStr()));
            dictBean1.setSearchConfigStr(null);
            dictBean1.setHeaderConfigStr(null);
            dictBean1.setInputConfigStr(null);
        });
        //查询组织code的映射大宽表数据
        BoDhHeaderDataCfgBean bean = new BoDhHeaderDataCfgBean();
        bean.setBusinessType(data.getBusinessType());
        bean.setGroupType(data.getGroupType());
        bean.setGroupCode(data.getGroupCode());
        List<BoDhHeaderDataCfgBean> headerDataCfgBeanList = boDhHeaderDataCfgMapper.queryHeaderDataCfgListByGroupCode(bean);


        this.queryDynamicHeaderFieldListAndPutResult(result, dhTaskFieldDictList,headerDataCfgBeanList, boDhConfigBean);

        return result;
    }


    private void queryDynamicHeaderFieldListAndPutResult(Req5330317OBean result,
                                                         List<BoDhFiledDictBean> dhTaskFieldDictList,
                                                         List<BoDhHeaderDataCfgBean> headerDataCfgBeanList,
                                                         BoDhConfigBean boDhConfigBean) {
        //读取T_BO_DH_HEADER_IMPORT数据
        List<BoDhHeaderImportBean> boTpDhHeaderImportList = boDhHeaderImportMapper.queryDhHeaderImportListByConfigId(boDhConfigBean.getBoDhConfigId());

        //读取T_BO_DH_HEADER_COLUMN数据
        List<BoDhHeaderColumnBean> boTpDhHeaderColumnList = boDhHeaderColumnMapper.queryDhHeaderColumnListByImportIds(
                boTpDhHeaderImportList.stream().map(BoDhHeaderImportBean::getBoDhHeaderImportId).collect(
                        Collectors.toList()));

        //循环处理导入表头
        List<Req5330317OBean.ImportHeader> importHeaderList = new ArrayList<>();
        //表头ID和表头名称映射
        Map<String, BoDhHeaderImportBean> boDhHeaderImportBeanMap = boTpDhHeaderImportList.stream().collect(Collectors.toMap(BoDhHeaderImportBean::getBoDhHeaderImportId, Function.identity(), (key1, key2) -> key2));
        //表头字段映射了字典数据，一对一或者一对多
        Map<String, List<BoDhHeaderColumnBean>> importColumnsMap = boTpDhHeaderColumnList.stream().collect(Collectors.groupingBy(BoDhHeaderColumnBean::getBoDhHeaderImportId));
        //映射的字典表
        Map<String, BoDhFiledDictBean> boDhFiledDictBeanMap = dhTaskFieldDictList.stream().collect(Collectors.toMap(BoDhFiledDictBean::getBoDhFieldDictId, Function.identity(), (key1, key2) -> key2));
        //表头映射大宽表数据
        Map<String, BoDhHeaderDataCfgBean> boDhHeaderDataCfgBeanMap = headerDataCfgBeanList.stream().collect(Collectors.toMap(BoDhHeaderDataCfgBean::getHeaderName, Function.identity(), (key1, key2) -> key2));
        boTpDhHeaderImportList.forEach(item -> {
            //组装importHeader
            Req5330317OBean.ImportHeader importHeader = new Req5330317OBean.ImportHeader();
            importHeader.setHeaderName(item.getHeaderName());
            importHeader.setIsRequired(item.getIsRequired());
            importHeader.setIsCreateRequired(item.getIsCreateRequired());
            importHeader.setIsUnique(item.getIsUnique());
            importHeader.setIsShow(item.getIsShow());
            importHeader.setAppIsShow(item.getAppIsShow());
            importHeader.setSearchType(item.getSearchType());
            importHeader.setIsPrecise(item.getIsPrecise());
            importHeader.setIsExport(item.getIsExport());
            importHeader.setIsInput(item.getIsInput());
            importHeader.setIsImport(item.getIsImport());
            importHeader.setCanModify(item.getCanModify());
            importHeader.setDefaultValue(item.getDefaultValue());
            importHeader.setSortNum(item.getSortNum());
            importHeader.setAppSortNum(item.getAppSortNum());
            importHeader.setExportSortNum(item.getExportSortNum());
            //关联映射字段
            List<Req5330317OBean.ColumnInfo> columnList = new ArrayList<>();
            // 表头映射字典字段
            List<BoDhHeaderColumnBean> importColumns = importColumnsMap.get(item.getBoDhHeaderImportId());
            if (CollectionUtils.isNotEmpty(importColumns)) {
                importColumns.forEach(s -> {
                    Req5330317OBean.ColumnInfo columnInfo = new Req5330317OBean.ColumnInfo();
                    columnInfo.setBoDhFieldDictId(s.getBoDhFieldDictId());
                    columnInfo.setDefaultValue(s.getDefaultValue());
                    BoDhFiledDictBean boDhFiledDictBean = boDhFiledDictBeanMap.get(s.getBoDhFieldDictId());
                    if (boDhFiledDictBean != null) {
                        columnInfo.setTableName(boDhFiledDictBean.getTableName());
                        columnInfo.setFieldName(boDhFiledDictBean.getFieldName());
                        columnInfo.setFieldNameDesc(boDhFiledDictBean.getFieldNameDesc());
                        columnInfo.setFieldType(boDhFiledDictBean.getFieldType());
                        columnInfo.setMaxLength(boDhFiledDictBean.getMaxLength());
                        columnInfo.setNumberPrecision(boDhFiledDictBean.getNumberPrecision());
                        columnInfo.setInputConfig(boDhFiledDictBean.getInputConfig());
                        columnInfo.setHeaderConfig(boDhFiledDictBean.getHeaderConfig());
                        columnInfo.setSearchConfig(boDhFiledDictBean.getSearchConfig());
                    }
                    columnList.add(columnInfo);
                });
            } else {
                // 表头没有映射字典，需要根据表头名称查询
                BoDhHeaderDataCfgBean dataCfgBean = boDhHeaderDataCfgBeanMap.get(item.getHeaderName());
                if (dataCfgBean != null) {
                    Req5330317OBean.ColumnInfo columnInfo = new Req5330317OBean.ColumnInfo();
                    columnInfo.setFieldName(dataCfgBean.getDataColumnName());
                    columnInfo.setFieldType("0");//大宽表数据是字符串类型
                    columnInfo.setHeaderEnName(dataCfgBean.getHeaderEnName());
                    columnList.add(columnInfo);
                }
            }
            importHeader.setColumnList(columnList);
            importHeaderList.add(importHeader);
        });
        result.setHeaderList(importHeaderList);


        //处理生成合并展示数据
        List<BoDhHeaderShowBean> boTpDhHeaderShowList = boDhHeaderShowMapper.getHeaderShowByConfigId(boDhConfigBean.getBoDhConfigId());
        List<Req5330317OBean.HeaderShowInfo> headerShowList = new ArrayList<>();
        boTpDhHeaderShowList.forEach(boDhHeaderShowBean -> {
            Req5330317OBean.HeaderShowInfo headerShowInfo = new Req5330317OBean.HeaderShowInfo();
            headerShowInfo.setShowHeaderName(boDhHeaderShowBean.getShowHeaderName());
            headerShowInfo.setSplitChar(boDhHeaderShowBean.getSplitChar());
            //合并展示的原始表头ID
            List<String> headerImportIdList = boDhHeaderShowBean.getBindings().stream()
                    .map(BoDhHeaderShowBindBean::getBoDhHeaderImportId).collect(Collectors.toList());
            List<String> headerNameList = new ArrayList<>();
            headerImportIdList.forEach(s -> {
                BoDhHeaderImportBean dhHeaderImportBean = boDhHeaderImportBeanMap.get(s);
                if (dhHeaderImportBean != null) {
                    headerNameList.add(dhHeaderImportBean.getHeaderName());
                }
            });
            headerShowInfo.setHeaderNameList(headerNameList);
            headerShowList.add(headerShowInfo);
        });

        result.setHeaderShowList(headerShowList);
    }


    private  void checkParam(Req5330317IBean data) {
        StringToolkit.isAnyBlank("业务类型不能为空", data.getBusinessType());
        StringToolkit.inScope("组织类型传参错误", data.getGroupType(),"0","1","2");
        StringToolkit.isAnyBlank("组织code不能为空", data.getGroupCode());
        StringToolkit.inScope("是否查询默认模版传参错误", data.getDefaultConfig(),"0","1");
        StringToolkit.isAnyBlank("业务场景类型不能为空", data.getSceneType());
    }
}
