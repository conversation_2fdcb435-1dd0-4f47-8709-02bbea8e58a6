<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoInquireMapper">

    <update id="updateByPk">
        UPDATE T_BO_INQUIRE SET
        <if test="inquireState != null and inquireState != '' ">
            INQUIRE_STATE = #{inquireState},
        </if>
        <if test="inquireRealState != null and inquireRealState != '' ">
            INQUIRE_REAL_STATE = #{inquireRealState},
        </if>
        <if test="inquireOverTimeState != null and inquireOverTimeState !='' ">
            INQUIRE_OVER_TIME_STATE = #{inquireOverTimeState},
        </if>
        <if test="boInquireOfferId != null and boInquireOfferId !='' ">
            BO_INQUIRE_OFFER_ID = #{boInquireOfferId},
        </if>
        <if test="askUserId != null and askUserId !='' ">
            ASK_USER_ID = #{askUserId},
        </if>
        <if test="askTime != null and askTime !='' ">
            ASK_TIME = TO_DATE(#{askTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_INQUIRE_ID = #{boInquireId}
    </update>


    <select id="selectInquire" resultType="com.wtyt.dao.bean.syf.BoInquireBean">
        SELECT
           I.BO_INQUIRE_ID  boInquireId,
           I.BO_TRANS_ORDER_ID  boTransOrderId,
           I.BO_INQUIRE_OFFER_ID boInquireOfferId,
           I.INQUIRE_STATE  inquireState,
           I.INQUIRE_REAL_STATE  inquireRealState,
           I.INQUIRE_OVER_TIME_STATE  inquireOverTimeState,
           I.ASK_USER_ID  askUserId,
           TO_CHAR(I.ASK_TIME, 'YYYY-MM-DD HH24:MI:SS') askTime,
           O.ORG_ID orgId
        FROM
            T_BO_TRANS_ORDER O
        INNER JOIN T_BO_INQUIRE I ON
            O.BO_TRANS_ORDER_ID = I.BO_TRANS_ORDER_ID
        WHERE
            O.IS_DEL = 0
            AND I.IS_DEL = 0
            AND O.BO_TRANS_ORDER_ID = #{orderId}
    </select>

    <select id="selectInquireList" resultType="com.wtyt.dao.bean.syf.BoInquireBean">
        SELECT
           I.BO_INQUIRE_ID  boInquireId,
           I.BO_TRANS_ORDER_ID  boTransOrderId,
           I.BO_INQUIRE_OFFER_ID boInquireOfferId,
           I.INQUIRE_STATE  inquireState,
           I.INQUIRE_REAL_STATE  inquireRealState,
           I.INQUIRE_OVER_TIME_STATE  inquireOverTimeState,
           I.ASK_USER_ID  askUserId,
           GR.GROUP_ID groupId
        FROM
            T_BO_TRANS_ORDER O
        INNER JOIN T_BO_INQUIRE I ON
            O.BO_TRANS_ORDER_ID = I.BO_TRANS_ORDER_ID
            LEFT JOIN T_BO_ORDER_GROUP_REL GR ON GR.BO_TRANS_ORDER_ID = O.BO_TRANS_ORDER_ID
        WHERE
            O.IS_DEL = 0
            AND I.IS_DEL = 0
            AND O.BO_TRANS_ORDER_ID IN
            <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
    </select>

    <insert id="insertInquire">
        INSERT INTO
        T_BO_INQUIRE (BO_INQUIRE_ID,
        BO_TRANS_ORDER_ID,
        BO_INQUIRE_OFFER_ID,
        INQUIRE_STATE,
        INQUIRE_REAL_STATE,
        INQUIRE_OVER_TIME_STATE,
        ASK_USER_ID,
        FIRST_OFFER_TIME,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE)
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="inquireList" separator="UNION ALL">
            SELECT #{item.boInquireId} boInquireId,
            #{item.boTransOrderId} boTransOrderId,
            #{item.boInquireOfferId} boInquireOfferId,
            #{item.inquireState} inquireState,
            #{item.inquireRealState} inquireRealState,
            #{item.inquireOverTimeState} inquireOverTimeState,
            #{item.askUserId} askUserId,
            null firstOfferTime,
            0 isDel,
            SYSDATE createdTime,
            SYSDATE lastModifiedTime,
            NULL note
            FROM DUAL
        </foreach>
        ) A
    </insert>

    <select id="getInquireState" resultType="string">
        SELECT
            tbi.INQUIRE_STATE
        FROM
            T_BO_TRANS_ORDER tbto
        LEFT JOIN T_BO_INQUIRE tbi ON
            tbto.BO_TRANS_ORDER_ID = tbi.BO_TRANS_ORDER_ID
            WHERE tbto.IS_DEL = 0
            AND tbto.ORG_ID = #{orgId}
	        AND tbto.CUSTOMER_ORDER_NO = #{customerOrderNo}
	        and rownum = 1
    </select>

    <select id="countApplyCount" resultType="string">
        SELECT
        COUNT(1)
        FROM
        T_BO_INQUIRE I
        INNER JOIN T_BO_TRANS_ORDER O ON O.BO_TRANS_ORDER_ID = I.BO_TRANS_ORDER_ID
        WHERE
        I.IS_DEL = 0
        AND O.IS_DEL = 0
        AND O.ORG_ID = #{orgId}
        <if test="type == 1">
            AND I.INQUIRE_STATE = 2
            AND EXISTS (SELECT 1 FROM T_BO_INQUIRE_OFFER WHERE IS_DEL = 0 AND BO_TRANS_ORDER_ID = O.BO_TRANS_ORDER_ID)
            AND I.FIRST_OFFER_TIME IS NOT NULL
        </if>
        <if test="type == 2">
            AND I.INQUIRE_STATE = 3
            AND I.BO_INQUIRE_OFFER_ID IS NOT NULL
        </if>
    </select>

    <select id="getInquireInfo" resultType="BoTransOrderBean">
        SELECT
            DECODE(b.INQUIRE_STATE,0,0,1,1,2) inquireState,
            b.INQUIRE_OVER_TIME_STATE inquireOverTimeState,
            c.GROUP_ID groupId
        FROM
            T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_INQUIRE b ON a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID
        LEFT JOIN T_BO_ORDER_GROUP_REL c ON
            a.BO_TRANS_ORDER_ID = c.BO_TRANS_ORDER_ID
            WHERE a.BO_TRANS_ORDER_ID = #{boTransOrderId}
            AND rownum = 1
    </select>

    <resultMap id="BoAskOfferInfoMap" type="BoAskOfferInfo">
        <result property="askUserId" column="askUserId"/>
        <result property="askTime" column="askTime"/>
        <result property="applyTime" column="applyTime"/>
        <result property="applyUserId" column="applyUserId"/>
        <collection property="offerList" ofType="com.wtyt.dao.bean.BoAskOfferInfo$Offer">
            <result property="offerUserId" column="offerUserId"/>
            <result property="offerTime" column="offerTime"/>
        </collection>
    </resultMap>
    <select id="getAskOfferInfo" resultMap="BoAskOfferInfoMap">
        SELECT
            b.CREATED_USER_ID askUserId,
            TO_CHAR(b.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') askTime,
            c.APPLY_USER_ID applyUserId,
            TO_CHAR(c.AGREE_TIME, 'YYYY-MM-DD HH24:MI:SS') applyTime,
            d.CREATED_USER_ID offerUserId,
            TO_CHAR(d.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') offerTime
        FROM
            T_BO_TRANS_ORDER a
        INNER JOIN T_BO_INQUIRE_ASK b ON
            a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID  AND b.IS_DEL = 0
        LEFT JOIN T_BO_INQUIRE_OFFER c ON
            a.BO_TRANS_ORDER_ID = c.BO_TRANS_ORDER_ID AND c.APPLY_STATE = 2 AND c.IS_DEL = 0
        LEFT JOIN T_BO_INQUIRE_OFFER_REC d ON
            a.BO_TRANS_ORDER_ID = d.BO_TRANS_ORDER_ID AND d.IS_DEL = 0
            WHERE a.BO_TRANS_ORDER_ID = #{boTransOrderId}
            ORDER BY d.CREATED_TIME DESC
    </select>

</mapper>