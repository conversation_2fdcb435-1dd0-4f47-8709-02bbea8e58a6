<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoUserGuideRecordMapper">

    <select id="countUserGuideRecord" resultType="java.lang.Integer">
        SELECT NVL(count(*), 0)
        FROM T_BO_USER_GUIDE_RECORD
        WHERE USER_ID = #{userId} AND BO_USER_GUIDE_ITEM_ID = #{boUserGuideItemId} AND IS_DEL = 0
    </select>

    <insert id="insertUserGuideRecord">
        INSERT INTO T_BO_USER_GUIDE_RECORD(BO_USER_GUIDE_RECORD_ID, USER_ID, BO_USER_GUIDE_ITEM_ID)
        VALUES (#{boUserGuideRecordId}, #{userId}, #{boUserGuideItemId})
    </insert>
</mapper>