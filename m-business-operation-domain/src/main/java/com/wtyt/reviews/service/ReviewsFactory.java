package com.wtyt.reviews.service;

import com.wtyt.common.toolkits.SpringContextToolkit;
import com.wtyt.reviews.service.impl.TransTaskExtendServiceImpl;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年03月15日 18时41分
 */
public class ReviewsFactory {

    private static final Map<String, Class<?>> SUBJECT_EXTEND_MAP = new HashMap<>(16);

    static {
        SUBJECT_EXTEND_MAP.put("0", TransTaskExtendServiceImpl.class);
    }

    public static ReviewsSubjectExtendService getSubjectExtendService(String subjectType) {
        Class<?> clazz = SUBJECT_EXTEND_MAP.get(subjectType);
        if (clazz == null) {
            throw new NullPointerException("主体类型[" + subjectType + "]未实现ReviewsSubjectExtendService服务");
        }
        return (ReviewsSubjectExtendService) SpringContextToolkit.getBean(clazz);
    }
}
