<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.OperationFeeQueryMapper">

    <resultMap id="taskOperationFee" type="com.wtyt.board.bean.TaskOperationFeeBean">
        <id property="boTransTaskId" column="BO_TRANS_TASK_ID"/>
        <result property="userFreight" column="USER_FREIGHT"/>
        <collection property="paymentFees" ofType="com.wtyt.board.bean.TaskOperationFeeBean$PaymentFee">
            <result property="configKey" column="CONFIG_KEY"/>
            <result property="configValue" column="CONFIG_VALUE"/>
            <result property="configName" column="CONFIG_NAME"/>
        </collection>
    </resultMap>

    <select id="queryOperationFeeList" resultMap="taskOperationFee">
        SELECT uf.BO_TRANS_TASK_ID, uf.USER_FREIGHT, cf.CONFIG_KEY, cf.CONFIG_VALUE, cf.CONFIG_NAME
        FROM (SELECT 1 BO_TRANS_TASK_ID, SUM(NVL(USER_FREIGHT,0)) USER_FREIGHT
        FROM T_BO_TRANS_TASK tt
        WHERE tt.IS_DEL = 0
        AND tt.CREATED_TIME BETWEEN #{beginTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler} AND
        #{endTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler}
        AND tt.ORG_ID IN
        <foreach item="orgId" collection="orgIds" separator="," open="(" close=")">
            #{orgId}
        </foreach>) uf
        LEFT JOIN
        (SELECT 1 BO_TRANS_TASK_ID, CONFIG_KEY, CONFIG_NAME, CONFIG_VALUE
        FROM (
        SELECT f.CONFIG_KEY CONFIG_KEY,
        f.CONFIG_NAME CONFIG_NAME, SUM(NVL(f.CONFIG_VALUE,0)) CONFIG_VALUE
        FROM T_BO_TRANS_TASK t
        INNER JOIN T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND f.IS_DEL = 0 AND f.EXPENSIVE_TYPE = 0
        WHERE t.IS_DEL = 0
        AND t.CREATED_TIME BETWEEN #{beginTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler} AND
        #{endTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler}
        AND t.ORG_ID IN
        <foreach item="orgId" collection="orgIds" separator="," open="(" close=")">
            #{orgId}
        </foreach>
        GROUP BY f.CONFIG_KEY, f.CONFIG_NAME)) cf ON uf.BO_TRANS_TASK_ID = cf.BO_TRANS_TASK_ID
    </select>

    <select id="queryOrgOperationFeeSummary" resultType="com.wtyt.board.bean.GroupOperationFeeBean">
        SELECT tmp.ORGID groupName,
        SUM(tmp.CONFIGVALUE) costAmount
        FROM (
        <if test="configKey == null or expensiveType == '2'.toString()">
            SELECT tt.ORG_ID orgId,
            tt.USER_FREIGHT CONFIGVALUE
            FROM T_BO_TRANS_TASK tt
            WHERE tt.IS_DEL = 0
            AND tt.CREATED_TIME BETWEEN #{beginTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler} AND
            #{endTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler}
            AND tt.ORG_ID IN
            <foreach item="orgId" collection="orgIds" separator="," open="(" close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="configKey == null and expensiveType != '2'.toString()">
            UNION ALL
        </if>
        <if test="expensiveType == null">
            SELECT t.ORG_ID orgId,
            f.CONFIG_VALUE CONFIGVALUE
            FROM T_BO_TRANS_TASK_FEE f
            INNER JOIN T_BO_TRANS_TASK t ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
            WHERE f.IS_DEL = 0
            AND f.EXPENSIVE_TYPE = 0
            <if test="configKey != null">
                AND f.CONFIG_KEY = #{configKey}
            </if>
            AND t.CREATED_TIME BETWEEN #{beginTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler} AND
            #{endTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler}
            AND t.ORG_ID IN
            <foreach item="orgId" collection="orgIds" separator="," open="(" close=")">
                #{orgId}
            </foreach>
        </if>
        ) tmp
        GROUP BY tmp.ORGID
    </select>

    <select id="queryWbItemOperationFeeSummary" resultType="com.wtyt.board.bean.GroupOperationFeeBean">
        SELECT tmp.WBITEM groupName, SUM(tmp.CONFIGVALUE) costAmount
        FROM (
        <if test="configKey == null or expensiveType == '2'.toString()">
            SELECT tt.WB_ITEM wbItem,
            tt.USER_FREIGHT CONFIGVALUE
            FROM T_BO_TRANS_TASK tt
            WHERE tt.IS_DEL = 0
            AND tt.WB_ITEM IS NOT NULL
            AND tt.CREATED_TIME BETWEEN #{beginTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler} AND
            #{endTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler}
            AND tt.ORG_ID IN
            <foreach item="orgId" collection="orgIds" separator="," open="(" close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="configKey == null and expensiveType != '2'.toString()">
            UNION ALL
        </if>
        <if test="expensiveType == null">
            SELECT t.WB_ITEM wbItem,
            f.CONFIG_VALUE CONFIGVALUE
            FROM T_BO_TRANS_TASK_FEE f
            INNER JOIN T_BO_TRANS_TASK t ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
            WHERE f.IS_DEL = 0
            AND f.EXPENSIVE_TYPE = 0
            AND t.WB_ITEM IS NOT NULL
            <if test="configKey != null">
                AND f.CONFIG_KEY = #{configKey}
            </if>
            AND t.CREATED_TIME BETWEEN #{beginTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler} AND
            #{endTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler}
            AND t.ORG_ID IN
            <foreach item="orgId" collection="orgIds" separator="," open="(" close=")">
                #{orgId}
            </foreach>
        </if>
        ) tmp
        GROUP BY tmp.WBITEM
    </select>

    <select id="queryWbItemLineOperationFeeSummary" resultType="com.wtyt.board.bean.GroupOperationFeeBean">
        SELECT NVL(tmp.START_CITY_NAME, '') || NVL(tmp.START_COUNTY_NAME, '') || '-' || NVL(tmp.END_CITY_NAME, '') ||
        NVL(tmp.END_COUNTY_NAME, '') AS groupName,
        tmp.END_CITY_NAME, tmp.END_COUNTY_NAME, SUM(tmp.CONFIGVALUE) costAmount
        FROM (
        SELECT tt.START_CITY_NAME,
        tt.START_COUNTY_NAME,
        tt.END_CITY_NAME,
        tt.END_COUNTY_NAME,
        tt.USER_FREIGHT CONFIGVALUE
        FROM T_BO_TRANS_TASK tt
        WHERE tt.IS_DEL = 0
        AND tt.WB_ITEM = #{wbItem}
        AND ((tt.START_CITY_NAME IS NOT NULL OR START_COUNTY_NAME IS NOT NULL) AND (tt.END_CITY_NAME IS NOT NULL OR
        tt.END_COUNTY_NAME IS NOT NULL))
        AND tt.CREATED_TIME BETWEEN #{beginTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler} AND
        #{endTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler}
        AND tt.ORG_ID IN
        <foreach item="orgId" collection="orgIds" separator="," open="(" close=")">
            #{orgId}
        </foreach>
        UNION ALL
        SELECT t.START_CITY_NAME,
        t.START_COUNTY_NAME,
        t.END_CITY_NAME,
        t.END_COUNTY_NAME,
        f.CONFIG_VALUE
        FROM T_BO_TRANS_TASK_FEE f
        INNER JOIN T_BO_TRANS_TASK t ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
        WHERE f.IS_DEL = 0
        AND f.EXPENSIVE_TYPE = 0
        AND t.WB_ITEM = #{wbItem}
        AND ((t.START_CITY_NAME IS NOT NULL OR START_COUNTY_NAME IS NOT NULL) AND (t.END_CITY_NAME IS NOT NULL OR
        END_COUNTY_NAME IS NOT NULL))
        AND t.CREATED_TIME BETWEEN #{beginTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler} AND
        #{endTime, typeHandler=org.apache.ibatis.type.LocalDateTimeTypeHandler}
        AND t.ORG_ID IN
        <foreach item="orgId" collection="orgIds" separator="," open="(" close=")">
            #{orgId}
        </foreach>
        ) tmp
        GROUP BY tmp.START_CITY_NAME, tmp.START_COUNTY_NAME, tmp.END_CITY_NAME, tmp.END_COUNTY_NAME
    </select>
</mapper>