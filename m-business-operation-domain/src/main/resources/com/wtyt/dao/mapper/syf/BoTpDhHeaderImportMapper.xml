<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhHeaderImportMapper">

    <sql id="columnField">
        BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
        BO_TP_DH_CONFIG_ID boTpDhConfigId,
        HEADER_NAME headerName,
        SAMPLE_DATA sampleData,
        IS_REQUIRED isRequired,
        SHOW_TYPE showType,
        NVL(SHIPPING_SHOW_TYPE,'2') shippingShowType,
        SEARCH_TYPE searchType,
        IS_PRECISE isPrecise,
        IS_EXPORT isExport,
        IS_INPUT isInput,
        DEFAULT_VALUE defaultValue,
        SORT_NUM sortNum,
        FROM_SOURCE fromSource,
        MERGE_SHOW_ID mergeShowId,
        HEADER_TYPE headerType,
        OPTION_INFO optionInfo,
        IS_UNIQUE isUnique,
        COLUMN_LENGTH columnLength,
        MERGE_PREFIX mergePrefix,
        MERGE_SUFFIX mergeSuffix
    </sql>

    <!-- 含新颜动态表头开关功能开关新增字段 -->
    <sql id="columnList">
        BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
        BO_TP_DH_CONFIG_ID boTpDhConfigId,
        HEADER_NAME headerName,
        SAMPLE_DATA sampleData,
        IS_REQUIRED isRequired,
        SHOW_TYPE showType,
        NVL(SHIPPING_SHOW_TYPE,'2') shippingShowType,
        SEARCH_TYPE searchType,
        IS_PRECISE isPrecise,
        IS_EXPORT isExport,
        IS_INPUT isInput,
        DEFAULT_VALUE defaultValue,
        SORT_NUM sortNum,
        FROM_SOURCE fromSource,
        MERGE_SHOW_ID mergeShowId,
        HEADER_TYPE headerType,
        OPTION_INFO optionInfo,
        IS_UNIQUE isUnique,
        CREATE_SORT_NUM createSortNum,
        IS_CREATE_REQUIRE createRequire,
        INPUT_TIPS inputTips,
        CAN_MODIFY canModify,
        COLUMN_LENGTH columnLength,
        MERGE_PREFIX mergePrefix,
        MERGE_SUFFIX mergeSuffix
    </sql>


    <insert id="insertBatch">

        INSERT INTO T_BO_TP_DH_HEADER_IMPORT(BO_TP_DH_HEADER_IMPORT_ID, BO_TP_DH_CONFIG_ID, HEADER_NAME, SAMPLE_DATA, IS_REQUIRED, SHOW_TYPE,SHIPPING_SHOW_TYPE, SEARCH_TYPE, IS_PRECISE,
        IS_EXPORT, IS_INPUT, DEFAULT_VALUE, SORT_NUM, FROM_SOURCE, MERGE_SHOW_ID, HEADER_TYPE, OPTION_INFO, IS_UNIQUE)

        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTpDhHeaderImportId} BO_TP_DH_HEADER_IMPORT_ID,
            #{item.boTpDhConfigId} BO_TP_DH_CONFIG_ID,
            #{item.headerName} HEADER_NAME,
            #{item.sampleData} SAMPLE_DATA,
            #{item.isRequired} IS_REQUIRED,
            #{item.showType} SHOW_TYPE,
            #{item.shippingShowType} SHIPPING_SHOW_TYPE,
            #{item.searchType} SEARCH_TYPE,
            #{item.isPrecise} IS_PRECISE,
            #{item.isExport} IS_EXPORT,
            #{item.isInput} IS_INPUT,
            #{item.defaultValue} DEFAULT_VALUE,
            #{item.sortNum} SORT_NUM,
            #{item.fromSource} FROM_SOURCE,
            #{item.mergeShowId} MERGE_SHOW_ID,
            #{item.headerType} HEADER_TYPE,
            #{item.optionInfo} OPTION_INFO,
            #{item.isUnique} IS_UNIQUE
            FROM
            DUAL
        </foreach>
    </insert>

    <insert id="batchInsert">

        INSERT INTO T_BO_TP_DH_HEADER_IMPORT(BO_TP_DH_HEADER_IMPORT_ID, BO_TP_DH_CONFIG_ID, HEADER_NAME, SAMPLE_DATA,
        IS_REQUIRED, SHOW_TYPE,SHIPPING_SHOW_TYPE, SEARCH_TYPE, IS_PRECISE,
        IS_EXPORT, IS_INPUT, DEFAULT_VALUE, SORT_NUM, FROM_SOURCE, MERGE_SHOW_ID, HEADER_TYPE, OPTION_INFO, IS_UNIQUE,
        CREATE_SORT_NUM, IS_CREATE_REQUIRE, INPUT_TIPS, CAN_MODIFY, COLUMN_LENGTH, MERGE_PREFIX, MERGE_SUFFIX)

        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTpDhHeaderImportId} BO_TP_DH_HEADER_IMPORT_ID,
            #{item.boTpDhConfigId} BO_TP_DH_CONFIG_ID,
            #{item.headerName} HEADER_NAME,
            #{item.sampleData,jdbcType=VARCHAR} SAMPLE_DATA,
            #{item.isRequired} IS_REQUIRED,
            #{item.showType} SHOW_TYPE,
            #{item.shippingShowType} SHIPPING_SHOW_TYPE,
            #{item.searchType} SEARCH_TYPE,
            #{item.isPrecise} IS_PRECISE,
            #{item.isExport} IS_EXPORT,
            #{item.isInput} IS_INPUT,
            #{item.defaultValue} DEFAULT_VALUE,
            #{item.sortNum} SORT_NUM,
            #{item.fromSource} FROM_SOURCE,
            #{item.mergeShowId} MERGE_SHOW_ID,
            #{item.headerType} HEADER_TYPE,
            #{item.optionInfo,jdbcType=VARCHAR} OPTION_INFO,
            #{item.isUnique} IS_UNIQUE,
            #{item.createSortNum,jdbcType=NUMERIC} CREATE_SORT_NUM,
            #{item.createRequire} IS_CREATE_REQUIRE,
            #{item.inputTips,jdbcType=VARCHAR} INPUT_TIPS,
            #{item.canModify} CAN_MODIFY,
            #{item.columnLength} COLUMN_LENGTH,
            #{item.mergePrefix,jdbcType=VARCHAR} MERGE_PREFIX,
            #{item.mergeSuffix,jdbcType=VARCHAR} MERGE_SUFFIX
            FROM
            DUAL
        </foreach>
    </insert>

    <update id="updateHeaderName">
        UPDATE T_BO_TP_DH_HEADER_IMPORT
        SET HEADER_NAME = #{headerName}, LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TP_DH_HEADER_IMPORT_ID = #{boTpDhHeaderImportId}
    </update>

    <update id="updateOrgHeaderNameByGroup">
        UPDATE T_BO_TP_DH_HEADER_IMPORT
        SET HEADER_NAME = #{headerName}, LAST_MODIFIED_TIME = SYSDATE
        WHERE HEADER_NAME = #{headerOldName}
        and BO_TP_DH_CONFIG_ID IN
            <foreach collection="list" separator="," item="item" close=")" open="(">
             #{item}
            </foreach>
    </update>



    <select id="queryOriginalDhHeaderListByConfigId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM
            T_BO_TP_DH_HEADER_IMPORT
        WHERE
            is_del = 0
            AND BO_TP_DH_CONFIG_ID = #{boTpDhConfigId} AND HEADER_TYPE = 1
            ORDER BY SORT_NUM ASC
    </select>

    <select id="queryAllDhHeaderImportListByConfigId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM
        T_BO_TP_DH_HEADER_IMPORT
        WHERE
        is_del = 0
        AND BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        ORDER BY SORT_NUM ASC
    </select>

    <select id="queryDhHeaderImportListByConfigId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderImportBean">
        SELECT
        <include refid="columnList"/>
        FROM
        T_BO_TP_DH_HEADER_IMPORT
        WHERE
        is_del = 0
        AND BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        ORDER BY SORT_NUM ASC
    </select>

    <select id="querySearchItemListByConfigId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM T_BO_TP_DH_HEADER_IMPORT
        WHERE IS_DEL = 0
        AND BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        AND SEARCH_TYPE > 0
        ORDER BY SORT_NUM ASC
    </select>
    <select id="queryHeaderImportByConfigAndName" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM T_BO_TP_DH_HEADER_IMPORT WHERE
        IS_DEL = 0
        AND BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        AND HEADER_NAME = #{headerName}
        AND rownum = 1
    </select>

    <select id="queryHeaderImportById" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM T_BO_TP_DH_HEADER_IMPORT WHERE
        IS_DEL = 0
        AND BO_TP_DH_HEADER_IMPORT_ID = #{boTpDhHeaderImportId}
    </select>

    <update id="updateCreateAttributes">
            UPDATE T_BO_TP_DH_HEADER_IMPORT SET CREATE_SORT_NUM = #{createSortNum}, IS_CREATE_REQUIRE =
            #{createRequire}, INPUT_TIPS = #{inputTips}, CAN_MODIFY = #{canModify} WHERE
            BO_TP_DH_HEADER_IMPORT_ID = #{boTpDhHeaderImportId}
    </update>
    <select id="queryHeaderImportByMergeShowId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM T_BO_TP_DH_HEADER_IMPORT WHERE
        IS_DEL = 0
        AND MERGE_SHOW_ID = #{boTpDhHeaderImportId}
    </select>

    <select id="queryAllDhHeaderImportListByImportId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM
        T_BO_TP_DH_HEADER_IMPORT
        WHERE
        is_del = 0
        AND BO_TP_DH_HEADER_IMPORT_ID in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        ORDER BY SORT_NUM ASC
    </select>



</mapper>
