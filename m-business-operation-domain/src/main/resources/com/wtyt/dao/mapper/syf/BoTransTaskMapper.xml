<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskMapper">

    <insert id="save" parameterType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        INSERT INTO T_BO_TRANS_TASK(
            BO_TRANS_TASK_ID,
            TAX_WAYBILL_ID,
            TRANS_MODE,
            OFFER_TYPE,
            CREATED_USER_ID,
            <if test="createdTime!=null and createdTime!=''">
                CREATED_TIME,
            </if>
            CREATED_USER_SYS_ROLE_TYPE,
            XCY_USER_ID,
            NODE_ID,
            NODE_TIME,
            TRANS_PATTERN,CREATED_USER_JOB_NAME,
            MOBILE_NO,
            ORG_ID,
            START_CITY_CODE,
            START_CITY_NAME,
            END_CITY_CODE,
            END_CITY_NAME,
            ALL_FREIGHT,
            <if test="serviceFee != null and serviceFee != ''">
                SERVICE_FEE,
            </if>
            <if test="dataServiceFee != null and dataServiceFee != ''">
                DATA_SERVICE_FEE,
            </if>
            DRIVER_NAME,
            CART_BADGE_NO,
            CART_BADGE_COLOR,
            GOODS_NAME,
            GOODS_AMOUNT,
            START_PROVINCE_NAME,
            START_PROVINCE_CODE,
            END_PROVINCE_NAME,
            END_PROVINCE_CODE,
            TAX_WAYBILL_NO,
            <if test="goodsAmountType!=null and goodsAmountType!=''">
                GOODS_AMOUNT_TYPE,
            </if>
            <if test="prepayments!=null and prepayments!=''">
                PREPAYMENTS,
            </if>
            ORDER_CREATE_TYPE,
            START_COUNTY_NAME,
            END_COUNTY_NAME,
            START_COUNTY_CODE,
            END_COUNTY_CODE,
            <if test="backFee!=null and backFee!=''">
                BACK_FEE,
            </if>
            <if test="mileage!=null and mileage!=''">
                MILEAGE,
            </if>
            <if test="userFreight!=null and userFreight!=''">
                USER_FREIGHT,
            </if>
            <if test="freightIncr!=null and freightIncr!=''">
                FREIGHT_INCR,
            </if>
            <if test="goodsCost!=null and goodsCost!=''">
                GOODS_COST,
            </if>
            <if test="prepaymentsOilcard!=null and prepaymentsOilcard!=''">
                PREPAYMENTS_OILCARD,
            </if>
            PREPAYMENTS_GASCARD,
            PREPAYMENTS_BUY_OIL,
            PREPAYMENTS_BUY_GAS,
            START_LONGITUDE,
            START_LATITUDE,
            END_LONGITUDE,
            END_LATITUDE,
            ADVANCE_PAY_STATE,
            <if test="unitPrice!=null and unitPrice!=''">
                UNIT_PRICE,
            </if>
            <if test="lossFee!=null and lossFee!=''">
                LOSS_FEE,
            </if>
            RECEIVER,
            RECEIVER_MOBILE,
            TRANSPORT_TYPE,
            TRANSPORT_LINE_ID,
            BO_BUSINESS_LINE_ID,
            LOADING_TONNAGE,
            UNLOADING_TONNAGE,
            LOADING_PLACE_NAME,
            UNLOADING_PLACE_NAME,
            STATE,
            <if test="lossEnsureState!=null and lossEnsureState!=''">
                LOSS_ENSURE_STATE,
            </if>
            <if test="insFee!=null and insFee!=''">
                INS_FEE,
            </if>
            HYB_STATE,
            WB_ITEM,
            OWNER_ORG_ID,
            SETTLE_MODE,
            SETTLE_TYPE,
            SETTLE_TARGET,
            INST_ID,
            TASK_NOTE,
            CAPACITY_TYPE,
            CAPACITY_TYPE_NAME,
            OPERATE_SCHEME,
            IS_PARTAKE_OPERATE,
            <if test="waybillBelongActSys!=null and waybillBelongActSys!=''">
                WAYBILL_BELONG_ACT_SYS,
            </if>
            <if test="fixedCosts != null and fixedCosts != ''">
                FIXED_COSTS,
            </if>
            DRIVER_ID_CARD,
            <if test="withholdTaxFee != null and withholdTaxFee != ''">
                WITHHOLD_TAX_FEE,
            </if>
            CUSTOMER_REMARK
        )
        VALUES(
            #{boTransTaskId},
            #{taxWaybillId},
            #{transMode},
            #{offerType},
            #{createdUserId},
            <if test="createdTime!=null and createdTime!=''">
                to_date(#{createdTime}, 'yyyy-mm-dd hh24:mi:ss'),
            </if>
            #{createdUserSysRoleType},
            #{xcyUserId},
            #{nodeId},
            to_date(#{nodeTime}, 'yyyy-mm-dd hh24:mi:ss'),
            #{transPattern},#{createdUserJobName},
            #{mobileNo},
            #{orgId},
            #{startCityCode},
            #{startCityName},
            #{endCityCode},
            #{endCityName},
            #{allFreight},
            <if test="serviceFee != null and serviceFee != ''">
                #{serviceFee},
            </if>
            <if test="dataServiceFee != null and dataServiceFee != ''">
                #{dataServiceFee},
            </if>
            #{driverName},
            #{cartBadgeNo},
            #{cartBadgeColor},
            #{goodsName},
            #{goodsAmount},
            #{startProvinceName},
            #{startProvinceCode},
            #{endProvinceName},
            #{endProvinceCode},
            #{taxWaybillNo},
            <if test="goodsAmountType!=null and goodsAmountType!=''">
                #{goodsAmountType},
            </if>
            <if test="prepayments!=null and prepayments!=''">
                #{prepayments},
            </if>
            #{orderCreateType},
            #{startCountyName},
            #{endCountyName},
            #{startCountyCode},
            #{endCountyCode},
            <if test="backFee!=null and backFee!=''">
                #{backFee},
            </if>
            <if test="mileage!=null and mileage!=''">
                #{mileage},
            </if>
            <if test="userFreight!=null and userFreight!=''">
                #{userFreight},
            </if>
            <if test="freightIncr!=null and freightIncr!=''">
                #{freightIncr},
            </if>
            <if test="goodsCost!=null and goodsCost!=''">
                #{goodsCost},
            </if>
            <if test="prepaymentsOilcard!=null and prepaymentsOilcard!=''">
                #{prepaymentsOilcard},
            </if>
            #{prepaymentsGascard},
            #{prepaymentsBuyOil},
            #{prepaymentsBuyGas},
            #{startLongitude},
            #{startLatitude},
            #{endLongitude},
            #{endLatitude},
            #{advancePayState},
            <if test="unitPrice!=null and unitPrice!=''">
                #{unitPrice},
            </if>
            <if test="lossFee!=null and lossFee!=''">
                #{lossFee},
            </if>
            #{receiver},
            #{receiverMobile},
            #{transportType},
            #{transportLineId},
            #{boBusinessLineId},
            #{loadingTonnage},
            #{unloadingTonnage},
            #{loadingPlaceName},
            #{unloadingPlaceName},
            0,
            <if test="lossEnsureState!=null and lossEnsureState!=''">
                #{lossEnsureState},
            </if>
            <if test="insFee!=null and insFee!=''">
                #{insFee},
            </if>
            0,
            #{wbItem},
            #{ownerOrgId},
            #{settleMode},
            #{settleType},
            #{settleTarget},
            #{instId},
            #{taskNote},
            #{capacityType},
            #{capacityTypeName},
            #{operateScheme},
            #{isPartakeOperate},
            <if test="waybillBelongActSys!=null and waybillBelongActSys!=''">
                #{waybillBelongActSys},
            </if>
            <if test="fixedCosts != null and fixedCosts != ''">
                #{fixedCosts},
            </if>
            #{driverIdCard},
            <if test="withholdTaxFee != null and withholdTaxFee != ''">
                #{withholdTaxFee},
            </if>
            #{customerRemark}
        )
    </insert>

    <update id="updateTransMode">
        update T_BO_TRANS_TASK t set t.TRANS_MODE = #{transMode},
        t.LAST_MODIFIED_TIME=SYSDATE
        where t.is_del = 0 and t.BO_TRANS_TASK_ID = #{boTransTaskId} and t.TRANS_MODE != #{transMode}
    </update>

    <update id="setTaskIsDelByTaskId">
        UPDATE T_BO_TRANS_TASK SET IS_DEL =1,LAST_MODIFIED_TIME =SYSDATE WHERE BO_TRANS_TASK_ID =#{boTransTaskId} AND IS_DEL =0
    </update>

    <select id="queryTransTaskByBoTransTaskId" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TRANSPORT_TYPE transportType,
            T.TRANS_PATTERN transPattern,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.TRANSPORT_LINE_ID transportLineId,
            T.TRANS_MODE transMode,
            T.OFFER_TYPE offerType,
            T.CREATED_USER_ID createdUserId,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.XCY_USER_ID xcyUserId,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            TO_CHAR(T.START_TIME, 'yyyy-mm-dd hh24:mi:ss') startTime,
            T.TRANS_PATTERN transPattern,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            T.CREATED_USER_JOB_NAME createdUserJobName,
            T.MODIFY_USER_JOB_NAME modifyUserJobName,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            TO_CHAR(T.FIRST_RECEIPT_TIME, 'YYYY-MM-DD HH24:MI:SS') firstReceiptTime,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            T.GOODS_NAME goodsName,
            TO_CHAR(T.GOODS_AMOUNT, 'FM999999990.0000') goodsAmount,
            T.GOODS_AMOUNT_TYPE goodsAmountType,
            T.CUSTOMER_REMARK customerRemark,
            TO_CHAR(NVL(T.ALL_FREIGHT, 0), 'FM999999990.00') allFreight,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), 'FM999999990.00') userFreight,
            TO_CHAR(NVL(T.PREPAYMENTS, 0) , 'FM999999990.00') prepayments,
            TO_CHAR(NVL(T.BACK_FEE, 0) , 'FM999999990.00') backFee,
            TO_CHAR(NVL(T.PREPAYMENTS_OILCARD , 0), 'FM999999990.00') prepaymentsOilcard,
            TO_CHAR(NVL(T.PREPAYMENTS_GASCARD , 0), 'FM999999990.00') prepaymentsGascard,
            TO_CHAR(NVL(T.PREPAYMENTS_BUY_GAS , 0), 'FM999999990.00') prepaymentsBuyGas,
            TO_CHAR(NVL(T.PREPAYMENTS_BUY_OIL , 0), 'FM999999990.00') prepaymentsBuyOil,
            TO_CHAR(NVL(T.FREIGHT_INCR , 0), 'FM999999990.00') freightIncr,
            TO_CHAR(NVL(T.LOSS_FEE , 0), 'FM999999990.00') lossFee,
            TO_CHAR(NVL(T.GOODS_COST , 0), 'FM999999990.00') goodsCost,
            TO_CHAR(NVL(T.INS_FEE , 0), 'FM999999990.00') insFee,
            TO_CHAR(NVL(T.SERVICE_FEE , 0), 'FM999999990.00') serviceFee,
            TO_CHAR(NVL(T.DATA_SERVICE_FEE , 0), 'FM999999990.00') dataServiceFee,
            NVL(t.ADVANCE_PAY_STATE, 0) advancePayState,
            T.LOSS_ENSURE_STATE lossEnsureState,
            T.HYB_STATE hybState,
            T.STATE state,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TRANS_PATTERN transPattern,
            T.OFFER_TYPE offerType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            T.IS_DEL isDel,
            T.CAPACITY_TYPE capacityType,
            T.CAPACITY_TYPE_NAME capacityTypeName,
            T.OPERATE_SCHEME operateScheme,
            T.WAYBILL_BELONG_ACT_SYS waybillBelongActSys,
            T.IS_PARTAKE_OPERATE isPartakeOperate,
            T.ETC_AMOUNT   etcAmount,
            T.IS_DEL isDel
        FROM
            T_BO_TRANS_TASK T
        WHERE
            T.is_del = 0
            AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="queryTransTaskByBoTransTaskIdDeletedOrNot" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TRANSPORT_TYPE transportType,
            T.TRANS_PATTERN transPattern,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.TRANSPORT_LINE_ID transportLineId,
            T.TRANS_MODE transMode,
            T.OFFER_TYPE offerType,
            T.CREATED_USER_ID createdUserId,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.XCY_USER_ID xcyUserId,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            TO_CHAR(T.START_TIME, 'yyyy-mm-dd hh24:mi:ss') startTime,
            T.TRANS_PATTERN transPattern,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            T.CREATED_USER_JOB_NAME createdUserJobName,
            T.MODIFY_USER_JOB_NAME modifyUserJobName,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            TO_CHAR(T.FIRST_RECEIPT_TIME, 'YYYY-MM-DD HH24:MI:SS') firstReceiptTime,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), 'FM999999990.00') userFreight,
            TO_CHAR(NVL(T.PREPAYMENTS, 0) , 'FM999999990.00') prepayments,
            TO_CHAR(NVL(T.BACK_FEE, 0) , 'FM999999990.00') backFee,
            TO_CHAR(NVL(T.PREPAYMENTS_OILCARD , 0), 'FM999999990.00') prepaymentsOilcard,
            TO_CHAR(NVL(T.PREPAYMENTS_GASCARD , 0), 'FM999999990.00') prepaymentsGascard,
            TO_CHAR(NVL(T.FREIGHT_INCR , 0), 'FM999999990.00') freightIncr,
            TO_CHAR(NVL(T.LOSS_FEE , 0), 'FM999999990.00') lossFee,
            TO_CHAR(NVL(T.GOODS_COST , 0), 'FM999999990.00') goodsCost,
            NVL(t.ADVANCE_PAY_STATE, 0) advancePayState,
            T.LOSS_ENSURE_STATE lossEnsureState,
            T.HYB_STATE hybState,
            T.STATE state,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TRANS_PATTERN transPattern,
            T.OFFER_TYPE offerType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            T.IS_DEL isDel,
            T.CAPACITY_TYPE capacityType,
            T.CAPACITY_TYPE_NAME capacityTypeName,
            T.OPERATE_SCHEME operateScheme,
            T.IS_PARTAKE_OPERATE isPartakeOperate
        FROM
            T_BO_TRANS_TASK T
        WHERE
            T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="queryTaskWithoutIsDel" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.IS_DEL isDel,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TRANSPORT_TYPE transportType,
            T.TRANS_PATTERN transPattern,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.TRANSPORT_LINE_ID transportLineId,
            T.TRANS_MODE transMode,
            T.OFFER_TYPE offerType,
            T.CREATED_USER_ID createdUserId,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.XCY_USER_ID xcyUserId,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            T.TRANS_PATTERN transPattern,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            T.CREATED_USER_JOB_NAME createdUserJobName,
            T.MODIFY_USER_JOB_NAME modifyUserJobName,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            TO_CHAR(T.START_TIME, 'YYYY-MM-DD HH24:MI:SS') startTime,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), 'FM999999990.00') userFreight,
            TO_CHAR(NVL(T.PREPAYMENTS, 0) , 'FM999999990.00') prepayments,
            TO_CHAR(NVL(T.BACK_FEE, 0) , 'FM999999990.00') backFee,
            TO_CHAR(NVL(T.PREPAYMENTS_OILCARD , 0), 'FM999999990.00') prepaymentsOilcard,
            TO_CHAR(NVL(T.FREIGHT_INCR , 0), 'FM999999990.00') freightIncr,
            TO_CHAR(NVL(T.LOSS_FEE , 0), 'FM999999990.00') lossFee,
            TO_CHAR(NVL(T.GOODS_COST , 0), 'FM999999990.00') goodsCost,
            NVL(t.ADVANCE_PAY_STATE, 0) advancePayState,
            T.LOSS_ENSURE_STATE lossEnsureState,
            T.HYB_STATE hybState,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TRANS_PATTERN transPattern,
            T.OFFER_TYPE offerType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode
        FROM
            T_BO_TRANS_TASK T
        WHERE
            T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="queryDeletedTransTaskByBoTransTaskId" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TRANSPORT_TYPE transportType,
            T.TRANS_PATTERN transPattern,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.TRANSPORT_LINE_ID transportLineId,
            T.TRANS_MODE transMode,
            T.OFFER_TYPE offerType,
            T.CREATED_USER_ID createdUserId,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.XCY_USER_ID xcyUserId,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            T.TRANS_PATTERN transPattern,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            T.CREATED_USER_JOB_NAME createdUserJobName,
            T.MODIFY_USER_JOB_NAME modifyUserJobName,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), 'FM999999990.00') userFreight,
            TO_CHAR(NVL(T.PREPAYMENTS, 0) , 'FM999999990.00') prepayments,
            TO_CHAR(NVL(T.BACK_FEE, 0) , 'FM999999990.00') backFee,
            TO_CHAR(NVL(T.PREPAYMENTS_OILCARD , 0), 'FM999999990.00') prepaymentsOilcard,
            TO_CHAR(NVL(T.FREIGHT_INCR , 0), 'FM999999990.00') freightIncr,
            TO_CHAR(NVL(T.LOSS_FEE , 0), 'FM999999990.00') lossFee,
            TO_CHAR(NVL(T.GOODS_COST , 0), 'FM999999990.00') goodsCost,
            NVL(t.ADVANCE_PAY_STATE, 0) advancePayState,
            T.LOSS_ENSURE_STATE lossEnsureState,
            T.HYB_STATE hybState,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TRANS_PATTERN transPattern,
            T.OFFER_TYPE offerType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            T.IS_DEL isDel
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 1
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <update id="updateTaskInfos" parameterType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        UPDATE T_BO_TRANS_TASK
        SET
        <if test="taxWaybillId!=null and taxWaybillId!=''">
            TAX_WAYBILL_ID = #{taxWaybillId},
        </if>
        <if test="xcyUserId != null">
            XCY_USER_ID = #{xcyUserId},
        </if>
        <if test="transMode != null and transMode != ''">
            TRANS_MODE = #{transMode},
        </if>
        <if test="modifyUserId != null and modifyUserId !='' and modifyUserId !='-1'.toString()">
            MODIFY_USER_ID = #{modifyUserId},
        </if>
        <if test="modifyDriverId != null and modifyDriverId !=''">
            MODIFY_DRIVER_ID = #{modifyDriverId},
        </if>
        <if test="modifySysRoleType != null and modifySysRoleType != ''">
            MODIFY_SYS_ROLE_TYPE = #{modifySysRoleType},
        </if>
        <if test="modifyUserJobName != null">
            MODIFY_USER_JOB_NAME = #{modifyUserJobName},
        </if>
        <!-- 20230509物理解耦新增 -->
        <if test="mobileNo != null and mobileNo != ''">
            MOBILE_NO = #{mobileNo},
        </if>
        <if test="startProvinceName != null and startProvinceName != ''">
            START_PROVINCE_NAME = #{startProvinceName},
            START_PROVINCE_CODE = #{startProvinceCode},
        </if>
        <if test="startCityName != null and startCityName != ''">
            START_CITY_NAME = #{startCityName},
            START_CITY_CODE = #{startCityCode},
            START_COUNTY_NAME = #{startCountyName},
            START_COUNTY_CODE = #{startCountyCode},
        </if>
        <if test="endProvinceName != null and endProvinceName != ''">
            END_PROVINCE_NAME = #{endProvinceName},
            END_PROVINCE_CODE = #{endProvinceCode},
        </if>
        <if test="endCityName != null and endCityName != ''">
            END_CITY_NAME = #{endCityName},
            END_CITY_CODE = #{endCityCode},
            END_COUNTY_NAME = #{endCountyName},
            END_COUNTY_CODE = #{endCountyCode},
        </if>
        <if test="driverName != null">
            DRIVER_NAME = #{driverName},
        </if>
        <if test="cartBadgeNo != null and cartBadgeNo != ''">
            CART_BADGE_NO = #{cartBadgeNo},
        </if>
        <if test="cartBadgeColor != null and cartBadgeColor != ''">
            CART_BADGE_COLOR = #{cartBadgeColor},
        </if>
        <if test="goodsName != null and goodsName != ''">
            GOODS_NAME = #{goodsName},
        </if>
        <if test="goodsAmount != null">
            GOODS_AMOUNT = #{goodsAmount},
        </if>
        <if test="goodsAmountType != null and goodsAmountType != ''">
            GOODS_AMOUNT_TYPE = #{goodsAmountType},
        </if>
        <if test="allFreight != null">
            ALL_FREIGHT = #{allFreight},
        </if>
        <if test="serviceFee != null">
            SERVICE_FEE = #{serviceFee},
        </if>
        <if test="dataServiceFee != null">
            DATA_SERVICE_FEE = #{dataServiceFee},
        </if>
        <if test="prepayments != null and prepayments != ''">
            PREPAYMENTS = #{prepayments},
        </if>
        <if test="mileage != null">
            MILEAGE = #{mileage},
        </if>
        <if test="unitPrice != null and unitPrice != ''">
            UNIT_PRICE = #{unitPrice},
        </if>
        <if test="userFreight != null and userFreight != ''">
            USER_FREIGHT = #{userFreight},
        </if>
        <if test="freightIncr != null and freightIncr != ''">
            FREIGHT_INCR = #{freightIncr},
        </if>
        <if test="lossFee != null and lossFee != ''">
            LOSS_FEE = #{lossFee},
        </if>
        <if test="goodsCost != null and goodsCost != ''">
            GOODS_COST = #{goodsCost},
        </if>
        <if test="taxWaybillNo != null and taxWaybillNo != ''">
            TAX_WAYBILL_NO = #{taxWaybillNo},
        </if>
        <if test="prepaymentsOilcard != null and prepaymentsOilcard != ''">
            PREPAYMENTS_OILCARD = #{prepaymentsOilcard},
        </if>
        <if test="backFee != null and backFee != ''">
            BACK_FEE = #{backFee},
        </if>
        <if test="prepaymentsGascard != null and prepaymentsGascard != ''">
            PREPAYMENTS_GASCARD = #{prepaymentsGascard},
        </if>
        <if test="startLongitude != null">
            START_LONGITUDE = #{startLongitude},
        </if>
        <if test="startLatitude != null">
            START_LATITUDE = #{startLatitude},
        </if>
        <if test="endLongitude != null">
            END_LONGITUDE = #{endLongitude},
        </if>
        <if test="endLatitude != null">
            END_LATITUDE = #{endLatitude},
        </if>
        <if test="advancePayState != null and advancePayState != ''">
            ADVANCE_PAY_STATE = #{advancePayState},
        </if>
        <if test="receiver != null and receiver != ''">
            RECEIVER = #{receiver},
        </if>
        <if test="receiverMobile != null and receiverMobile != ''">
            RECEIVER_MOBILE = #{receiverMobile},
        </if>
        <if test="loadingPlaceName != null and loadingPlaceName != ''">
            LOADING_PLACE_NAME = #{loadingPlaceName},
        </if>
        <if test="unloadingPlaceName != null and unloadingPlaceName != ''">
            UNLOADING_PLACE_NAME = #{unloadingPlaceName},
        </if>
        <if test="loadingTonnage != null and loadingTonnage != ''">
            LOADING_TONNAGE = #{loadingTonnage},
        </if>
        <if test="unloadingTonnage != null and unloadingTonnage != ''">
            UNLOADING_TONNAGE = #{unloadingTonnage},
        </if>
        <!-- 20230509物理解耦新增 -->
        <if test="startTime != null and startTime != ''">
            START_TIME = TO_DATE(#{startTime}, 'yyyy-mm-dd hh24:mi:ss'),
        </if>
        <if test="settleMode != null and settleMode != ''">
            SETTLE_MODE = #{settleMode},
        </if>
        <if test="taskNote != null">
            TASK_NOTE = #{taskNote},
        </if>
        <if test="capacityType != null">
            CAPACITY_TYPE = #{capacityType},
        </if>
        <if test="capacityTypeName != null">
            CAPACITY_TYPE_NAME = #{capacityTypeName},
        </if>
        <if test="isPartakeOperate != null">
            IS_PARTAKE_OPERATE = #{isPartakeOperate},
        </if>
        <if test="wbItem != null and wbItem != ''">
            WB_ITEM = #{wbItem},
        </if>
        <if test="customerRemark != null">
            CUSTOMER_REMARK = #{customerRemark},
        </if>
        <if test="waybillBelongActSys!=null and waybillBelongActSys!=''">
            WAYBILL_BELONG_ACT_SYS = #{waybillBelongActSys},
        </if>
        <if test="settleType != null and settleType != ''">
            SETTLE_TYPE = #{settleType},
        </if>
        <if test="settleTarget != null and settleTarget != ''">
            SETTLE_TARGET = #{settleTarget},
        </if>
        <if test="instId != null">
            INST_ID = #{instId},
        </if>
        <if test="prepaymentsBuyOil!=null">
            PREPAYMENTS_BUY_OIL = #{prepaymentsBuyOil},
        </if>
        <if test="prepaymentsBuyGas!=null">
            PREPAYMENTS_BUY_GAS = #{prepaymentsBuyGas},
        </if>
        <if test="driverIdCard!=null">
            DRIVER_ID_CARD = #{driverIdCard},
        </if>
        <if test="withholdTaxFee!=null">
            WITHHOLD_TAX_FEE = #{withholdTaxFee},
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>


    <select id="queryTransTaskIdByWaybillId" resultType="java.lang.String">
        SELECT T.BO_TRANS_TASK_ID
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL =0
        AND (
            T.TAX_WAYBILL_ID =#{taxWaybillId}
            OR
            EXISTS(SELECT 1 FROM T_BO_TRANS_TASK_ALLOCATE A WHERE A.IS_DEL = 0 AND T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.TAX_WAYBILL_ID = #{taxWaybillId})
        )
        AND ROWNUM = 1
    </select>



    <select id="queryTaxWayBillIdById" resultType="java.lang.String">
        SELECT TAX_WAYBILL_ID FROM T_BO_TRANS_TASK WHERE BO_TRANS_TASK_ID =#{boTransTaskId} AND IS_DEL =0
    </select>


    <update id="updateTaskNodeId" parameterType="Req1735203Bean">
        UPDATE T_BO_TRANS_TASK
        SET node_id = #{nodeId},
        <if test="userId != null and userId !='' and userId !='-1'.toString()">
            MODIFY_USER_ID = #{userId},
        </if>
        <if test="driverId != null and driverId !=''">
            MODIFY_DRIVER_ID = #{driverId},
        </if>
        <if test="sysRoleType != null and sysRoleType != ''">
            MODIFY_SYS_ROLE_TYPE = #{sysRoleType},
        </if>
        NODE_TIME = SYSDATE,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_TASK_ID = #{boTransTaskId}
        <if test="upNodeIdList != null and upNodeIdList.size() > 0">
            AND NODE_ID IN
            <foreach collection="upNodeIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="batchDelByTaskId">
        UPDATE T_BO_TRANS_TASK
           SET IS_DEL = 1,
               <if test="userInfo.userId!=null and userInfo.userId!=''">
                modify_user_id = #{userInfo.userId},
                modify_user_job_name = #{userInfo.jobName},
                modify_sys_role_type = #{userInfo.sysRoleType},
               </if>
               LAST_MODIFIED_TIME = SYSDATE
         WHERE BO_TRANS_TASK_ID IN
         <foreach collection="list" item="item" close=")" open="(" separator=",">
               #{item}
         </foreach>
           AND IS_DEL =0
    </update>

    <update id="updateCustomerOrderNoByTransTaskId">
        update T_BO_TRANS_TASK_EXTRA set CUSTOMER_ORDER_NO = #{customerOrderNo} where BO_TRANS_TASK_ID = #{taskId} AND IS_DEL = 0
    </update>

    <update id="updateElectronicReceiptStatus" parameterType="java.lang.String">
        update T_BO_TRANS_TASK_EXTRA set ELECTRONIC_RECEIPT_STATUS = #{status}, ELECTRONIC_RECEIPT_AUDIT_TIME = SYSDATE, LAST_MODIFIED_TIME = SYSDATE
        where BO_TRANS_TASK_ID = #{boTransTaskId} and IS_DEL = 0
    </update>
    <update id="updateTaskWhenCancelDispatch">
        UPDATE
            T_BO_TRANS_TASK
        SET
            DRIVER_NAME = '',
            CART_BADGE_NO = '',
            CART_BADGE_COLOR = '',
            DRIVER_ID_CARD = NULL,
            MOBILE_NO = '',
            HYB_STATE = 0,
            OFFER_TYPE = 1,
            TAX_WAYBILL_ID = null,
            GOODS_COST = 0,
            INS_FEE = null,
            CAPACITY_TYPE = null,
            CAPACITY_TYPE_NAME = null,
            IS_PARTAKE_OPERATE = null,
            SETTLE_TARGET = null,
            INST_ID = null,
            SERVICE_FEE = NULL,
            DATA_SERVICE_FEE = NULL,
            WITHHOLD_TAX_FEE = NULL,
            ETC_AMOUNT = NULL,
            ETC_INVOICE_STATE = NULL,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID =#{boTransTaskId}
    </update>
    <update id="updateTaskFreightInfo">
        UPDATE T_BO_TRANS_TASK
        SET
        <if test="userFreight != null">
            USER_FREIGHT = #{userFreight},
        </if>
        <if test="prepaymentsOilcard != null">
            PREPAYMENTS_OILCARD = #{prepaymentsOilcard},
        </if>
        <if test="prepayments != null ">
            PREPAYMENTS = #{prepayments},
        </if>
        <if test="backFee != null ">
            BACK_FEE = #{backFee},
        </if>
        <if test="freightIncr != null ">
            FREIGHT_INCR = #{freightIncr},
        </if>
        <if test="allFreight != null ">
            ALL_FREIGHT = #{allFreight},
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <select id="queryTaskByIdList" resultType="BoTransTaskBean">
        SELECT
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.DRIVER_NAME driverName,
            T.CART_BADGE_NO cartBadgeNo,
            T.SETTLE_TYPE settleType,
            T.INST_ID instId,
            NVL(T.TRANS_MODE,1)  transMode,
            T.RECEIVER_MOBILE receiverMobile,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            T.WB_ITEM wbItem,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), 'FM999999990.00') userFreight,
            TO_CHAR(NVL(T.LOSS_FEE, 0), 'FM999999990.00') lossFee,
            TO_CHAR(NVL(T.FREIGHT_INCR, 0), 'FM999999990.00') freightIncr
        FROM T_BO_TRANS_TASK T
        WHERE t.is_del=0 and T.BO_TRANS_TASK_ID IN
        <foreach collection="ids" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getTransTaskListByTaskIdList" resultType="BoTransTaskBean">
        SELECT T.TAX_WAYBILL_ID taxWaybillId,
               T.TAX_WAYBILL_NO taxWaybillNo,
               T.BO_TRANS_TASK_ID boTransTaskId,
               T.ORG_ID orgId,
               T.DRIVER_NAME driverName,
               T.MOBILE_NO mobileNo,
               T.DRIVER_NAME driverName,
               T.CART_BADGE_NO cartBadgeNo,
               T.SETTLE_TYPE settleType,
               T.INST_ID instId,
               NVL(T.TRANS_MODE,1)  transMode,
               T.RECEIVER_MOBILE receiverMobile,
               TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
               T.WB_ITEM wbItem
        FROM T_BO_TRANS_TASK T
        WHERE
        <choose>
            <when test="isDel != null and isDel == '1'.toString()">
                T.IS_DEL = 1
            </when>
            <otherwise>
                T.IS_DEL = 0
            </otherwise>
        </choose>
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="taskIdList" item="taskId" close=")" open="(" separator=",">
            #{taskId}
        </foreach>
    </select>

    <update id="syncWaybillInfo" parameterType="BoTransTaskBean">
        UPDATE T_BO_TRANS_TASK
        SET MOBILE_NO            = #{bean.mobileNo},
            START_CITY_CODE      = #{bean.startCityCode},
            START_CITY_NAME      = #{bean.startCityName},
            END_CITY_CODE        = #{bean.endCityCode},
            END_CITY_NAME        = #{bean.endCityName},
            DRIVER_NAME          = #{bean.driverName},
            CART_BADGE_NO        = #{bean.cartBadgeNo},
            CART_BADGE_COLOR     = #{bean.cartBadgeColor},
            <if test="bean.driverIdCard!=null and bean.driverIdCard!=''">
                DRIVER_ID_CARD   = #{bean.driverIdCard},
            </if>
            GOODS_NAME           = #{bean.goodsName},
            GOODS_AMOUNT         = #{bean.goodsAmount},
            ETC_AMOUNT           = #{bean.etcAmount},
            ETC_INVOICE_STATE    = #{bean.etcInvoiceState},
            LAST_MODIFIED_TIME   = SYSDATE,
            START_PROVINCE_NAME  = #{bean.startProvinceName},
            START_PROVINCE_CODE  = #{bean.startProvinceCode},
            END_PROVINCE_NAME    = #{bean.endProvinceName},
            END_PROVINCE_CODE    = #{bean.endProvinceCode},
            TAX_WAYBILL_NO       = #{bean.taxWaybillNo},
            GOODS_AMOUNT_TYPE    = #{bean.goodsAmountType},
            START_COUNTY_NAME    = #{bean.startCountyName},
            END_COUNTY_NAME      = #{bean.endCountyName},
            START_COUNTY_CODE    = #{bean.startCountyCode},
            END_COUNTY_CODE      = #{bean.endCountyCode},
            MILEAGE              = #{bean.mileage},
            RECEIVER             = #{bean.receiver},
            <if test="bean.receiverMobile != null">
              RECEIVER_MOBILE      = #{bean.receiverMobile},
            </if>
            START_LONGITUDE      = #{bean.startLongitude},
            START_LATITUDE       = #{bean.startLatitude},
            END_LONGITUDE        = #{bean.endLongitude},
            END_LATITUDE         = #{bean.endLatitude},
            TRANSPORT_LINE_ID    = #{bean.transportLineId},
            LOADING_TONNAGE      = #{bean.loadingTonnage},
            UNLOADING_TONNAGE    = #{bean.unloadingTonnage},
            LOADING_PLACE_NAME   = #{bean.loadingPlaceName},
            UNLOADING_PLACE_NAME = #{bean.unloadingPlaceName},
            <if test="bean.customerRemark != null">
                CUSTOMER_REMARK = #{bean.customerRemark},
            </if>
             <!-- 防止大宗的上传装货榜单发车同步导致START_TIME由有值更新为null的情况-->
            <if test="bean.startTime != null and bean.startTime != ''">
                START_TIME           = TO_DATE(#{bean.startTime}, 'YYYY-MM-DD HH24:MI:SS'),
            </if>
            <!-- 防止大宗的上传装货榜单发车同步导致state由1变为0的情况 -->
            STATE                = CASE WHEN STATE =1 AND 0=#{bean.state} THEN STATE ELSE TO_NUMBER(#{bean.state}) END,
            HYB_STATE            = #{bean.hybState},
            FIRST_RECEIPT_TIME   = TO_DATE(#{bean.firstReceiptTime}, 'YYYY-MM-DD HH24:MI:SS'),
            LOSS_ENSURE_STATE    = #{bean.lossEnsureState},
            WB_ITEM              = #{bean.wbItem},
            WAYBILL_BELONG_ACT_SYS = #{bean.waybillBelongActSys},
            END_TIME             = TO_DATE(#{bean.endTime}, 'YYYY-MM-DD HH24:MI:SS')
            <if test="bean.transMode != null and bean.transMode != ''">
                ,PAY_STATE           = #{bean.payState},
                ALL_FREIGHT          = #{bean.allFreight},
                SERVICE_FEE          = #{bean.serviceFee},
                DATA_SERVICE_FEE     = #{bean.dataServiceFee},
                USER_FREIGHT         = #{bean.userFreight},
                FREIGHT_INCR         = #{bean.freightIncr},
                LOSS_FEE             = #{bean.lossFee},
                UNIT_PRICE           = #{bean.unitPrice},
                PREPAYMENTS_OILCARD  = #{bean.prepaymentsOilcard},
                PREPAYMENTS_GASCARD  = #{bean.prepaymentsGascard},
                PREPAYMENTS_BUY_OIL  = #{bean.prepaymentsBuyOil},
                PREPAYMENTS_BUY_GAS  = #{bean.prepaymentsBuyGas},
                PREPAYMENTS          = #{bean.prepayments},
                BACK_FEE             = #{bean.backFee},
                GOODS_COST           = #{bean.goodsCost},
                ADVANCE_PAY_STATE    = #{bean.advancePayState},
                WITHHOLD_TAX_FEE     = #{bean.withholdTaxFee},
                PAY_OVER_TIME        = TO_DATE(#{bean.payOverTime}, 'YYYY-MM-DD HH24:MI:SS')
            </if>
        WHERE BO_TRANS_TASK_ID = #{bean.boTransTaskId}
        <if test="oldMobileNo!=null and oldMobileNo!=''">
          and MOBILE_NO=#{oldMobileNo}
        </if>
    </update>

    <select id="queryUdBoTransTaskByWaybillId" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TRANSPORT_TYPE transportType,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.TRANSPORT_LINE_ID transportLineId,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.TRANS_MODE transMode,
            T.OFFER_TYPE offerType,
            T.CREATED_USER_ID createdUserId,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.XCY_USER_ID xcyUserId,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            T.TRANS_PATTERN transPattern,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            T.CREATED_USER_JOB_NAME createdUserJobName,
            T.MODIFY_USER_JOB_NAME modifyUserJobName,
            T.DRIVER_NAME driverName,
            T.CART_BADGE_NO cartBadgeNo,
            T.MOBILE_NO mobileNo,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.START_PROVINCE_CODE startProvinceCode,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_CODE startCityCode,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_CODE startCountyCode,
            T.START_COUNTY_NAME startCountyName,
            T.END_PROVINCE_CODE endProvinceCode,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_CODE endCityCode,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_CODE endCountyCode,
            T.END_COUNTY_NAME endCountyName,
            T.IS_PARTAKE_OPERATE isPartakeOperate
        FROM (
            SELECT T1.*
            FROM T_BO_TRANS_TASK T1
            WHERE T1.IS_DEL = 0
            AND T1.TAX_WAYBILL_ID = #{taxWaybillId}

            UNION

            SELECT T3.*
            FROM T_BO_TRANS_TASK_ALLOCATE T2
            INNER JOIN T_BO_TRANS_TASK T3
            ON T3.BO_TRANS_TASK_ID = T2.BO_TRANS_TASK_ID AND T3.IS_DEL = 0
            WHERE T2.IS_DEL = 0
            AND T2.TAX_WAYBILL_ID = #{taxWaybillId}
        )T
    </select>

    <update id="updateTransTaskHybStateAndFirstReceiptTime">
        UPDATE T_BO_TRANS_TASK T SET
            <if test="hybState != null and hybState.length > 0">
                T.HYB_STATE = #{hybState},
            </if>
            <if test="firstReceiptTime != null and firstReceiptTime.length > 0">
                T.FIRST_RECEIPT_TIME =
                CASE
                    WHEN T.FIRST_RECEIPT_TIME IS NULL THEN TO_DATE(#{firstReceiptTime}, 'yyyy-mm-dd hh24:mi:ss')
                    ELSE T.FIRST_RECEIPT_TIME
                END,
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateTransTaskStartTime">
        UPDATE T_BO_TRANS_TASK T SET
            T.STATE = 1,
            T.START_TIME = TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS'),
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND (T.STATE IS NULL OR T.STATE!=2)
    </update>

    <update id="updateTaskStart">
        UPDATE T_BO_TRANS_TASK T SET
            T.HYB_STATE = 1,
            <if test="modifyDriverId != null and modifyDriverId.length > 0">
                T.MODIFY_DRIVER_ID = #{modifyDriverId},
                T.MODIFY_SYS_ROLE_TYPE = #{modifySysRoleType},
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId} and t.HYB_STATE = 0
    </update>
    <update id="updateTaskNodeIdByWayBillId">
        UPDATE T_BO_TRANS_TASK
        SET node_id = #{nodeId},
        <if test="userId != null and userId !='' and userId !='-1'.toString()">
            MODIFY_USER_ID = #{userId},
        </if>
        <if test="driverId != null and driverId !=''">
            MODIFY_DRIVER_ID = #{driverId},
        </if>
        <if test="sysRoleType != null and sysRoleType != ''">
            MODIFY_SYS_ROLE_TYPE = #{sysRoleType},
        </if>
        NODE_TIME = SYSDATE,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE TAX_WAYBILL_ID = #{taxWaybillId}
        <if test="upNodeIdList != null and upNodeIdList.size() > 0">
            AND NODE_ID IN
            <foreach collection="upNodeIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>
    <update id="updateTaskState">
        UPDATE T_BO_TRANS_TASK T SET
        T.STATE = 2,
        T.LAST_MODIFIED_TIME = SYSDATE,
        T.END_TIME = SYSDATE
        WHERE IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <select id="getGoodsAmountByTaskId" resultType="string">
        SELECT GOODS_AMOUNT FROM T_BO_TRANS_TASK WHERE BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="selectBoTransTaskInfoByNo" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
    SELECT
            TAX_WAYBILL_ID taxWaybillId,
            HYB_STATE hybState,
            ORG_ID orgId,
            BO_TRANS_TASK_ID boTransTaskId,
            TAX_WAYBILL_NO taxWaybillNo
        FROM
            T_BO_TRANS_TASK
        WHERE
            IS_DEL = 0
            AND TAX_WAYBILL_NO = #{taxWaybillNo}
        ORDER BY
            CREATED_TIME DESC
    </select>

    <select id="getBoTransTaskIdList" resultType="java.lang.String">
        SELECT
            BO_TRANS_TASK_ID boTransTaskId,
            TAX_WAYBILL_ID taxWaybillId
        FROM T_BO_TRANS_TASK
        WHERE
        IS_DEL = 0
        AND TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryOrgIdByBoTransTaskId" resultType="java.lang.String">
        SELECT ORG_ID FROM T_BO_TRANS_TASK WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="getInTransitTransTaskList" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_NO taxWaybillNo
        FROM T_BO_TRANS_TASK T left join T_BO_TRANS_TASK_ALLOCATE a on T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND A.IS_DEL = 0
        WHERE T.IS_DEL = 0
            AND T.STATE = 1
            AND (T.MOBILE_NO = #{mobileNo} OR T.CART_BADGE_NO = #{cartBadgeNo})
            AND (T.SETTLE_MODE is null or T.SETTLE_MODE = 1 or A.SETTLE_MODE=1)
            AND T.CREATED_TIME > SYSDATE - 365
        ORDER BY
            T.CREATED_TIME
    </select>

    <select id="getStateByTaxWaybillId" resultType="java.lang.Integer">
        SELECT
            T.STATE
        FROM T_BO_TRANS_TASK T
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
            AND A.IS_DEL = 0
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID =
            (
            SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE IS_DEL = 0 AND TAX_WAYBILL_ID = #{taxWaybillId}
            UNION
            SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE IS_DEL = 0 AND TAX_WAYBILL_ID = #{taxWaybillId}
            )
    </select>

    <select id="queryTaxWaybillIdList" resultType="java.lang.String">
        SELECT TAX_WAYBILL_ID
          FROM (SELECT A.TAX_WAYBILL_ID, ROWNUM RN
                  FROM (SELECT T.TAX_WAYBILL_ID
                          FROM T_BO_TRANS_TASK T
                         WHERE T.TAX_WAYBILL_ID IS NOT NULL
                           AND T.IS_DEL = 0
                         ORDER BY T.CREATED_TIME) A)
         WHERE RN >= #{startRow}
           AND RN &lt; #{endRow}
    </select>
    <select id="batchQueryTaxWaybill" resultType="com.wtyt.tt.bean.Resp5330239Bean"
            parameterType="java.util.List">
        SELECT TAX_WAYBILL_ID taxWaybillId,
               BO_TRANS_TASK_ID boTransTaskId,
               NODE_ID nodeId
        FROM T_BO_TRANS_TASK
        WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getBoTransTaskByOrgTaxWayBillNo" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_ID taxWayBillId
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
        AND T.TAX_WAYBILL_NO = #{taxWaybillNo}
        AND T.ORG_ID = #{orgId}
    </select>

    <select id="getTaskByNosOrIds" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
        AND T.ORG_ID = #{orgId}
        <if test="boTransTaskIds!=null and boTransTaskIds.size>0">
            AND T.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="taxWaybillNos!=null and taxWaybillNos.size > 0">
            AND T.TAX_WAYBILL_NO IN
            <foreach collection="taxWaybillNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSupplierIdByTaskId" resultType="java.lang.String">
    SELECT
        *
    FROM
        (
        SELECT
            GROUP_ID
        FROM
            T_BO_TRANS_TASK_GROUP_REL
        WHERE
            IS_DEL = 0
            AND SUPPLIER_TYPE IN (1, 2)
            AND BO_TRANS_TASK_ID =#{boTransTaskId}
        ORDER BY
            CREATED_TIME DESC)
    WHERE
        ROWNUM = 1
    </select>
    <select id="countOrgWaybillNo" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
        AND T.TAX_WAYBILL_NO = #{taxWaybillNo}
        AND T.ORG_ID IN
        <foreach collection="orgIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="boTransTaskId != null and boTransTaskId != ''">
            AND T.BO_TRANS_TASK_ID != #{boTransTaskId}
        </if>

    </select>

    <update id="updateFrozenBackFeeState">
        UPDATE T_BO_TRANS_TASK SET FROZEN_BACK_FEE_STATE = #{frozenBackFeeState},LAST_MODIFIED_TIME =SYSDATE WHERE BO_TRANS_TASK_ID = #{boTransTaskId} AND IS_DEL =0
    </update>


    <select id="getExistsTaxWaybillNo" resultType="java.lang.String">
        SELECT
            T.TAX_WAYBILL_NO
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
        AND T.ORG_ID = #{orgId}
        AND T.TAX_WAYBILL_NO IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="batchQueryTransTask" resultType="com.wtyt.bo.bean.DhPlanBaseBean" >
        SELECT
        ttw.BO_TRANS_TASK_ID  boTransTaskId ,
        DECODE(ttw.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) dispatchState,
        <if test="configId != null and configId == '104'.toString()">
            DECODE(ttw.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 2, 500, 3, 600, 4, 650, 4, 700, 5, 800, 5, 1000, 5, 1100, 5, 1200, 6, 1) transportState,
        </if>
        <if test="configId != null and configId == '105'.toString()">
            DECODE(ttw.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 3, 501, 2, 600, 4, 650, 4, 700, 5, 800, 5, 1000, 5, 1100, 5, 1200, 6, 1) transportState,
        </if>
        ttw.TAX_WAYBILL_NO taxWaybillNo,
        ttw.TAX_WAYBILL_ID taxWaybillId,
        ttw.DRIVER_NAME driverName,
        ttw.MOBILE_NO driverMobileNo,
        ttw.CART_BADGE_NO cartBadgeNo,
        ttw.UNIT_PRICE unitPrice
        FROM T_BO_TRANS_TASK ttw
        WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getTransTaskWbItemList" resultType="java.lang.String">
        SELECT DISTINCT T.WB_ITEM
        FROM (
            SELECT
                ST.WB_ITEM,
                ST.CREATED_TIME
            FROM T_BO_TRANS_TASK ST
            WHERE ST.IS_DEL = 0
            <choose>
                <when test="retrieval != null and retrieval.length > 0">
                    AND INSTR(ST.WB_ITEM, #{retrieval}) > 0
                </when>
                <otherwise>
                    AND ST.WB_ITEM IS NOT NULL
                </otherwise>
            </choose>
            AND ST.ORG_ID = #{orgId}
            ORDER BY ST.CREATED_TIME DESC
        )T
    </select>

    <select id="getAllocateTransTaskWbItemList" resultType="java.lang.String">
        SELECT DISTINCT T.WB_ITEM
        FROM (
            SELECT
                ST1.WB_ITEM,
                ST1.CREATED_TIME
            FROM T_BO_TRANS_TASK ST1
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE ST2
                ON ST1.BO_TRANS_TASK_ID = ST2.BO_TRANS_TASK_ID
            WHERE ST1.IS_DEL = 0
            AND ST2.IS_DEL = 0
            <choose>
                <when test="retrieval != null and retrieval.length > 0">
                    AND INSTR(ST1.WB_ITEM, #{retrieval}) > 0
                </when>
                <otherwise>
                    AND ST1.WB_ITEM IS NOT NULL
                </otherwise>
            </choose>
            AND ST2.ORG_ID = #{orgId}
            ORDER BY ST1.CREATED_TIME DESC
        )T
    </select>

     <select id="getTransTaskWbItemListByOrgIdList" resultType="java.lang.String">
        SELECT DISTINCT T.WB_ITEM
        FROM (
            SELECT
                ST.WB_ITEM,
                ST.CREATED_TIME
            FROM T_BO_TRANS_TASK ST
            WHERE ST.IS_DEL = 0
              AND ST.WB_ITEM IS NOT NULL
             AND (ST.ORG_ID IN
                    <foreach collection="queryOrgIds" index="index" item="orgId" open="(" separator="," close=")">
                        <if test="(index % 999) == 998"> NULL) OR ST.ORG_ID IN(</if>#{orgId}
                    </foreach>
                    )
            ORDER BY ST.CREATED_TIME DESC
        )T
    </select>

    <select id="queryCountByBoTransTaskId" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT COUNT(*) FROM T_BO_TRANS_TASK WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>
    <select id="queryOilSettleTaskList" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.PAY_STATE payState
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
            AND T.SETTLE_TYPE = 3
            AND T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIds" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getTaskIdByTaxWaybillId" resultType="java.lang.String">
        SELECT
            T.BO_TRANS_TASK_ID
        FROM
            T_BO_TRANS_TASK T
        WHERE
            T.TAX_WAYBILL_ID = #{taxWaybillId}
            AND T.IS_DEL = 0
    </select>

    <select id="queryTaskIdByTaxWaybillId" resultType="java.lang.String">
        SELECT
        T.BO_TRANS_TASK_ID
        FROM
        T_BO_TRANS_TASK T
        WHERE
        T.TAX_WAYBILL_ID = #{taxWaybillId}
        <if test="isDel!=null and isDel!=''">
            AND T.IS_DEL = #{isDel}
        </if>
    </select>

    <update id="updateSettleMode" parameterType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        UPDATE T_BO_TRANS_TASK T
        SET T.SETTLE_MODE = 2,
            T.SETTLE_TYPE = 2,
            T.SETTLE_TARGET = NULL,
            T.TAX_WAYBILL_ID = NULL,
            T.SERVICE_FEE = NULL,
            T.DATA_SERVICE_FEE = NULL,
            T.WITHHOLD_TAX_FEE = NULL,
            T.ETC_AMOUNT = NULL,
            T.ETC_INVOICE_STATE = NULL,
            <if test="modifyUserId != null and modifyUserId.length > 0">
                T.MODIFY_USER_ID = #{modifyUserId},
            </if>
            <if test="modifySysRoleType != null and modifySysRoleType.length > 0">
                T.MODIFY_SYS_ROLE_TYPE = #{modifySysRoleType},
            </if>
            <if test="modifyUserJobName != null and modifyUserJobName.length > 0">
                T.MODIFY_USER_JOB_NAME = #{modifyUserJobName},
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.SETTLE_TYPE = 1
        AND T.SETTLE_MODE = 1
        AND T.TAX_WAYBILL_ID IS NOT NULL
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateBoTaskDetail" parameterType="com.wtyt.bo.bean.BoTaskDetailBean">
        UPDATE T_BO_TRANS_TASK T SET
         <if test="state != null and state != ''">
            T.STATE = #{state},
         </if>
        <if test="nodeId != null and nodeId != ''">
            T.NODE_ID = #{nodeId},
        </if>
        <if test="endTime != null and endTime != ''">
            T.END_TIME = TO_DATE(#{endTime}, 'yyyy-mm-dd hh24:mi:ss'),
        </if>
        <if test="hybState != null and hybState != ''">
            T.HYB_STATE = #{hybState},
        </if>
         T.LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0
          AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateModifyUser">
        UPDATE T_BO_TRANS_TASK T
            SET T.MODIFY_USER_ID = #{modifyUserId},
                T.MODIFY_USER_JOB_NAME = #{modifyUserJobName},
                T.MODIFY_SYS_ROLE_TYPE = #{modifySysRoleType},
                T.MODIFY_DRIVER_ID = #{modifyDriverId},
                T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>
    <update id="batchTransferTask" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_TRANS_TASK T
                <set>
                    T.LAST_MODIFIED_TIME = SYSDATE,
                    <if test="item.toUserId != null and item.toUserId != ''">
                        T.CREATED_USER_ID = #{item.toUserId},
                        <if test="item.createdUserJobName != null and item.createdUserJobName != ''">
                            T.CREATED_USER_JOB_NAME = #{item.createdUserJobName},
                        </if>
                        <if test="item.createdUserSysRoleType != null and item.createdUserSysRoleType != ''">
                            T.CREATED_USER_SYS_ROLE_TYPE = #{item.createdUserSysRoleType},
                        </if>
                    </if>
                    <if test="item.toBelongActSys != null and item.toBelongActSys != ''">
                        T.WAYBILL_BELONG_ACT_SYS = #{item.toBelongActSys},
                    </if>
                    <if test="item.toOrgId != null and item.toOrgId != ''">
                        T.ORG_ID = #{item.toOrgId},
                        T.OWNER_ORG_ID = #{item.toOrgId},
                    </if>
                    <if test="item.serviceFee != null and item.serviceFee != ''">
                        T.SERVICE_FEE = #{item.serviceFee},
                    </if>
                </set>
            WHERE T.BO_TRANS_TASK_ID = #{item.boTransTaskId} AND T.IS_DEL = 0
            <if test="item.fromOrgId != null and item.fromOrgId != ''">
                AND T.ORG_ID = #{item.fromOrgId}
            </if>
            <if test="item.fromBelongActSys != null and item.fromBelongActSys != ''">
                AND T.WAYBILL_BELONG_ACT_SYS = #{item.fromBelongActSys}
            </if>
            <if test="item.toUserId != null and item.toUserId != ''">
                AND T.CREATED_USER_ID = #{item.createdUserId}
            </if>
        </foreach>
    </update>
    <update id="reDistribute" parameterType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        UPDATE T_BO_TRANS_TASK T
        SET
        <if test="transMode != null and transMode.length > 0">
            T.TRANS_MODE = #{transMode},
        </if>
        <if test="unitPrice != null and unitPrice.length > 0">
            T.UNIT_PRICE = #{unitPrice},
        </if>
        <if test="userFreight != null and userFreight.length > 0">
            T.USER_FREIGHT = #{userFreight},
        </if>
        <if test="allFreight != null and allFreight.length > 0">
            T.ALL_FREIGHT = #{allFreight},
        </if>
        <if test="goodsCost != null">
            T.GOODS_COST = #{goodsCost},
        </if>
        <if test="settleMode != null and settleMode.length > 0">
            T.SETTLE_MODE = #{settleMode},
        </if>
        <if test="prepaymentsGascard != null">
            PREPAYMENTS_GASCARD = #{prepaymentsGascard},
        </if>
        <if test="prepaymentsOilcard != null">
            PREPAYMENTS_OILCARD = #{prepaymentsOilcard},
        </if>
        <if test="settleType != null and settleType.length > 0">
            T.SETTLE_TYPE = #{settleType},
        </if>
        <if test="settleTarget != null and settleTarget.length > 0">
            T.SETTLE_TARGET = #{settleTarget},
        </if>
        T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="updateTransTaskPaymentStatus">
        UPDATE T_BO_TRANS_TASK T
        SET T.PAY_STATE = #{payState},
            <if test="payState == '2'.toString()">
                T.PAY_OVER_TIME = SYSDATE,
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        <choose>
            <when test="payState == '0'.toString()">
                AND T.PAY_STATE = 3
            </when>
            <when test="payState == '2'.toString()">
                AND T.PAY_STATE IN (0, 3, 5)
            </when>
            <when test="payState == '3'.toString()">
                AND (T.PAY_STATE IS NULL OR T.PAY_STATE IN (0, 5))
            </when>
            <when test="payState == '5'.toString()">
                AND T.PAY_STATE = 3
            </when>
            <otherwise>
                T.PAY_STATE = -1
            </otherwise>
        </choose>
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="syncTransTaskEnsure">
        UPDATE T_BO_TRANS_TASK T
        SET T.INS_STATE = #{insState},
            <if test="insFee != null">
                T.INS_FEE = #{insFee},
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <select id="queryPreviousGoodsAmountType" resultType="java.lang.String">
        SELECT * FROM (
        SELECT GOODS_AMOUNT_TYPE  FROM SYF.T_BO_TRANS_TASK WHERE IS_DEL =0 AND ORG_ID = #{orgId} AND CREATED_USER_ID = #{userId} AND GOODS_AMOUNT_TYPE IS NOT NULL ORDER BY CREATED_TIME DESC
        ) WHERE  ROWNUM = 1
    </select>
    <select id="queryTransTaskByBoTransTaskIdWithDel" resultType="com.wtyt.dao.bean.syf.BoTransTaskBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.TRANSPORT_TYPE transportType,
            T.TRANS_PATTERN transPattern,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.TRANSPORT_LINE_ID transportLineId,
            T.TRANS_MODE transMode,
            T.OFFER_TYPE offerType,
            T.CREATED_USER_ID createdUserId,
            T.CREATED_USER_SYS_ROLE_TYPE createdUserSysRoleType,
            T.XCY_USER_ID xcyUserId,
            T.NODE_ID nodeId,
            TO_CHAR(T.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            TO_CHAR(T.START_TIME, 'yyyy-mm-dd hh24:mi:ss') startTime,
            T.TRANS_PATTERN transPattern,
            T.MODIFY_USER_ID modifyUserId,
            T.MODIFY_DRIVER_ID modifyDriverId,
            T.MODIFY_SYS_ROLE_TYPE modifySysRoleType,
            T.CREATED_USER_JOB_NAME createdUserJobName,
            T.MODIFY_USER_JOB_NAME modifyUserJobName,
            T.LOADING_PLACE_NAME loadingPlaceName,
            T.UNLOADING_PLACE_NAME unloadingPlaceName,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            TO_CHAR(T.FIRST_RECEIPT_TIME, 'YYYY-MM-DD HH24:MI:SS') firstReceiptTime,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo,
            T.CART_BADGE_COLOR cartBadgeColor,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            T.GOODS_NAME goodsName,
            TO_CHAR(T.GOODS_AMOUNT, 'FM999999990.0000') goodsAmount,
            T.GOODS_AMOUNT_TYPE goodsAmountType,
            T.CUSTOMER_REMARK customerRemark,
            TO_CHAR(NVL(T.ALL_FREIGHT, 0), 'FM999999990.00') allFreight,
            TO_CHAR(NVL(T.USER_FREIGHT, 0), 'FM999999990.00') userFreight,
            TO_CHAR(NVL(T.PREPAYMENTS, 0) , 'FM999999990.00') prepayments,
            TO_CHAR(NVL(T.BACK_FEE, 0) , 'FM999999990.00') backFee,
            TO_CHAR(NVL(T.PREPAYMENTS_OILCARD , 0), 'FM999999990.00') prepaymentsOilcard,
            TO_CHAR(NVL(T.PREPAYMENTS_GASCARD , 0), 'FM999999990.00') prepaymentsGascard,
            TO_CHAR(NVL(T.PREPAYMENTS_BUY_GAS , 0), 'FM999999990.00') prepaymentsBuyGas,
            TO_CHAR(NVL(T.PREPAYMENTS_BUY_OIL , 0), 'FM999999990.00') prepaymentsBuyOil,
            TO_CHAR(NVL(T.FREIGHT_INCR , 0), 'FM999999990.00') freightIncr,
            TO_CHAR(NVL(T.LOSS_FEE , 0), 'FM999999990.00') lossFee,
            TO_CHAR(NVL(T.GOODS_COST , 0), 'FM999999990.00') goodsCost,
            TO_CHAR(NVL(T.INS_FEE , 0), 'FM999999990.00') insFee,
            TO_CHAR(NVL(T.SERVICE_FEE , 0), 'FM999999990.00') serviceFee,
            TO_CHAR(NVL(T.DATA_SERVICE_FEE , 0), 'FM999999990.00') dataServiceFee,
            NVL(t.ADVANCE_PAY_STATE, 0) advancePayState,
            T.LOSS_ENSURE_STATE lossEnsureState,
            T.HYB_STATE hybState,
            T.STATE state,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TRANS_PATTERN transPattern,
            T.OFFER_TYPE offerType,
            T.OWNER_ORG_ID ownerOrgId,
            T.SETTLE_MODE settleMode,
            T.IS_DEL isDel,
            T.CAPACITY_TYPE capacityType,
            T.CAPACITY_TYPE_NAME capacityTypeName,
            T.OPERATE_SCHEME operateScheme,
            T.WAYBILL_BELONG_ACT_SYS waybillBelongActSys,
            T.DRIVER_ID_CARD driverIdCard,
            T.IS_PARTAKE_OPERATE isPartakeOperate
        FROM
            T_BO_TRANS_TASK T
        WHERE
            T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <update id="hideByTaskId">
        UPDATE T_BO_TRANS_TASK T SET IS_DEL=7,LAST_MODIFIED_TIME=SYSDATE WHERE BO_TRANS_TASK_ID=#{taskId} AND IS_DEL=0
    </update>

    <update id="removeHideByTaskId">
        UPDATE T_BO_TRANS_TASK T SET IS_DEL=0,LAST_MODIFIED_TIME=SYSDATE WHERE BO_TRANS_TASK_ID=#{taskId} AND IS_DEL=7
    </update>

    <select id="queryTaxWaybllNoByTaskIds" resultType="java.lang.String">
        SELECT TAX_WAYBILL_NO FROM T_BO_TRANS_TASK WHERE BO_TRANS_TASK_ID IN
        <foreach item="item" collection="taskIds" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
    </select>


</mapper>
