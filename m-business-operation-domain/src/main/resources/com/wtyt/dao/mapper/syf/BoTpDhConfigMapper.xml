<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhConfigMapper">

    <select id="queryDhConfigByOrgId" resultType="com.wtyt.dao.bean.syf.BoTpDhConfigBean">
        SELECT
        BO_TP_DH_CONFIG_ID boTpDhConfigId,
        ORG_ID orgId,
        HEADER_ROW_NUM headerRowNum,
        DATA_START_ROW_NUM dataStartRowNum,
        ORDER_NO_MERGE_FLAG orderNoMergeFlag,
        ORDER_NO_TAX_WAYBILL_NO orderNoTaxWaybillNo,
        CREATE_TASK_UNIT createTaskUnit,
        IMPORT_MAX_ROW_NUM importMaxRowNum,
        ORDER_QUERY_RANGE_DATE orderQueryRangeDate
        FROM
        T_BO_TP_DH_CONFIG
        WHERE
        is_del = 0 AND ORG_ID =#{orgId}
        AND VALID_STATE = 1
    </select>

       <select id="queryDhConfigAdminView" resultType="com.wtyt.dao.bean.syf.BoTpDhConfigBean">
        SELECT
            BO_TP_DH_CONFIG_ID boTpDhConfigId,
            ORG_ID orgId,
            HEADER_ROW_NUM headerRowNum,
            DATA_START_ROW_NUM dataStartRowNum,
            ORDER_NO_MERGE_FLAG orderNoMergeFlag,
            ORDER_NO_TAX_WAYBILL_NO orderNoTaxWaybillNo,
            CREATE_TASK_UNIT createTaskUnit,
            IMPORT_MAX_ROW_NUM importMaxRowNum,
            ACCOUNT_GROUP_ID accountGroupId,
            ORDER_QUERY_RANGE_DATE orderQueryRangeDate
        FROM
            T_BO_TP_DH_CONFIG
        WHERE
            is_del = 0 AND ACCOUNT_GROUP_ID =#{accountGroupId}
          AND VALID_STATE = 1
    </select>

    <select id="getDhConfigInfoById" resultType="com.wtyt.dao.bean.syf.BoTpDhConfigBean">
        SELECT
        BO_TP_DH_CONFIG_ID boTpDhConfigId,
        ORG_ID orgId,
        HEADER_ROW_NUM headerRowNum,
        DATA_START_ROW_NUM dataStartRowNum,
        ORDER_NO_MERGE_FLAG orderNoMergeFlag,
        ORDER_NO_TAX_WAYBILL_NO orderNoTaxWaybillNo,
        CREATE_TASK_UNIT createTaskUnit,
        IMPORT_MAX_ROW_NUM importMaxRowNum,
        ACCOUNT_GROUP_ID accountGroupId,
        ORDER_QUERY_RANGE_DATE orderQueryRangeDate
        FROM
        T_BO_TP_DH_CONFIG
        WHERE
        is_del = 0 AND BO_TP_DH_CONFIG_ID =#{boTpDhConfigId} AND VALID_STATE = 1
    </select>
    <select id="getDhConfigInfoByConfigId" resultType="com.wtyt.dao.bean.syf.BoTpDhConfigBean">
        SELECT
            BO_TP_DH_CONFIG_ID boTpDhConfigId,
            ORG_ID orgId,
            HEADER_ROW_NUM headerRowNum,
            DATA_START_ROW_NUM dataStartRowNum,
            ORDER_NO_MERGE_FLAG orderNoMergeFlag,
            CREATE_TASK_UNIT createTaskUnit
        FROM
            T_BO_TP_DH_CONFIG
        WHERE
            is_del = 0 AND BO_TP_DH_CONFIG_ID =#{boTpDhConfigId}
    </select>

    <insert id="insertBoTpDhConfig">
        INSERT INTO T_BO_TP_DH_CONFIG(BO_TP_DH_CONFIG_ID, ORG_ID, HEADER_ROW_NUM, DATA_START_ROW_NUM, ORDER_NO_MERGE_FLAG,ORDER_NO_TAX_WAYBILL_NO, CREATE_TASK_UNIT, VALID_STATE, OPT_USER_ID, SYS_ROLE_TYPE,ACCOUNT_GROUP_ID,  ORDER_QUERY_RANGE_DATE)
        VALUES(#{boTpDhConfigId}, #{orgId}, #{headerRowNum}, #{dataStartRowNum}, #{orderNoMergeFlag},#{orderNoTaxWaybillNo}, #{createTaskUnit}, #{validState}, #{optUserId}, #{sysRoleType},#{accountGroupId},#{orderQueryRangeDate})
    </insert>

    <update id="invalidAllConfig">
        UPDATE T_BO_TP_DH_CONFIG SET VALID_STATE = 0, LAST_MODIFIED_TIME = SYSDATE WHERE VALID_STATE = 1 AND IS_DEL = 0 AND ORG_ID = #{orgId}
    </update>

     <update id="invalidAllAccountGroupConfig">
        UPDATE T_BO_TP_DH_CONFIG SET VALID_STATE = 0, LAST_MODIFIED_TIME = SYSDATE WHERE VALID_STATE = 1 AND IS_DEL = 0 AND ACCOUNT_GROUP_ID =#{accountGroupId}
    </update>

    <select id="queryDhConfigShareByConfigId" resultType="com.wtyt.dao.bean.syf.BoTpDhConfigShareBean">
        SELECT
            BO_TP_DH_CONFIG_SHARE_ID boTpDhConfigShareId,
            ACCOUNT_GROUP_ID accountGroupId,
            ORG_ID orgId
        FROM
            T_BO_TP_DH_CONFIG_SHARE
        WHERE
            is_del = 0
          AND ACCOUNT_GROUP_ID = #{accountGroupId}
        ORDER BY BO_TP_DH_CONFIG_SHARE_ID ASC
    </select>

    <select  id="getOrdIsGroupBind" resultType="java.lang.Boolean">
        select case when
                exists(select 1 from T_BO_TP_DH_CONFIG_SHARE  WHERE IS_DEL = 0 AND ORG_ID = #{orgId})
                then 1
            else 0 end
        from dual
    </select>

    <select  id="queryDhConfigShareValueByShareId" resultType="com.wtyt.dao.bean.syf.BoTpDhConfigShareBean">
        SELECT BO_TP_DH_CONFIG_SHARE_ID as boTpDhConfigShareId,
            MAX(CASE WHEN CONFIG_KEY = 'ORDER_NO_TAX_WAYBILL_NO' THEN CONFIG_VALUE END) AS orderNoTaxWaybillNo,
            MAX(CASE WHEN CONFIG_KEY = 'CREATE_TASK_UNIT' THEN CONFIG_VALUE END) AS createTaskUnit
        FROM T_BO_TP_DH_CONFIG_SHR_ARG A  WHERE  A.IS_DEL =0 and  A.BO_TP_DH_CONFIG_SHARE_ID IN
            <foreach collection="list" separator="," item="boTpDhConfigShareId" close=")" open="(">
             #{boTpDhConfigShareId}
            </foreach>
        GROUP BY BO_TP_DH_CONFIG_SHARE_ID
    </select>

    <update id="modifyTaskUnitByConfigId">
        UPDATE T_BO_TP_DH_CONFIG
        SET CREATE_TASK_UNIT = #{createTaskUnit},
            ORDER_NO_TAX_WAYBILL_NO =#{orderNoTaxWaybillNo} ,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE  BO_TP_DH_CONFIG_ID =#{boTpDhConfigId}
    </update>

    <select id="queryDhConfigByOrgIdList" resultType="com.wtyt.dao.bean.syf.BoTpDhConfigBean">
        SELECT
            BO_TP_DH_CONFIG_ID boTpDhConfigId,
            ORG_ID orgId,
            HEADER_ROW_NUM headerRowNum,
            DATA_START_ROW_NUM dataStartRowNum,
            ORDER_NO_MERGE_FLAG orderNoMergeFlag,
            ORDER_NO_TAX_WAYBILL_NO orderNoTaxWaybillNo,
            CREATE_TASK_UNIT createTaskUnit,
            IMPORT_MAX_ROW_NUM importMaxRowNum
        FROM
            T_BO_TP_DH_CONFIG
        WHERE
            is_del = 0   AND VALID_STATE = 1
            AND ORG_ID  IN
            <foreach collection="list" separator="," item="orgId" close=")" open="(">
                 #{orgId}
            </foreach>
    </select>

</mapper>
