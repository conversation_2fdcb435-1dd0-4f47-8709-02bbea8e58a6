<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskFeeVrfMapper">

    <update id="mergeIntoTaskFeeVafBatch">
        merge into T_BO_TRANS_TASK_FEE_VRF a
        using (
        <foreach collection="list" item="item" index="index" separator="union" >
            select
                #{item.boTransTaskFeeVrfId} boTransTaskFeeVrfId ,
                #{item.boTransTaskFeeId} boTransTaskFeeId,
                #{item.verifyUserId} verifyUserId ,
                #{item.verifyUserJobName} verifyUserJobName ,
                #{item.verifyUserName} verifyUserName
            from dual
        </foreach>
        ) b on (a.BO_TRANS_TASK_FEE_ID = b.boTransTaskFeeId  AND a.is_del = 0)
        when matched then
            update set VERIFY_TIME = SYSDATE,
                VERIFY_USER_ID = b.verifyUserId,
                VERIFY_USER_JOB_NAME = b.verifyUserJobName,
                VERIFY_USER_NAME = b.verifyUserName,
                last_modified_time = sysdate
        when not matched then
            insert
                (BO_TRANS_TASK_FEE_VRF_ID, BO_TRANS_TASK_FEE_ID, VERIFY_TIME, VERIFY_USER_ID, VERIFY_USER_JOB_NAME,VERIFY_USER_NAME)
                values
                (b.boTransTaskFeeVrfId, b.boTransTaskFeeId, sysdate, b.verifyUserId, b.verifyUserJobName, b.verifyUserName)
    </update>
</mapper>

