package com.wtyt;

import com.dtp.core.spring.EnableDynamicTp;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.wtyt.common.annotation.ExcludeAppComponent;
import com.wtyt.common.annotation.ExcludeJobComponent;

/**
 * 程序入口
 * 
 * <AUTHOR>
 *
 */
@ServletComponentScan
@EnableApolloConfig(value = { "application", "yanfa.money.commons.system", "yanfa.datasource.oracle.syf", "yanfa.datasource.oracle.cim3", "yanfa.money.rabbitmq.syf",
        "yanfa.money.redis.syf" , "yanfa.money.commons.eureka","yanfa.location.config", "org.payCtrl.whitelist"})
@SpringBootApplication
@EnableDynamicTp
@EnableDiscoveryClient
@EnableAsync
@ExcludeAppComponent
@ExcludeJobComponent
public class MBusinessOperationDomainApplication {
	public static void main(String[] args) {
		SpringApplication.run(MBusinessOperationDomainApplication.class, args);
	}

}
