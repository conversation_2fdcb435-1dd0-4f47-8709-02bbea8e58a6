<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpStowageOrderRelMapper">

    <insert id="insert">
	INSERT
		INTO
		T_BO_TP_STOWAGE_ORDER_REL (
		BO_TP_STOWAGE_ORDER_REL_ID,
		BO_TRANS_ORDER_ID,
		BO_TP_STOWAGE_RECORD_ID,
		STOWAGE_STATE,
		CANCEL_FROM_SOURCE,
		IS_DEL,
		CREATED_TIME,
		LAST_MODIFIED_TIME,
		NOTE)
	VALUES (
		#{boTpStowageOrderRelId},
		#{boTransOrderId},
		#{boTpStowageRecordId},
		#{stowageState},
		#{cancelFromSource},
		0,
		SY<PERSON>AT<PERSON>,
		SY<PERSON><PERSON><PERSON>,
		#{note})
    </insert>
	<insert id="batchInsert">
		INSERT
		INTO
		T_BO_TP_STOWAGE_ORDER_REL (
		BO_TP_STOWAGE_ORDER_REL_ID,
		BO_TRANS_ORDER_ID,
		BO_TP_STOWAGE_RECORD_ID,
		STOWAGE_STATE,
		CANCEL_FROM_SOURCE,
		IS_DEL,
		CREATED_TIME,
		LAST_MODIFIED_TIME,
		NOTE)
		SELECT
		A.*
		FROM (
		<foreach item="item" index="index" collection="list" separator="UNION ALL">
			SELECT
			#{item.boTpStowageOrderRelId} boTpStowageOrderRelId,
			#{item.boTransOrderId} boTransOrderId,
			#{item.boTpStowageRecordId} boTpStowageRecordId,
			#{item.stowageState} stowageState,
			#{item.cancelFromSource} cancelFromSource,
			0 isDel,
			SYSDATE createdTime,
			SYSDATE lastModifiedTime,
			NULL note
			FROM DUAL
		</foreach>
		) A
	</insert>
	<update id="cancelStowage">
		UPDATE
			T_BO_TP_STOWAGE_ORDER_REL
		SET
		<if test="stowageState != null and stowageState != ''">
			STOWAGE_STATE = #{stowageState},
		</if>
		<if test="cancelFromSource != null and cancelFromSource != ''">
			CANCEL_FROM_SOURCE = #{cancelFromSource},
		</if>
		IS_DEL = 1,
		LAST_MODIFIED_TIME = SYSDATE
		WHERE
		BO_TP_STOWAGE_ORDER_REL_ID = #{boTpStowageOrderRelId}
	</update>
	<select id="selectByRecordId" resultType="com.wtyt.dao.bean.syf.BoTpStowageOrderRelBean">
		SELECT
			BO_TP_STOWAGE_ORDER_REL_ID boTpStowageOrderRelId,
			BO_TRANS_ORDER_ID boTransOrderId,
			BO_TP_STOWAGE_RECORD_ID boTpStowageRecordId,
			STOWAGE_STATE stowageState
		FROM
			T_BO_TP_STOWAGE_ORDER_REL
		WHERE
			IS_DEL = 0
			AND BO_TP_STOWAGE_RECORD_ID = #{recordId}
	</select>
	<select id="checkOrderHasStowage" resultType="java.lang.String">
	SELECT
		1
	FROM
		DUAL
	WHERE
		EXISTS (
		SELECT
			1
		FROM
			T_BO_TP_STOWAGE_ORDER_REL
		WHERE
			IS_DEL = 0
			<if test="state !=null and state != ''">
				AND STOWAGE_STATE = #{state}
			</if>
			AND BO_TRANS_ORDER_ID IN
			<foreach collection="list" item="item" close=")" open="(" separator=",">
				#{item}
			</foreach>
			)
	</select>

	<select id="selectByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoTpStowageOrderRelBean">
		SELECT
			BO_TP_STOWAGE_ORDER_REL_ID boTpStowageOrderRelId,
			BO_TRANS_ORDER_ID boTransOrderId,
			BO_TP_STOWAGE_RECORD_ID boTpStowageRecordId,
			STOWAGE_STATE stowageState
		FROM
			T_BO_TP_STOWAGE_ORDER_REL
		WHERE
			IS_DEL = 0
			AND BO_TRANS_ORDER_ID IN
			<foreach collection="list" item="item" close=")" open="(" separator=",">
				#{item}
			</foreach>
		ORDER BY CREATED_TIME
	</select>


	<select id="selectStowageByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoTpStowageOrderRelBean">
		SELECT
		BO_TP_STOWAGE_ORDER_REL_ID boTpStowageOrderRelId,
		BO_TRANS_ORDER_ID boTransOrderId,
		BO_TP_STOWAGE_RECORD_ID boTpStowageRecordId,
		DECODE(STOWAGE_STATE,1,1,2,2,3,2,4,1,1) stowageState
		FROM
		T_BO_TP_STOWAGE_ORDER_REL
		WHERE
		IS_DEL = 0
		AND BO_TRANS_ORDER_ID IN
		<foreach collection="list" item="item" close=")" open="(" separator=",">
			#{item}
		</foreach>
		ORDER BY stowageState DESC
	</select>


</mapper>