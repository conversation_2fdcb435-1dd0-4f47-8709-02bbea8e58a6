<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardXTJWMapper">


    <sql id="dateAndOrgListCondition">
        <if test="startDate !=null and startDate!=''">
            <![CDATA[ AND T.CREATED_TIME >= to_date(#{startDate},'yyyy-MM-dd') ]]>
        </if>
        <if test="endDate !=null and endDate !=''">
            <![CDATA[ AND T.CREATED_TIME <= to_date(#{endDate} || '23:59:59','yyyy-MM-dd Hh24:mi:ss') ]]>
        </if>
        AND T.ORG_ID IN
        <foreach collection="orgIdList" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </sql>


    <select id="orgTotalAmount" resultType="com.wtyt.board.bean.TaskAmountBean">
        SELECT
        TO_CHAR(NVL(SUM(GUARANTEE_AMOUNT), 0), 'FM999999990.00') guaranteeAmount ,
        TO_CHAR(NVL(SUM(decode(rn, 1, USER_FREIGHT, 0)),0) , 'FM999999990.00') userFreight,
        count(DISTINCT BO_TRANS_TASK_ID) taskCount
        FROM
        (
        SELECT
        A.GUARANTEE_AMOUNT,
        A.USER_FREIGHT,
        A.BO_TRANS_TASK_ID,
        ROW_NUMBER() OVER(PARTITION BY BO_TRANS_TASK_ID ORDER BY GUARANTEE_AMOUNT DESC) RN
        FROM
        (
        SELECT
        NVL(G.GUARANTEE_AMOUNT, 0) GUARANTEE_AMOUNT , NVL(T.USER_FREIGHT, 0) USER_FREIGHT , T.BO_TRANS_TASK_ID BO_TRANS_TASK_ID
        FROM T_BO_TRANS_TASK T LEFT JOIN T_BO_TRANS_TASK_EXTRA E ON  T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE G ON E.BO_TASK_DRIVER_GUARANTEE_ID = G.BO_TASK_DRIVER_GUARANTEE_ID
        WHERE T.IS_DEL = 0  AND E.IS_DEL = 0  AND G.IS_DEL = 0 AND G.GUARANTEE_STATE = 5
        AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL
        AND G.GUARANTEE_AMOUNT >0
        <include refid="dateAndOrgListCondition"/>
        UNION ALL
        SELECT
        NVL(G.GUARANTEE_AMOUNT, 0) GUARANTEE_AMOUNT ,
        CASE WHEN T.IS_DEL = 0  AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL  THEN  NVL(T.USER_FREIGHT, 0)   ELSE 0 END AS USER_FREIGHT,
        CASE WHEN T.IS_DEL = 0  AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL  THEN  T.BO_TRANS_TASK_ID ELSE NULL END AS  BO_TRANS_TASK_ID
        FROM
        T_BO_TASK_DRIVER_GUARANTEE G
        LEFT JOIN T_BO_TRANS_TASK T ON  G.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_EXTRA E ON  T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        WHERE
        G.is_del = 0 AND G.GUARANTEE_AMOUNT >0  AND G.GUARANTEE_STATE = 4
        <include refid="dateAndOrgListCondition"/>)A )
    </select>

    <select id="orgAmountSortList" resultType="com.wtyt.board.bean.TaskAmountBean">
        SELECT
        ORG_ID orgId,
        TO_CHAR(NVL(SUM(GUARANTEE_AMOUNT), 0), 'FM999999990.00') guaranteeAmount ,
        TO_CHAR(NVL(SUM(decode(rn, 1, USER_FREIGHT, 0)),0) , 'FM999999990.00') userFreight,
        count(DISTINCT BO_TRANS_TASK_ID) taskCount
        FROM(SELECT
        A.ORG_ID,
        A.GUARANTEE_AMOUNT,
        A.USER_FREIGHT,
        A.BO_TRANS_TASK_ID,
        ROW_NUMBER() OVER(PARTITION BY BO_TRANS_TASK_ID ORDER BY GUARANTEE_AMOUNT DESC) RN
        from (
        SELECT
        T.ORG_ID ,NVL(G.GUARANTEE_AMOUNT, 0) GUARANTEE_AMOUNT , NVL(T.USER_FREIGHT, 0) USER_FREIGHT , T.BO_TRANS_TASK_ID
        BO_TRANS_TASK_ID
        FROM T_BO_TRANS_TASK T
        left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID=E.BO_TRANS_TASK_ID
        left JOIN T_BO_TASK_DRIVER_GUARANTEE G ON E.BO_TASK_DRIVER_GUARANTEE_ID =G.BO_TASK_DRIVER_GUARANTEE_ID
        WHERE T.IS_DEL = 0 AND E.IS_DEL=0 AND G.IS_DEL=0 AND G.GUARANTEE_STATE = 5
        AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL
        AND G.GUARANTEE_AMOUNT >0
        <include refid="dateAndOrgListCondition"/>
        UNION ALL
        SELECT
        T.ORG_ID ,
        NVL(G.GUARANTEE_AMOUNT, 0) GUARANTEE_AMOUNT ,
        CASE WHEN T.IS_DEL = 0  AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL  THEN  NVL(T.USER_FREIGHT, 0)   ELSE 0 END AS USER_FREIGHT,
        CASE WHEN T.IS_DEL = 0  AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL  THEN  T.BO_TRANS_TASK_ID ELSE NULL END AS  BO_TRANS_TASK_ID
        FROM
        T_BO_TASK_DRIVER_GUARANTEE G
        LEFT JOIN T_BO_TRANS_TASK T ON G.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_EXTRA E ON  T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        WHERE
        G.is_del = 0 AND G.GUARANTEE_AMOUNT >0 AND G.GUARANTEE_STATE = 4
        <include refid="dateAndOrgListCondition"/>)A )
        group by ORG_ID
        order by NVL(SUM(GUARANTEE_AMOUNT), 0) desc, taskCount desc
    </select>

    <select id="groupAmountSortList" resultType="com.wtyt.board.bean.TaskAmountBean">
        SELECT
        ORG_ID orgId,
        GROUP_NAME groupName,
        TO_CHAR(NVL(SUM(GUARANTEE_AMOUNT), 0), 'FM999999990.00')  guaranteeAmount from
        (
        SELECT T.ORG_ID,UG.GROUP_NAME,NVL(G.GUARANTEE_AMOUNT, 0) GUARANTEE_AMOUNT
        FROM T_BO_TRANS_TASK T
        left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID=E.BO_TRANS_TASK_ID
        left JOIN T_BO_TASK_DRIVER_GUARANTEE G ON E.BO_TASK_DRIVER_GUARANTEE_ID =G.BO_TASK_DRIVER_GUARANTEE_ID
        left join T_BO_BOARD_USER_GROUP UG ON UG.ORG_ID =T.ORG_ID   and  UG.USER_ID =G.DISPATCH_USER_ID
        WHERE T.IS_DEL = 0  AND E.IS_DEL=0  AND G.IS_DEL=0  AND UG.IS_DEL=0  AND G.GUARANTEE_STATE =5
        AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL
        AND G.GUARANTEE_AMOUNT >0
        <include refid="dateAndOrgListCondition"/>
        UNION ALL
        SELECT
        T.ORG_ID,UG.GROUP_NAME,NVL(G.GUARANTEE_AMOUNT, 0) GUARANTEE_AMOUNT
        FROM
        T_BO_TASK_DRIVER_GUARANTEE G LEFT JOIN T_BO_TRANS_TASK T ON G.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        left join T_BO_BOARD_USER_GROUP UG ON UG.ORG_ID =T.ORG_ID   and  UG.USER_ID =G.DISPATCH_USER_ID
        WHERE
        G.is_del = 0 AND G.GUARANTEE_AMOUNT >0 AND UG.IS_DEL=0   AND G.GUARANTEE_STATE = 4
        <include refid="dateAndOrgListCondition"/>
        )A
        group by ORG_ID ,GROUP_NAME
        order by NVL(SUM(GUARANTEE_AMOUNT), 0) desc
    </select>


    <sql id="userDescCondition">
        <include refid="dateAndOrgListCondition"/>
        <if test="groupName !=null and groupName !=''">
            AND UG.GROUP_NAME = #{groupName}
        </if>
        <if test="userIdList !=null and userIdList.size()>0">
            AND G.DISPATCH_USER_ID IN
            <foreach collection="userIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>



    <select id="userDescList" resultType="com.wtyt.board.bean.TaskAmountBean" parameterType="com.wtyt.board.bean.BoardXTJWDbQueryBean">
        SELECT
        ORG_ID orgId,
        GROUP_NAME groupName,
        DISPATCH_USER_ID dispatchUserId,
        TO_CHAR(NVL(SUM(GUARANTEE_AMOUNT), 0), 'FM999999990.00') guaranteeAmount ,
        TO_CHAR(NVL(SUM(decode(rn, 1, USER_FREIGHT, 0)),0) , 'FM999999990.00') userFreight,
        count(DISTINCT BO_TRANS_TASK_ID) taskCount
        FROM (SELECT
        A.ORG_ID,
        A.GROUP_NAME,
        A.DISPATCH_USER_ID,
        A.GUARANTEE_AMOUNT,
        A.USER_FREIGHT,
        A.BO_TRANS_TASK_ID,
        ROW_NUMBER() OVER(PARTITION BY BO_TRANS_TASK_ID ORDER BY GUARANTEE_AMOUNT DESC) RN
        FROM (
        SELECT
        T.ORG_ID ,
        UG.GROUP_NAME ,
        G.DISPATCH_USER_ID ,
        NVL(G.GUARANTEE_AMOUNT, 0) GUARANTEE_AMOUNT ,
        NVL(T.USER_FREIGHT, 0) USER_FREIGHT ,
        T.BO_TRANS_TASK_ID
        FROM T_BO_TRANS_TASK T
        left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID=E.BO_TRANS_TASK_ID
        left JOIN T_BO_TASK_DRIVER_GUARANTEE G ON E.BO_TASK_DRIVER_GUARANTEE_ID =G.BO_TASK_DRIVER_GUARANTEE_ID
        left join T_BO_BOARD_USER_GROUP UG ON UG.ORG_ID =T.ORG_ID AND UG.IS_DEL=0 and UG.USER_ID =G.DISPATCH_USER_ID
        WHERE T.IS_DEL = 0 AND E.IS_DEL=0 AND G.IS_DEL=0 AND G.GUARANTEE_STATE =5
        AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL
        AND G.GUARANTEE_AMOUNT >0
        <include refid="userDescCondition"/>
        UNION ALL
        SELECT
        T.ORG_ID ,
        UG.GROUP_NAME ,
        G.DISPATCH_USER_ID ,
        NVL(G.GUARANTEE_AMOUNT, 0) GUARANTEE_AMOUNT ,
        CASE WHEN T.IS_DEL = 0  AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL  THEN  NVL(T.USER_FREIGHT, 0)   ELSE 0 END AS USER_FREIGHT,
        CASE WHEN T.IS_DEL = 0  AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL  THEN  T.BO_TRANS_TASK_ID ELSE NULL END AS  BO_TRANS_TASK_ID
        FROM
        T_BO_TASK_DRIVER_GUARANTEE G
        LEFT JOIN T_BO_TRANS_TASK T ON G.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID=E.BO_TRANS_TASK_ID
        left join T_BO_BOARD_USER_GROUP UG ON UG.ORG_ID =T.ORG_ID AND UG.IS_DEL=0  and  UG.USER_ID =G.DISPATCH_USER_ID
        WHERE
        G.is_del = 0 AND G.GUARANTEE_AMOUNT >0   AND G.GUARANTEE_STATE = 4
        <include refid="userDescCondition"/>)A)
        group by ORG_ID,GROUP_NAME,DISPATCH_USER_ID
        <if test="sortField !=null and sortField != ''">
            <choose>
                <when test="sortField=='guaranteeAmount'">
                    order by NVL(SUM(GUARANTEE_AMOUNT), 0)
                </when>
                <when test="sortField=='userFreight'">
                    order by NVL(SUM(USER_FREIGHT), 0)
                </when>
                <otherwise>
                    order by taskCount
                </otherwise>
            </choose>
        </if>
        <if test="sortType !=null and sortType != ''">
            <choose>
                <when test="sortType=='0'.toString()">
                    asc
                </when>
                <otherwise>
                    desc
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="userDescTotal" resultType="com.wtyt.board.bean.TaskAmountBean" parameterType="com.wtyt.board.bean.BoardXTJWDbQueryBean">
        SELECT
        TO_CHAR(NVL(SUM(GUARANTEE_AMOUNT), 0), 'FM999999990.00') guaranteeAmount ,
        TO_CHAR(NVL(SUM(decode(rn, 1, USER_FREIGHT, 0)),0) , 'FM999999990.00') userFreight,
        count(DISTINCT BO_TRANS_TASK_ID) taskCount
        FROM
        (
        SELECT
        A.GUARANTEE_AMOUNT,
        A.USER_FREIGHT,
        A.BO_TRANS_TASK_ID,
        ROW_NUMBER() OVER(PARTITION BY BO_TRANS_TASK_ID ORDER BY GUARANTEE_AMOUNT DESC) RN
        FROM
        (
        SELECT
        NVL(G.GUARANTEE_AMOUNT, 0) GUARANTEE_AMOUNT , NVL(T.USER_FREIGHT, 0) USER_FREIGHT , T.BO_TRANS_TASK_ID BO_TRANS_TASK_ID
        FROM T_BO_TRANS_TASK T
        left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID=E.BO_TRANS_TASK_ID
        left JOIN T_BO_TASK_DRIVER_GUARANTEE G ON E.BO_TASK_DRIVER_GUARANTEE_ID =G.BO_TASK_DRIVER_GUARANTEE_ID
        left join T_BO_BOARD_USER_GROUP UG ON UG.ORG_ID =T.ORG_ID AND UG.IS_DEL=0 and UG.USER_ID =G.DISPATCH_USER_ID
        WHERE T.IS_DEL = 0 AND E.IS_DEL = 0 AND G.IS_DEL = 0 AND G.GUARANTEE_STATE = 5
        AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL
        AND G.GUARANTEE_AMOUNT >0
        <include refid="userDescCondition"/>
        UNION ALL
        SELECT
        NVL(G.GUARANTEE_AMOUNT, 0) GUARANTEE_AMOUNT ,
        CASE WHEN T.IS_DEL = 0  AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL  THEN  NVL(T.USER_FREIGHT, 0)   ELSE 0 END AS USER_FREIGHT,
        CASE WHEN T.IS_DEL = 0  AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL  THEN  T.BO_TRANS_TASK_ID ELSE NULL END AS  BO_TRANS_TASK_ID
        FROM
        T_BO_TASK_DRIVER_GUARANTEE G
        LEFT JOIN T_BO_TRANS_TASK T ON G.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID=E.BO_TRANS_TASK_ID
        left join T_BO_BOARD_USER_GROUP UG ON UG.ORG_ID =T.ORG_ID  AND UG.IS_DEL=0  and  UG.USER_ID =G.DISPATCH_USER_ID
        WHERE
        G.is_del = 0 AND G.GUARANTEE_AMOUNT >0 AND G.GUARANTEE_STATE = 4
        <include refid="userDescCondition"/>)A
        )
    </select>




</mapper>
