package com.wtyt.commons.toolkits;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

public class ThreadPoolToolkit {
    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolToolkit.class);

    public static ThreadPoolTaskExecutor creatThreadPoolTaskExecutor(Map<String, String> properties) {
        return creatThreadPoolTaskExecutor(properties, null, new ThreadPoolExecutor.CallerRunsPolicy(), null);
    }

    public static ThreadPoolTaskExecutor creatThreadPoolTaskExecutor(Map<String, String> properties,
        String threadNamePrefix) {
        return creatThreadPoolTaskExecutor(properties, threadNamePrefix, new ThreadPoolExecutor.CallerRunsPolicy(),
            null);
    }

    public static ThreadPoolTaskExecutor creatThreadPoolTaskExecutor(Map<String, String> properties,
        String threadNamePrefix, RejectedExecutionHandler handler, TaskDecorator taskDecorator) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Integer.parseInt(properties.get("core.size")));
        executor.setMaxPoolSize(Integer.parseInt(properties.get("max.size")));
        executor.setQueueCapacity(Integer.parseInt(properties.get("queue.size")));
        executor.setKeepAliveSeconds(Integer.parseInt(properties.get("keep.alive")));
        executor.setThreadNamePrefix(properties.get("name.prefix") + StringUtils.defaultString(threadNamePrefix));
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(600);
        executor.setRejectedExecutionHandler(handler);
        executor.initialize();
        executor.setTaskDecorator(taskDecorator);
        return executor;
    }

}
