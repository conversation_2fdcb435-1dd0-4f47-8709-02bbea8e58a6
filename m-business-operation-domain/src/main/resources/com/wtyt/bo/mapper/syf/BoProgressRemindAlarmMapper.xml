<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoProgressRemindAlarmMapper">
    
    <select id="getMonthProgressRemindInfo" resultType="BoXxlJobAlarmBean">
    
        select org_id orgId,
       count(1) totalNum,
       sum(late_start_state) sendTimeOutNum,
       sum(start_state) sendNum,
       sum(dispach_state) dispatchNum
  from (select T.ORG_ID,
               case
                 when T.start_time is not null and
                      l.kpi_deal_line_time is not null and
                      T.start_time > l.kpi_deal_line_time then
                  1
                 else
                  0
               end late_start_state,
               case
                 when T.start_time is not null then
                  1
                 else
                  0
               end start_state,
               case
                 when T.cart_badge_no is not null then
                  1
                 else
                  0
               end dispach_state
        
          from T_BO_TRANS_TASK T
          left join T_BO_TRANS_NODE_DEAD_LINE L
            on T.BO_TRANS_TASK_ID = l.BO_TRANS_TASK_ID
           and l.node_id = 600
         where T.is_del = 0
           and T.org_id= #{orgId}
           and T.created_time >= trunc(sysdate, 'mm'))
 group by org_id
    
    </select>
    <select id="getDaysProgressRemindInfo" resultType="com.wtyt.bo.bean.BoXxlJobAlarmBean">
         select org_id orgId,
               created_time createdDate,
               count(1) totalNum,
               sum(late_start_state) sendTimeOutNum,
               sum(start_state) sendNum,
               sum(dispach_state) dispatchNum
          from (select T.ORG_ID,
                       to_char(T.created_time,'yyyy-mm-dd') created_time,
                       case
                         when T.start_time is not null and
                              l.kpi_deal_line_time is not null and
                              T.start_time > l.kpi_deal_line_time then
                          1
                         else
                          0
                       end late_start_state,
                       case
                         when T.start_time is not null then
                          1
                         else
                          0
                       end start_state,
                       case
                         when T.cart_badge_no is not null then
                          1
                         else
                          0
                       end dispach_state
                  from T_BO_TRANS_TASK T
                  left join T_BO_TRANS_NODE_DEAD_LINE L
                    on T.BO_TRANS_TASK_ID = l.BO_TRANS_TASK_ID
                   and l.node_id = 600
                 where T.is_del = 0
                   and T.org_id= #{orgId}
                   and T.created_time >= to_date(#{createdTimeBefore},'yyyy-mm-dd hh24:mi:ss')
                   )
         group by org_id,created_time
    
    </select>

</mapper>