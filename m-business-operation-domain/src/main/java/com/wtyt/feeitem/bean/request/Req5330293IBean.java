package com.wtyt.feeitem.bean.request;

import com.wtyt.common.rpc.bean.base.BoTokenBean;

/**
 * 
 */
public class Req5330293IBean extends BoTokenBean {

    private String boTransTaskId;//运输任务ID
    private String taxWaybillId;
    private String orgId;
    private String feeItemType;//类型 ,2:运费增补,1:运费扣减

    public String getBoTransTaskId() {
        return boTransTaskId;
    }

    public void setBoTransTaskId(String boTransTaskId) {
        this.boTransTaskId = boTransTaskId;
    }

    public String getTaxWaybillId() {
        return taxWaybillId;
    }

    public void setTaxWaybillId(String taxWaybillId) {
        this.taxWaybillId = taxWaybillId;
    }

    @Override
    public String getOrgId() {
        return orgId;
    }

    @Override
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getFeeItemType() {
        return feeItemType;
    }

    public void setFeeItemType(String feeItemType) {
        this.feeItemType = feeItemType;
    }
}
