<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardGuaranteeMapper">

    <select id="queryWithDrawGuaranteeByOrgIdList" resultType="com.wtyt.board.bean.BoardGuaranteeBean">
        SELECT
        count(1) AS withDrawNum ,
        TO_CHAR(NVL(SUM(T.GUARANTEE_PAY_AMOUNT), 0), 'FM9999999990.00') AS withDrawAmount
        FROM
        T_BO_TASK_DRIVER_GUARANTEE T
        LEFT JOIN T_BO_TRANS_TASK E ON
        T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        WHERE
        T.IS_DEL = 0
        AND T.GUARANTEE_STATE =4
        AND T.GUARANTEE_WITHDRAW_STATE=0
        AND T.CAPITAL_FLOW_CHANNEL IS NULL
        AND E.ORG_ID IN
        <foreach collection="orgIdList" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </select>

    <select id="queryRefundGuaranteeByOrgIdList" resultType="com.wtyt.board.bean.BoardGuaranteeBean">
        SELECT
        count(1) AS refundNum,
        TO_CHAR(NVL(SUM(T.GUARANTEE_PAY_AMOUNT), 0), 'FM9999999990.00') AS  refundAmount
        FROM
        T_BO_TASK_DRIVER_GUARANTEE T
        LEFT JOIN T_BO_TRANS_TASK E ON
        T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        WHERE
        T.IS_DEL = 0
        AND T.GUARANTEE_STATE =2
        AND T.CAPITAL_FLOW_CHANNEL IS NULL
        AND E.ORG_ID IN
        <foreach collection="orgIdList" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
    </select>


    <select id="queryGuaranteeList" parameterType="com.wtyt.board.bean.BoardGuaranteeDbQueryBean" resultType="com.wtyt.board.bean.BoardGuaranteeDetailBean">
        SELECT
        G.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
        TO_CHAR(G.GUARANTEE_PAY_TIME, 'MM-DD HH24:MI:SS') guaranteePayTime,
        T.ORG_ID  orgId,
        G.DRIVER_NAME driverName,
        G.MOBILE_NO    mobileNo,
        G.DISPATCH_USER_ID dispatchUserId,
        G.JOB_NAME jobName,
        TO_CHAR(NVL(G.GUARANTEE_AMOUNT, 0), 'FM9999999990.00') guaranteeAmount,
        TO_CHAR(NVL(G.GUARANTEE_PAY_AMOUNT, 0), 'FM9999999990.00') guaranteePayAmount,
        G.GUARANTEE_STATE guaranteeState,
        G.GUARANTEE_WITHDRAW_STATE   guaranteeWithdrawState,
        G.GUARANTEE_CHANNEL guaranteeChannel,
        G.GUARANTEE_DISPATCH_CAR_RULE guaranteeDispatchCarRule,
        G.GUARANTEE_DEAL_USER_ID guaranteeDealUserId
        FROM
        T_BO_TASK_DRIVER_GUARANTEE G
        LEFT JOIN T_BO_TRANS_TASK T ON
        T.BO_TRANS_TASK_ID = G.BO_TRANS_TASK_ID
        WHERE
        <include refid="queryGuaranteeListCondition"/>
    </select>

    <sql id="queryGuaranteeListCondition">
        G.IS_DEL = 0
        AND  G.GUARANTEE_PAY_TIME IS NOT NULL
        AND  G.CAPITAL_FLOW_CHANNEL IS NULL
        <if test="payTimeStart !=null and payTimeStart!=''">
            AND G.GUARANTEE_PAY_TIME &gt;= to_date(#{payTimeStart,jdbcType=TIMESTAMP},'yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="payTimeEnd !=null and payTimeEnd !=''">
            AND G.GUARANTEE_PAY_TIME &lt;= to_date(#{payTimeEnd,jdbcType=TIMESTAMP},'yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="driverInfo !=null and driverInfo !=''">
            AND  (G.DRIVER_NAME = #{driverInfo} OR G.MOBILE_NO = #{driverInfo})
        </if>
        <if test="guaranteeWithdrawState!=null and guaranteeWithdrawState!=''">
            AND G.GUARANTEE_WITHDRAW_STATE= #{guaranteeWithdrawState}
        </if>
        <if test="guaranteeStateList !=null and guaranteeStateList.size()>0">
            AND G.GUARANTEE_STATE IN
            <foreach collection="guaranteeStateList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="orgIdList !=null and orgIdList.size()>0">
            AND T.ORG_ID IN
            <foreach collection="orgIdList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        order by G.GUARANTEE_PAY_TIME desc
    </sql>

    <select id="queryGuaranteeDownloadList" parameterType="com.wtyt.board.bean.BoardGuaranteeDbQueryBean" resultType="com.wtyt.board.bean.BoardGuaranteeDownloadBean">
        SELECT
        G.BO_TASK_DRIVER_GUARANTEE_ID boTaskDriverGuaranteeId,
        TO_CHAR(G.GUARANTEE_PAY_TIME, 'YYYY/MM/DD HH24:MI:SS') guaranteePayTime,
        T.ORG_ID  orgId,
        G.DRIVER_NAME driverName,
        G.MOBILE_NO    mobileNo,
        G.CART_BADGE_NO cartBadgeNo,
        G.DISPATCH_USER_ID dispatchUserId,
        G.JOB_NAME jobName,
        TO_CHAR(NVL(G.GUARANTEE_AMOUNT, 0), 'FM9999999990.00') guaranteeAmount,
        TO_CHAR(NVL(G.GUARANTEE_PAY_AMOUNT, 0), 'FM9999999990.00') guaranteePayAmount,
        G.GUARANTEE_STATE guaranteeState,
        G.GUARANTEE_WITHDRAW_STATE   guaranteeWithdrawState,
        G.GUARANTEE_DEAL_REASON guaranteeDealReason,
        G.GUARANTEE_CHANNEL guaranteeChannel,
        TO_CHAR(G.GUARANTEE_DEAL_TIME, 'YYYY/MM/DD HH24:MI:SS') guaranteeDealTime,
        G.GUARANTEE_DISPATCH_CAR_RULE guaranteeDispatchCarRule,

        T.START_PROVINCE_NAME startProvinceName,
        T.START_CITY_NAME startCityName,
        T.START_COUNTY_NAME startCountyName,
        T.END_PROVINCE_NAME endProvinceName,
        T.END_CITY_NAME endCityName,
        T.END_COUNTY_NAME endCountyName,
        TE.LOADING_ADDRESS_NAME loadingAddressName,
        TE.UNLOADING_ADDRESS_NAME unloadingAddressName,
        TE.CART_TYPE cartType,
        TE.CART_LENGTH cartLength,
        TO_CHAR(T.USER_FREIGHT, 'FM999999990.00') userFreight,
        T.GOODS_NAME goodsName,
        CASE
        WHEN T.GOODS_AMOUNT - TRUNC(T.GOODS_AMOUNT)= 0 THEN
        TO_CHAR(TRUNC(T.GOODS_AMOUNT), 'FM999999990')
        ELSE
        TO_CHAR(T.GOODS_AMOUNT, 'FM999999990.9999')
        END goodsAmount,
        T.GOODS_AMOUNT_TYPE goodsAmountType,
        T.TAX_WAYBILL_NO taxWaybillNo,
        TO_CHAR(T.START_TIME, 'YYYY/MM/DD') startTime,
        TE.BO_TASK_DRIVER_GUARANTEE_ID taskBindGuaranteeId
        FROM
        T_BO_TASK_DRIVER_GUARANTEE G
        LEFT JOIN T_BO_TRANS_TASK T ON T.BO_TRANS_TASK_ID = G.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TE ON TE.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        WHERE
        <include refid="queryGuaranteeListCondition"/>
    </select>

</mapper>
