<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhStaticColCfgMapper">

    <select id="queryDhStaticConfig" resultType="com.wtyt.dao.bean.syf.BoTpDhStaticColCfgBean">
        SELECT HEADER_NAME headerName, BO_TP_DH_STATIC_COL_ID boTpDhStaticColId From T_BO_TP_DH_STATIC_COL_CFG WHERE IS_DEL = 0 AND ORG_ID = #{orgId} ORDER BY CREATED_TIME ASC
    </select>

    <select id="queryDhStaticConfigAdminView" resultType="com.wtyt.dao.bean.syf.BoTpDhStaticColCfgBean">
        SELECT HEADER_NAME headerName, BO_TP_DH_STATIC_COL_ID boTpDhStaticColId From T_BO_TP_DH_STATIC_COL_CFG
        WHERE IS_DEL = 0 AND ACCOUNT_GROUP_ID = #{accountGroupId} ORDER BY CREATED_TIME
    </select>

    <insert id="batchInsert">
        INSERT INTO T_BO_TP_DH_STATIC_COL_CFG(BO_TP_DH_STATIC_COL_CFG_ID, HEADER_NAME, BO_TP_DH_STATIC_COL_ID,ORG_ID)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTpDhStaticColCfgId} BO_TP_DH_STATIC_COL_CFG_ID,
            #{item.headerName} HEADER_NAME,
            #{item.boTpDhStaticColId} BO_TP_DH_STATIC_COL_ID,
            #{item.orgId} ORG_ID
            FROM
            DUAL
        </foreach>
    </insert>

    <update id="deleteByOrgIds">
        UPDATE T_BO_TP_DH_STATIC_COL_CFG SET IS_DEL = 1 WHERE ORG_ID IN
        <foreach collection="list" separator="," item="orgId" close=")" open="(">
            #{orgId}
        </foreach>
    </update>
</mapper>
