<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.DriverFollowRecordMapper">

    <resultMap type="com.wtyt.dao.bean.syf.DriverFollowRecordBean" id="DriverFollowRecordMap">
        <result property="driverFollowRecordId" column="DRIVER_FOLLOW_RECORD_ID" jdbcType="VARCHAR"/>
        <result property="mobileNo" column="MOBILE_NO" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="followTime" column="FOLLOW_TIME" jdbcType="VARCHAR"/>
        <result property="businessType" column="BUSINESS_TYPE" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="driverId" column="DRIVER_ID" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="list" resultMap="DriverFollowRecordMap">
        SELECT
        BO_TRANS_TASK_ID
        FROM
        T_DRIVER_FOLLOW_RECORD
        WHERE
        BUSINESS_TYPE = 1
        AND BO_TRANS_TASK_ID IN
        <foreach collection="taskIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND IS_DEL = 0
    </select>

</mapper>