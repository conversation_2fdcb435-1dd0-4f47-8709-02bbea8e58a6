<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhOrderByMapper">

    <select id="queryOrderByStr" resultType="com.wtyt.dao.bean.syf.BoTpDhOrderByBean">
        SELECT  * FROM (
        SELECT * FROM (
            <!-- 完全匹配  -->
            SELECT
                BO_TP_DH_ORDER_BY_ID boTpDhOrderById,
                ORG_ID orgId,
                TAB_STATE tabState,
                STATE state,
                ORDER_BY_STR orderByStr,
                1 sortNum
            FROM T_BO_TP_DH_ORDER_BY
            WHERE IS_DEL = 0
            AND ORG_ID = #{orgId}
            AND TAB_STATE = #{tabState}
            AND STATE = #{state}
            <if test="accountGroupId !=null and accountGroupId !='' ">
                 AND ACCOUNT_GROUP_ID = #{accountGroupId}
            </if>
            UNION ALL
            <!-- 匹配orgId和tabState  -->
            SELECT
                BO_TP_DH_ORDER_BY_ID boTpDhOrderById,
                ORG_ID orgId,
                TAB_STATE tabState,
                STATE state,
                ORDER_BY_STR orderByStr,
                2 sortNum
            FROM T_BO_TP_DH_ORDER_BY
            WHERE IS_DEL = 0
            AND ORG_ID = #{orgId}
            AND TAB_STATE = #{tabState}
            <if test="accountGroupId !=null and accountGroupId !='' ">
                 AND ACCOUNT_GROUP_ID = #{accountGroupId}
            </if>
            UNION ALL
            <!-- 匹配orgId进行保底  -->
            SELECT
                BO_TP_DH_ORDER_BY_ID boTpDhOrderById,
                ORG_ID orgId,
                TAB_STATE tabState,
                STATE state,
                ORDER_BY_STR orderByStr,
                3 sortNum
            FROM T_BO_TP_DH_ORDER_BY
            WHERE IS_DEL = 0
            AND ORG_ID = #{orgId}
            <if test="accountGroupId !=null and accountGroupId !='' ">
                 AND ACCOUNT_GROUP_ID = #{accountGroupId}
            </if>

        ) ORDER BY sortNum ASC, tabState ASC, state ASC ) WHERE ROWNUM = 1
    </select>

    <select id="queryOrdersByOrgId" resultType="com.wtyt.dao.bean.syf.BoTpDhOrderByBean">
        SELECT BO_TP_DH_ORDER_BY_ID boTpDhOrderById,
               ORG_ID               orgId,
               TAB_STATE            tabState,
               STATE                state,
               ORDER_BY_STR         orderByStr
        FROM T_BO_TP_DH_ORDER_BY
        WHERE IS_DEL = 0
          AND ORG_ID = #{orgId}
    </select>

    <select id="queryOrdersByGroupId" resultType="com.wtyt.dao.bean.syf.BoTpDhOrderByBean">
        SELECT BO_TP_DH_ORDER_BY_ID boTpDhOrderById,
               ORG_ID               orgId,
               TAB_STATE            tabState,
               STATE                state,
               ORDER_BY_STR         orderByStr
        FROM T_BO_TP_DH_ORDER_BY
        WHERE IS_DEL = 0
          AND ACCOUNT_GROUP_ID = #{accountGroupId}
    </select>

    <insert id="batchInsert">
        INSERT INTO T_BO_TP_DH_ORDER_BY(BO_TP_DH_ORDER_BY_ID, ORG_ID, TAB_STATE, STATE, ORDER_BY_STR)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTpDhOrderById} BO_TP_DH_ORDER_BY_ID,
            #{item.orgId} ORG_ID,
            #{item.tabState} TAB_STATE,
            #{item.state} STATE,
            #{item.orderByStr} ORDER_BY_STR
            FROM
            DUAL
        </foreach>
    </insert>

    <update id="deleteByOrgIds">
        UPDATE T_BO_TP_DH_ORDER_BY
        SET IS_DEL = 1
        WHERE ORG_ID IN
        <foreach collection="list" separator="," item="orgId" close=")" open="(">
            #{orgId}
        </foreach>
    </update>
</mapper>
