<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskFeeItemMapper">
    
    <resultMap type="com.wtyt.dao.bean.syf.BoTransTaskFeeItemBean" id="BoTransTaskFeeItemMap">
        <result property="boTransTaskFeeItemId" column="BO_TRANS_TASK_FEE_ITEM_ID"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID"/>
        <result property="isAllocate" column="IS_ALLOCATE"/>
        <result property="feeItemType" column="FEE_ITEM_TYPE"/>
        <result property="feeItemKey" column="FEE_ITEM_KEY"/>
        <result property="feeItemValue" column="FEE_ITEM_VALUE"/>
        <result property="feeItemName" column="FEE_ITEM_NAME"/>
        <result property="feeItemNote" column="FEE_ITEM_NOTE"/>
        <result property="operationUserId" column="OPERATION_USER_ID"/>
        <result property="operationSysRole" column="OPERATION_SYS_ROLE"/>
        <result property="isDel" column="IS_DEL"/>
        <result property="createdTime" column="CREATED_TIME"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME"/>
        <result property="note" column="NOTE"/>
    </resultMap>
    
    <insert id="batchSave">
        INSERT INTO T_BO_TRANS_TASK_FEE_ITEM(
        BO_TRANS_TASK_FEE_ITEM_ID,
        BO_TRANS_TASK_ID,
        IS_ALLOCATE,
        FEE_ITEM_TYPE,
        FEE_ITEM_KEY,
        FEE_ITEM_VALUE,
        FEE_ITEM_NAME,
        FEE_ITEM_NOTE,
        OPERATION_USER_ID,
        OPERATION_SYS_ROLE
        ) SELECT
        A.*
        FROM(
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            ${item.boTransTaskFeeItemId} BO_TRANS_TASK_FEE_ITEM_ID,
            #{item.boTransTaskId} BO_TRANS_TASK_ID,
            #{item.isAllocate} IS_ALLOCATE,
            #{item.feeItemType} FEE_ITEM_TYPE,
            #{item.feeItemKey} FEE_ITEM_KEY,
            #{item.feeItemValue} FEE_ITEM_VALUE,
            #{item.feeItemName} FEE_ITEM_NAME,
            #{item.feeItemNote} FEE_ITEM_NOTE,
            #{item.operationUserId} OPERATION_USER_ID,
            #{item.operationSysRole} OPERATION_SYS_ROLE
            FROM
            DUAL
        </foreach>) A
    </insert>
    
    
    <select id="queryFeeItemByTaskId" resultMap="BoTransTaskFeeItemMap">
        select
            t.BO_TRANS_TASK_FEE_ITEM_ID,
            t.BO_TRANS_TASK_ID,
            t.IS_ALLOCATE,
            t.FEE_ITEM_TYPE,
            t.FEE_ITEM_KEY,
            t.FEE_ITEM_VALUE,
            t.FEE_ITEM_NAME,
            t.FEE_ITEM_NOTE,
            t.OPERATION_USER_ID,
            t.OPERATION_SYS_ROLE,
            TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') CREATED_TIME,
            t.note
        from T_BO_TRANS_TASK_FEE_ITEM t where t.is_del=0 and t.bo_trans_task_id = #{boTransTaskId}
        <if test="isAllocate != null and isAllocate !=''">
            AND t.IS_ALLOCATE = #{isAllocate}
        </if>
        <if test="feeItemType!= null and feeItemType !=''">
            AND t.FEE_ITEM_TYPE = #{feeItemType}
        </if>
        ORDER BY T.CREATED_TIME DESC
    </select>
    

</mapper>