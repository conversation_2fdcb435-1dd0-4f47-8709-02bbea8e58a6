<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoInquireUserRelMapper">

    <insert id="insert">
        INSERT INTO
        T_BO_INQUIRE_USER_REL (BO_INQUIRE_USER_REL_ID,
        BO_TRANS_ORDER_ID,
        USER_ID,
        CREATED_USER_ID,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE)
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="dataList" separator="UNION ALL">
            SELECT #{item.boInquireUserRelId} boInquireUserRelId,
            #{item.boTransOrderId} boTransOrderId,
            #{item.userId} userId,
            #{item.createdUserId} createdUserId,
            0 isDel,
            SYSDATE createdTime,
            <PERSON>Y<PERSON><PERSON><PERSON> lastModifiedTime,
            NULL note
            FROM DUAL
        </foreach>
        ) A
    </insert>

    <update id="delUserRel" parameterType="BoOrderGroupRelBean">
        UPDATE T_BO_INQUIRE_USER_REL SET
        IS_DEL = 1,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_INQUIRE_USER_REL_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoInquireUserRelBean">
    SELECT
        BO_INQUIRE_USER_REL_ID boInquireUserRelId,
        BO_TRANS_ORDER_ID boTransOrderId,
        USER_ID userId,
        CREATED_USER_ID createdUserId
    FROM
        T_BO_INQUIRE_USER_REL
    WHERE
        IS_DEL = 0
        AND BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
</mapper>