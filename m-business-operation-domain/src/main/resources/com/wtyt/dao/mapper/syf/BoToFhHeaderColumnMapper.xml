<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoToFhHeaderColumnMapper">

    <select id="getFhHeaderColumn" resultType="com.wtyt.dao.bean.syf.BoToFhHeaderColumnBean">
        SELECT
            BO_TO_FH_HEADER_COLUMN_ID boToFhHeaderColumnId,
            GROUP_CODE groupCode,
            GROUP_TYPE groupType,
            DATA_COLUMN_NAME dataColumnName,
            HEADER_NAME headerName
        FROM T_BO_TO_FH_HEADER_COLUMN
        WHERE IS_DEL = 0
          AND GROUP_CODE = #{groupCode}
          AND GROUP_TYPE = #{groupType}
          AND HEADER_NAME IN
        <foreach collection="headerNames" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

</mapper>
