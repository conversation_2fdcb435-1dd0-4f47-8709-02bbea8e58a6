<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.TBusinessLogDetailMapper">

    <insert id="batchInsert">
        insert into T_BUSINESS_LOG_DETAIL
        (
        BUSINESS_LOG_DETAIL_ID,
        BUSINESS_LOG_ID,
        FIELD_NAME,
        ORIGINAL_VALUE,
        CURRENT_VALUE,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME
        )
        SELECT
        A.*
        FROM(
        <foreach collection="list" item="item" index="index" separator="union all">
            SELECT
            ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()} BUSINESS_LOG_DETAIL_ID,
            #{item.businessLogId} BUSINESS_LOG_ID,
            #{item.fieldName} FIELD_NAME,
            #{item.originalValue} ORIGINAL_VALUE,
            #{item.currentValue} CURRENT_VALUE,
            0 IS_DEL ,
            SYSDATE CREATED_TIME,
            SYSDATE LAST_MODIFIED_TIME
            FROM
            DUAL
        </foreach>) A
    </insert>

</mapper>
