<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhColumEnumMapper">

 <select id="queryColumnEnumList" resultType="com.wtyt.dao.bean.syf.BoColumnEnumBean">
        SELECT
            COLUMN_NAME columnName,
            COLUMN_VALUE  columnValue,
            COLUMN_SHOW_VALUE columnShowValue
        from T_BO_TP_DH_COLUMN_ENUM e where is_del =0 and e.COLUMN_NAME = #{columnName} and ORG_ID =#{orgId}
        order by e.BO_TP_DH_COLUMN_ENUM_ID
    </select>

     <select id="queryColumnEnumListByOrgIds" resultType="com.wtyt.dao.bean.syf.BoColumnEnumBean">
        SELECT
           DISTINCT  COLUMN_NAME columnName,
            COLUMN_VALUE  columnValue,
            COLUMN_SHOW_VALUE columnShowValue
        from T_BO_TP_DH_COLUMN_ENUM e where is_del =0 and e.COLUMN_NAME = #{columnName} and ORG_ID in
            <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
       order by columnValue
    </select>

    <select id="batchQueryColumnEnumList" resultType="com.wtyt.dao.bean.syf.BoColumnEnumBean">
        SELECT
            COLUMN_NAME columnName,
            COLUMN_VALUE  columnValue,
            COLUMN_SHOW_VALUE columnShowValue
        from T_BO_TP_DH_COLUMN_ENUM e where  is_del =0 and  e.COLUMN_NAME IN
        <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                #{item}
        </foreach>
        and ORG_ID =#{orgId}
        order by e.BO_TP_DH_COLUMN_ENUM_ID
    </select>



</mapper>
