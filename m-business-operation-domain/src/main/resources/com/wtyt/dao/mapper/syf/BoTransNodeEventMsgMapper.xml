<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransNodeEventMsgMapper">
    <insert id="batchSaveTransNodeEventMsg">
        INSERT INTO T_BO_TRANS_NODE_EVENT_MSG(
            BO_TRANS_NODE_EVENT_MSG_ID,
            EVENT_NAME,
            NODE_ID,
            ORG_ID,
            TAX_WAYBILL_ID,
            BO_TRANS_TASK_ID,
            EVENT_DESC,
            STATUS,
            ERROR_REASON,
            EXPECT_EXEC_TIME,
            MESSAGE_PUSH_CONFIG_ID,
            PUSH_CHANNEL,
            MSG_BODY,
            MSG_BODY1,
            MSG_BODY2,
            MSG_BODY3,
            MSG_BODY4,
            MSG_BODY5
        ) SELECT
        A.*
        FROM(
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            ${item.boTransNodeEventMsgId} BO_TRANS_NODE_EVENT_MSG_ID,
            #{item.eventName} EVENT_NAME,
            #{item.nodeId} NODE_ID,
            #{item.orgId} ORG_ID,
            #{item.taxWaybillId} TAX_WAYBILL_ID,
            #{item.boTransTaskId} BO_TRANS_TASK_ID,
            #{item.eventDesc} EVENT_DESC,
            #{item.status} STATUS,
            #{item.errorReason} ERROR_REASON,
            to_date(#{item.expectExecTime},'yyyy-mm-dd hh24:mi:ss') EXPECT_EXEC_TIME,
            #{item.messagePushConfigId} MESSAGE_PUSH_CONFIG_ID,
            #{item.pushChannel} PUSH_CHANNEL,
            #{item.msgBody} MSG_BODY,
            #{item.msgBody1} MSG_BODY1,
            #{item.msgBody2} MSG_BODY2,
            #{item.msgBody3} MSG_BODY3,
            #{item.msgBody4} MSG_BODY4,
            #{item.msgBody5} MSG_BODY5
            FROM
            DUAL
        </foreach>) A
    </insert>
    
    
    <select id="checkHasSendSuccessMsg" resultType="int">
        select count(*) from T_BO_TRANS_NODE_EVENT_MSG t
        where t.is_del = 0 and t.STATUS = 1
        <if test="eventName != null and eventName != ''">
            and EVENT_NAME = #{eventName}
        </if>
        <if test="nodeId != null and nodeId != ''">
            and NODE_ID = #{nodeId}
        </if>
        <if test="orgId != null and orgId != ''">
            and ORG_ID = #{orgId}
        </if>
        <if test="taxWaybillId != null and taxWaybillId != ''">
            and TAX_WAYBILL_ID = #{taxWaybillId}
        </if>
        <if test="boTransTaskId != null and boTransTaskId != ''">
            and BO_TRANS_TASK_ID = #{boTransTaskId}
        </if>
        <if test="messagePushConfigId != null and messagePushConfigId != ''">
            and MESSAGE_PUSH_CONFIG_ID = #{messagePushConfigId}
        </if>
        <if test="expectExecTime != null and expectExecTime != ''">
            AND EXPECT_EXEC_TIME = TO_DATE(#{expectExecTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
    </select>

    <select id="getHasSendSuccessList" resultType="BoTransNodeEventMsgBean">
        SELECT T.BO_TRANS_TASK_ID boTransTaskId,
               T.MESSAGE_PUSH_CONFIG_ID messagePushConfigId
          FROM T_BO_TRANS_NODE_EVENT_MSG T
         WHERE T.BO_TRANS_TASK_ID IN
           <foreach item="item" index="index" collection="taskIdList" separator="," open="(" close=")">
                #{item}
           </foreach>
           AND T.STATUS = 1
           AND T.EVENT_NAME = #{eventName}
           AND T.NODE_ID = #{nodeId}
           AND T.IS_DEL = 0
    </select>

	<select id ="getSendSuccessCount" resultType="int">
		SELECT count(*) FROM T_BO_TRANS_NODE_EVENT_MSG 
		WHERE
        BO_TRANS_TASK_ID =#{boTransTaskId}
		AND EVENT_NAME = #{eventName}
		AND NODE_ID = #{nodeId}
		AND IS_DEL =0 
		AND STATUS = 1
	</select>
    <update id="delNodeMsgByTransTaskId">
         update T_BO_TRANS_NODE_EVENT_MSG T set t.is_del=1,T.LAST_MODIFIED_TIME = SYSDATE
         WHERE T.BO_TRANS_TASK_ID = #{boTransTaskId} and t.is_del=0
    </update>
    <update id="batchTransferOrg">
        UPDATE T_BO_TRANS_NODE_EVENT_MSG
        <set>
            LAST_MODIFIED_TIME = SYSDATE,
            ORG_ID = #{data.toOrgId},
            NOTE = #{data.note}
        </set>
        <where>
            IS_DEL = 0
            AND ORG_ID = #{data.fromOrgId}
            AND BO_TRANS_TASK_ID IN
            <foreach collection="taskIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </update>

</mapper>