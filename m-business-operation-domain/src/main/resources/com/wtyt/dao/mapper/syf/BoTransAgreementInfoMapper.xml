<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransAgreementInfoMapper">

    <insert id="insertBatch">
        INSERT INTO T_BO_TRANS_AGREEMENT_INFO(BO_TRANS_AGREEMENT_INFO_ID, INFO_TYPE, REL_ID, AGREEMENT_INFO, SORT_NO)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
                #{item.boTransAgreementInfoId},
                #{item.infoType},
                #{item.relId},
                #{item.agreementInfo},
                #{item.sortNo}
            FROM
            DUAL
        </foreach>
    </insert>

    <update id="removeAgreementInfo">
        UPDATE T_BO_TRANS_AGREEMENT_INFO SET IS_DEL = 1, LAST_MODIFIED_TIME = SYSDATE
        WHERE REL_ID = #{relId}
        AND IS_DEL = 0
        AND INFO_TYPE = #{infoType}
    </update>

    <select id="queryAgreementInfoList" resultType="java.lang.String">
        SELECT AGREEMENT_INFO
        FROM T_BO_TRANS_AGREEMENT_INFO
        WHERE REL_ID = #{relId}
        AND INFO_TYPE = #{infoType}
        AND IS_DEL = 0
        ORDER BY SORT_NO, CREATED_TIME
    </select>



</mapper>
