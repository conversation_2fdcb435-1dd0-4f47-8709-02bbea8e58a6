<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.tt.mapper.syf.TaskCapacityMapper">


    <select id="historyPrice" resultType="com.wtyt.tt.bean.Resp5330253Bean">
        SELECT
            TO_CHAR(MAX(bt.ALL_FREIGHT), 'FM999999990.00') maxAllFreight,
            TO_CHAR(MIN(bt.ALL_FREIGHT), 'FM999999990.00') minAllFreight,
            TO_CHAR(MAX(bt.ALL_FREIGHT / bt.GOODS_AMOUNT), 'FM999999990.00') maxUnitPrice,
            TO_CHAR(MIN(bt.ALL_FREIGHT / bt.GOODS_AMOUNT), 'FM999999990.00') minUnitPrice,
            TO_CHAR(AVG(bt.ALL_FREIGHT), 'FM999999990.00') averageAllFreight,
            TO_CHAR(SUM(bt.ALL_FREIGHT)/SUM(bt.GOODS_AMOUNT), 'FM999999990.00') averageUnitPrice
        FROM
            T_BO_TRANS_TASK bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
            bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID
            AND bte.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
            AND ORG_ID = #{orgId}
            AND bt.PAY_STATE = 2
            <!--只查吨的 -->
            AND bt.GOODS_AMOUNT_TYPE = 0
            AND GOODS_AMOUNT >0
            AND ALL_FREIGHT >0
            AND bt.START_PROVINCE_NAME = #{startProvinceName}
            AND bt.START_CITY_NAME = #{startCityName}
            AND bt.END_PROVINCE_NAME = #{endProvinceName}
            AND bt.END_CITY_NAME = #{endCityName}
            AND bte.CART_LENGTH = #{cartLength}
            AND bte.CART_TYPE = #{cartType}
            <if test="createdTimeStart !=null and createdTimeStart!=''">
                AND bt.CREATED_TIME &gt;= to_date(#{createdTimeStart},'yyyy-MM-dd')
            </if>
    </select>
    <select id="driverInfos" resultType="com.wtyt.tt.bean.CapacityResultBean">
        SELECT
            *
            FROM
            (
                SELECT
                    bt.MOBILE_NO mobileNo,
                    TO_CHAR(bt.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') cooperateTime,
                    TO_CHAR(bt.ALL_FREIGHT, 'FM999999990.00') allFreight,
                    COUNT(1) OVER (PARTITION BY bt.MOBILE_NO) cooperateNum,
                    COUNT(1) OVER (PARTITION BY bt.MOBILE_NO ORDER BY bt.CREATED_TIME DESC) rn
                FROM
                T_BO_TRANS_TASK bt
                JOIN T_BO_TRANS_TASK_EXTRA bte ON
                bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID
                AND bte.IS_DEL = 0
                WHERE
                    bt.IS_DEL = 0
                    AND ORG_ID = #{orgId}
                    AND bt.PAY_STATE = 2
                    AND MOBILE_NO IN
                    <foreach collection="mobileNoList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    AND bt.START_PROVINCE_NAME = #{startProvinceName}
                    AND bt.START_CITY_NAME = #{startCityName}
                    AND bt.END_PROVINCE_NAME = #{endProvinceName}
                    AND bt.END_CITY_NAME = #{endCityName}
            ) temp
        WHERE
            temp.rn = 1
    </select>

    <select id="allDriverInfos" resultType="com.wtyt.tt.bean.CapacityResultBean">
        SELECT
            *
            FROM
            (
                SELECT
                MOBILE_NO mobileNo,
                DRIVER_NAME driverName,
                CART_BADGE_NO cartBadgeNo,
                CART_BADGE_COLOR cartBadgeColor,
                GOODS_NAME goodsName,
                CASE
                WHEN TRANSPORT_TYPE = 1 THEN LOADING_TONNAGE
                ELSE GOODS_AMOUNT
                END goodsAmount,
                GOODS_AMOUNT_TYPE goodsAmountType,
                CART_TYPE cartType,
                CART_LENGTH cartLength,
                CPD_POOL_GROUP_ID cpdPoolGroupId,
                TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') cooperateTime,
                COUNT(1) OVER (PARTITION BY MOBILE_NO,DRIVER_NAME,CART_BADGE_NO) cooperateNum,
                COUNT(1) OVER (PARTITION BY MOBILE_NO,DRIVER_NAME,CART_BADGE_NO ORDER BY CREATED_TIME DESC) rn
            FROM
            (
                SELECT
                    bt.MOBILE_NO,
                    bt.DRIVER_NAME,
                    bt.CART_BADGE_NO,
                    bt.CART_BADGE_COLOR,
                    bt.GOODS_NAME,
                    bt.TRANSPORT_TYPE,
                    bt.LOADING_TONNAGE,
                    bt.GOODS_AMOUNT,
                    bt.GOODS_AMOUNT_TYPE,
                    bte.CART_TYPE,
                    bte.CART_LENGTH,
                    bte.CPD_POOL_GROUP_ID,
                    bt.CREATED_TIME
                FROM
                T_BO_TRANS_TASK bt
                JOIN T_BO_TRANS_TASK_EXTRA bte ON bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID
                WHERE
                    bt.IS_DEL = 0
                    AND bte.IS_DEL =0
                    AND bt.ORG_ID = #{orgId}
                    AND bt.STATE = 2
                    AND bt.MOBILE_NO IS NOT NULL
                    AND bt.DRIVER_NAME IS NOT NULL
                    AND bt.CART_BADGE_NO IS NOT NULL
                UNION ALL
                SELECT
                    bt.MOBILE_NO,
                    bt.DRIVER_NAME,
                    bt.CART_BADGE_NO,
                    bt.CART_BADGE_COLOR,
                    bt.GOODS_NAME,
                    bt.TRANSPORT_TYPE,
                    bt.LOADING_TONNAGE,
                    bt.GOODS_AMOUNT,
                    bt.GOODS_AMOUNT_TYPE,
                    bte.CART_TYPE,
                    bte.CART_LENGTH,
                    bte.CPD_POOL_GROUP_ID,
                    bt.CREATED_TIME
                FROM
                T_BO_TRANS_TASK bt
                JOIN T_BO_TRANS_TASK_EXTRA bte ON bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID
                JOIN T_BO_TRANS_TASK_ALLOCATE tta ON tta.BO_TRANS_TASK_ID=bt.BO_TRANS_TASK_ID
                WHERE
                    bt.IS_DEL = 0
                    AND bte.IS_DEL =0
                    AND tta.IS_DEL =0
                    AND tta.ORG_ID = #{orgId}
                    AND bt.STATE = 2
                    AND bt.MOBILE_NO IS NOT NULL
                    AND bt.DRIVER_NAME IS NOT NULL
                    AND bt.CART_BADGE_NO IS NOT NULL
            )  T
        ) temp
        WHERE
        temp.rn = 1
    </select>

    <select id="driverLines" resultType="com.wtyt.tt.bean.CapacityResultBean">
        SELECT
            bt.MOBILE_NO mobileNo,
            bt.START_PROVINCE_NAME startProvinceName,
            bt.START_CITY_NAME startCityName,
            bt.END_PROVINCE_NAME endProvinceName,
            bt.END_CITY_NAME endCityName,
            COUNT(1) cooperateNum
        FROM
        T_BO_TRANS_TASK bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
            bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID
            AND bte.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
            AND ORG_ID = #{orgId}
            AND bt.STATE = 2
            AND bt.MOBILE_NO IN
            <foreach collection="mobileNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        GROUP BY
            bt.MOBILE_NO,bt.START_PROVINCE_NAME,bt.START_CITY_NAME,bt.END_PROVINCE_NAME,bt.END_CITY_NAME
    </select>
    <select id="driverTasks" resultType="com.wtyt.tt.bean.CapacityResultBean">
        SELECT
            bt.ORG_ID orgId,
            bt.MOBILE_NO mobileNo,
            bt.GOODS_NAME goodsName,
            bt.GOODS_AMOUNT goodsAmount,
            bt.GOODS_AMOUNT_TYPE goodsAmountType,
            TO_CHAR(bt.ALL_FREIGHT, 'FM999999990.00') allFreight,
            bt.START_PROVINCE_NAME startProvinceName,
            bt.START_CITY_NAME startCityName,
            bt.START_COUNTY_NAME startCountyName,
            bte.LOADING_ADDRESS_NAME loadingAddressName,
            bt.END_PROVINCE_NAME endProvinceName,
            bt.END_CITY_NAME endCityName,
            bt.END_COUNTY_NAME endCountyName,
            bte.UNLOADING_ADDRESS_NAME unloadingAddressName,
            TO_CHAR(bt.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
        FROM
            T_BO_TRANS_TASK bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
            bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID
            AND bte.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
            <if test="orgId!=null and orgId!=''">
                AND bt.ORG_ID = #{orgId}
            </if>
            AND bt.STATE = 2
            AND bt.MOBILE_NO IN
            <foreach collection="mobileNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="createdTimeStart !=null and createdTimeStart!=''">
                AND bt.CREATED_TIME &gt;= to_date(#{createdTimeStart},'yyyy-MM-dd')
            </if>
        ORDER BY
            bt.CREATED_TIME DESC
    </select>

    <select id="driverHistoryCpdGroups" resultType="java.lang.String">
        SELECT
            CPD_POOL_GROUP_ID
        FROM
        (
            SELECT
                *
            FROM
            (
                SELECT
                b.CPD_POOL_GROUP_ID,
                a.CREATED_TIME,
                ROW_NUMBER() OVER(PARTITION BY b.CPD_POOL_GROUP_ID ORDER BY a.CREATED_TIME DESC) rn
                FROM
                T_BO_TRANS_TASK a
                JOIN T_BO_TRANS_TASK_EXTRA b ON
                a.BO_TRANS_TASK_ID = b.BO_TRANS_TASK_ID
                AND b.IS_DEL = 0
                WHERE
                a.IS_DEL = 0
                AND a.ORG_ID = #{orgId}
                AND a.MOBILE_NO = #{mobileNo}
                AND b.CPD_POOL_GROUP_ID IS NOT NULL) temp1
            WHERE
                rn = 1
            ORDER BY
                CREATED_TIME DESC) temp2
        WHERE
            rownum &lt; 6
    </select>

    <select id="driverCooperateNum" resultType="com.wtyt.tt.bean.CapacityResultBean">
        SELECT
            bt.ORG_ID orgId,
            bt.MOBILE_NO mobileNo,
            COUNT(*) cooperateNum
        FROM
        T_BO_TRANS_TASK bt
        WHERE
        bt.IS_DEL = 0
        AND bt.STATE = 2
        AND (
        <foreach collection="driverList" item="item" separator=" OR ">
            (bt.MOBILE_NO IN
            <foreach collection="item.mobileNoList" item="mobileNo" separator="," open="(" close=")">
                #{mobileNo}
            </foreach>
            AND bt.ORG_ID IN
            <foreach collection="item.orgIds" item="orgId" separator="," open="(" close=")">
                #{orgId}
            </foreach>
            )
        </foreach>
        )
        GROUP BY bt.ORG_ID,bt.MOBILE_NO
    </select>

    <select id="driverLatestTaskInfo" resultType="com.wtyt.tt.bean.CapacityResultBean">
        SELECT * FROM (
            SELECT
                bt.BO_TRANS_TASK_ID boTransTaskId,
                bt.MOBILE_NO mobileNo,
                bt.DRIVER_NAME driverName,
                bt.CART_BADGE_NO cartBadgeNo,
                bt.CART_BADGE_COLOR cartBadgeColor,
                bte.CART_TYPE cartType,
                bte.CART_LENGTH cartLength,
                bte.DISPATCH_CAR_RECORD_ID dispatchCarRecordId,
                bt.START_CITY_NAME startCityName,
                bt.START_COUNTY_NAME startCountyName,
                bt.END_CITY_NAME endCityName,
                bt.END_COUNTY_NAME endCountyName,
                bt.GOODS_NAME goodsName,
                bt.GOODS_AMOUNT goodsAmount,
                bt.GOODS_AMOUNT_TYPE goodsAmountType,
                bt.STATE,
                TO_CHAR(bt.END_TIME, 'yyyy-MM-dd Hh24:mi:ss') endTime,
                ROW_NUMBER() OVER(PARTITION BY bt.MOBILE_NO ORDER BY bt.CREATED_TIME DESC) RN
            FROM
                T_BO_TRANS_TASK bt
            JOIN T_BO_TRANS_TASK_EXTRA bte ON bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID
            WHERE
                bt.IS_DEL = 0
                AND bt.MOBILE_NO IN
                <foreach collection="mobileNos" item="mobileNo" separator="," open="(" close=")">
                    #{mobileNo}
                </foreach>
                AND bt.ORG_ID IN
                <foreach collection="orgIds" item="orgId" separator="," open="(" close=")">
                    #{orgId}
                </foreach>
                AND bte.DISPATCH_CAR_RECORD_ID IS NOT NULL
                <if test="hybState!=null and hybState!=''">
                    <choose>
                        <when test="hybState=='0'.toString()">
                            AND (bt.HYB_STATE IS NULL OR bt.HYB_STATE=0)
                        </when>
                        <otherwise>
                            AND (bt.HYB_STATE IS NOT NULL AND bt.HYB_STATE!=0)
                        </otherwise>
                    </choose>
                </if>
                <if test="state!=null and state.size()>0">
                    AND bt.STATE IN
                    <foreach collection="state" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
        )
        WHERE RN=1
    </select>

    <select id="driverTaskCount" resultType="com.wtyt.tt.bean.DriverCountBean">
        SELECT
            bt.MOBILE_NO mobileNo,
            bt.STATE,
            COUNT(*) count
        FROM
        T_BO_TRANS_TASK bt
        WHERE
            bt.IS_DEL =0
            AND bt.MOBILE_NO IN
            <foreach collection="mobileNos" item="mobileNo" separator="," open="(" close=")">
                #{mobileNo}
            </foreach>
            <if test="startProvinceName!=null and startProvinceName!=''">
                AND bt.START_PROVINCE_NAME = #{startProvinceName}
            </if>
            <if test="endProvinceName!=null and endProvinceName!=''">
                AND bt.END_PROVINCE_NAME = #{endProvinceName}
            </if>
            <if test="startCityName!=null and startCityName!=''">
                AND bt.START_CITY_NAME = #{startCityName}
            </if>
            <if test="endCityName!=null and endCityName!=''">
                AND bt.END_CITY_NAME = #{endCityName}
            </if>
            <if test="createdTimeStart!=null and createdTimeStart!=''">
                AND bt.CREATED_TIME &gt;= to_date(#{createdTimeStart},'yyyy-MM-dd')
            </if>
            GROUP BY bt.MOBILE_NO,bt.STATE
    </select>

    <select id="driverCommonLine" resultType="com.wtyt.tt.bean.LineDataInfo">
        SELECT
            *
            FROM
            (
                SELECT
                *
                FROM
                (
                    SELECT
                        bt.START_PROVINCE_NAME startProvinceName,
                        bt.END_PROVINCE_NAME endProvinceName,
                        <if test="lineType == '2'.toString()">
                            bt.START_CITY_NAME startCityName,
                            bt.END_CITY_NAME endCityName,
                        </if>
                        COUNT(*) taskCount
                    FROM
                    T_BO_TRANS_TASK bt
                    WHERE
                        bt.IS_DEL = 0
                        AND bt.MOBILE_NO = #{mobileNo}
                        AND bt.START_PROVINCE_NAME IS NOT NULL
                        AND bt.END_PROVINCE_NAME IS NOT NULL
                        <if test="lineType == '2'.toString()">
                            AND bt.START_CITY_NAME IS NOT NULL
                            AND bt.END_CITY_NAME IS NOT NULL
                        </if>
                        <if test="createdTimeStart!=null and createdTimeStart!=''">
                            AND bt.CREATED_TIME &gt;= to_date(#{createdTimeStart},'yyyy-MM-dd')
                        </if>
                        <if test="hybState!=null and hybState!=''">
                            <choose>
                                <when test="hybState=='0'.toString()">
                                    AND bt.HYB_STATE=0
                                </when>
                                <otherwise>
                                    AND bt.HYB_STATE!=0
                                </otherwise>
                            </choose>
                        </if>
                    GROUP BY
                        bt.START_PROVINCE_NAME
                        ,bt.END_PROVINCE_NAME
                        <if test="lineType == '2'.toString()">
                            ,bt.START_CITY_NAME
                            ,bt.END_CITY_NAME
                        </if>
                )
            ORDER BY
                taskCount DESC
            )
        WHERE
        rownum &lt;= #{topSize}
    </select>

    <select id="driverDispatcherCount" resultType="com.wtyt.tt.bean.CapacityResultBean">
        SELECT
            bt.MOBILE_NO mobileNo,
            COUNT(*) cooperateNum
        FROM
        T_BO_TRANS_TASK bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
            bt.BO_TRANS_TASK_ID = bte.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_NODE_RECORD nr ON
            NR.BO_TRANS_NODE_RECORD_ID = bte.DISPATCH_CAR_RECORD_ID
            AND nr.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
            AND bt.MOBILE_NO IN
            <foreach collection="mobileNos" item="mobileNo" separator="," open="(" close=")">
                #{mobileNo}
            </foreach>
            AND bt.ORG_ID IN
            <foreach collection="orgIds" item="orgId" separator="," open="(" close=")">
                #{orgId}
            </foreach>
            <if test="state!=null and state!=''">
                AND bt.STATE = #{state}
            </if>
            AND nr.USER_ID = #{dispatcherUserId}
        GROUP BY bt.MOBILE_NO
    </select>

    <select id="driverTaskInfos" resultType="com.wtyt.tt.bean.CapacityResultBean">
            SELECT
                BT.BO_TRANS_TASK_ID boTransTaskId,
                BT.MOBILE_NO mobileNo
            FROM T_BO_TRANS_TASK BT
            WHERE BT.IS_DEL = 0
            AND BT.MOBILE_NO IN
            <foreach collection="mobileNos" item="mobileNo" open="(" separator="," close=")">
                #{mobileNo}
            </foreach>
            <if test="orgIds != null and orgIds.size() > 0">
                AND BT.ORG_ID IN
                <foreach collection="orgIds" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
            <if test="states != null and states.size() > 0">
                AND BT.STATE IN
                <foreach collection="states" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="hybState != null and hybState.length > 0">
                <choose>
                    <when test="hybState == '0'.toString()">
                        AND BT.HYB_STATE = 0
                    </when>
                    <otherwise>
                        AND BT.HYB_STATE != 0
                    </otherwise>
                </choose>
            </if>
            <if test="createdTimeStart != null and createdTimeStart.length > 0">
                AND BT.CREATED_TIME <![CDATA[>=]]> to_date(#{createdTimeStart}, 'yyyy-MM-dd hh24:mi:ss')
            </if>
            <if test="createdTimeEnd != null and createdTimeEnd.length > 0">
                AND BT.CREATED_TIME <![CDATA[<=]]> to_date(#{createdTimeEnd}, 'yyyy-MM-dd hh24:mi:ss')
            </if>
            <if test="endTimeStart != null and endTimeStart.length > 0">
                AND BT.END_TIME <![CDATA[>=]]> to_date(#{endTimeStart}, 'yyyy-MM-dd hh24:mi:ss')
            </if>
            <if test="endTimeEnd != null and endTimeEnd.length > 0">
                AND BT.END_TIME <![CDATA[<=]]> to_date(#{endTimeEnd}, 'yyyy-MM-dd hh24:mi:ss')
            </if>
            ORDER BY BT.CREATED_TIME DESC
    </select>

</mapper>
