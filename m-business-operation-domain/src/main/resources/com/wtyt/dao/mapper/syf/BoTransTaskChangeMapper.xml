<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskChangeMapper">
    <resultMap type="com.wtyt.dao.bean.syf.BoTransTaskChangeBean" id="TBoTransTaskChangeMap">
        <result property="boTransTaskChangeId" column="BO_TRANS_TASK_CHANGE_ID" jdbcType="VARCHAR"/>
        <result property="changeEntityId" column="CHANGE_ENTITY_ID" jdbcType="VARCHAR"/>
        <result property="changeEntityType" column="CHANGE_ENTITY_TYPE" jdbcType="VARCHAR"/>
        <result property="modifyField" column="MODIFY_FIELD" jdbcType="VARCHAR"/>
        <result property="modifyFieldName" column="MODIFY_FIELD_NAME" jdbcType="VARCHAR"/>
        <result property="modifyUserId" column="MODIFY_USER_ID" jdbcType="VARCHAR"/>
        <result property="modifyUserName" column="MODIFY_USER_NAME" jdbcType="VARCHAR"/>
        <result property="oldValue" column="OLD_VALUE" jdbcType="VARCHAR"/>
        <result property="newValue" column="NEW_VALUE" jdbcType="VARCHAR"/>
        <result property="reason" column="REASON" jdbcType="VARCHAR"/>
        <result property="batchNo" column="BATCH_NO" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="modifyFieldName" column="MODIFY_FIELD_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.wtyt.bo.bean.response.RespTransChangeInfoBean" id="queryChangeInfoEntityMap">
        <result property="boTransTaskChangeId" column="BO_TRANS_TASK_CHANGE_ID" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="modifyUserName" column="MODIFY_USER_NAME" jdbcType="VARCHAR"/>
        <result property="modifyField" column="MODIFY_FIELD_NAME" jdbcType="VARCHAR"/>
        <result property="oldValue" column="OLD_VALUE" jdbcType="VARCHAR"/>
        <result property="newValue" column="NEW_VALUE" jdbcType="VARCHAR"/>
        <result property="reason" column="REASON" jdbcType="VARCHAR"/>
        <result property="modifySysRoleType" column="MODIFY_SYS_ROLE_TYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">
        T_BO_TRANS_TASK_CHANGE
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="TBoTransTaskChangeMap">
        select BO_TRANS_TASK_CHANGE_ID,
               CHANGE_ENTITY_ID,
               CHANGE_ENTITY_TYPE,
               MODIFY_FIELD,
               MODIFY_USER_ID,
               MODIFY_USER_NAME,
               OLD_VALUE,
               NEW_VALUE,
               REASON,
               BATCH_NO,
               IS_DEL,
               CREATED_TIME,
               LAST_MODIFIED_TIME,
               NOTE
        from T_BO_TRANS_TASK_CHANGE
        where BO_TRANS_TASK_CHANGE_ID = #{boTransTaskChangeId}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from T_BO_TRANS_TASK_CHANGE
        <where>
            <if test="boTransTaskChangeId != null and boTransTaskChangeId != ''">
                and BO_TRANS_TASK_CHANGE_ID = #{boTransTaskChangeId}
            </if>
            <if test="changeEntityId != null and changeEntityId != ''">
                and CHANGE_ENTITY_ID = #{changeEntityId}
            </if>
            <if test="changeEntityType != null and changeEntityType != ''">
                and CHANGE_ENTITY_TYPE = #{changeEntityType}
            </if>
            <if test="modifyField != null and modifyField != ''">
                and MODIFY_FIELD = #{modifyField}
            </if>
            <if test="modifyUserId != null and modifyUserId != ''">
                and MODIFY_USER_ID = #{modifyUserId}
            </if>
            <if test="modifyUserName != null and modifyUserName != ''">
                and MODIFY_USER_NAME = #{modifyUserName}
            </if>
            <if test="oldValue != null and oldValue != ''">
                and OLD_VALUE = #{oldValue}
            </if>
            <if test="newValue != null and newValue != ''">
                and NEW_VALUE = #{newValue}
            </if>
            <if test="reason != null and reason != ''">
                and REASON = #{reason}
            </if>
            <if test="batchNo != null and batchNo != ''">
                and BATCH_NO = #{batchNo}
            </if>
            <if test="isDel != null and isDel != ''">
                and IS_DEL = #{isDel}
            </if>
            <if test="createdTime != null">
                and CREATED_TIME = #{createdTime}
            </if>
            <if test="lastModifiedTime != null">
                and LAST_MODIFIED_TIME = #{lastModifiedTime}
            </if>
            <if test="note != null and note != ''">
                and NOTE = #{note}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.wtyt.dao.bean.syf.BoTransTaskChangeBean">
        insert into T_BO_TRANS_TASK_CHANGE(BO_TRANS_TASK_CHANGE_ID, CHANGE_ENTITY_ID, CHANGE_ENTITY_TYPE, MODIFY_FIELD, MODIFY_USER_ID,
                                           MODIFY_USER_NAME, MODIFY_FIELD_NAME, OLD_VALUE, NEW_VALUE, REASON, BATCH_NO, IS_DEL,
                                           CREATED_TIME, LAST_MODIFIED_TIME, NOTE)
        values (#{boTransTaskChangeId}, #{changeEntityId}, #{changeEntityType}, #{modifyField}, #{modifyUserId}, #{modifyUserName}, #{modifyFieldName}, #{oldValue},
                #{newValue}, #{reason}, #{batchNo}, #{isDel}, #{createdTime}, #{lastModifiedTime}, #{note})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update T_BO_TRANS_TASK_CHANGE
        <set>
            <if test="changeEntityId != null and changeEntityId != ''">
                CHANGE_ENTITY_ID = #{changeEntityId},
            </if>
            <if test="changeEntityType != null and changeEntityType != ''">
                CHANGE_ENTITY_TYPE = #{changeEntityType},
            </if>
            <if test="modifyField != null and modifyField != ''">
                MODIFY_FIELD = #{modifyField},
            </if>
            <if test="modifyUserId != null and modifyUserId != ''">
                MODIFY_USER_ID = #{modifyUserId},
            </if>
            <if test="modifyUserName != null and modifyUserName != ''">
                MODIFY_USER_NAME = #{modifyUserName},
            </if>
            <if test="modifyFieldName != null and modifyFieldName != ''">
                MODIFY_FIELD_NAME = #{modifyFieldName},
            </if>
            <if test="oldValue != null and oldValue != ''">
                OLD_VALUE = #{oldValue},
            </if>
            <if test="newValue != null and newValue != ''">
                NEW_VALUE = #{newValue},
            </if>
            <if test="reason != null and reason != ''">
                REASON = #{reason},
            </if>
            <if test="batchNo != null and batchNo != ''">
                BATCH_NO = #{batchNo},
            </if>
            <if test="isDel != null and isDel != ''">
                IS_DEL = #{isDel},
            </if>
            <if test="createdTime != null">
                CREATED_TIME = #{createdTime},
            </if>
            <if test="lastModifiedTime != null">
                LAST_MODIFIED_TIME = #{lastModifiedTime},
            </if>
            <if test="note != null and note != ''">
                NOTE = #{note},
            </if>
        </set>
        where BO_TRANS_TASK_CHANGE_ID = #{boTransTaskChangeId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from T_BO_TRANS_TASK_CHANGE
        where BO_TRANS_TASK_CHANGE_ID = #{boTransTaskChangeId}
    </delete>

    <select id="queryChangedDataByChangeEntityId" resultMap="queryChangeInfoEntityMap">
        select BO_TRANS_TASK_CHANGE_ID, TO_CHAR(LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI') LAST_MODIFIED_TIME,MODIFY_USER_NAME, MODIFY_FIELD_NAME,
        (CASE WHEN OLD_VALUE = '空' THEN '空' ELSE TO_CHAR(NVL(OLD_VALUE,0),'FM999999990.00') END) OLD_VALUE,
        (CASE WHEN NEW_VALUE = '空' THEN '空' ELSE TO_CHAR(NVL(NEW_VALUE,0),'FM999999990.00') END) NEW_VALUE,
        REASON, MODIFY_SYS_ROLE_TYPE from
        <include refid="tableName"/>
        <where>
            CHANGE_ENTITY_ID = #{changeEntityId}
            and CHANGE_ENTITY_TYPE = #{changeEntityType}
            and IS_DEL = 0
        </where>
        order by CREATED_TIME DESC
    </select>
    
    <insert id="batchSave">
        insert into T_BO_TRANS_TASK_CHANGE(BO_TRANS_TASK_CHANGE_ID, CHANGE_ENTITY_ID, CHANGE_ENTITY_TYPE,
        MODIFY_FIELD,MODIFY_FIELD_NAME, MODIFY_USER_ID,
        MODIFY_USER_NAME,MODIFY_SYS_ROLE_TYPE, OLD_VALUE, NEW_VALUE, REASON, BATCH_NO)
        SELECT A.* FROM(
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()} BO_TRANS_TASK_CHANGE_ID,
            #{item.changeEntityId} CHANGE_ENTITY_ID,
            #{item.changeEntityType} CHANGE_ENTITY_TYPE,
            #{item.modifyField} MODIFY_FIELD,
            #{item.modifyFieldName} MODIFY_FIELD_NAME,
            #{item.modifyUserId} MODIFY_USER_ID,
            #{item.modifyUserName} MODIFY_USER_NAME,
            #{item.modifySysRoleType} MODIFY_SYS_ROLE_TYPE,
            #{item.oldValue} OLD_VALUE,
            #{item.newValue} NEW_VALUE,
            #{item.reason} REASON,
            #{item.batchNo} BATCH_NO
            FROM
            DUAL
        </foreach>) A
    </insert>

</mapper>

