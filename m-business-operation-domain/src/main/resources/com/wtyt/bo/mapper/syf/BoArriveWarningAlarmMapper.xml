<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoArriveWarningAlarmMapper">

    <select id="getNeedAlarmWaybillList" resultType="BoXxlJobAlarmBean">
        SELECT
            NVL(T.TAX_WAYBILL_ID,TA.TAX_WAYBILL_ID) taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.CART_BADGE_NO cartBadgeNo,
            T.DRIVER_NAME driverName,
            T.START_PROVINCE_NAME startProvinceName,
            T.END_PROVINCE_NAME endProvinceName,
            T.START_CITY_NAME startCityName,
            T.END_CITY_NAME endCityName,
            T.START_COUNTY_NAME startCountyName,
            T.END_COUNTY_NAME endCountyName,
            T.MOBILE_NO mobileNo,
            T.CREATED_USER_ID klbUserId,
            '500' nodeId,
            TO_CHAR(E.ARRIVE_END_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
            TO_CHAR(E.DISPATCH_CAR_TIME, 'YYYY-MM-DD HH24:MI:SS') dispatchTime,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude
        FROM
            T_bo_trans_task T
        JOIN T_BO_TRANS_TASK_EXTRA E ON
            E.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TA ON
            T.BO_TRANS_TASK_ID = TA.BO_TRANS_TASK_ID
            AND TA.IS_DEL = 0
        WHERE
            T.IS_DEL = 0
            AND (T.TRANSPORT_TYPE =0 OR T.TRANSPORT_TYPE IS NULL)
            AND T.CREATED_TIME >= SYSDATE - 90
            AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL
            AND E.DISPATCH_CAR_TIME IS NOT NULL
            AND E.ARRIVE_TIME IS NULL
            AND T.STATE =0
            AND t.NODE_ID &lt; 500
            AND E.ARRIVE_END_TIME >SYSDATE
            AND T.ORG_ID IN
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
    </select>
</mapper>
