<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoBoardProgressMapper">

    <insert id="insertProgressBatch">
        INSERT INTO T_BO_BOARD_PROGRESS(BO_BOARD_PROGRESS_ID, PROGRESS_DATE, BO_BUSINESS_PROJECT_ID, BO_BUSINESS_LINE_ID, PID, PROGRESS_TYPE, FIN_GOODS_QUANTITY)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boBoardProgressId} BO_BOARD_PROGRESS_ID,
            TO_DATE(#{item.progressDate}, 'YYYY-MM-DD') PROGRESS_DATE,
            #{item.boBusinessProjectId} BO_BUSINESS_PROJECT_ID,
            #{item.boBusinessLineId} BO_BUSINESS_LINE_ID,
            #{item.pid} PID,
            #{item.progressType} PROGRESS_TYPE,
            #{item.finGoodsQuantity} FIN_GOODS_QUANTITY
            FROM
            DUAL
        </foreach>
    </insert>

    <select id="queryProgressCountByBpId" resultType="java.lang.Integer">
        SELECT
            COUNT(BO_BOARD_PROGRESS_ID)
        FROM T_BO_BOARD_PROGRESS
        WHERE  BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId}
        AND PROGRESS_DATE = TO_DATE(#{progressDate}, 'YYYY-MM-DD')
        AND IS_DEL = 0
    </select>

    <select id="queryBoardLastProgressListByProjectId" resultType="com.wtyt.dao.bean.syf.BoBoardProgressBean">
        SELECT
            BO_BOARD_PROGRESS_ID boBoardProgressId,
            TO_CHAR(PROGRESS_DATE, 'YYYY-MM-DD HH24:MI') progressDate,
            BO_BUSINESS_PROJECT_ID boBusinessProjectId,
            BO_BUSINESS_LINE_ID boBusinessLineId,
            PID pid,
            PROGRESS_TYPE progressType,
            FIN_GOODS_QUANTITY finGoodsQuantity
        FROM T_BO_BOARD_PROGRESS
        WHERE BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId}
        AND PROGRESS_DATE = (SELECT MAX(PROGRESS_DATE) FROM T_BO_BOARD_PROGRESS WHERE PROGRESS_DATE &lt;= TO_DATE(#{progressDate}, 'YYYY-MM-DD'))
        AND IS_DEL = 0
    </select>

    <select id="queryProgressLinesByMonth" resultType="com.wtyt.board.bean.BoardBpProgressLineBean">
        SELECT
            to_char(progress_date,'yyyy-mm') dateStr,
            bo_business_line_id boBusinessLineId,
            fin_goods_quantity finGoodsQuantity
        FROM (
            SELECT
                ROW_NUMBER() OVER(PARTITION BY to_char(progress_date,'yyyy-mm'),
                bo_business_line_id ORDER BY progress_date DESC) rn,
                t.*
            FROM T_BO_BOARD_PROGRESS t
            WHERE BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId}
            AND IS_DEL = 0
            AND progress_date >= TO_DATE(#{beginTime}, 'yyyy-mm-dd')
            AND progress_date &lt;= TO_DATE(#{endTime}, 'yyyy-mm-dd')
        ) WHERE rn = 1 ORDER BY dateStr ASC

    </select>

    <select id="queryProgressLinesByDay" resultType="com.wtyt.board.bean.BoardBpProgressLineBean">
        SELECT
            to_char(progress_date,'yyyy-mm-dd') dateStr,
            bo_business_line_id boBusinessLineId,
            fin_goods_quantity finGoodsQuantity
        FROM T_BO_BOARD_PROGRESS t
        WHERE BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId}
        AND IS_DEL = 0
        AND progress_date >= TO_DATE(#{beginTime}, 'yyyy-mm-dd')
        AND progress_date &lt;= TO_DATE(#{endTime}, 'yyyy-mm-dd')
        ORDER BY dateStr ASC
    </select>

    <select id="queryPrevFinGoodsQuantity" resultType="com.wtyt.commons.bean.StringStringPair">
        SELECT
            BO_BUSINESS_LINE_ID key,
            FIN_GOODS_QUANTITY value
        FROM
            (
                SELECT
                ROW_NUMBER() OVER(PARTITION BY BO_BUSINESS_LINE_ID ORDER BY PROGRESS_DATE DESC) rn,
                NVL(BO_BUSINESS_LINE_ID,-1)  BO_BUSINESS_LINE_ID,
                FIN_GOODS_QUANTITY FIN_GOODS_QUANTITY
                FROM
                T_BO_BOARD_PROGRESS
                WHERE
                IS_DEL = 0
                AND BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId}
                AND (BO_BUSINESS_LINE_ID IS NULL
                    <if test="boBusinessLineIds != null and boBusinessLineIds.size() > 0">
                        OR BO_BUSINESS_LINE_ID IN
                        <foreach collection="boBusinessLineIds" item="item" close=")" open="(" separator=",">
                            #{item}
                        </foreach>
                    </if>
                )
                AND PROGRESS_DATE &lt; TO_DATE(#{beginTime}, 'YYYY-MM-DD')
            )
        WHERE
            rn = 1
    </select>

    <select id="queryPrevFinGoodsQuantityByLineId" resultType="com.wtyt.commons.bean.StringStringPair">
        SELECT
        BO_BUSINESS_LINE_ID key,
        FIN_GOODS_QUANTITY value
        FROM
        (
        SELECT
        ROW_NUMBER() OVER(PARTITION BY BO_BUSINESS_LINE_ID ORDER BY PROGRESS_DATE DESC) rn,
        NVL(BO_BUSINESS_LINE_ID,-1)  BO_BUSINESS_LINE_ID,
        FIN_GOODS_QUANTITY FIN_GOODS_QUANTITY
        FROM
        T_BO_BOARD_PROGRESS
        WHERE
        IS_DEL = 0
        AND BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId}
        <if test="boBusinessLineIds != null and boBusinessLineIds.size() > 0">
            AND BO_BUSINESS_LINE_ID IN
            <foreach collection="boBusinessLineIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        AND PROGRESS_DATE &lt; TO_DATE(#{beginTime}, 'YYYY-MM-DD')
        ) WHERE rn = 1
    </select>

    <select id="queryProjectProgressByBpIdAndDate" resultType="com.wtyt.dao.bean.syf.BoBoardProgressBean">
        SELECT
            BO_BOARD_PROGRESS_ID boBoardProgressId,
            BO_BUSINESS_PROJECT_ID boBusinessProjectId,
            FIN_GOODS_QUANTITY finGoodsQuantity
        FROM T_BO_BOARD_PROGRESS
        WHERE  BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId}
        AND PROGRESS_DATE = TO_DATE(#{progressDate}, 'YYYY-MM-DD')
        AND IS_DEL = 0 AND PROGRESS_TYPE = 1 AND ROWNUM = 1
    </select>

    <update id="updateProgress">
        UPDATE
            T_BO_BOARD_PROGRESS
        SET
            FIN_GOODS_QUANTITY = #{finGoodsQuantity},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND BO_BOARD_PROGRESS_ID = #{boBoardProgressId}
    </update>
    <update id="deleteProgress">
        UPDATE
            T_BO_BOARD_PROGRESS
        SET
            IS_DEL =1,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
          AND BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId} and PROGRESS_DATE>=TO_DATE(#{progressDate}, 'YYYY-MM-DD')
    </update>

    <update id="saveOrUpdateProgress">
        MERGE INTO T_BO_BOARD_PROGRESS A
        USING (
        <foreach collection="list" index="index" item="item" open=""
                 close="" separator="union">
            SELECT #{item.boBoardProgressId} BO_BOARD_PROGRESS_ID, #{item.progressDate} PROGRESS_DATE, #{item.boBusinessProjectId} BO_BUSINESS_PROJECT_ID,
             #{item.boBusinessLineId} BO_BUSINESS_LINE_ID, #{item.pid} PID, #{item.progressType} PROGRESS_TYPE, #{item.finGoodsQuantity} FIN_GOODS_QUANTITY
             FROM DUAL
        </foreach>
        ) B
        ON (A.IS_DEL = 0 AND A.BO_BUSINESS_PROJECT_ID = B.BO_BUSINESS_PROJECT_ID AND A.BO_BUSINESS_LINE_ID = B.BO_BUSINESS_LINE_ID AND A.PROGRESS_DATE = TO_DATE(B.PROGRESS_DATE, 'YYYY-MM-DD'))
        WHEN MATCHED THEN
        UPDATE SET
        A.FIN_GOODS_QUANTITY = B.FIN_GOODS_QUANTITY,
        A.NOTE = A.NOTE || 'job初始化线下数据',
        A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
        INSERT (BO_BOARD_PROGRESS_ID, PROGRESS_DATE, BO_BUSINESS_PROJECT_ID, BO_BUSINESS_LINE_ID, PID, PROGRESS_TYPE, FIN_GOODS_QUANTITY, note)
        VALUES (B.BO_BOARD_PROGRESS_ID, TO_DATE(B.PROGRESS_DATE, 'YYYY-MM-DD'), B.BO_BUSINESS_PROJECT_ID, B.BO_BUSINESS_LINE_ID, B.PID, B.PROGRESS_TYPE, B.FIN_GOODS_QUANTITY, 'job初始化线下数据')
    </update>
    <update id="updateProjectHistory">
        UPDATE
            T_BO_BOARD_PROGRESS
        SET
            FIN_GOODS_QUANTITY = FIN_GOODS_QUANTITY + #{finGoodsQuantity},
            NOTE = NOTE || 'job初始化线下数据增加' || #{finGoodsQuantity},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId}
            AND PROGRESS_TYPE = 1
            AND PROGRESS_DATE > TO_DATE(#{progressDate}, 'YYYY-MM-DD')
            AND PROGRESS_DATE &lt;= SYSDATE - 1
    </update>
    <update id="updateLineHistory">
        UPDATE
            T_BO_BOARD_PROGRESS
        SET
            FIN_GOODS_QUANTITY = FIN_GOODS_QUANTITY + #{finGoodsQuantity},
            NOTE = NOTE || 'job初始化线下数据增加' || #{finGoodsQuantity},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND BO_BUSINESS_LINE_ID = #{boBusinessLineId}
            AND PROGRESS_TYPE = 2
            AND PROGRESS_DATE > TO_DATE(#{progressDate}, 'YYYY-MM-DD')
            AND PROGRESS_DATE &lt;= SYSDATE - 1
    </update>

    <select id="queryTaskLineLoadingTonnageList" resultType="com.wtyt.dao.bean.syf.BoBoardProgressBean">
        SELECT
            TO_CHAR(trunc(START_TIME),'yyyy-MM-dd') AS progressDate,
            BO_BUSINESS_LINE_ID  boBusinessLineId,
            <choose>
                <when test="progressRule !=null and progressRule != '' and progressRule == '2'.toString()">
                    NVL(SUM(UNLOADING_TONNAGE), 0) finGoodsQuantity
                </when>
                <otherwise>
                    NVL(SUM(LOADING_TONNAGE), 0) finGoodsQuantity
                </otherwise>
            </choose>
        FROM  T_BO_TRANS_TASK tbtt
        WHERE tbtt.IS_DEL=0
          and TBTT .START_TIME &lt; trunc(SYSDATE)
          AND LOADING_TONNAGE >0
          AND TBTT .BO_BUSINESS_LINE_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        GROUP BY trunc(START_TIME) ,BO_BUSINESS_LINE_ID
        order by trunc(START_TIME)
    </select>

</mapper>
