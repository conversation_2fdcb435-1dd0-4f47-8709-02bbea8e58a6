<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskConfigMapper">

    <resultMap type="BoTaskConfigBean" id="baseResultMap">
        <result property="configType" column="CONFIG_TYPE" jdbcType="VARCHAR"/>
        <result property="configKey" column="CONFIG_KEY" jdbcType="VARCHAR"/>
        <result property="configValue" column="CONFIG_VALUE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getConfigList" resultMap="baseResultMap">
        SELECT
            T.CONFIG_TYPE,
            T.CONFIG_KEY,
            T.CONFIG_VALUE
        FROM T_BO_TASK_CONFIG T
        WHERE T.IS_DEL = 0
        AND T.ORG_ID = #{orgId}
    </select>

    <select id="getConfigListByConfigType" resultMap="baseResultMap">
        SELECT
            T.CONFIG_TYPE,
            T.CONFIG_KEY,
            T.CONFIG_VALUE
        FROM T_BO_TASK_CONFIG T
        WHERE T.IS_DEL = 0
        AND T.ORG_ID = #{orgId}
        AND T.CONFIG_TYPE = #{configType}
    </select>

    <select id="getConfigListByConfigTypeList" resultMap="baseResultMap">
        SELECT
            T.CONFIG_TYPE,
            T.CONFIG_KEY,
            T.CONFIG_VALUE
        FROM T_BO_TASK_CONFIG T
        WHERE T.IS_DEL = 0
        AND T.ORG_ID = #{orgId}
        AND T.CONFIG_TYPE IN
        <foreach collection="configTypeList" item="configType" open="(" separator="," close=")">
            #{configType}
        </foreach>
    </select>

    <select id="getConfigByConfig" resultMap="baseResultMap">
        SELECT
            T.CONFIG_TYPE,
            T.CONFIG_KEY,
            T.CONFIG_VALUE
        FROM T_BO_TASK_CONFIG T
        WHERE T.IS_DEL = 0
        AND T.ORG_ID = #{orgId}
        AND T.CONFIG_TYPE = #{configType}
        AND T.CONFIG_KEY = #{configKey}
    </select>

</mapper>