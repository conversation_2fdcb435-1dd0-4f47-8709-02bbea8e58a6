<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransOrderGoodsCfgMapper">

    <sql id="BASE_COLUMN">
        BO_TRANS_ORDER_GOODS_CFG_ID boTransOrderGoodsCfgId,
        ORG_ID orgId,
        GOODS_DESC goodsDesc,
        GOODS_QUANTITY goodsQuantity
    </sql>

    <select id="queryByOrgIds" resultType="com.wtyt.dao.bean.syf.BoTransOrderGoodsCfgBean">
        select <include refid="BASE_COLUMN"/>
        from T_BO_TRANS_ORDER_GOODS_CFG
        where ORG_ID in
        <foreach collection="orgIds" item="orgId" open="(" close=")" separator=",">
            #{orgId}
        </foreach>
        AND IS_DEL=0
    </select>
</mapper>