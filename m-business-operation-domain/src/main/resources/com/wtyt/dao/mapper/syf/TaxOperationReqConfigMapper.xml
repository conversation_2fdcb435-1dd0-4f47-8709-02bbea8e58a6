<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.TaxOperationReqConfigMapper">
    <resultMap type="com.wtyt.dao.bean.syf.TaxOperationReqConfigBean" id="TaxOperationReqConfigMap">
        <result property="taxOperationReqConfigId" column="TAX_OPERATION_REQ_CONFIG_ID" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="operationTitleKey" column="OPERATION_TITLE_KEY" jdbcType="VARCHAR"/>
        <result property="operationTitle" column="OPERATION_TITLE" jdbcType="VARCHAR"/>
        <result property="keyword" column="KEYWORD" jdbcType="VARCHAR"/>
        <result property="operationType" column="OPERATION_TYPE" jdbcType="VARCHAR"/>
        <result property="state" column="STATE" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="sort" column="SORT" jdbcType="VARCHAR"/>
        <result property="operationContent" column="OPERATION_CONTENT" jdbcType="VARCHAR"/>
        <result property="timeFlag" column="TIME_FLAG" jdbcType="VARCHAR"/>
        <result property="timeNode" column="TIME_NODE" jdbcType="VARCHAR"/>
        <result property="configLevel" column="CONFIG_LEVEL" jdbcType="VARCHAR"/>
        <result property="configType" column="CONFIG_TYPE" jdbcType="VARCHAR"/>
        <result property="configEntityId" column="CONFIG_ENTITY_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="query" resultMap="TaxOperationReqConfigMap">
        SELECT
            <if test="curLevelConfigType !=null and curLevelConfigType!=''">
                CASE WHEN CONFIG_TYPE =#{curLevelConfigType} THEN  TAX_OPERATION_REQ_CONFIG_ID ELSE NULL END TAX_OPERATION_REQ_CONFIG_ID,
            </if>
            ORG_ID,
            NVL(BUSINESS_CUSTOMER,ORG_ID) CONFIG_ENTITY_ID,
            OPERATION_TITLE,
            OPERATION_TITLE_KEY,
            OPERATION_CONTENT,
            KEYWORD ,
            OPERATION_TYPE ,
            STATE,
            SORT,
            NVL(TIME_NODE, TIME_FLAG) TIME_FLAG,
            CONFIG_TYPE,
            CONFIG_LEVEL
        FROM
            T_TAX_OPERATION_REQ_CONFIG
        WHERE
            IS_DEL = 0
            AND ORG_ID = #{orgId}
            <if test="state != null and state !=''">
                AND state=#{state}
            </if>
            <if test="timeFlag != null and timeFlag !=''">
                AND ',' || NVL(TIME_NODE, TIME_FLAG) || ',' LIKE '%,' || #{timeFlag} || ',%'
            </if>
            <if test="operationTitleKeys != null  and operationTitleKeys.size() > 0">
                AND OPERATION_TITLE_KEY IN
                <foreach collection="operationTitleKeys" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="configList != null  and configList.size() > 0">
                AND
                <foreach collection="configList" index="index" item="configInfo" open="(" close=")" separator="or">
                    (
                     CONFIG_TYPE = #{configInfo.configType} AND CASE WHEN  #{configInfo.configType}='1' THEN TO_CHAR(ORG_ID) ELSE BUSINESS_CUSTOMER END = #{configInfo.configEntityId}
                    )
                </foreach>
            </if>
        ORDER BY OPERATION_TITLE_KEY,OPERATION_TYPE,sort ASC
    </select>

    <insert id="insertOperation">
        INSERT INTO T_TAX_OPERATION_REQ_CONFIG (TAX_OPERATION_REQ_CONFIG_ID,ORG_ID,BUSINESS_CUSTOMER
        ,OPERATION_TITLE,OPERATION_TITLE_KEY,OPERATION_CONTENT,KEYWORD ,OPERATION_TYPE ,STATE,SORT,TIME_NODE,CONFIG_LEVEL,CONFIG_TYPE)
        (select
        temp.* from (
        <foreach collection="list" item="item" separator="union all">
            select
            ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()} as taxOperationReqConfigId,
            #{item.orgId,jdbcType = VARCHAR},
            #{item.configEntityId,jdbcType = VARCHAR},
            #{item.operationTitle,jdbcType = VARCHAR},
            #{item.operationTitleKey,jdbcType = VARCHAR},
            #{item.operationContent,jdbcType = VARCHAR},
            #{item.keyword,jdbcType = VARCHAR},
            #{item.operationType,jdbcType = VARCHAR},
            #{item.state,jdbcType = VARCHAR},
            #{item.sort,jdbcType = VARCHAR},
            #{item.timeFlag,jdbcType = VARCHAR},
            #{item.configLevel,jdbcType = VARCHAR},
            #{item.configType,jdbcType = VARCHAR}
            from dual
        </foreach>
        ) temp
        )
    </insert>

    <update id="updateOperation">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_TAX_OPERATION_REQ_CONFIG
            SET LAST_MODIFIED_TIME = SYSDATE
            <if test="item.operationTitle != null and item.operationTitle != ''">
                ,OPERATION_TITLE = #{item.operationTitle}
            </if>
            <if test="item.operationContent != null and item.operationContent != ''">
                ,OPERATION_CONTENT = #{item.operationContent}
            </if>
            <if test="item.keyword != null">
                ,KEYWORD = #{item.keyword}
            </if>
            <if test="item.operationType != null and item.operationType != ''">
                ,OPERATION_TYPE = #{item.operationType}
            </if>
            <if test="item.state != null and item.state != ''">
                ,STATE = #{item.state}
            </if>
            <if test="item.sort != null and item.sort != ''">
                ,SORT = #{item.sort}
            </if>
            <if test="item.timeFlag != null">
                ,TIME_NODE = #{item.timeFlag}
            </if>
            <if test="item.configLevel != null and item.configLevel != ''">
                ,CONFIG_LEVEL = #{item.configLevel}
            </if>
            WHERE TAX_OPERATION_REQ_CONFIG_ID = #{item.taxOperationReqConfigId} AND IS_DEL = 0
        </foreach>
    </update>

    <delete id="delOperation">
        UPDATE T_TAX_OPERATION_REQ_CONFIG SET IS_DEL = 1, LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0
        AND TAX_OPERATION_REQ_CONFIG_ID IN
        <foreach collection="taxOperationReqConfigIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <select id="switchQuery" resultMap="TaxOperationReqConfigMap">
        SELECT
            CONFIG_TYPE,
            CASE
            WHEN CONFIG_TYPE = 1 THEN TO_CHAR(ORG_ID)
            ELSE BUSINESS_CUSTOMER
            END CONFIG_ENTITY_ID
        FROM
            T_TAX_OPERATION_REQ_CONFIG
        WHERE
            IS_DEL = 0
            <if test="orgId!=null and orgId!=''">
                AND ORG_ID = #{orgId}
            </if>
            <if test="configList!=null and configList.size()>0">
                AND
                <foreach collection="configList" index="index" item="configInfo" open="(" close=")" separator="or">
                    (
                    CONFIG_TYPE = #{configInfo.configType} AND CASE WHEN  #{configInfo.configType}='1' THEN TO_CHAR(ORG_ID) ELSE BUSINESS_CUSTOMER END = #{configInfo.configEntityId}
                    )
                </foreach>
            </if>
        GROUP BY
            CONFIG_TYPE,
            CASE
            WHEN CONFIG_TYPE = 1 THEN TO_CHAR(ORG_ID)
            ELSE BUSINESS_CUSTOMER
            END
    </select>

    <select id="queryOrgAllConfigType" resultType="java.lang.String">
        SELECT DISTINCT CONFIG_TYPE FROM T_TAX_OPERATION_REQ_CONFIG WHERE IS_DEL = 0 AND ORG_ID = #{orgId}
    </select>

    <select id="querySettlementConfigByOrgId" resultType="java.lang.String">
        SELECT OPERATION_CONTENT FROM T_TAX_OPERATION_REQ_CONFIG WHERE IS_DEL = 0 AND ORG_ID = #{orgId} AND OPERATION_TYPE =11 AND STATE =1 AND OPERATION_CONTENT IS NOT NULL
    </select>

</mapper>
