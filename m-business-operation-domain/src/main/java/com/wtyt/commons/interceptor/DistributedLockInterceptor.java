package com.wtyt.commons.interceptor;

import cn.hutool.core.util.RandomUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.wtyt.common.annotation.ExcludeJobComponent;
import com.wtyt.common.constants.RedisKeyConstants;
import com.wtyt.common.toolkits.GsonToolkit;
import com.wtyt.common.toolkits.RedisKeyToolkit;
import com.wtyt.commons.annotation.DistributedLock;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁拦截器
 * @author: <EMAIL>
 * @date: 2023年07月25日 09时02分
 */
@Aspect
@Component
@ExcludeJobComponent
public class DistributedLockInterceptor {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(DistributedLockInterceptor.class);

    private final static String DEFAULT_RESOURCE = "userId";
    private final static String DEFAULT_LOCK_HINT = "该操作已被锁定、请稍后";

    private HttpServletRequest request;
    private RedissonClient redissonClient;

    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint proceedingJoinPoint, DistributedLock distributedLock) throws Throwable {
        LOGGER.debug("进入redis分布式锁拦截器");
        // 验证分布式锁参数
        this.verifyDistributedLock(distributedLock);
        String lockHint = StringUtils.defaultIfBlank(distributedLock.lockHint(), DEFAULT_LOCK_HINT);
        long awaitTime = distributedLock.awaitTime();
        long releaseTime = distributedLock.releaseTime();
        boolean autoRelease = distributedLock.autoRelease();
        // 获取或创建锁的key
        String lockKey = this.getOrCreateLockKey(proceedingJoinPoint, distributedLock);
        lockKey = RedisKeyToolkit.getLockKey(RedisKeyConstants.DISTRIBUTED_LOCK, lockKey);
        RLock rLock = this.redissonClient.getLock(lockKey);
        try {
            if (autoRelease) {
                if (rLock.tryLock(awaitTime, TimeUnit.MILLISECONDS)) {
                    LOGGER.debug("开启redis分布式锁");
                    LOGGER.debug("lockKey = {}、awaitTime = {}L、autoRelease = true", lockKey, awaitTime);
                    return proceedingJoinPoint.proceed();
                }
                throw new UnifiedBusinessException(lockHint);
            }
            if (rLock.tryLock(awaitTime, releaseTime, TimeUnit.MILLISECONDS)) {
                LOGGER.debug("开启redis分布式锁");
                LOGGER.debug("lockKey = {}、awaitTime = {}L、releaseTime = {}L、autoRelease = false", lockKey, awaitTime, releaseTime);
                return proceedingJoinPoint.proceed();
            }
            throw new UnifiedBusinessException(lockHint);
        } finally {
            if (autoRelease) {
                // 释放锁
                this.releaseLock(rLock);
            } else {
                LOGGER.debug("{}毫秒后自动释放redis分布式锁", releaseTime);
            }
        }
    }

    /**
     * 验证分布式锁参数
     * @param distributedLock
     */
    private void verifyDistributedLock(DistributedLock distributedLock) {
        long awaitTime = distributedLock.awaitTime();
        long releaseTime = distributedLock.releaseTime();
        boolean autoRelease = distributedLock.autoRelease();
        if (awaitTime < 0L) {
            throw new UnifiedBusinessException("@DistributedLock注解异常:awaitTime需大于等于0");
        }
        if (autoRelease) {
            if (releaseTime > 0L) {
                LOGGER.warn("@DistributedLock注解告警:当autoRelease为true时、releaseTime将不会生效");
            }
        } else {
            if (releaseTime <= 0L) {
                throw new UnifiedBusinessException("@DistributedLock注解异常:当autoRelease为false时、需指定releaseTime");
            }
            if (releaseTime <= awaitTime) {
                throw new UnifiedBusinessException("@DistributedLock注解异常:当autoRelease为false时、releaseTime需大于awaitTime");
            }
        }
    }

    /**
     * 释放锁
     * @param rLock
     */
    private void releaseLock(RLock rLock) {
        if (rLock.isHeldByCurrentThread()) {
            try {
                rLock.unlock();
                LOGGER.debug("释放redis分布式锁");
            } catch (Exception e) {
                String errorMsg = e.getLocalizedMessage();
                LOGGER.error("释放redis分布式锁异常:{}", errorMsg, e);
            }
        }
    }

    /**
     * 获取或创建锁的key
     * @param proceedingJoinPoint
     * @param distributedLock
     * @return
     */
    private String getOrCreateLockKey(ProceedingJoinPoint proceedingJoinPoint, DistributedLock distributedLock) {
        String lockKey = distributedLock.lockKey();
        try {
            Object[] args;
            if (StringUtils.isBlank(lockKey) && (args = proceedingJoinPoint.getArgs()) != null) {
                String resource = StringUtils.defaultIfBlank(distributedLock.resource(), DEFAULT_RESOURCE);
                for (Object arg : args) {
                    JsonObject dataJson = this.getRequestDataJson(arg);
                    if (dataJson != null) {
                        if (!StringUtils.equals(lockKey, DEFAULT_RESOURCE) && !dataJson.has(resource)) {
                            // 如果指定的资源检测不到的话、采用默认资源进行检测、此时再检测不到就不再进行检测处理
                            resource = DEFAULT_RESOURCE;
                        }
                        if (dataJson.has(resource)) {
                            JsonElement jsonElement = dataJson.get(resource);
                            if (jsonElement.isJsonPrimitive() && jsonElement.getAsJsonPrimitive().isString()) {
                                lockKey = this.request.getRequestURI() + resource + jsonElement.getAsString();
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Throwable t) {
            // 不处理
        }
        if (StringUtils.isBlank(lockKey)) {
            lockKey = this.createLockKey();
        }
        return DigestUtils.sha1Hex(lockKey);
    }

    /**
     * 获取请求数据包json
     * @param arg
     * @return
     */
    private JsonObject getRequestDataJson(Object arg) {
        try {
            String json = GsonToolkit.beanToJson(arg);
            JsonObject requestJson = GsonToolkit.json2JsonObject(json);
            if (requestJson.has("data")) {
                JsonElement jsonElement = requestJson.get("data");
                if (jsonElement.isJsonObject()) {
                    return jsonElement.getAsJsonObject();
                }
            }
        } catch (Exception e) {
            // 不处理
        }
        return null;
    }

    /**
     * 创建锁的key
     * @return
     */
    private String createLockKey() {
        try {
            HttpSession session = this.request.getSession();
            if (session != null) {
                return this.request.getRequestURI() + session.getId();
            }
        } catch (Throwable t) {
            // 不处理
        }
        return this.request.getRequestURI() + RandomUtil.randomInt();
    }

    @Autowired(required = false)
    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    @Autowired
    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }
}
