<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransAgreementMapper">
    <insert id="insertBoTransAgreement">
        INSERT INTO T_BO_TRANS_AGREEMENT(BO_TRANS_AGREEMENT_ID, ORG_ID, BO_TRANS_TASK_ID)
        VALUES(#{boTransAgreementId}, #{orgId}, #{boTransTaskId})
    </insert>

    <update id="updateBoTransAgreement">
        UPDATE T_BO_TRANS_AGREEMENT
        SET AGREEMENT_NO_SYS = #{agreement.agreementNoSys}, AGREEMENT_NO_USER = #{agreement.agreementNoUser}, LAST_MODIFIED_TIME = SYSDATE
        <!-- 合同编号新增或修改时，就更新为当前时间 -->
        <if test="isAddAgreementNo">
            ,AGREEMENT_NO_TIME = SYSDATE
        </if>
        WHERE BO_TRANS_AGREEMENT_ID = #{agreement.boTransAgreementId}
    </update>



    <select id="getAgreementInfoByTaskId" resultType="com.wtyt.dao.bean.syf.BoTransAgreementBean">
        SELECT
            BO_TRANS_AGREEMENT_ID boTransAgreementId,
            ORG_ID orgId,
            BO_TRANS_TASK_ID boTransTaskId,
            AGREEMENT_NO_SYS agreementNoSys,
            AGREEMENT_NO_USER agreementNoUser
        FROM T_BO_TRANS_AGREEMENT
        WHERE IS_DEL = 0
        AND BO_TRANS_TASK_ID = #{boTransTaskId}
        AND ROWNUM = 1
    </select>

    <select id="getAgreementInfoById" resultType="com.wtyt.dao.bean.syf.BoTransAgreementBean">
        SELECT
        BO_TRANS_AGREEMENT_ID boTransAgreementId,
        ORG_ID orgId,
        BO_TRANS_TASK_ID boTransTaskId,
        AGREEMENT_NO_SYS agreementNoSys,
        AGREEMENT_NO_USER agreementNoUser
        FROM T_BO_TRANS_AGREEMENT
        WHERE IS_DEL = 0
        AND BO_TRANS_AGREEMENT_ID = #{boTransAgreementId}
    </select>

    <select id="getOrgAgreementCount" resultType="java.lang.Integer">
        <!-- is_del=1也统计-->
        SELECT COUNT(BO_TRANS_AGREEMENT_ID)
        FROM T_BO_TRANS_AGREEMENT
        WHERE ORG_ID = #{orgId}
        AND AGREEMENT_NO_TIME >= TO_DATE(#{nowDate} || ' 00:00:00', 'yyyy-mm-dd HH24:MI:ss')
        AND AGREEMENT_NO_TIME &lt;= TO_DATE(#{nowDate} || ' 23:59:59', 'yyyy-mm-dd HH24:MI:ss')
    </select>

    <select id="getAllAgreementInfo" resultType="com.wtyt.dao.bean.syf.BoTransAgreementBean">
        SELECT
        BO_TRANS_AGREEMENT_ID boTransAgreementId,
        AGREEMENT_INFO agreementInfo
        FROM T_BO_TRANS_AGREEMENT
        WHERE IS_DEL = 0
    </select>

</mapper>
