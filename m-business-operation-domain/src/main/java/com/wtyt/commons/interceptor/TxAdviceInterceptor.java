package com.wtyt.commons.interceptor;

import com.wtyt.common.annotation.ExcludeSelectComponent;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.aop.Advisor;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource;
import org.springframework.transaction.interceptor.RollbackRuleAttribute;
import org.springframework.transaction.interceptor.RuleBasedTransactionAttribute;
import org.springframework.transaction.interceptor.TransactionAttribute;
import org.springframework.transaction.interceptor.TransactionInterceptor;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * AOP事务配置
 * 
 * <AUTHOR> -- 毕创
 *
 */
@ExcludeSelectComponent
@Aspect
@Configuration
public class TxAdviceInterceptor {

    private static final String AOP_POINTCUT_EXPRESSION = "execution(* com.wtyt..service.*Service.*(..))";

    @Bean(name = "txAdviceForSyf")
    public TransactionInterceptor txAdviceForSyf(@Qualifier("syfTransactionManager") DataSourceTransactionManager syfTransactionManager) {
        return txAdvice(syfTransactionManager);
    }

    @Bean(name = "txAdviceAdvisorForSyf")
    public Advisor txAdviceAdvisorForSyf(@Qualifier("txAdviceForSyf") TransactionInterceptor txAdvice) {
        AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();
        pointcut.setExpression(AOP_POINTCUT_EXPRESSION);
        return new DefaultPointcutAdvisor(pointcut, txAdvice);
    }

    public TransactionInterceptor txAdvice(DataSourceTransactionManager transactionManager) {
        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();
        /* 只读事务，不做更新操作 */
        RuleBasedTransactionAttribute readOnlyTx = new RuleBasedTransactionAttribute();
        readOnlyTx.setReadOnly(true);
        readOnlyTx.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        /* 当前存在事务就使用当前事务，当前不存在事务就创建一个新的事务 */
        RuleBasedTransactionAttribute requiredTx = new RuleBasedTransactionAttribute();
        requiredTx.setRollbackRules(Collections.singletonList(new RollbackRuleAttribute(Exception.class)));
        requiredTx.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        RuleBasedTransactionAttribute requiredNewTx = new RuleBasedTransactionAttribute();
        requiredNewTx.setRollbackRules(Collections.singletonList(new RollbackRuleAttribute(Exception.class)));
        requiredNewTx.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);

        RuleBasedTransactionAttribute notSupportedTx = new RuleBasedTransactionAttribute();
        notSupportedTx.setReadOnly(true);
        notSupportedTx.setPropagationBehavior(TransactionDefinition.PROPAGATION_NOT_SUPPORTED);

        RuleBasedTransactionAttribute nestedTx = new RuleBasedTransactionAttribute();
        nestedTx.setRollbackRules(Collections.singletonList(new RollbackRuleAttribute(Exception.class)));
        nestedTx.setPropagationBehavior(TransactionDefinition.PROPAGATION_NESTED);

        Map<String, TransactionAttribute> txMap = new LinkedHashMap<>();
        txMap.put("find*", readOnlyTx);
        txMap.put("query*", readOnlyTx);
        txMap.put("get*", readOnlyTx);
        txMap.put("load*", readOnlyTx);
        txMap.put("rpc*", notSupportedTx);
        txMap.put("add*", requiredTx);
        txMap.put("save*", requiredTx);
        txMap.put("insert*", requiredTx);
        txMap.put("upd*", requiredTx);
        txMap.put("mod*", requiredTx);
        txMap.put("del*", requiredTx);
        txMap.put("proNew*", requiredNewTx);
        txMap.put("proNested*", nestedTx);
        txMap.put("proNotSup*", notSupportedTx);
        txMap.put("firstProNotSup*", null);
        txMap.put("*", requiredTx);
        source.setNameMap(txMap);
        return new TransactionInterceptor(transactionManager, source);
    }

}
