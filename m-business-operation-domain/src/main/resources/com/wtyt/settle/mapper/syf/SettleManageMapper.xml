<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.settle.mapper.syf.SettleManageMapper">
    
    <resultMap id="transFeeVerifyTaskEntity" type="com.wtyt.settle.bean.entity.TransFeeVerifyTaskEntity">
        <id property="boTransFeeVerifyTaskId" column="BO_TRANS_FEE_VERIFY_TASK_ID"/>
        <result property="orgId" column="ORG_ID"/>
        <result property="cartBadgeNo" column="CART_BADGE_NO"/>
        <result property="wbItem" column="WB_ITEM"/>
        <result property="createdTime" column="CREATED_TIME"/>
        <association property="boTransTaskEntity" javaType="com.wtyt.settle.bean.entity.BoTransTaskEntity">
            <id property="boTransTaskId" column="BO_TRANS_TASK_ID"/>
            <result property="createdTime" column="TASK_CREATED_TIME"/>
            <result property="taxWaybillNo" column="TAX_WAYBILL_NO"/>
            <result property="driverName" column="DRIVER_NAME"/>
            <result property="mobileNo" column="MOBILE_NO"/>
            <result property="startProvinceName" column="START_PROVINCE_NAME"/>
            <result property="startCityName" column="START_CITY_NAME"/>
            <result property="startCountyName" column="START_COUNTY_NAME"/>
            <result property="endProvinceName" column="END_PROVINCE_NAME"/>
            <result property="endCityName" column="END_CITY_NAME"/>
            <result property="endCountyName" column="END_COUNTY_NAME"/>
            <result property="wbItem" column="WB_ITEM"/>
            <result property="cartBadgeNo" column="CART_BADGE_NO"/>
            <result property="allFreight" column="ALL_FREIGHT"/>
            <result property="prepaymentsOilCard" column="PREPAYMENTS_OILCARD"/>
            <result property="startTime" column="START_TIME"/>
            <result property="endTime" column="END_TIME"/>
            <result property="mileage" column="MILEAGE"/>
            <result property="goodsName" column="GOODS_NAME"/>
            <result property="goodsAmount" column="GOODS_AMOUNT"/>
            <result property="goodsAmountType" column="GOODS_AMOUNT_TYPE"/>
            <result property="capacityTypeName" column="CAPACITY_TYPE_NAME"/>
            <result property="serviceFee" column="SERVICE_FEE"/>
            <result column="CREATED_USER_NAME" property="createdUserName"/>
            <result column="INVOICE_STATE" property="invoiceState"/>
            <result column="SETTLE_MODE" property="settleMode"/>
            <association property="boTaskReturnTripEntity" javaType="com.wtyt.settle.bean.entity.BoTaskReturnTripEntity">
                <id property="boTaskReturnTripId" column="BO_TASK_RETURN_TRIP_ID"/>
                <result property="startProvinceName" column="TRT_START_PROVINCE_NAME"/>
                <result property="startCityName" column="TRT_START_CITY_NAME"/>
                <result property="startCountyName" column="TRT_START_COUNTY_NAME"/>
                <result property="endProvinceName" column="TRT_END_PROVINCE_NAME"/>
                <result property="endCityName" column="TRT_END_CITY_NAME"/>
                <result property="endCountyName" column="TRT_END_COUNTY_NAME"/>
                <result property="mileage" column="TRT_MILEAGE"/>
                <result property="middleMileage" column="MIDDLE_MILEAGE"/>
                <result property="goodsName" column="TRT_GOODS_NAME"/>
                <result property="goodsAmount" column="TRT_GOODS_AMOUNT"/>
                <result property="goodsAmountType" column="TRT_GOODS_AMOUNT_TYPE"/>
                <result property="expectedEndTime" column="EXPECTED_END_TIME"/>
            </association>
        </association>

        <association property="boTransTaskExtraBean" javaType="com.wtyt.dao.bean.syf.BoTransTaskExtraBean">
            <id property="boTransTaskExtraId" column="BO_TRANS_TASK_ID"/>
            <result property="customerOrderNo" column="CUSTOMER_ORDER_NO"/>
            <result property="arriveEndTime" column="ARRIVE_END_TIME"/>
        </association>
        <collection property="boTransTaskFeeEntityList" ofType="com.wtyt.settle.bean.entity.BoTransTaskFeeEntity">
            <id property="boTransTaskFeeId" column="BO_TRANS_TASK_FEE_ID"/>
            <result property="configName" column="CONFIG_NAME"/>
            <result property="configKey" column="CONFIG_KEY"/>
            <result property="configValue" column="CONFIG_VALUE"/>
            <result property="actualValue" column="ACTUAL_VALUE"/>
            <result property="confirmStatus" column="CONFIRM_STATUS"/>
            <result property="expensiveType" column="EXPENSIVE_TYPE"/>
            <result property="reason" column="REASON"/>
            <result property="createdTime" column="FEE_CREATED_TIME"/>
            <result property="settleVerifyStatus" column="SETTLE_VERIFY_STATUS"/>
            <association property="boTransTaskFeeVrfBean" javaType="com.wtyt.settle.bean.BoTransTaskFeeVrfBean">
                <id property="boTransTaskFeeVrfId" column="BO_TRANS_TASK_FEE_VRF_ID"/>
                <result property="verifyUserName" column="VERIFY_USER_NAME"/>
                <result property="relatedNo" column="RELATED_NO"/>
                <result property="verifyTime" column="VERIFY_TIME"/>
            </association>
        </collection>
    </resultMap>

    <resultMap id="transFeeVerifyTaskEntityDetail" type="com.wtyt.settle.bean.entity.TransFeeVerifyTaskEntity">
        <id property="boTransFeeVerifyTaskId" column="BO_TRANS_FEE_VERIFY_TASK_ID"/>
        <result property="orgId" column="ORG_ID"/>
        <result property="cartBadgeNo" column="CART_BADGE_NO"/>
        <result property="wbItem" column="WB_ITEM"/>
        <result property="createdTime" column="CREATED_TIME"/>
        <association property="boTransTaskEntity" javaType="com.wtyt.settle.bean.entity.BoTransTaskEntity">
            <id property="boTransTaskId" column="BO_TRANS_TASK_ID"/>
            <result property="createdTime" column="TASK_CREATED_TIME"/>
            <result property="taxWaybillNo" column="TAX_WAYBILL_NO"/>
            <result property="driverName" column="DRIVER_NAME"/>
            <result property="mobileNo" column="MOBILE_NO"/>
            <result property="startProvinceName" column="START_PROVINCE_NAME"/>
            <result property="startCityName" column="START_CITY_NAME"/>
            <result property="startCountyName" column="START_COUNTY_NAME"/>
            <result property="endProvinceName" column="END_PROVINCE_NAME"/>
            <result property="endCityName" column="END_CITY_NAME"/>
            <result property="endCountyName" column="END_COUNTY_NAME"/>
            <result property="wbItem" column="WB_ITEM"/>
            <result property="cartBadgeNo" column="CART_BADGE_NO"/>
            <result property="allFreight" column="ALL_FREIGHT"/>
            <result property="prepaymentsOilCard" column="PREPAYMENTS_OILCARD"/>
            <result property="startTime" column="START_TIME"/>
            <result property="endTime" column="END_TIME"/>
            <result property="mileage" column="MILEAGE"/>
            <result property="goodsName" column="GOODS_NAME"/>
            <result property="goodsAmount" column="GOODS_AMOUNT"/>
            <result property="goodsAmountType" column="GOODS_AMOUNT_TYPE"/>
            <result property="capacityTypeName" column="CAPACITY_TYPE_NAME"/>
            <result property="serviceFee" column="SERVICE_FEE"/>
            <association property="boTaskReturnTripEntity" javaType="com.wtyt.settle.bean.entity.BoTaskReturnTripEntity">
                <id property="boTaskReturnTripId" column="BO_TASK_RETURN_TRIP_ID"/>
                <result property="startProvinceName" column="TRT_START_PROVINCE_NAME"/>
                <result property="startCityName" column="TRT_START_CITY_NAME"/>
                <result property="startCountyName" column="TRT_START_COUNTY_NAME"/>
                <result property="endProvinceName" column="TRT_END_PROVINCE_NAME"/>
                <result property="endCityName" column="TRT_END_CITY_NAME"/>
                <result property="endCountyName" column="TRT_END_COUNTY_NAME"/>
                <result property="mileage" column="TRT_MILEAGE"/>
                <result property="middleMileage" column="MIDDLE_MILEAGE"/>
                <result property="goodsName" column="TRT_GOODS_NAME"/>
                <result property="goodsAmount" column="TRT_GOODS_AMOUNT"/>
                <result property="goodsAmountType" column="TRT_GOODS_AMOUNT_TYPE"/>
                <result property="expectedEndTime" column="EXPECTED_END_TIME"/>
            </association>
        </association>

        <association property="boTransTaskExtraBean" javaType="com.wtyt.dao.bean.syf.BoTransTaskExtraBean">
            <id property="boTransTaskExtraId" column="BO_TRANS_TASK_ID"/>
            <result property="customerOrderNo" column="CUSTOMER_ORDER_NO"/>
            <result property="arriveEndTime" column="ARRIVE_END_TIME"/>
        </association>
        <collection property="boTransTaskFeeEntityList" ofType="com.wtyt.settle.bean.entity.BoTransTaskFeeEntity">
            <id property="boTransTaskFeeId" column="BO_TRANS_TASK_FEE_ID"/>
            <result property="configName" column="CONFIG_NAME"/>
            <result property="configKey" column="CONFIG_KEY"/>
            <result property="configValue" column="CONFIG_VALUE"/>
            <result property="actualValue" column="ACTUAL_VALUE"/>
            <result property="confirmStatus" column="CONFIRM_STATUS"/>
            <result property="expensiveType" column="EXPENSIVE_TYPE"/>
            <result property="reason" column="REASON"/>
            <result property="createdTime" column="FEE_CREATED_TIME"/>
            <result property="settleVerifyStatus" column="SETTLE_VERIFY_STATUS"/>
            <association property="boTransTaskFeeVrfBean" javaType="com.wtyt.settle.bean.BoTransTaskFeeVrfBean">
                <id property="boTransTaskFeeVrfId" column="BO_TRANS_TASK_FEE_VRF_ID"/>
                <result property="verifyUserName" column="VERIFY_USER_NAME"/>
                <result property="relatedNo" column="RELATED_NO"/>
                <result property="verifyTime" column="VERIFY_TIME"/>
            </association>
            <collection property="boTransTaskFeeVrfReBeanList" ofType="com.wtyt.settle.bean.BoTransTaskFeeVrfReBean">
                <id property="boTransTaskFeeVrfReId" column="BO_TRANS_TASK_FEE_VRF_RE_ID"/>
                <result property="createdTime" column="vefTime"/>
                <result property="newValue" column="NEW_VALUE"/>
            </collection>
        </collection>
    </resultMap>

    <sql id="verifyQueryConditions">
        AND CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE f.CONFIG_VALUE END > 0
        <if test="tabState != null and tabState !=''">
            AND f.SETTLE_VERIFY_STATUS = #{tabState}
        </if>
        <if test="verifyBeginTime != null and verifyBeginTime !=''">
            AND v.VERIFY_TIME &gt;= TO_DATE(#{verifyBeginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="verifyEndTime != null and verifyEndTime !=''">
            AND v.VERIFY_TIME &lt;= TO_DATE(#{verifyEndTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="configName != null and configName !=''">
            AND f.CONFIG_NAME LIKE '%' || #{configName} || '%'
        </if>
        <if test="configNameList !=null and configNameList.size()>0">
            AND f.CONFIG_NAME IN
            <foreach collection="configNameList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="relatedNo != null and relatedNo !=''">
            AND v.RELATED_NO LIKE '%' || #{relatedNo} || '%'
        </if>
        <if test="verifyUserName != null and verifyUserName !=''">
            AND v.VERIFY_USER_NAME  =  #{verifyUserName}
        </if>
    </sql>
    
    <update id="delFeeVrfByFeeVrfId">
        UPDATE T_BO_TRANS_TASK_FEE_VRF SET IS_DEL = 1, LAST_MODIFIED_TIME = SYSDATE WHERE BO_TRANS_TASK_FEE_VRF_ID = #{boTransTaskFeeVrfId}
    </update>


    <select id="querySettleList" resultType="com.wtyt.dao.bean.syf.BoTransTaskFeeBean">
        SELECT
            f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
            f.CONFIG_KEY configKey,
            f.CONFIG_NAME configName,
            NVL(f.ACTUAL_VALUE, 0) actualValue,
            CASE
            <foreach item="value" index="key" collection="map" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE f.CONFIG_VALUE END AS configValue,
            f.EXPENSIVE_TYPE expensiveType,
            f.BO_TRANS_TASK_ID boTransTaskId,
            t.TAX_WAYBILL_NO taxWaybillNo,
            f.SETTLE_VERIFY_STATUS settleVerifyStatus
        FROM
            T_BO_TRANS_TASK_FEE f
        LEFT JOIN T_BO_TRANS_TASK t ON
            f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        WHERE
            f.IS_DEL = 0
            AND t.IS_DEL = 0
            AND f.IS_VERIFY = 1
            AND t.ORG_ID IN
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            AND CASE
            <foreach item="value" index="key" collection="map" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE f.CONFIG_VALUE END > 0
            <if test="beginTime != null and beginTime !=''">
                AND t.CREATED_TIME &gt;= TO_DATE(#{beginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endTime != null and endTime !=''">
                AND t.CREATED_TIME &lt;= TO_DATE(#{endTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
            </if>
    </select>
    <select id="queryNewSettleList" resultType="com.wtyt.dao.bean.syf.BoTransTaskFeeBean">
        SELECT
            f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
            f.CONFIG_KEY configKey,
            f.CONFIG_NAME configName,
            NVL(f.ACTUAL_VALUE, 0) actualValue,
            CASE
            <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE f.CONFIG_VALUE END AS configValue,
            f.EXPENSIVE_TYPE expensiveType,
            f.BO_TRANS_TASK_ID boTransTaskId,
            f.SETTLE_VERIFY_STATUS settleVerifyStatus
        FROM
            T_BO_TRANS_FEE_VERIFY_TASK vt
        LEFT JOIN T_BO_TRANS_TASK t ON vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        left join T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
        LEFT JOIN T_TAX_WAYBILL w on w.TAX_WAYBILL_ID = t.TAX_WAYBILL_ID  AND w.IS_DEL = 0
        WHERE
            vt.IS_DEL=0
            AND CASE
            <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE f.CONFIG_VALUE END > 0
            AND f.IS_DEL = 0
            AND f.IS_VERIFY = 1
            <include refid="verifyTaskCondition"></include>
        <if test="invoiceState != null and invoiceState == -1 and settleMode != null and settleMode == 0">
            UNION ALL
            SELECT
                f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
                f.CONFIG_KEY configKey,
                f.CONFIG_NAME configName,
                NVL(f.ACTUAL_VALUE, 0) actualValue,
                f.CONFIG_VALUE configValue,
                f.EXPENSIVE_TYPE expensiveType,
                null boTransTaskId,
                f.SETTLE_VERIFY_STATUS settleVerifyStatus
            FROM
                T_BO_TRANS_FEE_VERIFY_TASK vt
            LEFT JOIN T_BO_TRANS_TASK_FEE f ON
                f.BO_TRANS_FEE_VERIFY_TASK_ID = vt.BO_TRANS_FEE_VERIFY_TASK_ID
            WHERE
                vt.IS_DEL = 0
                AND f.CONFIG_VALUE > 0
                AND f.IS_DEL = 0
                AND f.IS_VERIFY = 1
                <include refid="customVerifyTaskCondition"></include>
        </if>
    </select>
    <sql id="verifyTaskCondition">
        AND vt.BO_TRANS_TASK_ID IS NOT NULL
        AND t.IS_DEL = 0
        AND t.ORG_ID IN
        <foreach collection="queryOrgIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        <if test="wbItem != null and wbItem != ''">
            AND INSTR(t.WB_ITEM, #{wbItem}) > 0
        </if>
        <if test="capacityType != null and capacityType != ''">
            AND t.CAPACITY_TYPE = #{capacityType}
        </if>
        <if test="capacityIntegration == 0">
            <if test="capacityTypeName != null and capacityTypeName !=''">
                AND t.CAPACITY_TYPE_NAME = #{capacityTypeName}
            </if>
        </if>
        <if test="capacityIntegration == 1">
            <if test="capacityTypeName != null and capacityTypeName !=''">
                AND e.CPD_POOL_GROUP_NAME = #{capacityTypeName}
            </if>
        </if>
        <if test="cartBadgeNo != null and cartBadgeNo != ''">
            AND INSTR(t.CART_BADGE_NO, #{cartBadgeNo}) > 0
        </if>
        <if test="driverName != null and driverName != ''">
            AND INSTR(t.DRIVER_NAME, #{driverName}) > 0
        </if>
        <if test="mobileNo != null and mobileNo != ''">
            AND INSTR(t.MOBILE_NO, #{mobileNo}) > 0
        </if>
        <if test="startProvinceName != null and startProvinceName != ''">
            AND t.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName != null and startCityName != ''">
            AND t.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName != null and startCountyName != ''">
            AND t.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="endProvinceName != null and endProvinceName != ''">
            AND t.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName != null and endCityName != ''">
            AND t.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName != null and endCountyName != ''">
            AND t.END_COUNTY_NAME = #{endCountyName}
        </if>
        <if test="createBeginTime != null and createBeginTime != ''">
            AND t.CREATED_TIME &gt;= TO_DATE(#{createBeginTime} || ' 00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="createEndTime != null and createEndTime != ''">
            AND t.CREATED_TIME &lt;= TO_DATE(#{createEndTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="createdUserName != null and createdUserName != ''">
            AND INSTR(vt.CREATED_USER_NAME, #{createdUserName}) > 0
        </if>
        <if test="settleMode != null and settleMode != 0">
            AND t.SETTLE_MODE = #{settleMode}
        </if>
        <if test="invoiceState != null and invoiceState != -1">
            AND w.invoice_state = #{invoiceState}
        </if>
    </sql>
    <sql id="customVerifyTaskCondition">
        AND vt.BO_TRANS_TASK_ID IS NULL
        AND vt.ORG_ID IN
        <foreach collection="queryOrgIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        <if test="wbItem != null and wbItem != ''">
            AND INSTR(vt.WB_ITEM, #{wbItem}) > 0
        </if>
        <if test="capacityType != null and capacityType != ''">
            AND 1=0
        </if>
        <if test="capacityTypeName != null and capacityTypeName != ''">
            AND 1=0
        </if>
        <if test="cartBadgeNo != null and cartBadgeNo != ''">
            AND INSTR(vt.CART_BADGE_NO, #{cartBadgeNo}) > 0
        </if>
        <if test="driverName != null and driverName != ''">
            AND 1=0
        </if>
        <if test="mobileNo != null and mobileNo != ''">
            AND 1=0
        </if>
        <if test="startProvinceName != null and startProvinceName != ''">
            AND 1=0
        </if>
        <if test="startCityName != null and startCityName != ''">
            AND 1=0
        </if>
        <if test="startCountyName != null and startCountyName != ''">
            AND 1=0
        </if>
        <if test="endProvinceName != null and endProvinceName != ''">
            AND 1=0
        </if>
        <if test="endCityName != null and endCityName != ''">
            AND 1=0
        </if>
        <if test="endCountyName != null and endCountyName != ''">
            AND 1=0
        </if>
        <if test="createBeginTime != null and createBeginTime != ''">
            AND vt.CREATED_TIME &gt;= TO_DATE(#{createBeginTime} || ' 00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="createEndTime != null and createEndTime != ''">
            AND vt.CREATED_TIME &lt;= TO_DATE(#{createEndTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="createdUserName != null and createdUserName != ''">
            AND INSTR(vt.CREATED_USER_NAME, #{createdUserName}) > 0
        </if>
    </sql>
    <select id="queryFeeVrfList" resultType="com.wtyt.settle.bean.BoTransTaskFeeVrfBean">
        SELECT
            f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
            f.CONFIG_NAME configName,
            f.CONFIG_VALUE configValue,
            NVL(f.ACTUAL_VALUE, 0) actualValue,
            f.EXPENSIVE_TYPE expensiveType,
            f.BO_TRANS_TASK_ID boTransTaskId,
            v.VERIFY_USER_NAME verifyUserName
        FROM
            T_BO_TRANS_TASK_FEE f
        LEFT JOIN T_BO_TRANS_TASK t ON
            f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON
            f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID
            AND v.IS_DEL = 0
        WHERE
            f.IS_DEL = 0
            AND t.IS_DEL = 0
            AND t.ORG_ID IN
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            AND f.IS_VERIFY = 1
            AND (f.CONFIG_VALUE > 0 OR f.EXPENSIVE_TYPE = 2)
            AND v.VERIFY_USER_NAME IS NOT NULL
            <if test="beginTime != null and beginTime !=''">
                AND t.CREATED_TIME &gt;= TO_DATE(#{beginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endTime != null and endTime !=''">
                AND t.CREATED_TIME &lt;= TO_DATE(#{endTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
            </if>
        ORDER BY  f.EXPENSIVE_TYPE desc ,f.config_key
    </select>
    <select id="queryNewFeeVrfList" resultType="com.wtyt.settle.bean.BoTransTaskFeeVrfBean">
        SELECT * FROM
        (
            SELECT
                f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
                f.CONFIG_NAME configName,
                f.CONFIG_VALUE configValue,
                NVL(f.ACTUAL_VALUE, 0) actualValue,
                f.EXPENSIVE_TYPE expensiveType,
                f.BO_TRANS_TASK_ID boTransTaskId,
                v.VERIFY_USER_NAME verifyUserName
            FROM
                T_BO_TRANS_FEE_VERIFY_TASK vt
            INNER JOIN T_BO_TRANS_TASK t ON vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            INNER JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID
            left join T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
            LEFT JOIN T_TAX_WAYBILL w on w.TAX_WAYBILL_ID = t.TAX_WAYBILL_ID  AND w.IS_DEL = 0
            INNER JOIN T_BO_TRANS_TASK_FEE_VRF v ON
                f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID
                AND v.IS_DEL = 0
            WHERE
                vt.IS_DEL = 0
                AND (f.CONFIG_VALUE > 0 OR f.EXPENSIVE_TYPE = 2)
                AND v.VERIFY_USER_NAME IS NOT NULL
                AND f.IS_DEL = 0
                AND f.IS_VERIFY = 1
                <include refid="verifyTaskCondition"></include>
        <if test="invoiceState != null and invoiceState == -1 and settleMode != null and settleMode == 0">
            UNION ALL
            SELECT
                f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
                f.CONFIG_NAME configName,
                f.CONFIG_VALUE configValue,
                NVL(f.ACTUAL_VALUE, 0) actualValue,
                f.EXPENSIVE_TYPE expensiveType,
                NULL boTransTaskId,
                v.VERIFY_USER_NAME verifyUserName
            FROM
                T_BO_TRANS_FEE_VERIFY_TASK vt
            INNER JOIN T_BO_TRANS_TASK_FEE f ON vt.BO_TRANS_FEE_VERIFY_TASK_ID = f.BO_TRANS_FEE_VERIFY_TASK_ID
            INNER JOIN T_BO_TRANS_TASK_FEE_VRF v ON
                f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID
                AND v.IS_DEL = 0
            WHERE
                vt.IS_DEL = 0
                AND f.CONFIG_VALUE > 0
                AND v.VERIFY_USER_NAME IS NOT NULL
                AND f.IS_DEL = 0
                AND f.IS_VERIFY = 1
                <include refid="customVerifyTaskCondition"></include>
        </if>
        ) ORDER BY  expensiveType desc , configName
    </select>
    <select id="queryFeeDetailList" resultMap="feeVerifyTaskMap" >
        SELECT * FROM (
                SELECT
                    vt.BO_TRANS_FEE_VERIFY_TASK_ID,
                    vt.CREATED_TIME,
                    t.BO_TRANS_TASK_ID,
                    vt.ORG_ID,
                    t.TAX_WAYBILL_NO,
                    t.STATE,
                    t.PAY_STATE,
                    t.NODE_ID,
                    t.DRIVER_NAME,
                    t.MOBILE_NO,
                    t.CART_BADGE_NO,
                    t.WB_ITEM,
                    <if test="capacityIntegration == 0">
                        t.CAPACITY_TYPE_NAME ,
                    </if>
                    <if test="capacityIntegration == 1">
                        e.CPD_POOL_GROUP_NAME  CAPACITY_TYPE_NAME,
                    </if>
                    t.START_PROVINCE_NAME,
                    t.START_CITY_NAME,
                    t.START_COUNTY_NAME,
                    t.END_PROVINCE_NAME,
                    t.END_CITY_NAME,
                    t.END_COUNTY_NAME,
                    vt.CREATED_USER_NAME,
                    w.INVOICE_STATE,
                    t.SETTLE_MODE
                FROM
                    T_BO_TRANS_FEE_VERIFY_TASK vt
                INNER JOIN T_BO_TRANS_TASK t ON
                    vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
                LEFT JOIN T_TAX_WAYBILL w on w.TAX_WAYBILL_ID = t.TAX_WAYBILL_ID  AND w.IS_DEL = 0
                left join T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
                WHERE
                    vt.IS_DEL = 0
                    <include refid="verifyTaskCondition"></include>
                    AND EXISTS (
                        SELECT 1 FROM T_BO_TRANS_TASK_FEE f WHERE t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID
                        AND f.IS_DEL = 0  AND f.IS_VERIFY = 1
                        AND CASE
                        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                            WHEN f.CONFIG_KEY = '${key}'
                            THEN ${value}
                        </foreach>
                        ELSE f.CONFIG_VALUE END > 0
                    )
                <if test="invoiceState != null and invoiceState == -1 and settleMode != null and settleMode == 0">
                    UNION ALL
                        SELECT
                            vt.BO_TRANS_FEE_VERIFY_TASK_ID,
                            vt.CREATED_TIME,
                            NULL BO_TRANS_TASK_ID,
                            vt.ORG_ID,
                            NULL TAX_WAYBILL_NO,
                            NULL STATE,
                            NULL PAY_STATE,
                            NULL NODE_ID,
                            NULL DRIVER_NAME,
                            NULL MOBILE_NO,
                            vt.CART_BADGE_NO,
                            vt.WB_ITEM,
                            NULL CAPACITY_TYPE_NAME,
                            NULL START_PROVINCE_NAME,
                            NULL START_CITY_NAME,
                            NULL START_COUNTY_NAME,
                            NULL END_PROVINCE_NAME,
                            NULL END_CITY_NAME,
                            NULL END_COUNTY_NAME,
                            vt.CREATED_USER_NAME,
                            NULL  INVOICE_STATE,
                            NULL  SETTLE_MODE
                        FROM
                            T_BO_TRANS_FEE_VERIFY_TASK vt
                        WHERE
                            vt.IS_DEL = 0
                            <include refid="customVerifyTaskCondition"></include>
                            AND EXISTS (
                                SELECT 1 FROM T_BO_TRANS_TASK_FEE f WHERE vt.BO_TRANS_FEE_VERIFY_TASK_ID = f.BO_TRANS_FEE_VERIFY_TASK_ID
                                AND f.IS_DEL = 0  AND f.IS_VERIFY = 1 AND f.CONFIG_VALUE > 0
                            )
                </if>
            ) ORDER BY CREATED_TIME DESC
    </select>
    <resultMap id="feeVerifyTaskMap" type="com.wtyt.settle.bean.FeeVerifyTaskDetail">
        <id column="BO_TRANS_FEE_VERIFY_TASK_ID" property="boTransFeeVerifyTaskId"></id>
        <result column="ORG_ID" property="orgId"></result>
        <result column="BO_TRANS_TASK_ID" property="boTransTaskId"></result>
        <result column="TAX_WAYBILL_NO" property="taxWaybillNo"></result>
        <result column="STATE" property="state"></result>
        <result column="PAY_STATE" property="payState"></result>
        <result column="NODE_ID" property="nodeId"></result>
        <result column="DRIVER_NAME" property="driverName"></result>
        <result column="MOBILE_NO" property="mobileNo"></result>
        <result column="CART_BADGE_NO" property="cartBadgeNo"></result>
        <result column="WB_ITEM" property="wbItem"></result>
        <result column="CAPACITY_TYPE_NAME" property="capacityTypeName"></result>
        <result column="START_PROVINCE_NAME" property="startProvinceName"></result>
        <result column="START_CITY_NAME" property="startCityName"></result>
        <result column="START_COUNTY_NAME" property="startCountyName"></result>
        <result column="END_PROVINCE_NAME" property="endProvinceName"></result>
        <result column="END_CITY_NAME" property="endCityName"></result>
        <result column="END_COUNTY_NAME" property="endCountyName"></result>
        <result column="CREATED_USER_NAME" property="createdUserName"></result>
        <result column="INVOICE_STATE" property="invoiceState"></result>
        <result column="SETTLE_MODE" property="settleMode"></result>
    </resultMap>
    <select id="verifyUserList" resultType="com.wtyt.settle.bean.VerifyUser">
        SELECT
            V.VERIFY_USER_NAME  verifyUserName
        FROM
        T_BO_TRANS_TASK t      LEFT JOIN  T_BO_TRANS_TASK_FEE f  ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        Left join T_BO_TRANS_TASK_FEE_VRF v on f.BO_TRANS_TASK_FEE_ID =v.BO_TRANS_TASK_FEE_ID
        WHERE t.IS_DEL = 0 AND f.IS_DEL = 0 AND V.IS_DEL=0  AND  v.VERIFY_USER_NAME  IS NOT null
        AND t.ORG_ID  in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        group by  v.VERIFY_USER_NAME
    </select>
    <select id="queryFeeVrfByFeeId" resultType="com.wtyt.settle.bean.BoTransTaskFeeVrfBean">
        SELECT
            f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
            f.CONFIG_KEY configKey,
            f.CONFIG_NAME configName,
            TO_CHAR(
            CASE
            <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE NVL(f.CONFIG_VALUE, 0) END, 'FM999999999990.00') configValue,
            NVL(f.ACTUAL_VALUE, 0) actualValue,
            f.CONFIRM_STATUS confirmStatus,
            f.EXPENSIVE_TYPE expensiveType,
            f.BO_TRANS_TASK_ID boTransTaskId,
            v.BO_TRANS_TASK_FEE_VRF_ID boTransTaskFeeVrfId
        FROM
            T_BO_TRANS_TASK_FEE f
        LEFT JOIN T_BO_TRANS_TASK t ON
            f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON
            f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID
            AND v.IS_DEL = 0
        WHERE
            f.IS_DEL = 0
            AND f.BO_TRANS_TASK_FEE_ID = #{boTransTaskFeeId}
    </select>

    <select id="queryRelatedNo" resultType="java.lang.String">
        SELECT
            t.RELATED_NO  relatedNo
        FROM
            T_BO_TRANS_TASK_FEE_VRF t
        WHERE t.IS_DEL = 0
          AND t.BO_TRANS_TASK_FEE_ID = #{boTransTaskFeeId}

    </select>


    <update id="mergeIntoTaskFeeVaf">
        merge into T_BO_TRANS_TASK_FEE_VRF a
        using (select #{boTransTaskFeeVrfId} boTransTaskFeeVrfId ,
                      #{boTransTaskFeeId} boTransTaskFeeId,
                      #{verifyTime} verifyTime,
                      #{relatedNo} relatedNo,
                      #{verifyUserId} verifyUserId ,
                      #{verifyUserJobName} verifyUserJobName ,
                      #{verifyUserName} verifyUserName
                  from dual) b
        on (a.BO_TRANS_TASK_FEE_ID = b.boTransTaskFeeId  AND a.is_del = 0)
        when matched then
            update set VERIFY_TIME = to_date(#{verifyTime,jdbcType=TIMESTAMP}, 'yyyy-mm-dd hh24:mi:ss'),
                       RELATED_NO = b.relatedNo,
                       VERIFY_USER_ID = b.verifyUserId,
                       VERIFY_USER_JOB_NAME = b.verifyUserJobName,
                       VERIFY_USER_NAME = b.verifyUserName,
                       last_modified_time = sysdate
        when not matched then
            insert
            (BO_TRANS_TASK_FEE_VRF_ID, BO_TRANS_TASK_FEE_ID, VERIFY_TIME, RELATED_NO, VERIFY_USER_ID, VERIFY_USER_JOB_NAME,VERIFY_USER_NAME)
            values
                (b.boTransTaskFeeVrfId,b.boTransTaskFeeId, to_date(#{verifyTime,jdbcType=TIMESTAMP}, 'yyyy-mm-dd hh24:mi:ss'),b.relatedNo, b.verifyUserId, b.verifyUserJobName, b.verifyUserName)
    </update>

    <update id="updateRelatedNo">
        merge into T_BO_TRANS_TASK_FEE_VRF a
        using (select #{boTransTaskFeeVrfId} boTransTaskFeeVrfId ,
                      #{boTransTaskFeeId} boTransTaskFeeId,
                      #{relatedNo} relatedNo
               from dual) b
        on (a.BO_TRANS_TASK_FEE_ID = b.boTransTaskFeeId  AND a.is_del = 0)
        when matched then
            update set
                       RELATED_NO = b.relatedNo,
                       last_modified_time = sysdate
        when not matched then
            insert
            (BO_TRANS_TASK_FEE_VRF_ID, BO_TRANS_TASK_FEE_ID, RELATED_NO)
            values
                (b.boTransTaskFeeVrfId,b.boTransTaskFeeId,b.relatedNo)
    </update>


    <insert id="insertFeeVerifyRecord">
        INSERT INTO T_BO_TRANS_TASK_FEE_VRF_RE(BO_TRANS_TASK_FEE_VRF_RE_ID,BO_TRANS_TASK_FEE_ID,EXPENSE_NAME,MODIFY_USER_ID,MODIFY_USER_NAME,MODIFY_USER_JOB_NAME,OLD_VALUE,NEW_VALUE,MODIFY_TYPE,VERIFY_OPT_TYPE,VERIFY_FROM_TYPE)
        values(#{boTransTaskFeeVrfReId},#{boTransTaskFeeId},#{expenseName},#{modifyUserId},#{modifyUserName},#{modifyUserJobName},#{oldValue},#{newValue},#{modifyType},#{verifyOptType},#{verifyFromType})
    </insert>

    <update id="delFeeVrfRecordByFeeVrfId">
        UPDATE T_BO_TRANS_TASK_FEE_VRF_RE SET IS_DEL = 1, LAST_MODIFIED_TIME = SYSDATE WHERE BO_TRANS_TASK_FEE_ID = #{boTransTaskFeeId} and IS_DEL=0 and MODIFY_TYPE in (0,3)
    </update>
    <update id="updateApprovalTaskFee">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE
            T_BO_TRANS_TASK_FEE
            SET
            ACTUAL_VALUE = #{item.configValue},
            SETTLE_VERIFY_STATUS = 2,
            LAST_MODIFIED_TIME = SYSDATE
            WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_FEE_ID = #{item.boTransTaskFeeId}
        </foreach>
    </update>

    <sql id="feeQueryWhereSql">
        f.IS_DEL = 0 AND t.IS_DEL = 0
        <if test="orgIdList !=null and orgIdList.size()>0">
            AND T.ORG_ID IN
            <foreach collection="orgIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        AND f.CONFIG_VALUE > 0 and f.EXPENSIVE_TYPE in (0,3)
        <if test="beginTime != null and beginTime !=''">
            AND t.CREATED_TIME &gt;= TO_DATE(#{beginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND t.CREATED_TIME &lt;= TO_DATE(#{endTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="tabState != null and tabState !=''">
            <choose>
                <when test="tabState==0">
                    AND NVL(f.ACTUAL_VALUE,0) = 0
                </when>
                <when test="tabState==1">
                    AND NVL(f.ACTUAL_VALUE,0) &gt; 0 and f.ACTUAL_VALUE &lt; f.CONFIG_VALUE
                </when>
                <when test="tabState==2">
                    AND NVL(f.ACTUAL_VALUE,0) &gt; 0 and f.ACTUAL_VALUE &gt;= f.CONFIG_VALUE
                </when>
            </choose>
        </if>
        <if test="taxWaybillNo != null and taxWaybillNo !=''">
            AND t.TAX_WAYBILL_NO LIKE '%' || #{taxWaybillNo} || '%'
        </if>
        <if test="taxWaybillNoList !=null and taxWaybillNoList.size()>0">
            AND T.TAX_WAYBILL_NO IN
            <foreach collection="taxWaybillNoList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="customerOrderNo != null and customerOrderNo !=''">
            AND e.CUSTOMER_ORDER_NO LIKE '%' || #{customerOrderNo} || '%'
        </if>
        <if test="customerOrderNoList !=null and customerOrderNoList.size()>0">
            AND e.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="configName != null and configName !=''">
            AND f.CONFIG_NAME LIKE '%' || #{configName} || '%'
        </if>
        <if test="configNameList !=null and configNameList.size()>0">
            AND f.CONFIG_NAME IN
            <foreach collection="configNameList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="relatedNo != null and relatedNo !=''">
            AND v.RELATED_NO LIKE '%' || #{relatedNo} || '%'
        </if>
        <if test="verifyUserName != null and verifyUserName !=''">
            AND v.VERIFY_USER_NAME  =  #{verifyUserName}
        </if>
        <if test="startProvinceName !=null and startProvinceName !='' ">
            AND t.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName !=null and startCityName !='' ">
            AND t.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName !=null and startCountyName !='' ">
            AND t.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="endProvinceName !=null and endProvinceName !='' ">
            AND t.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName !=null and endCityName !='' ">
            AND t.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName !=null and endCountyName !='' ">
            AND t.END_COUNTY_NAME = #{endCountyName}
        </if>
        <if test="payer !=null and payer !='' ">
            AND e.PAYER   LIKE '%' || #{payer} || '%'
        </if>
        <if test="customerName !=null and customerName !='' ">
            AND T.WB_ITEM   LIKE '%' || #{customerName} || '%'
        </if>
    </sql>

    <sql id="payableFreightQueryWhereSql">
        f.IS_DEL = 0 AND t.IS_DEL = 0
        <if test="orgIdList !=null and orgIdList.size()>0">
            AND T.ORG_ID IN
            <foreach collection="orgIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        AND f.EXPENSIVE_TYPE =2
        AND DECODE(f.CONFIG_KEY,'payableFreight',NVL(t.ALL_FREIGHT, 0) + NVL(t.SERVICE_FEE , 0) - NVL(t.PREPAYMENTS_OILCARD, 0) ,NVL(t.PREPAYMENTS_OILCARD,0)) >0
        <if test="beginTime != null and beginTime !=''">
            AND t.CREATED_TIME &gt;= TO_DATE(#{beginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND t.CREATED_TIME &lt;= TO_DATE(#{endTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="tabState != null and tabState !=''">
            <choose>
                <when test="tabState==0">
                    AND NVL(f.ACTUAL_VALUE,0) = 0
                </when>
                <when test="tabState==1">
                    AND NVL(f.ACTUAL_VALUE,0) &gt; 0 and f.ACTUAL_VALUE &lt;  DECODE(f.CONFIG_KEY,'payableFreight',NVL(t.ALL_FREIGHT, 0) + NVL(t.SERVICE_FEE , 0) - NVL(t.PREPAYMENTS_OILCARD, 0),NVL(t.PREPAYMENTS_OILCARD,0))
                </when>
                <when test="tabState==2">
                    AND NVL(f.ACTUAL_VALUE,0) &gt; 0 and f.ACTUAL_VALUE &gt;= DECODE(f.CONFIG_KEY,'payableFreight',NVL(t.ALL_FREIGHT, 0) + NVL(t.SERVICE_FEE , 0) - NVL(t.PREPAYMENTS_OILCARD, 0),NVL(t.PREPAYMENTS_OILCARD,0))
                </when>
            </choose>
        </if>
        <if test="taxWaybillNo != null and taxWaybillNo !=''">
            AND t.TAX_WAYBILL_NO LIKE '%' || #{taxWaybillNo} || '%'
        </if>
        <if test="taxWaybillNoList !=null and taxWaybillNoList.size()>0">
            AND T.TAX_WAYBILL_NO IN
            <foreach collection="taxWaybillNoList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="customerOrderNo != null and customerOrderNo !=''">
            AND e.CUSTOMER_ORDER_NO LIKE '%' || #{customerOrderNo} || '%'
        </if>
        <if test="customerOrderNoList !=null and customerOrderNoList.size()>0">
            AND e.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="configName != null and configName !=''">
            AND f.CONFIG_NAME LIKE '%' || #{configName} || '%'
        </if>
        <if test="configNameList !=null and configNameList.size()>0">
            AND f.CONFIG_NAME IN
            <foreach collection="configNameList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="relatedNo != null and relatedNo !=''">
            AND v.RELATED_NO LIKE '%' || #{relatedNo} || '%'
        </if>
        <if test="verifyUserName != null and verifyUserName !=''">
            AND v.VERIFY_USER_NAME  =  #{verifyUserName}
        </if>
        <if test="startProvinceName !=null and startProvinceName !='' ">
            AND t.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName !=null and startCityName !='' ">
            AND t.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName !=null and startCountyName !='' ">
            AND t.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="endProvinceName !=null and endProvinceName !='' ">
            AND t.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName !=null and endCityName !='' ">
            AND t.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName !=null and endCountyName !='' ">
            AND t.END_COUNTY_NAME = #{endCountyName}
        </if>
        <if test="payer !=null and payer !='' ">
            AND e.PAYER   LIKE '%' || #{payer} || '%'
        </if>
        <if test="customerName !=null and customerName !='' ">
            AND T.WB_ITEM   LIKE '%' || #{customerName} || '%'
        </if>
    </sql>

    <select id="feeQueryStatusCount" resultType="com.wtyt.settle.bean.TabInfoBean" parameterType="com.wtyt.settle.bean.Req5329313Bean">
        select
            CASE  WHEN actualValue = 0 THEN '0' WHEN actualValue &gt;= configValue THEN '2' ELSE '1' END  AS tabState,
            COUNT(*) AS tabCount
        from (SELECT
        f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
        f.CONFIG_VALUE configValue,
        NVL(f.ACTUAL_VALUE, 0) actualValue
        FROM  T_BO_TRANS_TASK t left join T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
            left join  T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            left join T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID and v.IS_DEL=0
        WHERE
        <include refid="feeQueryWhereSql"/>
        union all
        SELECT
        f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
        decode(f.CONFIG_KEY,'payableFreight', NVL(t.ALL_FREIGHT, 0) + NVL(t.SERVICE_FEE , 0) -NVL(t.PREPAYMENTS_OILCARD,0) ,NVL(t.PREPAYMENTS_OILCARD,0)) as configValue,
        NVL(f.ACTUAL_VALUE, 0) actualValue
        FROM  T_BO_TRANS_TASK t left join T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
            left join  T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            left join T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID and v.IS_DEL=0
        WHERE
        <include refid="payableFreightQueryWhereSql"/>
       ) GROUP BY  CASE  WHEN actualValue = 0 THEN '0' WHEN actualValue &gt;= configValue THEN '2' ELSE '1' END
    </select>


    <select id="feeVerifyList" resultType="com.wtyt.settle.bean.TaskFeeVerifyBean" parameterType="com.wtyt.settle.bean.Req5329313Bean">
        select
        boTransTaskFeeId,configKey,configName,reason,boTransTaskId,taxWaybillNo,customerOrderNo,expensiveType,receivableAmount,receivedAmount,allFreight,serviceFee,relatedNo,
        verifyTime,verifyUserName,verifyStatus,confirmStatus,driverName,mobileNo,startProvinceName,startCityName,startCountyName,endProvinceName,endCityName,endCountyName, wbItem, orgId, TO_CHAR(CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') taskCreatedTime
        from (SELECT
        f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
        f.CONFIG_KEY configKey,
        f.CONFIG_NAME configName,
        f.REASON  reason,
        T.BO_TRANS_TASK_ID boTransTaskId,
        t.TAX_WAYBILL_NO taxWaybillNo,
        e.CUSTOMER_ORDER_NO customerOrderNo,
        f.EXPENSIVE_TYPE expensiveType,
        TO_CHAR(NVL(f.CONFIG_VALUE, 0), 'FM999999999990.00') receivableAmount,
        TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999999990.00') receivedAmount,
        TO_CHAR(NVL(t.ALL_FREIGHT, 0), 'FM999999999990.00') allFreight,
        TO_CHAR(NVL(t.SERVICE_FEE, 0), 'FM999999999990.00') serviceFee,
        v.RELATED_NO relatedNo,
        TO_CHAR(v.VERIFY_TIME, 'yyyy-MM-dd Hh24:mi:ss') verifyTime,
        v.VERIFY_USER_NAME verifyUserName,
        CASE  WHEN NVL(f.ACTUAL_VALUE, 0) = 0 THEN '0' WHEN NVL(f.ACTUAL_VALUE, 0) &gt;= NVL(f.CONFIG_VALUE, 0) THEN '2' ELSE '1' END  AS  verifyStatus,
        NVL(f.CONFIRM_STATUS, 0)  confirmStatus,
        t.DRIVER_NAME driverName,
        t.MOBILE_NO mobileNo,
        t.START_PROVINCE_NAME startProvinceName,
        t.START_CITY_NAME startCityName,
        t.START_COUNTY_NAME startCountyName,
        t.END_PROVINCE_NAME endProvinceName,
        t.END_CITY_NAME endCityName,
        t.END_COUNTY_NAME endCountyName,
        t.CREATED_TIME,
        t.BO_TRANS_TASK_ID,
        t.WB_ITEM  wbItem,
        t.ORG_ID orgId
        FROM  T_BO_TRANS_TASK t  left join T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
            left join  T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            left join T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID and v.IS_DEL=0
        WHERE
        <include refid="feeQueryWhereSql"/>
        union all
        SELECT
        f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
        f.CONFIG_KEY configKey,
        f.CONFIG_NAME configName,
        f.REASON  reason,
        T.BO_TRANS_TASK_ID boTransTaskId,
        t.TAX_WAYBILL_NO taxWaybillNo,
        e.CUSTOMER_ORDER_NO customerOrderNo,
        f.EXPENSIVE_TYPE expensiveType,
        TO_CHAR(DECODE(f.CONFIG_KEY,'payableFreight', NVL(t.ALL_FREIGHT, 0) + NVL(t.SERVICE_FEE ,0) - NVL(t.PREPAYMENTS_OILCARD, 0),NVL(t.PREPAYMENTS_OILCARD, 0)) , 'FM999999990.00') receivableAmount,
        TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999999990.00') receivedAmount,
        TO_CHAR(NVL(t.ALL_FREIGHT, 0)-NVL(t.PREPAYMENTS_OILCARD, 0), 'FM999999999990.00') allFreight,
        TO_CHAR(NVL(t.SERVICE_FEE, 0), 'FM999999999990.00') serviceFee,
        v.RELATED_NO relatedNo,
        TO_CHAR(v.VERIFY_TIME, 'yyyy-MM-dd Hh24:mi:ss') verifyTime,
        v.VERIFY_USER_NAME verifyUserName,
        CASE  WHEN
            NVL(f.ACTUAL_VALUE, 0) = 0 THEN '0'
            WHEN f.CONFIG_KEY ='payableFreight' and   NVL(f.ACTUAL_VALUE, 0) &gt;= NVL(t.ALL_FREIGHT, 0) + NVL(t.SERVICE_FEE , 0) - NVL(t.PREPAYMENTS_OILCARD, 0) THEN '2'
            WHEN f.CONFIG_KEY ='prepaymentsOilcard' and   NVL(f.ACTUAL_VALUE, 0) &gt;= NVL(t.PREPAYMENTS_OILCARD, 0)  THEN '2'
            ELSE '1' END  AS  verifyStatus,
        NVL(f.CONFIRM_STATUS, 0)  confirmStatus,
        t.DRIVER_NAME driverName,
        t.MOBILE_NO mobileNo,
        t.START_PROVINCE_NAME startProvinceName,
        t.START_CITY_NAME startCityName,
        t.START_COUNTY_NAME startCountyName,
        t.END_PROVINCE_NAME endProvinceName,
        t.END_CITY_NAME endCityName,
        t.END_COUNTY_NAME endCountyName,
        t.CREATED_TIME,
        t.BO_TRANS_TASK_ID,
        t.WB_ITEM  wbItem,
        t.ORG_ID orgId
        FROM  T_BO_TRANS_TASK t  left join T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
            left join  T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            left join T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID and v.IS_DEL=0
        WHERE
        <include refid="payableFreightQueryWhereSql"/>
        ) order by CREATED_TIME desc ,BO_TRANS_TASK_ID desc ,expensiveType desc, configKey asc
    </select>

    <select id="queryVerifyRecordCount" resultType="java.lang.String">
        SELECT
            t.BO_TRANS_TASK_FEE_ID  boTransTaskFeeId
        FROM
        T_BO_TRANS_TASK_FEE_VRF_RE t
        WHERE t.IS_DEL = 0 and MODIFY_TYPE =0
        AND t.BO_TRANS_TASK_FEE_ID in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        group by BO_TRANS_TASK_FEE_ID
    </select>

    <select id="queryVerifyPopSummaryInfo" parameterType="com.wtyt.settle.bean.Req5329318Bean" resultType="com.wtyt.settle.bean.TaskFeeVerifyBean">
        SELECT
            boTransTaskFeeId,
            boTransTaskId,
            expensiveType,
            TO_CHAR(receivableAmount, 'FM999999999990.00') receivableAmount,
            TO_CHAR(receivedAmount, 'FM999999999990.00') receivedAmount,
            TO_CHAR(payableAmount, 'FM999999999990.00') payableAmount,
            TO_CHAR(paidAmount, 'FM999999999990.00') paidAmount,
            verifyStatus,
            confirmStatus
        from <include refid="queryVerifyInfo"/>
        WHERE 1 = 1
        <if test="boTransTaskFeeIdList !=null and boTransTaskFeeIdList.size()>0">
            AND boTransTaskFeeId IN
            <foreach collection="boTransTaskFeeIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryVerifyListSummaryInfo" parameterType="com.wtyt.settle.bean.Req5329318Bean" resultType="com.wtyt.settle.bean.Resp5329318Bean">
        select
            COUNT(boTransTaskFeeId) taskFeeCount,
            COUNT(DISTINCT boTransTaskId)taskCount,
            TO_CHAR(SUM(receivableAmount), 'FM999999999990.00') receivableAmount,
            TO_CHAR(SUM(receivedAmount), 'FM999999999990.00') receivedAmount,
            TO_CHAR(SUM(payableAmount), 'FM999999999990.00') payableAmount,
            TO_CHAR(SUM(paidAmount), 'FM999999999990.00') paidAmount
        from <include refid="queryVerifyInfo"/>
        WHERE 1 = 1
        <if test="boTransTaskFeeIdList !=null and boTransTaskFeeIdList.size()>0">
            AND boTransTaskFeeId IN
            <foreach collection="boTransTaskFeeIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryNewVerifyPopSummaryInfo" resultType="com.wtyt.settle.bean.TaskFeeVerifyBean">
        SELECT
            boTransTaskFeeId,
            boTransTaskId,
            expensiveType,
            TO_CHAR(receivableAmount, 'FM999999999990.00') receivableAmount,
            TO_CHAR(receivedAmount, 'FM999999999990.00') receivedAmount,
            TO_CHAR(payableAmount, 'FM999999999990.00') payableAmount,
            TO_CHAR(paidAmount, 'FM999999999990.00') paidAmount,
            verifyStatus,
            confirmStatus
        from <include refid="queryNewVerifyInfo"/>
        WHERE
        <choose>
            <when test="boTransFeeVerifyTaskIds !=null and boTransFeeVerifyTaskIds.size()>0 and boTransTaskFeeIdList !=null and boTransTaskFeeIdList.size()>0">
                (BO_TRANS_FEE_VERIFY_TASK_ID IN
                <foreach collection="boTransFeeVerifyTaskIds" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
                    OR boTransTaskFeeId IN
                <foreach collection="boTransTaskFeeIdList" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>)
            </when>
            <when test="boTransFeeVerifyTaskIds !=null and boTransFeeVerifyTaskIds.size()>0">
                BO_TRANS_FEE_VERIFY_TASK_ID IN
                <foreach collection="boTransFeeVerifyTaskIds" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            </when>
            <when test="boTransTaskFeeIdList !=null and boTransTaskFeeIdList.size()>0">
                boTransTaskFeeId IN
                <foreach collection="boTransTaskFeeIdList" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1 = 1
            </otherwise>
        </choose>
    </select>
    <select id="queryNewVerifyListSummaryInfo" resultType="com.wtyt.settle.bean.Resp5329318Bean">
        select
            COUNT(boTransTaskFeeId) taskFeeCount,
            COUNT(DISTINCT boTransTaskId)taskCount,
            TO_CHAR(SUM(receivableAmount), 'FM999999999990.00') receivableAmount,
            TO_CHAR(SUM(receivedAmount), 'FM999999999990.00') receivedAmount,
            TO_CHAR(SUM(payableAmount), 'FM999999999990.00') payableAmount,
            TO_CHAR(SUM(paidAmount), 'FM999999999990.00') paidAmount
        from <include refid="queryNewVerifyInfo"/>
        WHERE 1 = 1
        <if test="boTransFeeVerifyTaskIds !=null and boTransFeeVerifyTaskIds.size()>0">
            AND BO_TRANS_FEE_VERIFY_TASK_ID IN
            <foreach collection="boTransFeeVerifyTaskIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryReceivableFee" resultType="java.lang.String">
        SELECT
            SUM(CONFIG_VALUE)
        FROM
            T_BO_TRANS_TASK_FEE f
        INNER JOIN T_BO_TRANS_TASK t ON
            f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        WHERE
            f.is_del = 0
            AND t.IS_DEL = 0
            AND f.CONFIG_KEY = 'receivable'
            AND f.BO_TRANS_TASK_ID IN
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
    </select>
    <select id="queryTotalAutoVerify" resultType="java.lang.String">
        SELECT *
        FROM (SELECT TO_CHAR(NVL(NEW_VALUE, 0), 'FM999999999990.00') totalAutoVerify
              FROM T_BO_TRANS_TASK_FEE_VRF_RE
              WHERE is_del = 0
                AND BO_TRANS_TASK_FEE_ID = #{boTransTaskFeeId}
                AND VERIFY_FROM_TYPE = 1
                AND MODIFY_TYPE = 0
              ORDER BY CREATED_TIME DESC)
        WHERE ROWNUM = 1
    </select>
    <select id="queryTaskVerifyList" resultType="com.wtyt.settle.bean.BoTaskVerifyBean">
        SELECT
            BO_TRANS_TASK_ID boTransTaskId,
            TO_CHAR(SUM( CASE WHEN EXPENSIVE_TYPE = 3 THEN NVL(ACTUAL_VALUE, 0) ELSE 0 END), 'FM999999999990.00') totalReceivedAmount,
            TO_CHAR(SUM( CASE WHEN EXPENSIVE_TYPE != 3 THEN NVL(ACTUAL_VALUE, 0) ELSE 0 END), 'FM999999999990.00') totalPaidAmount
        FROM
            T_BO_TRANS_TASK_FEE
        WHERE
            IS_DEL = 0
            AND IS_VERIFY = 1
            AND BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIds" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        GROUP BY
            BO_TRANS_TASK_ID
    </select>
    <select id="queryVerifyStatusCount" resultType="com.wtyt.settle.bean.TabInfoBean">
        SELECT
            SETTLE_VERIFY_STATUS tabState,
            COUNT(*) AS tabCount
        FROM
            T_BO_TRANS_TASK t
        LEFT JOIN T_BO_TRANS_TASK_EXTRA e ON
            t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
            AND e.IS_DEL = 0
        INNER JOIN T_BO_TRANS_TASK_FEE f ON
            t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID
            AND f.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON
            f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID
            AND v.IS_DEL = 0
        WHERE
            <include refid="settleVerifyCondition"></include>
        GROUP BY
            f.SETTLE_VERIFY_STATUS
    </select>
    <select id="queryVerifyTaskFeeStatusCount" resultType="com.wtyt.settle.bean.TabInfoBean">
        SELECT
            SETTLE_VERIFY_STATUS tabState, sum(tabCount) tabCount
        FROM (
            SELECT
                SETTLE_VERIFY_STATUS,
                count(1) tabCount
            FROM
                T_BO_TRANS_FEE_VERIFY_TASK vt
            INNER JOIN T_BO_TRANS_TASK t ON
                vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
                AND t.IS_DEL = 0
            INNER JOIN T_BO_TRANS_TASK_EXTRA e ON
                t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
                AND e.IS_DEL = 0
            INNER JOIN T_BO_TRANS_TASK_FEE f ON
                t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID
                AND f.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID AND v.IS_DEL = 0
            WHERE
                <include refid="newSettleVerifyCondition"></include>
            GROUP BY
                f.SETTLE_VERIFY_STATUS
        <if test="settleMode != null and settleMode == 0">
            UNION ALL
            SELECT
            SETTLE_VERIFY_STATUS,
            count(1) tabCount
            FROM
            T_BO_TRANS_FEE_VERIFY_TASK vt
            INNER JOIN T_BO_TRANS_TASK_FEE f ON
            vt.BO_TRANS_FEE_VERIFY_TASK_ID  = f.BO_TRANS_FEE_VERIFY_TASK_ID
            AND f.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID AND v.IS_DEL = 0
            WHERE
            <include refid="customSettleVerifyCondition"></include>
            GROUP BY
            f.SETTLE_VERIFY_STATUS
        </if>
        ) GROUP BY SETTLE_VERIFY_STATUS
    </select>
    <select id="querySettleVerifyList" resultType="com.wtyt.settle.bean.TaskFeeVerifyBean">
        SELECT
            f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
            f.CONFIG_KEY configKey,
            f.CONFIG_NAME configName,
            f.REASON reason,
            T.BO_TRANS_TASK_ID boTransTaskId,
            t.TAX_WAYBILL_NO taxWaybillNo,
            e.CUSTOMER_ORDER_NO customerOrderNo,
            f.EXPENSIVE_TYPE expensiveType,
            TO_CHAR(
                    CASE
                    <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                        WHEN f.CONFIG_KEY = '${key}'
                        THEN ${value}
                    </foreach>
                      ELSE f.CONFIG_VALUE END, 'FM999999999990.00') receivableAmount,
            TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999999990.00') receivedAmount,
            TO_CHAR(NVL(t.ALL_FREIGHT, 0) - NVL(t.PREPAYMENTS_OILCARD, 0), 'FM999999999990.00') allFreight,
            TO_CHAR(NVL(t.SERVICE_FEE, 0), 'FM999999999990.00') serviceFee,
            v.RELATED_NO relatedNo,
            TO_CHAR(v.VERIFY_TIME, 'yyyy-MM-dd Hh24:mi:ss') verifyTime,
            v.VERIFY_USER_NAME verifyUserName,
            f.SETTLE_VERIFY_STATUS verifyStatus,
            NVL(f.CONFIRM_STATUS, 0) confirmStatus,
            t.DRIVER_NAME driverName,
            t.MOBILE_NO mobileNo,
            t.START_PROVINCE_NAME startProvinceName,
            t.START_CITY_NAME startCityName,
            t.START_COUNTY_NAME startCountyName,
            t.END_PROVINCE_NAME endProvinceName,
            t.END_CITY_NAME endCityName,
            t.END_COUNTY_NAME endCountyName,
            TO_CHAR(t.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') taskCreatedTime,
            t.WB_ITEM wbItem,
            t.ORG_ID orgId,
            t.CAPACITY_TYPE_NAME capacityTypeName,
            t.CART_BADGE_NO cartBadgeNo
        FROM
            T_BO_TRANS_TASK t
        LEFT JOIN T_BO_TRANS_TASK_EXTRA e ON
            t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID AND e.is_del=0
        INNER JOIN T_BO_TRANS_TASK_FEE f ON
            f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND f.is_del=0
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON
            f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID
            AND v.IS_DEL = 0
        WHERE
            <include refid="settleVerifyCondition"></include>
        ORDER BY
            t.CREATED_TIME DESC ,
            t.BO_TRANS_TASK_ID DESC ,
            f.EXPENSIVE_TYPE DESC,
            f.CONFIG_KEY ASC
    </select>
    <select id="queryTaskFeeVerifyStatus" resultType="com.wtyt.dao.bean.syf.BoTransTaskFeeBean">
        SELECT
            f.BO_TRANS_TASK_ID boTransTaskId,
            f.SETTLE_VERIFY_STATUS settleVerifyStatus
        FROM
            T_BO_TRANS_TASK_FEE f
            INNER JOIN T_BO_TRANS_TASK t ON
            f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        WHERE
            f.is_del = 0
            AND t.IS_DEL = 0
            AND f.IS_VERIFY = 1
            AND f.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIds" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
            AND CASE
            <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE f.CONFIG_VALUE END > 0
        GROUP BY
            f.BO_TRANS_TASK_ID ,
            f.SETTLE_VERIFY_STATUS
    </select>
    <select id="queryApprovalTaskFeeList" resultType="com.wtyt.dao.bean.syf.BoTransTaskFeeBean">
        SELECT
            f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
            f.BO_TRANS_TASK_ID boTransTaskId,
            f.CONFIG_KEY configKey,
            f.CONFIG_NAME configName,
            TO_CHAR(CASE
                    <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                        WHEN f.CONFIG_KEY = '${key}'
                        THEN ${value}
                    </foreach>
                    ELSE f.CONFIG_VALUE END, 'FM999999999990.00') configValue,
            TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999999990.00') actualValue,
            f.EXPENSIVE_TYPE expensiveType
        FROM
            T_BO_TRANS_TASK_FEE f INNER JOIN T_BO_TRANS_TASK t ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        WHERE
            f.IS_DEL = 0
            AND t.IS_DEL = 0
            AND f.IS_VERIFY = 1
            AND f.BO_TRANS_TASK_ID IN
            <foreach collection = "boTransTaskIdList" item = "item" close = ")" open = "(" separator = ",">
                #{item}
            </foreach>
            AND f.CONFIG_KEY  IN
            <foreach collection = "configKeyList" item = "item" close = ")" open = "(" separator = ",">
                #{item}
            </foreach>
    </select>
    <select id="queryVerifyTaskFeeList" resultType="com.wtyt.dao.bean.syf.BoTransTaskFeeBean">
        SELECT
            f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
            CASE
            <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE f.CONFIG_VALUE END configValue,
            NVL(f.ACTUAL_VALUE,0) actualValue,
            f.SETTLE_VERIFY_STATUS settleVerifyStatus
        FROM
            T_BO_TRANS_TASK_FEE f
        INNER JOIN T_BO_TRANS_TASK t ON
            f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        WHERE
            f.IS_DEL = 0
            AND t.IS_DEL = 0
            AND f.IS_VERIFY = 1
            AND f.BO_TRANS_TASK_ID = #{boTransTaskId}
            AND CASE
            <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE f.CONFIG_VALUE END > 0
    </select>
    <select id="querySettleVerifyExportList" resultType="com.wtyt.settle.bean.TaskFeeVerifyBean">
        SELECT * FROM (
            SELECT
                f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
                f.CONFIG_KEY configKey,
                f.CONFIG_NAME configName,
                f.REASON reason,
                t.BO_TRANS_TASK_ID boTransTaskId,
                t.TAX_WAYBILL_NO taxWaybillNo,
                e.CUSTOMER_ORDER_NO customerOrderNo,
                f.EXPENSIVE_TYPE expensiveType,
                TO_CHAR(
                        CASE
                        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                            WHEN f.CONFIG_KEY = '${key}'
                            THEN ${value}
                        </foreach>
                          ELSE f.CONFIG_VALUE END, 'FM999999999990.00') receivableAmount,
                TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999999990.00') receivedAmount,
                TO_CHAR(NVL(t.ALL_FREIGHT, 0) - NVL(t.PREPAYMENTS_OILCARD, 0), 'FM999999999990.00') allFreight,
                TO_CHAR(NVL(t.SERVICE_FEE, 0), 'FM999999999990.00') serviceFee,
                v.RELATED_NO relatedNo,
                TO_CHAR(v.VERIFY_TIME, 'yyyy-MM-dd Hh24:mi:ss') verifyTime,
                v.VERIFY_USER_NAME verifyUserName,
                f.SETTLE_VERIFY_STATUS verifyStatus,
                NVL(f.CONFIRM_STATUS, 0) confirmStatus,
                t.DRIVER_NAME driverName,
                t.MOBILE_NO mobileNo,
                t.START_PROVINCE_NAME startProvinceName,
                t.START_CITY_NAME startCityName,
                t.START_COUNTY_NAME startCountyName,
                t.END_PROVINCE_NAME endProvinceName,
                t.END_CITY_NAME endCityName,
                t.END_COUNTY_NAME endCountyName,
                TO_CHAR(t.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') taskCreatedTime,
                t.WB_ITEM wbItem,
                t.ORG_ID orgId,
                <if test="capacityIntegration == 0">
                    t.CAPACITY_TYPE_NAME capacityTypeName,
                </if>
                <if test="capacityIntegration == 1">
                    e.CPD_POOL_GROUP_NAME  capacityTypeName,
                </if>
                t.CART_BADGE_NO cartBadgeNo
            FROM
                T_BO_TRANS_FEE_VERIFY_TASK vt
            LEFT JOIN T_BO_TRANS_TASK t ON
                vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            LEFT JOIN T_BO_TRANS_TASK_FEE f ON
                vt.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID
            LEFT JOIN T_BO_TRANS_TASK_EXTRA e ON
                        t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID AND e.is_del=0
            LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON
                        f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID
                        AND v.IS_DEL = 0
            WHERE
                <include refid="newSettleVerifyCondition"></include>
            UNION ALL
            SELECT
                f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
                f.CONFIG_KEY configKey,
                f.CONFIG_NAME configName,
                f.REASON reason,
                NULL boTransTaskId,
                NULL taxWaybillNo,
                NULL customerOrderNo,
                f.EXPENSIVE_TYPE expensiveType,
                TO_CHAR(NVL(f.CONFIG_VALUE, 0), 'FM999999999990.00') receivableAmount,
                TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999999990.00') receivedAmount,
                NULL allFreight,
                NULL serviceFee,
                v.RELATED_NO relatedNo,
                TO_CHAR(v.VERIFY_TIME, 'yyyy-MM-dd Hh24:mi:ss') verifyTime,
                v.VERIFY_USER_NAME verifyUserName,
                f.SETTLE_VERIFY_STATUS verifyStatus,
                NVL(f.CONFIRM_STATUS, 0) confirmStatus,
                NULL driverName,
                NULL mobileNo,
                NULL startProvinceName,
                NULL startCityName,
                NULL startCountyName,
                NULL endProvinceName,
                NULL endCityName,
                NULL endCountyName,
                TO_CHAR(vt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') taskCreatedTime,
                vt.WB_ITEM wbItem,
                vt.ORG_ID orgId,
                NULL capacityTypeName,
                vt.CART_BADGE_NO cartBadgeNo
            FROM
                T_BO_TRANS_FEE_VERIFY_TASK vt
            LEFT JOIN T_BO_TRANS_TASK_FEE f ON
                vt.BO_TRANS_FEE_VERIFY_TASK_ID  = f.BO_TRANS_FEE_VERIFY_TASK_ID
            LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON
                        f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID
                        AND v.IS_DEL = 0
            WHERE
                <include refid="customSettleVerifyCondition"></include>
            ) ORDER BY taskCreatedTime DESC, expensiveType DESC
    </select>
    <sql id="customSettleVerifyCondition">
        vt.IS_DEL = 0
        AND vt.BO_TRANS_TASK_ID IS NULL
        AND f.IS_DEL = 0
        AND f.IS_VERIFY = 1
        <if test="orgIdList !=null and orgIdList.size()>0">
            AND vt.ORG_ID IN
            <foreach collection="orgIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        AND f.CONFIG_VALUE  > 0
        <if test="beginTime != null and beginTime !=''">
            AND vt.CREATED_TIME &gt;= TO_DATE(#{beginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND vt.CREATED_TIME &lt;= TO_DATE(#{endTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="tabState != null and tabState !=''">
            AND f.SETTLE_VERIFY_STATUS = #{tabState}
        </if>
        <if test="taxWaybillNo != null and taxWaybillNo !=''">
            AND 1 = 0
        </if>
        <if test="taxWaybillNoList !=null and taxWaybillNoList.size()>0">
            AND 1 = 0
        </if>
        <if test="customerOrderNo != null and customerOrderNo !=''">
            AND 1 = 0
        </if>
        <if test="customerOrderNoList !=null and customerOrderNoList.size()>0">
            AND 1 = 0
        </if>
        <if test="configName != null and configName !=''">
            AND f.CONFIG_NAME LIKE '%' || #{configName} || '%'
        </if>
        <if test="configNameList !=null and configNameList.size()>0">
            AND f.CONFIG_NAME IN
            <foreach collection="configNameList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="relatedNo != null and relatedNo !=''">
            AND v.RELATED_NO LIKE '%' || #{relatedNo} || '%'
        </if>
        <if test="verifyUserName != null and verifyUserName !=''">
            AND v.VERIFY_USER_NAME  =  #{verifyUserName}
        </if>
        <if test="startProvinceName !=null and startProvinceName !='' ">
            AND 1 = 0
        </if>
        <if test="startCityName !=null and startCityName !='' ">
            AND 1 = 0
        </if>
        <if test="startCountyName !=null and startCountyName !='' ">
            AND 1 = 0
        </if>
        <if test="endProvinceName !=null and endProvinceName !='' ">
            AND 1 = 0
        </if>
        <if test="endCityName !=null and endCityName !='' ">
            AND 1 = 0
        </if>
        <if test="endCountyName !=null and endCountyName !='' ">
            AND 1 = 0
        </if>
        <if test="capacityTypeName !=null and capacityTypeName !='' ">
            AND 1 = 0
        </if>
        <if test="payer !=null and payer !='' ">
            AND 1 = 0
        </if>
        <if test="customerName !=null and customerName !='' ">
            AND INSTR(vt.WB_ITEM, #{customerName}) > 0
        </if>
        <if test="searchKeyword != null and searchKeyword !=''">
            AND INSTR(vt.CART_BADGE_NO, upper(#{searchKeyword})) > 0
        </if>
        <if test="cartBadgeNo != null and cartBadgeNo !=''">
            AND INSTR(vt.CART_BADGE_NO, #{cartBadgeNo}) > 0
        </if>
        <if test="driverName != null and driverName !=''">
            AND 1 = 0
        </if>
        <if test="mobileNo != null and mobileNo !=''">
            AND 1 = 0
        </if>
        <if test="verifyBeginTime != null and verifyBeginTime !=''">
            AND v.VERIFY_TIME >= to_date(#{verifyBeginTime} || '00:00:00','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="verifyEndTime != null and verifyEndTime !=''">
            AND v.VERIFY_TIME &lt;= to_date(#{verifyEndTime} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
    </sql>
    <sql id="newSettleVerifyCondition">
        vt.IS_DEL = 0 AND vt.BO_TRANS_TASK_ID IS NOT NULL AND
        <include refid="settleVerifyCondition"></include>
        <if test="cartBadgeNo != null and cartBadgeNo !=''">
            AND INSTR(t.CART_BADGE_NO, upper(#{cartBadgeNo})) > 0
        </if>
        <if test="driverName != null and driverName !=''">
            AND INSTR(t.DRIVER_NAME, #{driverName}) > 0
        </if>
        <if test="mobileNo != null and mobileNo !=''">
            AND INSTR(t.MOBILE_NO, #{mobileNo}) > 0
        </if>
        <if test="capacityIntegration == 0">
            <if test="capacityTypeName != null and capacityTypeName !=''">
                AND t.CAPACITY_TYPE_NAME = #{capacityTypeName}
            </if>
        </if>
        <if test="capacityIntegration == 1">
            <if test="capacityTypeName != null and capacityTypeName !=''">
                AND e.CPD_POOL_GROUP_NAME = #{capacityTypeName}
            </if>
        </if>
        <if test="searchKeyword != null and searchKeyword !=''">
            AND (INSTR(t.TAX_WAYBILL_NO, #{searchKeyword}) > 0
                OR INSTR(t.CART_BADGE_NO, upper(#{searchKeyword})) > 0
                OR INSTR(t.DRIVER_NAME, #{searchKeyword}) > 0
                OR INSTR(t.MOBILE_NO, #{searchKeyword}) > 0
            )
        </if>
        <if test="settleMode != null and settleMode != 0">
            AND t.SETTLE_MODE = #{settleMode}
        </if>
    </sql>
    <sql id="settleVerifyCondition">
        t.IS_DEL = 0
        AND f.IS_DEL = 0
        AND f.IS_VERIFY = 1
        <if test="orgIdList !=null and orgIdList.size()>0">
            AND T.ORG_ID IN
            <foreach collection="orgIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        AND CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE f.CONFIG_VALUE END > 0
        <if test="beginTime != null and beginTime !=''">
            AND t.CREATED_TIME &gt;= TO_DATE(#{beginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND t.CREATED_TIME &lt;= TO_DATE(#{endTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="tabState != null and tabState !=''">
            AND f.SETTLE_VERIFY_STATUS = #{tabState}
        </if>
        <if test="taxWaybillNo != null and taxWaybillNo !=''">
            AND t.TAX_WAYBILL_NO LIKE '%' || #{taxWaybillNo} || '%'
        </if>
        <if test="taxWaybillNoList !=null and taxWaybillNoList.size()>0">
            AND T.TAX_WAYBILL_NO IN
            <foreach collection="taxWaybillNoList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="customerOrderNo != null and customerOrderNo !=''">
            AND e.CUSTOMER_ORDER_NO LIKE '%' || #{customerOrderNo} || '%'
        </if>
        <if test="customerOrderNoList !=null and customerOrderNoList.size()>0">
            AND e.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="configName != null and configName !=''">
            AND f.CONFIG_NAME LIKE '%' || #{configName} || '%'
        </if>
        <if test="configNameList !=null and configNameList.size()>0">
            AND f.CONFIG_NAME IN
            <foreach collection="configNameList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="relatedNo != null and relatedNo !=''">
            AND v.RELATED_NO LIKE '%' || #{relatedNo} || '%'
        </if>
        <if test="verifyUserName != null and verifyUserName !=''">
            AND v.VERIFY_USER_NAME  =  #{verifyUserName}
        </if>
        <if test="startProvinceName !=null and startProvinceName !='' ">
            AND t.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName !=null and startCityName !='' ">
            AND t.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName !=null and startCountyName !='' ">
            AND t.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="endProvinceName !=null and endProvinceName !='' ">
            AND t.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName !=null and endCityName !='' ">
            AND t.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName !=null and endCountyName !='' ">
            AND t.END_COUNTY_NAME = #{endCountyName}
        </if>
        <if test="payer !=null and payer !='' ">
            AND e.PAYER   LIKE '%' || #{payer} || '%'
        </if>
        <if test="customerName !=null and customerName !='' ">
            AND T.WB_ITEM   LIKE '%' || #{customerName} || '%'
        </if>
        <if test="verifyBeginTime != null and verifyBeginTime !=''">
            AND v.VERIFY_TIME >= to_date(#{verifyBeginTime} || '00:00:00','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="verifyEndTime != null and verifyEndTime !=''">
            AND v.VERIFY_TIME &lt;= to_date(#{verifyEndTime} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
    </sql>

    <sql id="queryVerifyInfo">
        (
        SELECT
        f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
        f.CONFIG_NAME configName,
        T.BO_TRANS_TASK_ID boTransTaskId,
        f.EXPENSIVE_TYPE expensiveType,
        DECODE(f.EXPENSIVE_TYPE, 3,
        CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE NVL(f.CONFIG_VALUE, 0) END , 0) receivableAmount,
        DECODE(f.EXPENSIVE_TYPE, 3, NVL(f.ACTUAL_VALUE, 0), 0) receivedAmount,
        DECODE(f.EXPENSIVE_TYPE, 3, 0,
        CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE NVL(f.CONFIG_VALUE, 0) END ) payableAmount,
        DECODE(f.EXPENSIVE_TYPE, 3, 0, NVL(f.ACTUAL_VALUE, 0)) paidAmount,
        CASE  WHEN NVL(f.ACTUAL_VALUE, 0) = 0 THEN '0' WHEN NVL(f.ACTUAL_VALUE, 0) &gt;= NVL(f.CONFIG_VALUE, 0) THEN '2' ELSE '1' END  AS  verifyStatus,
        NVL(f.CONFIRM_STATUS, 0)  confirmStatus
        FROM  T_BO_TRANS_TASK t  left join T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
        INNER JOIN  T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        left join T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID and v.IS_DEL=0
        WHERE
        <include refid="settleVerifyCondition"></include>
        ) QVI
    </sql>
    <sql id="queryNewVerifyInfo">
        (
        SELECT
        vt.BO_TRANS_FEE_VERIFY_TASK_ID,
        f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
        f.CONFIG_NAME configName,
        t.BO_TRANS_TASK_ID boTransTaskId,
        f.EXPENSIVE_TYPE expensiveType,
        DECODE(f.EXPENSIVE_TYPE, 3,
        CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE NVL(f.CONFIG_VALUE, 0) END , 0) receivableAmount,
        DECODE(f.EXPENSIVE_TYPE, 3, NVL(f.ACTUAL_VALUE, 0), 0) receivedAmount,
        DECODE(f.EXPENSIVE_TYPE, 3, 0,
        CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE NVL(f.CONFIG_VALUE, 0) END ) payableAmount,
        DECODE(f.EXPENSIVE_TYPE, 3, 0, NVL(f.ACTUAL_VALUE, 0)) paidAmount,
        f.SETTLE_VERIFY_STATUS AS verifyStatus,
        NVL(f.CONFIRM_STATUS, 0)  confirmStatus
        FROM T_BO_TRANS_FEE_VERIFY_TASK vt
        INNER JOIN T_BO_TRANS_TASK t ON vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
        INNER JOIN  T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID and v.IS_DEL=0
        WHERE
            <include refid="newSettleVerifyCondition"></include>
        <if test="settleMode != null and settleMode == 0">
        UNION ALL
            SELECT
            vt.BO_TRANS_FEE_VERIFY_TASK_ID,
            f.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
            f.CONFIG_NAME configName,
            NULL boTransTaskId,
            f.EXPENSIVE_TYPE expensiveType,
            DECODE(f.EXPENSIVE_TYPE, 3, NVL(f.CONFIG_VALUE, 0), 0) receivableAmount,
            DECODE(f.EXPENSIVE_TYPE, 3, NVL(f.ACTUAL_VALUE, 0), 0) receivedAmount,
            DECODE(f.EXPENSIVE_TYPE, 3, 0, NVL(f.CONFIG_VALUE, 0)) payableAmount,
            DECODE(f.EXPENSIVE_TYPE, 3, 0, NVL(f.ACTUAL_VALUE, 0)) paidAmount,
            f.SETTLE_VERIFY_STATUS AS verifyStatus,
            NVL(f.CONFIRM_STATUS, 0)  confirmStatus
            FROM T_BO_TRANS_FEE_VERIFY_TASK vt
            INNER JOIN  T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_FEE_VERIFY_TASK_ID = vt.BO_TRANS_FEE_VERIFY_TASK_ID
            left join T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID and v.IS_DEL=0
            WHERE
            <include refid="customSettleVerifyCondition"></include>
        </if>
        ) QVI
    </sql>
    <select id="queryTaskFeeList" resultType="com.wtyt.dao.bean.syf.BoTransTaskFeeBean">
        SELECT
            f.BO_TRANS_TASK_ID boTransTaskId,
            f.CONFIG_NAME configName,
            f.CONFIG_KEY configKey,
            CASE
            <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE f.CONFIG_VALUE END configValue,
            NVL(f.ACTUAL_VALUE, 0) actualValue,
            f.EXPENSIVE_TYPE expensiveType
        FROM
            T_BO_TRANS_TASK_FEE f
            LEFT JOIN T_BO_TRANS_TASK t ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID
        WHERE
            f.IS_DEL = 0
            AND f.IS_VERIFY = 1
            AND f.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            AND CASE
            <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE f.CONFIG_VALUE END > 0
    </select>
    <select id="queryCustomTaskFeeList" resultType="com.wtyt.dao.bean.syf.BoTransTaskFeeBean">
        SELECT
            f.BO_TRANS_FEE_VERIFY_TASK_ID boTransFeeVerifyTaskId,
            f.CONFIG_NAME configName,
            f.CONFIG_KEY configKey,
            f.CONFIG_VALUE configValue,
            NVL(f.ACTUAL_VALUE, 0) actualValue,
            f.EXPENSIVE_TYPE expensiveType
        FROM
            T_BO_TRANS_TASK_FEE f
        WHERE
            f.IS_DEL = 0
            AND f.IS_VERIFY = 1
            AND f.BO_TRANS_FEE_VERIFY_TASK_ID IN
            <foreach collection="boTransFeeVerifyTaskIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            AND f.CONFIG_VALUE > 0
    </select>

    <select id="queryVerifyTaskPage" resultMap="transFeeVerifyTaskEntity">
        SELECT * FROM (SELECT
        vt.BO_TRANS_FEE_VERIFY_TASK_ID,
        vt.ORG_ID,
        to_char(vt.CREATED_TIME, 'yyyy-MM-dd HH24:mi:ss') CREATED_TIME,
        t.BO_TRANS_TASK_ID,
        t.TAX_WAYBILL_NO,
        t.DRIVER_NAME,
        t.MOBILE_NO,
        t.START_PROVINCE_NAME,
        t.START_CITY_NAME,
        t.START_COUNTY_NAME,
        t.END_PROVINCE_NAME,
        t.END_CITY_NAME,
        t.END_COUNTY_NAME,
        t.WB_ITEM,
        t.ALL_FREIGHT,
        t.SERVICE_FEE,
        t.CART_BADGE_NO,
        <if test="capacityIntegration == 0">
            t.CAPACITY_TYPE_NAME,
        </if>
        <if test="capacityIntegration == 1">
            e.CPD_POOL_GROUP_NAME  CAPACITY_TYPE_NAME,
        </if>
        e.CUSTOMER_ORDER_NO,
        t.SETTLE_MODE
        FROM T_BO_TRANS_FEE_VERIFY_TASK vt
        INNER JOIN T_BO_TRANS_TASK t ON vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID AND e.is_del=0
        LEFT JOIN T_TAX_WAYBILL w on w.TAX_WAYBILL_ID = t.TAX_WAYBILL_ID  AND w.IS_DEL = 0
        WHERE vt.IS_DEL = 0 AND vt.BO_TRANS_TASK_ID IS NOT NULL
        <if test="queryOrgIdList !=null and queryOrgIdList.size()>0">
            AND vt.ORG_ID IN
            <foreach collection="queryOrgIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="taxWaybillNo != null and taxWaybillNo !=''">
            AND t.TAX_WAYBILL_NO LIKE '%' || #{taxWaybillNo} || '%'
        </if>
        <if test="taxWaybillNoList !=null and taxWaybillNoList.size()>0">
            AND T.TAX_WAYBILL_NO IN
            <foreach collection="taxWaybillNoList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="customerOrderNo != null and customerOrderNo !=''">
            AND e.CUSTOMER_ORDER_NO LIKE '%' || #{customerOrderNo} || '%'
        </if>
        <if test="customerOrderNoList !=null and customerOrderNoList.size()>0">
            AND e.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startProvinceName !=null and startProvinceName !='' ">
            AND t.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName !=null and startCityName !='' ">
            AND t.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName !=null and startCountyName !='' ">
            AND t.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="endProvinceName !=null and endProvinceName !='' ">
            AND t.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName !=null and endCityName !='' ">
            AND t.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName !=null and endCountyName !='' ">
            AND t.END_COUNTY_NAME = #{endCountyName}
        </if>
        <if test="searchKeyword !=null and searchKeyword !='' ">
            AND (t.TAX_WAYBILL_NO LIKE '%' || #{searchKeyword} || '%'
                OR t.CART_BADGE_NO   LIKE '%' || upper(#{searchKeyword}) || '%'
                OR t.DRIVER_NAME   LIKE '%' || #{searchKeyword} || '%'
                OR t.MOBILE_NO  LIKE '%' || #{searchKeyword} || '%'
                )
        </if>
        <if test="payer !=null and payer !='' ">
            AND e.PAYER  LIKE '%' || #{payer} || '%'
        </if>
        <if test="wbItem !=null and wbItem !='' ">
            AND t.WB_ITEM  LIKE '%' || #{wbItem} || '%'
        </if>
        <if test="cartBadgeNo !=null and cartBadgeNo !='' ">
            AND t.CART_BADGE_NO   LIKE '%' || #{cartBadgeNo} || '%'
        </if>
        <if test="driverName !=null and driverName !='' ">
            AND t.DRIVER_NAME   LIKE '%' || #{driverName} || '%'
        </if>
        <if test="mobileNo !=null and mobileNo !='' ">
            AND t.MOBILE_NO  LIKE '%' || #{mobileNo} || '%'
        </if>
        <if test="capacityIntegration == 0">
            <if test="capacityTypeName != null and capacityTypeName !=''">
                AND t.CAPACITY_TYPE_NAME = #{capacityTypeName}
            </if>
        </if>
        <if test="capacityIntegration == 1">
            <if test="capacityTypeName != null and capacityTypeName !=''">
                AND e.CPD_POOL_GROUP_NAME = #{capacityTypeName}
            </if>
        </if>
        <if test="createBeginTime != null and createBeginTime !=''">
            AND  vt.CREATED_TIME &gt;= TO_DATE(#{createBeginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="createEndTime != null and createEndTime !=''">
            AND vt.CREATED_TIME &lt;= TO_DATE(#{createEndTime} || '23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="settleMode != null and settleMode != 0">
            AND t.SETTLE_MODE = #{settleMode}
        </if>
        AND EXISTS (
        SELECT 1 FROM T_BO_TRANS_TASK_FEE f
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID AND v.IS_DEL = 0
        WHERE t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID
        AND f.IS_DEL = 0
        AND f.IS_VERIFY = 1
        <include refid="verifyQueryConditions"/>
        )
        <if test="!hasTaskConditions">
        <if test="settleMode != null and settleMode == 0">
        UNION ALL
        SELECT
        vt.BO_TRANS_FEE_VERIFY_TASK_ID,
        vt.ORG_ID,
        to_char(vt.CREATED_TIME, 'yyyy-MM-dd HH24:mi:ss') CREATED_TIME,
        NULL BO_TRANS_TASK_ID,
            NULL TAX_WAYBILL_NO,
            NULL DRIVER_NAME,
            NULL MOBILE_NO,
            NULL START_PROVINCE_NAME,
            NULL START_CITY_NAME,
            NULL START_COUNTY_NAME,
            NULL END_PROVINCE_NAME,
            NULL END_CITY_NAME,
            NULL END_COUNTY_NAME,
            vt.WB_ITEM,
            NULL ALL_FREIGHT,
            NULL SERVICE_FEE,
            vt.CART_BADGE_NO,
            NULL CAPACITY_TYPE_NAME,
            NULL CUSTOMER_ORDER_NO,
            NULL SETTLE_MODE
        FROM T_BO_TRANS_FEE_VERIFY_TASK vt
        WHERE vt.IS_DEL = 0 AND vt.BO_TRANS_TASK_ID IS NULL
        <if test="queryOrgIdList !=null and queryOrgIdList.size()>0">
            AND vt.ORG_ID IN
            <foreach collection="queryOrgIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="createBeginTime != null and createBeginTime !=''">
            AND  vt.CREATED_TIME &gt;= TO_DATE(#{createBeginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="createEndTime != null and createEndTime !=''">
            AND vt.CREATED_TIME &lt;= TO_DATE(#{createEndTime} || '23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="wbItem !=null and wbItem !='' ">
            AND vt.WB_ITEM  LIKE '%' || #{wbItem} || '%'
        </if>
        <if test="cartBadgeNo !=null and cartBadgeNo !='' ">
            AND vt.CART_BADGE_NO   LIKE '%' || #{cartBadgeNo} || '%'
        </if>
            <if test="searchKeyword !=null and searchKeyword !='' ">
                AND vt.CART_BADGE_NO   LIKE '%' || #{searchKeyword} || '%'
            </if>
        AND EXISTS (
        SELECT 1 FROM T_BO_TRANS_TASK_FEE f
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID AND v.IS_DEL = 0
        WHERE vt.BO_TRANS_FEE_VERIFY_TASK_ID = f.BO_TRANS_FEE_VERIFY_TASK_ID
        AND f.IS_DEL = 0
        AND f.IS_VERIFY = 1
            AND  f.CONFIG_VALUE > 0
            <if test="tabState != null and tabState !=''">
                AND f.SETTLE_VERIFY_STATUS = #{tabState}
            </if>
            <if test="verifyBeginTime != null and verifyBeginTime !=''">
                AND v.VERIFY_TIME &gt;= TO_DATE(#{verifyBeginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="verifyEndTime != null and verifyEndTime !=''">
                AND v.VERIFY_TIME &lt;= TO_DATE(#{verifyEndTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="configName != null and configName !=''">
                AND f.CONFIG_NAME LIKE '%' || #{configName} || '%'
            </if>
            <if test="configNameList !=null and configNameList.size()>0">
                AND f.CONFIG_NAME IN
                <foreach collection="configNameList" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="relatedNo != null and relatedNo !=''">
                AND v.RELATED_NO LIKE '%' || #{relatedNo} || '%'
            </if>
            <if test="verifyUserName != null and verifyUserName !=''">
                AND v.VERIFY_USER_NAME  =  #{verifyUserName}
            </if>
        )
        </if>
        </if>
        )
        ORDER BY
        CREATED_TIME DESC ,
        BO_TRANS_TASK_ID DESC
    </select>

    <select id="queryVerifyTaskFeeInfo" resultMap="transFeeVerifyTaskEntity">
        SELECT
        vt.BO_TRANS_FEE_VERIFY_TASK_ID,
        f.BO_TRANS_TASK_FEE_ID,
        f.CONFIG_NAME,
        TO_CHAR(
        CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE f.CONFIG_VALUE END, 'FM999999999990.00') CONFIG_VALUE,
        TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999999990.00') ACTUAL_VALUE,
        f.EXPENSIVE_TYPE
        FROM T_BO_TRANS_FEE_VERIFY_TASK vt
        LEFT JOIN T_BO_TRANS_TASK t ON vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE f ON (f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID OR f.BO_TRANS_FEE_VERIFY_TASK_ID = vt.BO_TRANS_FEE_VERIFY_TASK_ID) AND f.IS_DEL= 0 AND f.IS_VERIFY = 1
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID AND v.IS_DEL = 0
        WHERE vt.IS_DEL = 0
        AND f.IS_VERIFY = 1
        <if test="boTransFeeVerifyTaskIds !=null and boTransFeeVerifyTaskIds.size()>0">
            AND vt.BO_TRANS_FEE_VERIFY_TASK_ID IN
            <foreach collection="boTransFeeVerifyTaskIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <include refid="verifyQueryConditions"/>
        ORDER BY f.CREATED_TIME DESC
    </select>

    <select id="queryVerifyTaskInfo" resultMap="transFeeVerifyTaskEntity">
        SELECT
        vt.BO_TRANS_FEE_VERIFY_TASK_ID,
        t.TAX_WAYBILL_NO,
        f.BO_TRANS_TASK_FEE_ID,
        f.CONFIG_KEY,
        f.CONFIG_NAME,
        to_char(f.CREATED_TIME, 'yyyy-MM-dd HH24:mi:ss') FEE_CREATED_TIME,
        TO_CHAR(
        CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE f.CONFIG_VALUE END, 'FM999999999990.00') CONFIG_VALUE,
        TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999999990.00') ACTUAL_VALUE,
        NVL(f.CONFIRM_STATUS, 0) CONFIRM_STATUS,
        f.EXPENSIVE_TYPE,
        f.REASON,
        t.ALL_FREIGHT,
        t.PREPAYMENTS_OILCARD,
        t.SERVICE_FEE,
        f.SETTLE_VERIFY_STATUS,
        v.BO_TRANS_TASK_FEE_VRF_ID,
        v.VERIFY_USER_NAME,
        v.RELATED_NO,
        TO_CHAR(v.VERIFY_TIME, 'yyyy-MM-dd Hh24:mi:ss') VERIFY_TIME
        FROM T_BO_TRANS_FEE_VERIFY_TASK vt
        LEFT JOIN T_BO_TRANS_TASK t ON vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE f ON (f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID OR f.BO_TRANS_FEE_VERIFY_TASK_ID = vt.BO_TRANS_FEE_VERIFY_TASK_ID) AND f.IS_DEL= 0 AND f.IS_VERIFY = 1
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID AND v.IS_DEL = 0
        WHERE vt.IS_DEL = 0
        AND vt.BO_TRANS_FEE_VERIFY_TASK_ID = #{boTransFeeVerifyTaskId}
        AND f.IS_VERIFY = 1
        <include refid="verifyQueryConditions"/>
        ORDER BY
        f.CREATED_TIME DESC,
        f.EXPENSIVE_TYPE DESC,
        f.CONFIG_KEY ASC,
        f.CONFIG_NAME ASC
    </select>
    <select id="queryTaskFeeStatistic" resultType="com.wtyt.settle.bean.BoTaskFeeCountBean">
        SELECT BO_TRANS_FEE_VERIFY_TASK_ID boTransFeeVerifyTaskId, EXPENSIVE_TYPE expensiveType,NVL(SUM(CONFIG_VALUE), 0) configValue, NVL(SUM(ACTUAL_VALUE), 0) actualValue
        FROM (
                SELECT
                    vt.BO_TRANS_FEE_VERIFY_TASK_ID,
                    CASE
                    <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                        WHEN f.CONFIG_KEY = '${key}'
                        THEN ${value}
                    </foreach>
                    ELSE NVL(f.CONFIG_VALUE, 0) END CONFIG_VALUE,
                    f.ACTUAL_VALUE,
                    f.EXPENSIVE_TYPE
                FROM
                    T_BO_TRANS_FEE_VERIFY_TASK vt
                INNER JOIN T_BO_TRANS_TASK t ON
                    vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
                INNER JOIN T_BO_TRANS_TASK_FEE f ON
                    t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID
                LEFT JOIN T_TAX_WAYBILL w on w.TAX_WAYBILL_ID = t.TAX_WAYBILL_ID  AND w.IS_DEL = 0
                left join T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
                WHERE
                    vt.IS_DEL = 0
                    AND f.IS_DEL = 0
                    AND f.IS_VERIFY = 1
                    AND CASE
                        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                            WHEN f.CONFIG_KEY = '${key}'
                            THEN ${value}
                        </foreach>
                        ELSE f.CONFIG_VALUE END > 0
                    <include refid="verifyTaskCondition"></include>
            <if test="invoiceState != null and invoiceState == -1 and settleMode != null and settleMode == 0">
                    UNION ALL
                    SELECT
                        vt.BO_TRANS_FEE_VERIFY_TASK_ID,
                        f.CONFIG_VALUE,
                        f.ACTUAL_VALUE,
                        f.EXPENSIVE_TYPE
                    FROM
                        T_BO_TRANS_FEE_VERIFY_TASK vt
                    INNER JOIN T_BO_TRANS_TASK_FEE f ON
                        vt.BO_TRANS_FEE_VERIFY_TASK_ID = f.BO_TRANS_FEE_VERIFY_TASK_ID

                    WHERE
                        vt.IS_DEL = 0
                        AND f.IS_DEL = 0
                        AND f.IS_VERIFY = 1
                        AND f.CONFIG_VALUE > 0
                        <include refid="customVerifyTaskCondition"></include>
            </if>
        ) GROUP BY BO_TRANS_FEE_VERIFY_TASK_ID, EXPENSIVE_TYPE
    </select>
    <select id="queryVerifyTaskFee" resultMap="transFeeVerifyTaskEntity">
        SELECT * FROM (SELECT
        vt.BO_TRANS_FEE_VERIFY_TASK_ID,
        vt.ORG_ID,
        vt.CREATED_USER_NAME,
        to_char(vt.CREATED_TIME, 'yyyy-MM-dd HH24:mi:ss') CREATED_TIME,
        t.BO_TRANS_TASK_ID,
        t.TAX_WAYBILL_NO,
        t.DRIVER_NAME,
        t.MOBILE_NO,
        t.START_PROVINCE_NAME,
        t.START_CITY_NAME,
        t.START_COUNTY_NAME,
        t.END_PROVINCE_NAME,
        t.END_CITY_NAME,
        t.END_COUNTY_NAME,
        t.WB_ITEM,
        t.CART_BADGE_NO,
        to_char(t.START_TIME, 'yyyy-MM-dd HH24:mi:ss') START_TIME,
        to_char(t.END_TIME, 'yyyy-MM-dd HH24:mi:ss') END_TIME,
        t.MILEAGE,
        t.GOODS_NAME,
        t.GOODS_AMOUNT,
        t.GOODS_AMOUNT_TYPE,
        <if test="capacityIntegration == 0">
            t.CAPACITY_TYPE_NAME,
        </if>
        <if test="capacityIntegration == 1">
            e.CPD_POOL_GROUP_NAME  CAPACITY_TYPE_NAME,
        </if>
        trt.BO_TASK_RETURN_TRIP_ID,
        trt.START_PROVINCE_NAME TRT_START_PROVINCE_NAME,
        trt.START_CITY_NAME TRT_START_CITY_NAME,
        trt.START_COUNTY_NAME TRT_START_COUNTY_NAME,
        trt.END_PROVINCE_NAME TRT_END_PROVINCE_NAME,
        trt.END_CITY_NAME TRT_END_CITY_NAME,
        trt.END_COUNTY_NAME TRT_END_COUNTY_NAME,
        trt.MILEAGE TRT_MILEAGE,
        trt.MIDDLE_MILEAGE,
        trt.GOODS_NAME TRT_GOODS_NAME,
        trt.GOODS_AMOUNT TRT_GOODS_AMOUNT,
        trt.GOODS_AMOUNT_TYPE TRT_GOODS_AMOUNT_TYPE,
        to_char(trt.EXPECTED_END_TIME, 'yyyy-MM-dd HH24:mi:ss') EXPECTED_END_TIME,
        e.CUSTOMER_ORDER_NO,
        to_char(e.ARRIVE_END_TIME, 'yyyy-MM-dd HH24:mi:ss') ARRIVE_END_TIME,
        f.BO_TRANS_TASK_FEE_ID,
        f.CONFIG_NAME,
        TO_CHAR(
        CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE f.CONFIG_VALUE END, 'FM999999999990.00') CONFIG_VALUE,
        TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999999990.00') ACTUAL_VALUE,
        f.EXPENSIVE_TYPE,
        v.VERIFY_TIME,
        v.VERIFY_USER_NAME,
        case
        when w.INVOICE_STATE = 0 then '未开票'
        when w.INVOICE_STATE = 1 then '申请中'
        when w.INVOICE_STATE = 2 then '未通过'
        when w.INVOICE_STATE = 3 then '已开票'
        when w.INVOICE_STATE = 4 then '开票状态异常'
        when w.INVOICE_STATE = 5 then '部分开票'
        end INVOICE_STATE,
        case
            when t.SETTLE_MODE = 1 then '平台结算'
            when t.SETTLE_MODE = 2 then '非平台结算'
        end SETTLE_MODE
        FROM T_BO_TRANS_FEE_VERIFY_TASK vt
        INNER JOIN T_BO_TRANS_TASK t ON vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID AND e.is_del=0
        LEFT JOIN T_BO_TASK_RETURN_TRIP trt ON trt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND trt.IS_DEL = 0
        INNER JOIN T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND f.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID AND v.IS_DEL = 0
        LEFT JOIN T_TAX_WAYBILL w on w.TAX_WAYBILL_ID = t.TAX_WAYBILL_ID  AND w.IS_DEL = 0
        WHERE vt.IS_DEL = 0
        AND f.IS_DEL = 0
        <include refid="verifyTaskCondition"/>
        AND CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE f.CONFIG_VALUE END > 0
        <if test="invoiceState != null and invoiceState == -1 and settleMode != null and settleMode == 0">
            UNION ALL
            SELECT
            vt.BO_TRANS_FEE_VERIFY_TASK_ID,
            vt.ORG_ID,
            to_char(vt.CREATED_TIME, 'yyyy-MM-dd HH24:mi:ss') CREATED_TIME,
            NULL BO_TRANS_TASK_ID,
            NULL TAX_WAYBILL_NO,
            NULL DRIVER_NAME,
            NULL MOBILE_NO,
            NULL START_PROVINCE_NAME,
            NULL START_CITY_NAME,
            NULL START_COUNTY_NAME,
            NULL END_PROVINCE_NAME,
            NULL END_CITY_NAME,
            NULL END_COUNTY_NAME,
            vt.WB_ITEM,
            vt.CART_BADGE_NO,
            NULL START_TIME,
            NULL END_TIME,
            NULL MILEAGE,
            NULL GOODS_NAME,
            NULL GOODS_AMOUNT,
            NULL GOODS_AMOUNT_TYPE,
            NULL CAPACITY_TYPE_NAME,
            NULL BO_TASK_RETURN_TRIP_ID,
            NULL TRT_START_PROVINCE_NAME,
            NULL TRT_START_CITY_NAME,
            NULL TRT_START_COUNTY_NAME,
            NULL TRT_END_PROVINCE_NAME,
            NULL TRT_END_CITY_NAME,
            NULL TRT_END_COUNTY_NAME,
            NULL TRT_MILEAGE,
            NULL MIDDLE_MILEAGE,
            NULL TRT_GOODS_NAME,
            NULL TRT_GOODS_AMOUNT,
            NULL TRT_GOODS_AMOUNT_TYPE,
            NULL EXPECTED_END_TIME,
            NULL CUSTOMER_ORDER_NO,
            NULL ARRIVE_END_TIME,
            NULL CREATED_USER_NAME,
            f.BO_TRANS_TASK_FEE_ID,
            f.CONFIG_NAME,
            TO_CHAR(NVL(f.CONFIG_VALUE, 0), 'FM999999990.00') CONFIG_VALUE,
            TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999990.00') ACTUAL_VALUE,
            f.EXPENSIVE_TYPE,
            v.VERIFY_TIME,
            v.VERIFY_USER_NAME,
            NULL  INVOICE_STATE,
            NULL  SETTLE_MODE
            FROM T_BO_TRANS_FEE_VERIFY_TASK vt
            INNER JOIN T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_FEE_VERIFY_TASK_ID = vt.BO_TRANS_FEE_VERIFY_TASK_ID AND f.IS_DEL
            = 0
            LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID AND v.IS_DEL = 0
            WHERE vt.IS_DEL = 0
            AND f.IS_DEL = 0
            AND f.CONFIG_VALUE > 0
            <include refid="customVerifyTaskCondition"/>
        </if>
        )
        ORDER BY
        CREATED_TIME DESC ,
        BO_TRANS_TASK_ID DESC
    </select>
    <select id="queryVerifyTaskFeeDetail" resultMap="transFeeVerifyTaskEntityDetail">
        SELECT * FROM (SELECT
        vt.BO_TRANS_FEE_VERIFY_TASK_ID,
        vt.ORG_ID,
        to_char(vt.CREATED_TIME, 'yyyy-MM-dd HH24:mi:ss') CREATED_TIME,
        t.BO_TRANS_TASK_ID,
        t.TAX_WAYBILL_NO,
        t.DRIVER_NAME,
        t.MOBILE_NO,
        t.START_PROVINCE_NAME,
        t.START_CITY_NAME,
        t.START_COUNTY_NAME,
        t.END_PROVINCE_NAME,
        t.END_CITY_NAME,
        t.END_COUNTY_NAME,
        t.WB_ITEM,
        t.CART_BADGE_NO,
        to_char(t.START_TIME, 'yyyy-MM-dd HH24:mi:ss') START_TIME,
        to_char(t.END_TIME, 'yyyy-MM-dd HH24:mi:ss') END_TIME,
        t.MILEAGE,
        t.GOODS_NAME,
        t.GOODS_AMOUNT,
        t.GOODS_AMOUNT_TYPE,
        t.CAPACITY_TYPE_NAME,
        trt.BO_TASK_RETURN_TRIP_ID,
        trt.START_PROVINCE_NAME TRT_START_PROVINCE_NAME,
        trt.START_CITY_NAME TRT_START_CITY_NAME,
        trt.START_COUNTY_NAME TRT_START_COUNTY_NAME,
        trt.END_PROVINCE_NAME TRT_END_PROVINCE_NAME,
        trt.END_CITY_NAME TRT_END_CITY_NAME,
        trt.END_COUNTY_NAME TRT_END_COUNTY_NAME,
        trt.MILEAGE TRT_MILEAGE,
        trt.MIDDLE_MILEAGE,
        trt.GOODS_NAME TRT_GOODS_NAME,
        trt.GOODS_AMOUNT TRT_GOODS_AMOUNT,
        trt.GOODS_AMOUNT_TYPE TRT_GOODS_AMOUNT_TYPE,
        to_char(trt.EXPECTED_END_TIME, 'yyyy-MM-dd HH24:mi:ss') EXPECTED_END_TIME,
        e.CUSTOMER_ORDER_NO,
        to_char(e.ARRIVE_END_TIME, 'yyyy-MM-dd HH24:mi:ss') ARRIVE_END_TIME,
        f.BO_TRANS_TASK_FEE_ID,
        f.CONFIG_NAME,
        TO_CHAR(
        CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE f.CONFIG_VALUE END, 'FM999999999990.00') CONFIG_VALUE,
        TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999999990.00') ACTUAL_VALUE,
        f.EXPENSIVE_TYPE,
        v.VERIFY_TIME,
        v.VERIFY_USER_NAME,
        TO_CHAR(r.CREATED_TIME,'yyyy-MM-dd HH24:mi:ss') vefTime,
        TO_CHAR(r.NEW_VALUE, 'FM999999999990.00') NEW_VALUE,
        r.BO_TRANS_TASK_FEE_VRF_RE_ID,
        f.CONFIG_KEY
        FROM T_BO_TRANS_FEE_VERIFY_TASK vt
        INNER JOIN T_BO_TRANS_TASK t ON vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND t.IS_DEL = 0
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID AND e.is_del=0
        LEFT JOIN T_BO_TASK_RETURN_TRIP trt ON trt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND trt.IS_DEL = 0
        INNER JOIN T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND f.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID AND v.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF_RE r ON f.BO_TRANS_TASK_FEE_ID = r.BO_TRANS_TASK_FEE_ID AND r.IS_DEL = 0 AND r.MODIFY_TYPE = 0
        WHERE vt.IS_DEL = 0
        AND f.IS_DEL = 0
        <include refid="verifyTaskCondition"/>
        AND CASE
        <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
            WHEN f.CONFIG_KEY = '${key}'
            THEN ${value}
        </foreach>
        ELSE f.CONFIG_VALUE END > 0
        UNION ALL
        SELECT
        vt.BO_TRANS_FEE_VERIFY_TASK_ID,
        vt.ORG_ID,
        to_char(vt.CREATED_TIME, 'yyyy-MM-dd HH24:mi:ss') CREATED_TIME,
        NULL BO_TRANS_TASK_ID,
        NULL TAX_WAYBILL_NO,
        NULL DRIVER_NAME,
        NULL MOBILE_NO,
        NULL START_PROVINCE_NAME,
        NULL START_CITY_NAME,
        NULL START_COUNTY_NAME,
        NULL END_PROVINCE_NAME,
        NULL END_CITY_NAME,
        NULL END_COUNTY_NAME,
        vt.WB_ITEM,
        vt.CART_BADGE_NO,
        NULL START_TIME,
        NULL END_TIME,
        NULL MILEAGE,
        NULL GOODS_NAME,
        NULL GOODS_AMOUNT,
        NULL GOODS_AMOUNT_TYPE,
        NULL CAPACITY_TYPE_NAME,
        NULL BO_TASK_RETURN_TRIP_ID,
        NULL TRT_START_PROVINCE_NAME,
        NULL TRT_START_CITY_NAME,
        NULL TRT_START_COUNTY_NAME,
        NULL TRT_END_PROVINCE_NAME,
        NULL TRT_END_CITY_NAME,
        NULL TRT_END_COUNTY_NAME,
        NULL TRT_MILEAGE,
        NULL MIDDLE_MILEAGE,
        NULL TRT_GOODS_NAME,
        NULL TRT_GOODS_AMOUNT,
        NULL TRT_GOODS_AMOUNT_TYPE,
        NULL EXPECTED_END_TIME,
        NULL CUSTOMER_ORDER_NO,
        NULL ARRIVE_END_TIME,
        f.BO_TRANS_TASK_FEE_ID,
        f.CONFIG_NAME,
        TO_CHAR(NVL(f.CONFIG_VALUE, 0), 'FM999999990.00') CONFIG_VALUE,
        TO_CHAR(NVL(f.ACTUAL_VALUE, 0), 'FM999999990.00') ACTUAL_VALUE,
        f.EXPENSIVE_TYPE,
        v.VERIFY_TIME,
        v.VERIFY_USER_NAME,
        TO_CHAR(r.CREATED_TIME,'yyyy-MM-dd HH24:mi:ss') vefTime,
        TO_CHAR(r.NEW_VALUE, 'FM999999999990.00') NEW_VALUE,
        r.BO_TRANS_TASK_FEE_VRF_RE_ID,
        f.CONFIG_KEY
        FROM T_BO_TRANS_FEE_VERIFY_TASK vt
        INNER JOIN T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_FEE_VERIFY_TASK_ID = vt.BO_TRANS_FEE_VERIFY_TASK_ID AND f.IS_DEL
        = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF v ON f.BO_TRANS_TASK_FEE_ID = v.BO_TRANS_TASK_FEE_ID AND v.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_FEE_VRF_RE r ON f.BO_TRANS_TASK_FEE_ID = r.BO_TRANS_TASK_FEE_ID AND r.IS_DEL = 0 AND r.MODIFY_TYPE = 0
        WHERE vt.IS_DEL = 0
        AND f.IS_DEL = 0
        AND f.CONFIG_VALUE > 0
        <include refid="customVerifyTaskCondition"/>
        )
        ORDER BY
        CREATED_TIME DESC ,
        BO_TRANS_TASK_ID DESC
    </select>
    <select id="queryInvoiceFreight" resultType="com.wtyt.settle.bean.BoTaskFeeCountBean"
            parameterType="com.wtyt.settle.bean.SettleQueryBean">
        SELECT SUM(ALL_FREIGHT + TAX_FEE + SERVICE_FEE + NVL(FINANCE_SERVICE_FEE,0)+ NVL(DATA_SERVICE_FEE,0)+ NVL(INS_FEE,0)+ NVL(FINANCE_CREDIT_FEE,0)) invoiceFreight,INVOICE_STATE invoiceState FROM T_TAX_WAYBILL WHERE TAX_WAYBILL_ID IN (
            select w.TAX_WAYBILL_ID from T_BO_TRANS_TASK t
                inner join T_TAX_WAYBILL w on w.TAX_WAYBILL_ID = t.TAX_WAYBILL_ID  AND w.IS_DEL = 0
                inner JOIN T_BO_TRANS_FEE_VERIFY_TASK vt ON vt.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
                INNER JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID
                left join T_BO_TRANS_TASK_EXTRA e on  t.BO_TRANS_TASK_ID= e.BO_TRANS_TASK_ID
                where vt.IS_DEL = 0
                AND f.IS_DEL = 0
                AND f.IS_VERIFY = 1
                AND CASE
                <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                    WHEN f.CONFIG_KEY = '${key}'
                    THEN ${value}
                </foreach>
                ELSE f.CONFIG_VALUE END > 0
                <include refid="verifyTaskCondition"></include>
             GROUP BY w.INVOICE_STATE,w.TAX_WAYBILL_ID,vt.BO_TRANS_FEE_VERIFY_TASK_ID) GROUP BY TAX_WAYBILL_ID,INVOICE_STATE
    </select>
</mapper>
