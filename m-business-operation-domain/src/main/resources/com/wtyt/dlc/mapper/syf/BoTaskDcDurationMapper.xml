<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dlc.mapper.syf.BoTaskDcDurationMapper">

    <resultMap type="com.wtyt.dlc.bean.BoTaskDcDurationBean" id="BoTaskDcDurationMap">
        <result property="boTaskDcDurationId" column="BO_TASK_DC_DURATION_ID" jdbcType="VARCHAR"/>
        <result property="boTaskDeadlineConfId" column="BO_TASK_DEADLINE_CONF_ID" jdbcType="VARCHAR"/>
        <result property="minDistance" column="MIN_DISTANCE" jdbcType="VARCHAR"/>
        <result property="maxDistance" column="MAX_DISTANCE" jdbcType="VARCHAR"/>
        <result property="transportTime" column="TRANSPORT_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedUserName" column="LAST_MODIFIED_USER_NAME" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="commonField">
        BO_TASK_DC_DURATION_ID,
        BO_TASK_DEADLINE_CONF_ID,
        CASE WHEN TRUNC(MIN_DISTANCE)=MIN_DISTANCE THEN TO_CHAR(MIN_DISTANCE, 'FM999999990') ELSE TO_CHAR(MIN_DISTANCE, 'FM999999990.99') END MIN_DISTANCE,
        CASE WHEN TRUNC(MAX_DISTANCE)=MAX_DISTANCE THEN TO_CHAR(MAX_DISTANCE, 'FM999999990') ELSE TO_CHAR(MAX_DISTANCE, 'FM999999990.99') END MAX_DISTANCE,
        CASE WHEN TRUNC(TRANSPORT_TIME)=TRANSPORT_TIME THEN TO_CHAR(TRANSPORT_TIME, 'FM999999990') ELSE TO_CHAR(TRANSPORT_TIME, 'FM999999990.99') END TRANSPORT_TIME
    </sql>
    <select id="getById" resultMap="BoTaskDcDurationMap">
        SELECT
            <include refid="commonField"/>
        FROM
        T_BO_TASK_DC_DURATION
        WHERE
        BO_TASK_DC_DURATION_ID = #{boTaskDcDurationId}
        AND IS_DEL =0
    </select>

    <select id="list" resultMap="BoTaskDcDurationMap">
        SELECT
            <include refid="commonField"/>
        FROM
        T_BO_TASK_DC_DURATION
        WHERE
        BO_TASK_DEADLINE_CONF_ID = #{boTaskDeadlineConfId}
        AND IS_DEL =0
        ORDER BY CREATED_TIME ASC
    </select>

    <insert id="save">
        insert into T_BO_TASK_DC_DURATION(BO_TASK_DC_DURATION_ID,BO_TASK_DEADLINE_CONF_ID, MIN_DISTANCE, MAX_DISTANCE, TRANSPORT_TIME, LAST_MODIFIED_USER_NAME)
        values (#{boTaskDcDurationId},#{boTaskDeadlineConfId}, #{minDistance}, #{maxDistance}, #{transportTime}, #{lastModifiedUserName})
    </insert>

    <update id="delete">
        UPDATE
        T_BO_TASK_DC_DURATION
        SET
        IS_DEL = 1,
        LAST_MODIFIED_TIME = SYSDATE,
        LAST_MODIFIED_USER_NAME = #{userName}
        WHERE
        BO_TASK_DC_DURATION_ID = #{boTaskDcDurationId}
        AND IS_DEL = 0
    </update>

    <update id="update">
        update T_BO_TASK_DC_DURATION
        <set>
            <if test="minDistance != null and minDistance != ''">
                MIN_DISTANCE = #{minDistance},
            </if>
            <if test="maxDistance != null and maxDistance != ''">
                MAX_DISTANCE = #{maxDistance},
            </if>
            <if test="transportTime != null and transportTime != ''">
                TRANSPORT_TIME = #{transportTime},
            </if>
            <if test="lastModifiedUserName != null and lastModifiedUserName != ''">
                LAST_MODIFIED_USER_NAME = #{lastModifiedUserName},
            </if>
            LAST_MODIFIED_TIME = SYSDATE
        </set>
        where BO_TASK_DC_DURATION_ID = #{boTaskDcDurationId}
        AND IS_DEL=0
    </update>

</mapper>

