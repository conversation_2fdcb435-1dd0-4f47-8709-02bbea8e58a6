package com.wtyt.reviews.controller;

import com.wtyt.common.annotation.ExcludeAppComponent;
import com.wtyt.common.annotation.ExcludeJobComponent;
import com.wtyt.commons.annotation.Logger;
import com.wtyt.money.commons.bean.BaseBean;
import com.wtyt.money.commons.bean.ResDataBean;
import com.wtyt.reviews.bean.request.Req5329576Bean;
import com.wtyt.reviews.bean.request.Req5330279Bean;
import com.wtyt.reviews.service.ReviewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024年03月15日 15时19分
 */
@ExcludeJobComponent
@ExcludeAppComponent
@RestController
@RequestMapping(value = "/pm/reviews")
public class ReviewsController {

    private ReviewsService reviewsService;

    /**
     * 5330279-绩效管理-用户评价数据汇总
     * @param bean
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/user/reviews/collect")
    @Logger("5330279-绩效管理-用户评价数据汇总")
    public ResDataBean<?> userReviewsCollect(@RequestBody BaseBean<Req5330279Bean> bean) throws Exception {
        return new ResDataBean<>().success(this.reviewsService.userReviewsCollect(bean.getData()));
    }

    @Autowired
    public void setReviewsService(ReviewsService reviewsService) {
        this.reviewsService = reviewsService;
    }
}
