<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoTaskSyncMapper">


    <select id="queryTaskDetailByTaskOrWayBillId" resultType="com.wtyt.bo.bean.BoTaskSyncBean">
        SELECT
            T1.BO_TRANS_TASK_ID boTransTaskId,
            T1.TAX_WAYBILL_ID taxWaybillId,
            T1.TAX_WAYBILL_NO taxWaybillNo,
            T1.ORG_ID orgId,
            T1.NODE_ID nodeId,
            T1.CART_BADGE_NO cartBadgeNo,
            T1.MOBILE_NO mobileNo,
            T1.DRIVER_NAME driverName,
            T1.CART_BADGE_COLOR cartBadgeColor,
            T1.START_PROVINCE_NAME startProvinceName,
            T1.START_CITY_NAME startCityName,
            T1.START_COUNTY_NAME startCountyName,
            T1.END_PROVINCE_NAME endProvinceName,
            T1.END_CITY_NAME endCityName,
            T1.END_COUNTY_NAME endCountyName,
            T1.MILEAGE mileage,
            T1.GOODS_NAME goodsName,
            TO_CHAR(T1.GOODS_AMOUNT, 'FM999999990.0000') goodsAmount,
            T1.GOODS_AMOUNT_TYPE goodsAmountType,
            TO_CHAR(T1.PREPAYMENTS, 'FM999999990.00') prepayments,
            TO_CHAR(T1.PREPAYMENTS_OILCARD, 'FM999999990.00') prepaymentsOilCard,
            TO_CHAR(T1.PREPAYMENTS_GASCARD, 'FM999999990.00') prepaymentsGasCard,
            TO_CHAR(T1.ALL_FREIGHT, 'FM999999990.00') allFreight,
            TO_CHAR(T1.USER_FREIGHT, 'FM999999990.00') userFreight,
            TO_CHAR(T1.BACK_FEE, 'FM999999990.00') backFee,
            TO_CHAR(T1.FREIGHT_INCR, 'FM999999990.00') freightIncr,
            TO_CHAR(T1.LOSS_FEE, 'FM999999990.00') lossFee,
            T1.START_LONGITUDE startLongitude,
            T1.START_LATITUDE startLatitude,
            T1.END_LONGITUDE endLongitude,
            T1.END_LATITUDE endLatitude,
            T1.OFFER_TYPE offerType,
            T1.TRANSPORT_TYPE transportType,
            T1.STATE state,
            T2.CART_TYPE cartType,
            T2.CART_LENGTH cartLength,
            T2.LOADING_ADDRESS_NAME loadingAddressName,
            T2.UNLOADING_ADDRESS_NAME unloadingAddressName,
            NR.USER_ID dispatchUserId,
            NR.SYS_ROLE_TYPE dispatchSysRoleType,
            TO_CHAR(T2.HYB_RECEIVED_TIME, 'yyyy-mm-dd hh24:mi:ss') hybReceivedTime,
            TO_CHAR(T2.ARRIVE_END_TIME, 'yyyy-mm-dd hh24:mi:ss') promisedArriveTime,
            TO_CHAR(T4.DEAD_LINE_TIME, 'yyyy-mm-dd hh24:mi:ss') unLoadTime,
            T2.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
            T1.SETTLE_MODE settleMode,
            T1.TRANS_MODE transMode,
            A.BO_TRANS_TASK_ALLOCATE_ID boTransTaskAllocateId,
            A.SETTLE_MODE allocateSettleMode,
            A.NODE_ID allocateNodeId,
            NVL(T1.TAX_WAYBILL_ID, A.TAX_WAYBILL_ID) locTaxWaybillId,
            CASE
            WHEN T5.GUARANTEE_AMOUNT - TRUNC(T5.GUARANTEE_AMOUNT) = 0 THEN
            TO_CHAR(TRUNC(T5.GUARANTEE_AMOUNT), 'FM999999990')
            ELSE
            TO_CHAR(T5.GUARANTEE_AMOUNT, 'FM999999990.9999')
            END driverGuaranteeAmount,
            T5.GUARANTEE_CHANNEL driverGuaranteeChannel,
            T1.OPERATE_SCHEME operateScheme,
            T1.CAPACITY_TYPE capacityType,
            T1.CAPACITY_TYPE_NAME capacityTypeName,
            T1.IS_PARTAKE_OPERATE isPartakeOperate,
            CASE
            WHEN T1.LOADING_TONNAGE - TRUNC(T1.LOADING_TONNAGE)= 0 THEN
            TO_CHAR(TRUNC(T1.LOADING_TONNAGE), 'FM999999990')
            ELSE
            TO_CHAR(T1.LOADING_TONNAGE, 'FM999999990.9999')
            END loadingTonnage,
            CASE
            WHEN T1.UNLOADING_TONNAGE - TRUNC(T1.UNLOADING_TONNAGE) = 0 THEN
            TO_CHAR(TRUNC(T1.UNLOADING_TONNAGE), 'FM999999990')
            ELSE
            TO_CHAR(T1.UNLOADING_TONNAGE, 'FM999999990.9999')
            END unloadingTonnage,
            CASE
            WHEN T2.LOADING_ROUGH_WEIGHT - TRUNC(T2.LOADING_ROUGH_WEIGHT)= 0 THEN
            TO_CHAR(TRUNC(T2.LOADING_ROUGH_WEIGHT), 'FM999999990')
            ELSE
            TO_CHAR(T2.LOADING_ROUGH_WEIGHT, 'FM999999990.9999')
            END loadingRoughWeight,
            CASE
            WHEN T2.UNLOADING_ROUGH_WEIGHT - TRUNC(T2.UNLOADING_ROUGH_WEIGHT) = 0 THEN
            TO_CHAR(TRUNC(T2.UNLOADING_ROUGH_WEIGHT), 'FM999999990')
            ELSE
            TO_CHAR(T2.UNLOADING_ROUGH_WEIGHT, 'FM999999990.9999')
            END unloadingRoughWeight,
        
            CASE
            WHEN T2.LOADING_TARE - TRUNC(T2.LOADING_TARE)= 0 THEN
            TO_CHAR(TRUNC(T2.LOADING_TARE), 'FM999999990')
            ELSE
            TO_CHAR(T2.LOADING_TARE, 'FM999999990.9999')
            END loadingTare,
            CASE
            WHEN T2.UNLOADING_TARE - TRUNC(T2.UNLOADING_TARE) = 0 THEN
            TO_CHAR(TRUNC(T2.UNLOADING_TARE), 'FM999999990')
            ELSE
            TO_CHAR(T2.UNLOADING_TARE, 'FM999999990.9999')
            END unloadingTare,
            TO_CHAR(T2.LOSS_AMOUNT, 'FM999999990.00') lossAmount,
            TO_CHAR(T1.FIXED_COSTS, 'FM999999990.00') fixedCosts,
            TO_CHAR(T1.UNIT_PRICE, 'FM999999990.00') unitPrice,
            T1.LOADING_PLACE_NAME loadingPlaceName,
            T1.UNLOADING_PLACE_NAME unloadingPlaceName,
            T1.BO_BUSINESS_LINE_ID boBusinessLineId,
            T1.XCY_USER_ID xcyUserId,
            T1.WB_ITEM wbItem,
            T2.AUDIT_STATUS auditStatus,
            T2.TRANS_TASK_FLAG transTaskFlag,
            T1.CUSTOMER_REMARK customerRemark,
            T1.SETTLE_TYPE settleType
        FROM T_BO_TRANS_TASK T1
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON T1.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
            AND A.IS_DEL =0
        JOIN T_BO_TRANS_TASK_EXTRA T2 ON T1.BO_TRANS_TASK_ID = T2.BO_TRANS_TASK_ID AND T2.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR ON NR.BO_TRANS_NODE_RECORD_ID =T2.DISPATCH_CAR_RECORD_ID
        LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE T4 ON T4.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
            AND T4.NODE_ID = 800
            AND T4.IS_DEL = 0
        LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE T5 ON T5.BO_TASK_DRIVER_GUARANTEE_ID = T2.BO_TASK_DRIVER_GUARANTEE_ID
            AND T5.IS_DEL = 0
        WHERE
        <choose>
            <when test="taskId != null and taskId != ''">
                T1.BO_TRANS_TASK_ID =  #{taskId}
            </when>
            <otherwise>
                T1.BO_TRANS_TASK_ID =
                (
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE IS_DEL = 0 AND TAX_WAYBILL_ID = #{taxWaybillId}
                UNION
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE IS_DEL = 0 AND TAX_WAYBILL_ID = #{taxWaybillId}
                )
            </otherwise>
        </choose>
        <if test="isDel!=null and isDel!=''">
            AND T1.IS_DEL = #{isDel}
        </if>
    </select>
    <select id="queryTaskBasicByTaskOrWaybillId" resultType="com.wtyt.bo.bean.BoTaskSyncBean">
        SELECT
            T.TAX_WAYBILL_ID taxWaybillId,
            T.TAX_WAYBILL_NO taxWaybillNo,
            T.ORG_ID orgId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.TRANS_MODE transMode,
            T.TRANSPORT_TYPE transportType,
            NVL(TE.USER_FREIGHT_APPROVAL_STATUS,0) userFreightApprovalStatus,
            TE.TRANS_TASK_FLAG transTaskFlag,
            T.OWNER_ORG_ID ownerOrgId
        FROM T_BO_TRANS_TASK T
            JOIN T_BO_TRANS_TASK_EXTRA TE ON TE.BO_TRANS_TASK_ID =T.BO_TRANS_TASK_ID AND TE.IS_DEL =0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A ON
            T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
            AND A.IS_DEL = 0
        WHERE
        <choose>
            <when test="taskId != null and taskId != ''">
                T.BO_TRANS_TASK_ID =  #{taskId}
            </when>
            <otherwise>
                T.BO_TRANS_TASK_ID =
                (
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE IS_DEL = 0 AND TAX_WAYBILL_ID = #{taxWaybillId}
                UNION
                SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE IS_DEL = 0 AND TAX_WAYBILL_ID = #{taxWaybillId}
                )
            </otherwise>
        </choose>
        <if test="isDel != null and isDel != ''">
            AND T.IS_DEL = #{isDel}
        </if>
    </select>

</mapper>
