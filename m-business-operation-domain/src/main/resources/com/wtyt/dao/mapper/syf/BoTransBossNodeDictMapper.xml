<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransBossNodeDictMapper">


    <select id="queryAllBossNodes" resultType="com.wtyt.dao.bean.syf.BoTransBossNodeDictBean">
        SELECT
            BO_TRANS_BOSS_NODE_DICT_ID boTransBossNodeDictId,
            BOSS_NODE_CODE bossNodeCode,
            BOSS_NODE_NAME bossNodeName,
            BOSS_NODE_PID bossNodePid,
            PATH_LEVEL pathLevel
        FROM
            T_BO_TRANS_BOSS_NODE_DICT
        WHERE
            IS_DEL = 0
        ORDER BY
            BOSS_NODE_CODE
    </select>
    
    
    <select id="queryAllLevelSecondBossNodes" resultType="com.wtyt.dao.bean.syf.BoTransBossNodeDictBean">
        SELECT
            BO_TRANS_BOSS_NODE_DICT_ID boTransBossNodeDictId,
            BOSS_NODE_CODE bossNodeCode,
            BOSS_NODE_NAME bossNodeName,
            BOSS_NODE_PID bossNodePid,
            PATH_LEVEL pathLevel
        FROM
            T_BO_TRANS_BOSS_NODE_DICT
        WHERE
            IS_DEL = 0
            and PATH_LEVEL = 2
        ORDER BY
            BOSS_NODE_CODE
    </select>
    
</mapper>