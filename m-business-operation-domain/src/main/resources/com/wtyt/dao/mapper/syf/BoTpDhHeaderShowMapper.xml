<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhHeaderShowMapper">


    <insert id="insertBatch">
        INSERT INTO T_BO_TP_DH_HEADER_SHOW
        (BO_TP_DH_HEADER_SHOW_ID, BO_TP_DH_CONFIG_ID, BO_TP_DH_HEADER_IMPORT_ID, SPLIT_CHAR)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTpDhHeaderShowId} BO_TP_DH_HEADER_SHOW_ID,
            #{item.boTpDhConfigId} BO_TP_DH_CONFIG_ID,
            #{item.boTpDhHeaderImportId} BO_TP_DH_HEADER_IMPORT_ID,
            #{item.splitChar} SPLIT_CHAR
            FROM
            DUAL
        </foreach>
    </insert>

    <select id="queryDhHeaderShowListByImportId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderShowBean">
        SELECT
        BO_TP_DH_HEADER_SHOW_ID boTpDhHeaderShowId,
        BO_TP_DH_CONFIG_ID boTpDhConfigId,
        BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
        SPLIT_CHAR splitChar
        FROM T_BO_TP_DH_HEADER_SHOW WHERE BO_TP_DH_HEADER_IMPORT_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByConfigId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderShowBean">
        SELECT
            BO_TP_DH_CONFIG_ID boTpDhConfigId,
            BO_TP_DH_HEADER_SHOW_ID boTpDhHeaderShowId,
            BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
            SPLIT_CHAR splitChar
        FROM T_BO_TP_DH_HEADER_SHOW
        WHERE IS_DEL = 0
        AND BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
    </select>
</mapper>
