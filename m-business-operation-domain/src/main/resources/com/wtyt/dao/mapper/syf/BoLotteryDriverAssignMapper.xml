<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoLotteryDriverAssignMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoLotteryDriverAssignBean" id="BoLotteryDriverAssignMap">
        <result property="boLotteryDriverAssignId" column="BO_LOTTERY_DRIVER_ASSIGN_ID" jdbcType="VARCHAR"/>
        <result property="boTransOrderId" column="BO_TRANS_ORDER_ID" jdbcType="VARCHAR"/>
        <result property="cpdDriverMobilePoolId" column="CPD_DRIVER_MOBILE_POOL_ID" jdbcType="VARCHAR"/>
        <result property="driverName" column="DRIVER_NAME" jdbcType="VARCHAR"/>
        <result property="driverMobileNo" column="DRIVER_MOBILE_NO" jdbcType="VARCHAR"/>
        <result property="cartBadgeNo" column="CART_BADGE_NO" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="queryColumns">
        BO_LOTTERY_DRIVER_ASSIGN_ID,BO_TRANS_ORDER_ID,CPD_DRIVER_MOBILE_POOL_ID,DRIVER_NAME,DRIVER_MOBILE_NO,CART_BADGE_NO
    </sql>

    <select id="getLotteryDriverAssignListByOrderIdList" resultMap="BoLotteryDriverAssignMap">
        SELECT
            <include refid="queryColumns"/>
        FROM T_BO_LOTTERY_DRIVER_ASSIGN WHERE BO_TRANS_ORDER_ID IN
        <foreach collection="transOrderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND IS_DEL = 0
    </select>

    <insert id="batchInsertLotteryDriverAssign">
        INSERT INTO
        T_BO_LOTTERY_DRIVER_ASSIGN (
            BO_LOTTERY_DRIVER_ASSIGN_ID,
            BO_TRANS_ORDER_ID,
            CPD_DRIVER_MOBILE_POOL_ID,
            DRIVER_NAME,
            DRIVER_MOBILE_NO,
            CART_BADGE_NO )
        SELECT
        A.*
        FROM
            (
                <foreach item="item" index="index" collection="list" separator="UNION ALL">
                    SELECT
                    #{item.boLotteryDriverAssignId} boLotteryDriverAssignId,
                    #{item.boTransOrderId} boTransOrderId,
                    #{item.cpdDriverMobilePoolId} cpdDriverMobilePoolId,
                    #{item.driverName} driverName,
                    #{item.driverMobileNo} driverMobileNo,
                    #{item.cartBadgeNo} cartBadgeNo
                    FROM
                    DUAL
                </foreach>
            ) A
    </insert>

    <update id="cancelAssignDriver">
        UPDATE
            T_BO_LOTTERY_DRIVER_ASSIGN
        SET
            IS_DEL = 1, LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND BO_TRANS_ORDER_ID IN
            <foreach collection="boTransOrderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
    </update>
</mapper>
