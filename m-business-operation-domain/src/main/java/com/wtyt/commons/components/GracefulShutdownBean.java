package com.wtyt.commons.components;

import com.wtyt.common.constants.Constants;
import com.wtyt.common.toolkits.RedisToolkit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 资源清理器：接收容器发送的SIGKILL信号，执行必要的资源清理操作
 * <pre>
 * CICD在重启服务时，对于Docker容器是先使用docker stop -t 60 （生产环境是60s），然后再删除容器，然后再创建容器，再使用docker run... 启动容器，
 * 如果部分加锁代码部分执行过程比较耗时，可能60s执行时间不够，而docker如果在执行了docker stop -t 60命令，在60s之后，就会执行docker kill [CONTAINER_ID]（容器ID）命令，
 * 这个命令会立即向容器的主进程发送SIGKILL信号，不等待任何清理操作。这是一种“硬”停止的方式。
 * 这时会导致还在执行耗时处理的部分锁资源不能够及时释放。（在服务重启后，这个锁资源仍然处于占用状态，如果没有给锁资源设置超时时间，会导致锁资源一致处于占用状态）
 * </pre>
 * <AUTHOR>
 */
@Component
public class GracefulShutdownBean implements DisposableBean {
    private static final Logger logger = LoggerFactory.getLogger(GracefulShutdownBean.class);

    @Autowired
    private RedisToolkit redisToolkit;

    @PostConstruct
    public void init() {
        Runtime.getRuntime().addShutdownHook(new Thread() {
            @Override
            public void run() {
                // 执行资源清理逻辑
                logger.info("==================接收到容器SIGTERM, 执行必要资源清理开始==================");
                long startTime = System.currentTimeMillis();
                // 对必要锁资源进行释放
                releaseLockResource();
                logger.info("==================接收到容器SIGTERM, 执行必要资源清理结束, 耗时 = {}ms==================", System.currentTimeMillis() - startTime);
            }
        });
    }

    /**
     * 释放必要锁资源
     * <pre>释放必要锁，防止服务正在执行导出且导出执行时间过长，docker容器执行直接强制停止导致资源未进行释放的问题</pre>
     */
    private void releaseLockResource() {
        // 释放运输任务导出锁
        redisToolkit.del(Constants.EXPORT_TRANS_TASK_LOCK_KEY);
        // 释放运输任务导出过程，允许的最大执行数涉及到的锁
        redisToolkit.del(Constants.EXPORT_TRANS_TASK_COUNT_KEY);
    }

    @Override
    public void destroy() {

    }
}