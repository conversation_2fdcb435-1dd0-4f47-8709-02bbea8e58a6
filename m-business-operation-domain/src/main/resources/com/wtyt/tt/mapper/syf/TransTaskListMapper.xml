<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.tt.mapper.syf.TransTaskListMapper">

    <resultMap id="taskDetailResultMap" type="com.wtyt.bo.bean.BoTaskDetailBean">
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="taxWaybillId" column="TAX_WAYBILL_ID" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="mobileNo" column="MOBILE_NO" jdbcType="VARCHAR"/>
        <result property="driverName" column="DRIVER_NAME" jdbcType="VARCHAR"/>
        <result property="cartBadgeNo" column="CART_BADGE_NO" jdbcType="VARCHAR"/>
        <result property="cartBadgeColor" column="CART_BADGE_COLOR" jdbcType="VARCHAR"/>
        <result property="goodsName" column="GOODS_NAME" jdbcType="VARCHAR"/>
        <result property="goodsAmount" column="GOODS_AMOUNT" jdbcType="VARCHAR"/>
        <result property="taxWaybillNo" column="TAX_WAYBILL_NO" jdbcType="VARCHAR"/>
        <result property="goodsAmountType" column="GOODS_AMOUNT_TYPE" jdbcType="VARCHAR"/>
        <result property="loadingTonnage" column="LOADING_TONNAGE" jdbcType="VARCHAR"/>
        <result property="unloadingTonnage" column="UNLOADING_TONNAGE" jdbcType="VARCHAR"/>
        <result property="loadingPlaceName" column="LOADING_PLACE_NAME" jdbcType="VARCHAR"/>
        <result property="unloadingPlaceName" column="UNLOADING_PLACE_NAME" jdbcType="VARCHAR"/>
        <result property="startTime" column="START_TIME" jdbcType="VARCHAR"/>
        <result property="endTime" column="END_TIME" jdbcType="VARCHAR"/>
        <result property="state" column="STATE" jdbcType="VARCHAR"/>
        <result property="startProvinceName" column="START_PROVINCE_NAME" jdbcType="VARCHAR"/>
        <result property="startCityName" column="START_CITY_NAME" jdbcType="VARCHAR"/>
        <result property="startCountyName" column="START_COUNTY_NAME" jdbcType="VARCHAR"/>
        <result property="loadingAddressName" column="LOADING_ADDRESS_NAME" jdbcType="VARCHAR"/>
        <result property="startLongitude" column="START_LONGITUDE" jdbcType="VARCHAR"/>
        <result property="startLatitude" column="START_LATITUDE" jdbcType="VARCHAR"/>
        <result property="endProvinceName" column="END_PROVINCE_NAME" jdbcType="VARCHAR"/>
        <result property="endCityName" column="END_CITY_NAME" jdbcType="VARCHAR"/>
        <result property="endCountyName" column="END_COUNTY_NAME" jdbcType="VARCHAR"/>
        <result property="unloadingAddressName" column="UNLOADING_ADDRESS_NAME" jdbcType="VARCHAR"/>
        <result property="endLongitude" column="END_LONGITUDE" jdbcType="VARCHAR"/>
        <result property="endLatitude" column="END_LATITUDE" jdbcType="VARCHAR"/>
        <result property="cartType" column="CART_TYPE" jdbcType="VARCHAR"/>
        <result property="cartLength" column="CART_LENGTH" jdbcType="VARCHAR"/>
        <result property="arriveEndTime" column="ARRIVE_END_TIME" jdbcType="VARCHAR"/>
        <result property="unloadTime" column="UNLOAD_TIME" jdbcType="VARCHAR"/>
        <result property="allFreight" column="ALL_FREIGHT" jdbcType="VARCHAR"/>
        <result property="userFreight" column="USER_FREIGHT" jdbcType="VARCHAR"/>
        <result property="serviceFee" column="SERVICE_FEE" jdbcType="VARCHAR"/>
        <result property="dataServiceFee" column="DATA_SERVICE_FEE" jdbcType="VARCHAR"/>
        <result property="prepayments" column="PREPAYMENTS" jdbcType="VARCHAR"/>
        <result property="backFee" column="BACK_FEE" jdbcType="VARCHAR"/>
        <result property="freightIncr" column="FREIGHT_INCR" jdbcType="VARCHAR"/>
        <result property="lossFee" column="LOSS_FEE" jdbcType="VARCHAR"/>
        <result property="unitPrice" column="UNIT_PRICE" jdbcType="VARCHAR"/>
        <result property="prepaymentsOilcard" column="PREPAYMENTS_OILCARD" jdbcType="VARCHAR"/>
        <result property="prepaymentsGascard" column="PREPAYMENTS_GASCARD" jdbcType="VARCHAR"/>
        <result property="settleMode" column="SETTLE_MODE" jdbcType="VARCHAR"/>
        <result property="boBusinessLineId" column="BO_BUSINESS_LINE_ID" jdbcType="VARCHAR"/>
        <result property="nodeId" column="NODE_ID" jdbcType="VARCHAR"/>
        <result property="nodeTime" column="NODE_TIME" jdbcType="VARCHAR"/>
        <result property="payName" column="PAY_NAME" jdbcType="VARCHAR"/>
        <result property="payIdCard" column="PAY_ID_CARD" jdbcType="VARCHAR"/>
        <result property="payBankNo" column="PAY_BANK_NO" jdbcType="VARCHAR"/>
        <result property="payBankName" column="PAY_BANK_NAME" jdbcType="VARCHAR"/>
        <result property="province" column="PROVINCE" jdbcType="VARCHAR"/>
        <result property="cityName" column="CITY_NAME" jdbcType="VARCHAR"/>
        <result property="payMobileNo" column="PAY_MOBILE_NO" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="queryTransTaskListForHybOfBaseColumn">
        T.BO_TRANS_TASK_ID              boTransTaskId,
        T.TAX_WAYBILL_ID                taxWaybillId,
        T.TRANS_MODE                    transMode,
        T.OFFER_TYPE                    offerType,
        T.CREATED_USER_ID               createdUserId,
        TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        TO_CHAR(T.LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI:SS') lastModifiedTime,
        T.NOTE                          note,
        T.CREATED_USER_SYS_ROLE_TYPE    createdUserSysRoleType,
        T.XCY_USER_ID                   xcyUserId,
        T.NODE_ID                       nodeId,
        TO_CHAR(T.NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
        T.TRANS_PATTERN                 transPattern,
        T.MODIFY_USER_ID                modifyUserId,
        T.MODIFY_DRIVER_ID              modifyDriverId,
        T.MODIFY_SYS_ROLE_TYPE          modifySysRoleType,
        T.CREATED_USER_JOB_NAME         createdUserJobName,
        T.MODIFY_USER_JOB_NAME          modifyUserJobName,
        T.MOBILE_NO                     mobileNo,
        T.ORG_ID                        orgId,
        T.START_CITY_CODE               startCityCode,
        T.START_CITY_NAME               startCityName,
        T.END_CITY_CODE                 endCityCode,
        T.END_CITY_NAME                 endCityName,
        TO_CHAR(NVL(T.ALL_FREIGHT, 0), '**********0.00') allFreight,
        T.DRIVER_NAME                   driverName,
        T.CART_BADGE_NO                 cartBadgeNo,
        T.CART_BADGE_COLOR              cartBadgeColor,
        T.GOODS_NAME                    goodsName,
        TO_CHAR(NVL(T.GOODS_AMOUNT, 0), '**********0.0000') goodsAmount,
        T.START_PROVINCE_NAME           startProvinceName,
        T.START_PROVINCE_CODE           startProvinceCode,
        T.END_PROVINCE_NAME             endProvinceName,
        T.END_PROVINCE_CODE             endProvinceCode,
        T.TAX_WAYBILL_NO                taxWaybillNo,
        T.GOODS_AMOUNT_TYPE             goodsAmountType,
        TO_CHAR(NVL(T.PREPAYMENTS, 0), '**********0.00') prepayments,
        T.ORDER_CREATE_TYPE             orderCreateType,
        T.START_COUNTY_NAME             startCountyName,
        T.END_COUNTY_NAME               endCountyName,
        T.START_COUNTY_CODE             startCountyCode,
        T.END_COUNTY_CODE               endCountyCode,
        TO_CHAR(NVL(T.BACK_FEE, 0), '**********0.00') backFee,
        NVL(T.MILEAGE, 0) mileage,
        TO_CHAR(NVL(T.USER_FREIGHT, 0), '**********0.00') userFreight,
        TO_CHAR(NVL(T.FREIGHT_INCR, 0), '**********0.00') freightIncr,
        TO_CHAR(NVL(T.GOODS_COST, 0), '**********0.00') goodsCost,
        TO_CHAR(NVL(T.PREPAYMENTS_OILCARD, 0), '**********0.00') prepaymentsOilcard,
        TO_CHAR(NVL(T.PREPAYMENTS_GASCARD, 0), '**********0.00') prepaymentsGascard,
        T.START_LONGITUDE               startLongitude,
        T.START_LATITUDE                startLatitude,
        T.END_LONGITUDE                 endLongitude,
        T.END_LATITUDE                  endLatitude,
        T.ADVANCE_PAY_STATE             advancePayState,
        TO_CHAR(NVL(T.UNIT_PRICE, 0), '**********0.00') unitPrice,
        T.RECEIVER                      receiver,
        T.RECEIVER_MOBILE               receiverMobile,
        TO_CHAR(NVL(T.LOSS_FEE, 0), '**********0.00') lossFee,
        T.TRANSPORT_TYPE                transportType,
        T.TRANSPORT_LINE_ID             transportLineId,
        T.BO_BUSINESS_LINE_ID           boBusinessLineId,
        TO_CHAR(NVL(T.LOADING_TONNAGE, 0), '**********0.0000') loadingTonnage,
        TO_CHAR(NVL(T.UNLOADING_TONNAGE, 0), '**********0.0000') unloadingTonnage,
        T.LOADING_PLACE_NAME            loadingPlaceName,
        T.UNLOADING_PLACE_NAME          unloadingPlaceName,
        TO_CHAR(T.START_TIME, 'YYYY-MM-DD HH24:MI:SS') startTime,
        T.STATE                         state,
        TO_CHAR(T.PAY_OVER_TIME, 'YYYY-MM-DD HH24:MI:SS') payOverTime,
        T.HYB_STATE                     hybState,
        TO_CHAR(T.FIRST_RECEIPT_TIME, 'YYYY-MM-DD HH24:MI:SS') firstReceiptTime,
        T.PAY_STATE                     payState,
        T.LOSS_ENSURE_STATE             lossEnsureState,
        TO_CHAR(NVL(T.INS_FEE, 0), '**********0.00') insFee,
        T.WB_ITEM                       wbItem,
        T.OWNER_ORG_ID                  ownerOrgId,
        T.SETTLE_MODE                   settleMode,
        E.TRANS_VOUCHER_ACK_STATUS      transVoucherAckStatus,
        E.TRANS_VOUCHER_AUDIT_STATUS    transVoucherAuditStatus,
        E.ELECTRONIC_RECEIPT_STATUS     electronicReceiptStatus,
        E.PAPER_RECEIPT_STATUS          paperReceiptStatus,
        E.PAPER_RECEIPT_NEED_POST_TYPE  paperReceiptNeedPostType,
        E.CUSTOMER_ORDER_NO             customerOrderNo,
        TO_CHAR(E.PAPER_ESTIMATED_DELIVERY_TIME, 'YYYY-MM-DD HH24:MI:SS') paperEstimatedDeliveryTime,
        TO_CHAR(E.ELECTRONIC_RECEIPT_AUDIT_TIME, 'YYYY-MM-DD HH24:MI:SS') electronicReceiptAuditTime,
        E.LINE_TIME_REQUIRE lineTimeRequire,
        TO_CHAR(E.ARRIVE_END_TIME, 'YYYY-MM-DD HH24:MI:SS') arriveEndTime,
        TO_CHAR(E.DISPATCH_CAR_TIME, 'YYYY-MM-DD HH24:MI:SS') dispatchCarTime,
        E.PAY_TYPE                      payType,
        E.PAY_NAME                      payName,
        E.PAY_BANK_NO                   payBankNo,
        E.PAY_BANK_NAME                 payBankName,
        E.CITY_NAME                     cityName,
        E.PROVINCE                      province,
        E.PAY_MOBILE_NO                 payMobileNo,
        E.PAY_ID_CARD                   payIdCard,
        E.CART_TYPE                     cartType,
        E.CART_LENGTH                   cartLength,
        E.LOADING_ADDRESS_NAME          loadingAddressName,
        E.UNLOADING_ADDRESS_NAME        unloadingAddressName,
        E.SERVICE_REQUIRE               serviceRequire,
        E.TRAN_REQUIRE                  tranRequire,
        E.CART_TONNAGE                  cartTonnage,
        E.TRANS_VOUCHER                 transVoucher,
        E.OIL_CARD_NO                   oilCardNo,
        E.SHIPMENT_PHOTO                shipmentPhoto,
        E.IS_FREIGHT_SHOW               isFreightShow,
        E.CUSTOMIZE_NO                  customizeNo,
        E.RECEIPT_BZ_STATE              receiptBzState,
        E.AUDIT_STATUS                  auditStatus,
        E.DELETE_STATUS                 deleteStatus,
        TO_CHAR(E.RECEIPT_RECEIVE_TIME, 'YYYY-MM-DD HH24:MI:SS') receiptReceiveTime,
        E.TRANS_VOUCHER_EXPRESS_NUMBER  transVoucherExpressNumber,
        TO_CHAR(E.ARRIVE_TIME, 'YYYY-MM-DD HH24:MI:SS') arriveTime,
        E.IS_UNIT_PRICE_SHOW            isUnitPriceShow,
        E.SETTLE_ISSUE settleIssue,
        TO_CHAR(NVL(E.LOSS_AMOUNT, 0), '**********0.00') lossAmount,
        TO_CHAR(E.HYB_RECEIVED_TIME, 'YYYY-MM-DD HH24:MI:SS') hybReceivedTime,
        TO_CHAR(NVL(E.FREIGHT_GUARANTEE, 0), '**********0.00') freightGuarantee,
        E.DISPATCH_CAR_RECORD_ID        dispatchCarRecordId,
        CASE
        WHEN F.GUARANTEE_AMOUNT - TRUNC(F.GUARANTEE_AMOUNT) = 0 THEN
        TO_CHAR(TRUNC(F.GUARANTEE_AMOUNT), '**********0')
        ELSE
        TO_CHAR(F.GUARANTEE_AMOUNT, '**********0.9999')
        END driverGuaranteeAmount,
        CASE
        WHEN F.GUARANTEE_PAY_AMOUNT - TRUNC(F.GUARANTEE_PAY_AMOUNT) = 0 THEN
        TO_CHAR(TRUNC(F.GUARANTEE_PAY_AMOUNT), '**********0')
        ELSE
        TO_CHAR(F.GUARANTEE_PAY_AMOUNT, '**********0.9999')
        END driverGuaranteePayAmount,
        F.GUARANTEE_STATE driverGuaranteeState,
        F.GUARANTEE_CHANNEL driverGuaranteeChannel,
        E.APPOINT_STATUS appointStatus,
        E.SIGNIN_STATUS signinStatus,
        E.THIRD_TASK_NO thirdTaskNo,
        E.TRANS_TASK_FLAG transTaskFlag,
        E.BUSINESS_TYPE businessType,
        E.LOAD_TYPE loadType,
        E.VOUCHER_CONFIG_TYPE voucherConfigType,
        F.GUARANTEE_DISPATCH_CAR_RULE driverGuaranteeDispatchCarRule
    </sql>

    <sql id="queryTransTaskListForHybUpDownOfAllocateColumn">
        A.ORG_ID                        allocateOrgId,
        A.TAX_WAYBILL_ID                allocateTaxWaybillId,
        A.SETTLE_MODE                   allocateSettleMode,
        A.NODE_ID                       allocateNodeId,
        TO_CHAR(A.NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') allocateNodeTime,
        A.PAY_STATE                     allocatePayState,
        TO_CHAR(NVL(A.ALL_FREIGHT, 0), '**********0.00') allocateAllFreight,
        TO_CHAR(NVL(A.USER_FREIGHT, 0), '**********0.00') allocateUserFreight,
        TO_CHAR(NVL(A.FREIGHT_INCR, 0), '**********0.00') allocateFreightIncr,
        TO_CHAR(NVL(A.LOSS_FEE, 0), '**********0.00') allocateLossFee,
        TO_CHAR(NVL(A.UNIT_PRICE, 0), '**********0.00') allocateUnitPrice,
        TO_CHAR(NVL(A.PREPAYMENTS_OILCARD, 0), '**********0.00') allocatePrepaymentsOilcard,
        TO_CHAR(NVL(A.PREPAYMENTS_GASCARD, 0), '**********0.00') allocatePrepaymentsGascard,
        TO_CHAR(NVL(A.PREPAYMENTS, 0), '**********0.00') allocatePrepayments,
        TO_CHAR(NVL(A.BACK_FEE, 0), '**********0.00') allocateBackFee,
        TO_CHAR(A.PAY_OVER_TIME, 'YYYY-MM-DD HH24:MI:SS') allocatePayOverTime,
        A.PAY_TYPE                      allocatePayType,
        A.PAY_NAME                      allocatePayName,
        A.PAY_BANK_NO                   allocatePayBankNo,
        A.PAY_BANK_NAME                 allocatePayBankName,
        A.CITY_NAME                     allocateCityName,
        A.PROVINCE                      allocateProvince,
        A.PAY_MOBILE_NO                 allocatePayMobileNo,
        A.PAY_ID_CARD                   allocatePayIdCard
    </sql>

    <sql id="queryTransTaskListForHybBaseCondition">
     WHERE T.IS_PARTAKE_OPERATE = 1
        <choose>
            <when test="isDel != null and isDel == '1'.toString()">
                AND T.IS_DEL = 1
                AND E.DELETE_STATUS <![CDATA[<]]> 7
            </when>
            <otherwise>
                AND T.IS_DEL = 0
            </otherwise>
        </choose>
       <if test="(mobileNo != null and mobileNo != '') or (spCartBadgeNo != null and spCartBadgeNo != '')" >
           AND (
               <if test="mobileNo != null and mobileNo != ''">
                T.MOBILE_NO = #{mobileNo}
               </if>
               <if test="spCartBadgeNo != null and spCartBadgeNo != ''">
                   <if test="mobileNo != null and mobileNo != ''">
                       OR
                   </if>
                   (T.CART_BADGE_NO = #{spCartBadgeNo} and T.MOBILE_NO is null)
               </if>
           )
       </if>
       <if test="cartBadgeNo != null and cartBadgeNo != ''">
            AND T.CART_BADGE_NO = #{cartBadgeNo}
       </if>
       <if test="taxWaybillNo != null and taxWaybillNo != ''">
            AND T.TAX_WAYBILL_NO = #{taxWaybillNo}
       </if>
       <if test="hybStateList != null and hybStateList.size() > 0">
        AND T.HYB_STATE IN
        <foreach collection="hybStateList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
       </if>
       <if test="beginCreatedTime != null and beginCreatedTime != ''">
        AND T.CREATED_TIME > TO_DATE(#{beginCreatedTime}, 'YYYY-MM-DD HH24:MI:SS')
       </if>
       <if test="transportTypeList != null and transportTypeList.size() > 0">
        AND T.TRANSPORT_TYPE IN
        <foreach collection="transportTypeList" item="item" separator="," open="(" close=")">
           #{item}
        </foreach>
       </if>
    </sql>

    <select id="queryTransTaskListForHyb" resultType="Resp5330232Bean">
        SELECT <include refid="queryTransTaskListForHybOfBaseColumn"/>,
               <include refid="queryTransTaskListForHybUpDownOfAllocateColumn"/>
          FROM T_BO_TRANS_TASK T
          LEFT JOIN T_BO_TRANS_TASK_ALLOCATE A
            ON T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
           AND A.IS_DEL = 0
         INNER JOIN T_BO_TRANS_TASK_EXTRA E
            ON T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE F ON F.BO_TASK_DRIVER_GUARANTEE_ID =E.BO_TASK_DRIVER_GUARANTEE_ID AND F.IS_DEL =0
        <include refid="queryTransTaskListForHybBaseCondition"/>
           AND CASE WHEN T.TRANS_MODE IN (1, 2) THEN T.SETTLE_MODE ELSE A.SETTLE_MODE END IN
            <foreach collection="querySettleModeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            <choose>
                <when test="isCreatedWaybill == '1'.toString()">
                    AND CASE WHEN T.TRANS_MODE IN (1, 2) THEN T.TAX_WAYBILL_ID ELSE A.TAX_WAYBILL_ID END IS NULL
                </when>
                <when test="isCreatedWaybill == '2'.toString()">
                    AND CASE WHEN T.TRANS_MODE IN (1, 2) THEN T.TAX_WAYBILL_ID ELSE A.TAX_WAYBILL_ID END IS NOT NULL
                </when>
            </choose>
           <if test="orgIdList != null and orgIdList.size() > 0">
            AND CASE WHEN T.TRANS_MODE IN (1, 2) THEN T.ORG_ID ELSE A.ORG_ID END IN
            <foreach collection="orgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
           </if>
            <if test="nodeIdQueryList != null and nodeIdQueryList.size() > 0">
               AND CASE WHEN T.TRANS_MODE IN (1, 2) THEN T.NODE_ID ELSE A.NODE_ID END IN
                <foreach collection="nodeIdQueryList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="startProvinceName !=null and startProvinceName !='' ">
                AND T.START_PROVINCE_NAME = #{startProvinceName}
            </if>
            <if test="startCityName !=null and startCityName !='' ">
                AND T.START_CITY_NAME = #{startCityName}
            </if>
            <if test="startCountyName !=null and startCountyName !='' ">
                AND T.START_COUNTY_NAME = #{startCountyName}
            </if>
            <if test="endProvinceName !=null and endProvinceName !='' ">
                AND T.END_PROVINCE_NAME = #{endProvinceName}
            </if>
            <if test="endCityName !=null and endCityName !='' ">
                AND T.END_CITY_NAME = #{endCityName}
            </if>
            <if test="endCountyName !=null and endCountyName !='' ">
                AND T.END_COUNTY_NAME = #{endCountyName}
            </if>
           <choose>
               <when test="hybStateList != null and hybStateList.size() > 0">
                <choose>
                 <when test="hybStateList.contains('2'.toString()) or hybStateList.contains('3'.toString())">
                  ORDER BY firstReceiptTime DESC
                 </when>
                 <when test="hybStateList.contains('1'.toString())">
                  ORDER BY hybReceivedTime DESC
                 </when>
                 <otherwise>
                  ORDER BY createdTime DESC
                 </otherwise>
                </choose>
               </when>
           </choose>
    </select>

    <select id="queryTransTaskListByBusinessLineIds" resultType="boTaskDetailBean">
        SELECT
            T1.BO_TRANS_TASK_ID boTransTaskId,
            T1.TAX_WAYBILL_ID taxWaybillId,
            T1.BO_BUSINESS_LINE_ID boBusinessLineId,
            T1.CART_BADGE_NO cartBadgeNo,
            T1.GOODS_NAME goodsName,
            T1.LOADING_TONNAGE loadingTonnage,
            T1.UNLOADING_TONNAGE unloadingTonnage,
            T1.LOADING_PLACE_NAME loadingPlaceName,
            T1.UNLOADING_PLACE_NAME unloadingPlaceName,
            TO_CHAR(T1.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
            LISTAGG(T2.NODE_ID, ',') WITHIN GROUP(ORDER BY T2.CREATED_TIME) nodeIds
        FROM T_BO_TRANS_TASK T1
        LEFT JOIN T_BO_TRANS_NODE_RECORD T2
            ON T1.BO_TRANS_TASK_ID = T2.BO_TRANS_TASK_ID
            AND T2.IS_DEL = 0
        WHERE T1.IS_DEL = 0
        AND T1.BO_BUSINESS_LINE_ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
            T1.BO_TRANS_TASK_ID,
            T1.TAX_WAYBILL_ID,
            T1.BO_BUSINESS_LINE_ID,
            T1.CART_BADGE_NO,
            T1.GOODS_NAME,
            T1.LOADING_TONNAGE,
            T1.UNLOADING_TONNAGE,
            T1.LOADING_PLACE_NAME,
            T1.UNLOADING_PLACE_NAME,
            T1.CREATED_TIME
        ORDER BY T1.CREATED_TIME DESC
    </select>
    <select id="queryTaskListForDriverPlatform" resultType="com.wtyt.tt.bean.DpTaskResultBean">
        SELECT
            NVL(bt.TAX_WAYBILL_ID, tta.TAX_WAYBILL_ID) taxWaybillId,
            bt.TAX_WAYBILL_NO taxWaybillNo,
            bt.ORG_ID orgId,
            NVL(bt.PAY_STATE, 0) payState,
            bt.TRANSPORT_TYPE transportType,
            bt.NODE_ID nodeId,
            bt.HYB_STATE hybState,
            TO_CHAR(bt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') createdTime,
            bt.DRIVER_NAME driverName,
            bt.MOBILE_NO mobileNo,
            bt.CART_BADGE_NO cartBadgeNo,
            bt.CART_BADGE_COLOR cartBadgeColor,
            bt.START_PROVINCE_NAME startProvinceName,
            bt.START_CITY_NAME startCityName,
            bt.START_COUNTY_NAME startCountyName,
            bte.LOADING_ADDRESS_NAME loadingAddressName,
            bt.END_PROVINCE_NAME endProvinceName,
            bt.END_CITY_NAME endCityName,
            bt.END_COUNTY_NAME endCountyName,
            bte.UNLOADING_ADDRESS_NAME unloadingAddressName,
            TO_CHAR(NVL(bt.ALL_FREIGHT, 0), '**********0.00') allFreight,
            TO_CHAR(NVL(bt.SERVICE_FEE, 0), '**********0.00') serviceFee,
            TO_CHAR(NVL(bt.DATA_SERVICE_FEE, 0), '**********0.00') dataServiceFee,
            TO_CHAR(NVL(bt.USER_FREIGHT, 0), '**********0.00') userFreight,
            TO_CHAR(NVL(bt.PREPAYMENTS, 0) , '**********0.00') prepayments,
            TO_CHAR(NVL(bt.BACK_FEE, 0) , '**********0.00') backFee,
            CASE
                WHEN BT.INS_STATE = 1
                THEN TO_CHAR(NVL(bt.ALL_FREIGHT, 0) + NVL(bt.SERVICE_FEE, 0) + NVL(bt.DATA_SERVICE_FEE, 0) + NVL(bt.INS_FEE, 0) + NVL(bt.WITHHOLD_TAX_FEE, 0), '**********0.00')
                ELSE TO_CHAR(NVL(bt.ALL_FREIGHT, 0) + NVL(bt.SERVICE_FEE, 0) + NVL(bt.DATA_SERVICE_FEE, 0) + NVL(bt.WITHHOLD_TAX_FEE, 0), '**********0.00')
            END payableFreight,
            TO_CHAR(NVL(bt.PREPAYMENTS_OILCARD , 0), '**********0.00') prepaymentsOilcard,
            TO_CHAR(NVL(bt.PREPAYMENTS_GASCARD , 0), '**********0.00') prepaymentsGasCard,
            NVL(bte.RECEIPT_BZ_STATE, 0) receiptBzState,
            bte.TRANS_VOUCHER transVoucher,
            NVL(bt.ADVANCE_PAY_STATE, 0) advancePayState,
            NVL(bt.TRANS_PATTERN, 1) transPattern,
            bte.TRANS_VOUCHER_ACK_STATUS transVoucherAckStatus,
            bte.TRANS_VOUCHER_AUDIT_STATUS transVoucherAuditStatus,
            bt.BO_TRANS_TASK_ID boTransTaskId,
            bte.ELECTRONIC_RECEIPT_STATUS electronicReceiptStatus,
            bte.PAPER_RECEIPT_STATUS paperReceiptStatus,
            TO_CHAR(bte.ARRIVE_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveTime,
            bt.SETTLE_MODE settleMode,
            NR.USER_ID dispatchCarUserId,
            to_char(bte.PAPER_ESTIMATED_DELIVERY_TIME, 'MM-dd') paperEstimatedDeliveryTime,
            TO_CHAR(NR.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') dispatchCarTime,
            TO_CHAR(bte.ARRIVE_END_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveEndTime,
            bte.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
            bt.TRANS_MODE transMode,
            bt.WB_ITEM wbItem,
            bt.state state,
            bte.SETTLE_ISSUE settleIssue,
            bt.LOADING_PLACE_NAME loadingPlaceName,
            bt.UNLOADING_PLACE_NAME unloadingPlaceName,
            bt.GOODS_NAME goodsName,
            bt.GOODS_AMOUNT_TYPE goodsAmountType,
            TO_CHAR(bt.UNIT_PRICE, '**********0.00') unitPrice,
            TO_CHAR(bt.END_TIME, 'yyyy-MM-dd Hh24:mi:ss') endTime,
            TO_CHAR(bt.START_TIME, 'yyyy-MM-dd Hh24:mi:ss') startTime,
            TO_CHAR(bte.HYB_RECEIVED_TIME, 'yyyy-MM-dd Hh24:mi:ss') hybReceivedTime
        FROM
            T_BO_TRANS_TASK bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
            bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND bte.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE tta ON
            tta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND tta.IS_DEL = 0
        JOIN T_BO_TRANS_NODE_RECORD NR ON
            NR.BO_TRANS_NODE_RECORD_ID = bte.DISPATCH_CAR_RECORD_ID
            AND NR.IS_DEL = 0
        WHERE
            <include refid="queryConditionForDriverPlatform" />
            <include refid="tabStateQueryCondition" />
            <choose>
                <when test="tabState == 3">
                    ORDER BY bte.ARRIVE_TIME ASC,bt.CREATED_TIME DESC
                </when>
                <otherwise>
                    ORDER BY bt.CREATED_TIME DESC
                </otherwise>
            </choose>
    </select>

    <select id="queryTaskListCountForDriverPlatform" resultType="com.wtyt.tt.bean.TaskTabCountBean">
        SELECT
            bt.STATE state,
            bt.PAY_STATE payState,
            bt.NODE_ID nodeId,
            COUNT(*) count
        FROM
            T_BO_TRANS_TASK bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
            bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND bte.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE tta ON
            tta.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND tta.IS_DEL = 0
        JOIN T_BO_TRANS_NODE_RECORD NR ON
            NR.BO_TRANS_NODE_RECORD_ID = bte.DISPATCH_CAR_RECORD_ID
            AND NR.IS_DEL = 0
        WHERE
            <include refid="queryConditionForDriverPlatform"/>
        GROUP BY
            bt.STATE,bt.PAY_STATE,bt.NODE_ID
    </select>

    <sql id="queryConditionForDriverPlatform">
        bt.IS_DEL = 0
        <!-- 只查已派车的 -->
        AND bte.DISPATCH_CAR_RECORD_ID IS NOT NULL
        <if test="taxWaybillIds!=null and taxWaybillIds.size()>0">
            AND BT.BO_TRANS_TASK_ID IN
            (
            SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK WHERE IS_DEL = 0 AND TAX_WAYBILL_ID IN
            <foreach collection="taxWaybillIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            UNION
            SELECT BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_ALLOCATE WHERE IS_DEL = 0 AND TAX_WAYBILL_ID IN
            <foreach collection="taxWaybillIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="orgIds!=null and orgIds.size()>0">
            AND (
                bt.ORG_ID IN
                <foreach collection="orgIds" index="index" open="(" close=")" item="id" separator=",">
                    <if test="(index % 999) == 998"> NULL) OR bt.ORG_ID IN(</if>#{id}
                </foreach>
            )
        </if>
        <if test="createdTimeStart !=null and createdTimeStart!=''">
            AND bt.CREATED_TIME &gt;= to_date(#{createdTimeStart},'yyyy-MM-dd')
        </if>
        <if test="createdTimeEnd !=null and createdTimeEnd !=''">
            AND bt.CREATED_TIME &lt;= to_date(#{createdTimeEnd} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="dispatchCarTimeStart !=null and dispatchCarTimeStart!=''">
            AND NR.CREATED_TIME &gt;= to_date(#{dispatchCarTimeStart},'yyyy-MM-dd')
        </if>
        <if test="dispatchCarTimeEnd !=null and dispatchCarTimeEnd !=''">
            AND NR.CREATED_TIME &lt;= to_date(#{dispatchCarTimeEnd} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')
        </if>
        <if test="cartBadgeNo !=null and cartBadgeNo != ''">
            AND bt.CART_BADGE_NO = #{cartBadgeNo}
        </if>
        <if test="mobileNo !=null and mobileNo != ''">
            AND bt.MOBILE_NO = #{mobileNo}
        </if>
        <if test="driverName !=null and driverName != ''">
            AND bt.DRIVER_NAME LIKE '%' || #{driverName} || '%'
        </if>
        <if test="followStatus!=null and followStatus!=''">
            <choose>
                <when test="followStatus==1">
                    AND EXISTS (SELECT 1 FROM T_DRIVER_FOLLOW_RECORD dfr WHERE
                    dfr.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                    AND dfr.IS_DEL = 0
                    AND dfr.BUSINESS_TYPE = 1)
                </when>
                <otherwise>
                    AND NOT EXISTS (SELECT 1 FROM T_DRIVER_FOLLOW_RECORD dfr WHERE
                    dfr.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                    AND dfr.IS_DEL = 0
                    AND dfr.BUSINESS_TYPE = 1)
                </otherwise>
            </choose>
        </if>
        <if test="alarmTypes!=null and alarmTypes.size()>0">
            AND EXISTS (
                SELECT
                    1
                FROM
                    T_BO_TRANS_NODE_ALARM tna
                WHERE
                    tna.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
                    AND tna.IS_DEL = 0
                    AND tna.ALARM_TYPE = 1
                    <if test="followStatus!=1">
                        AND tna.ALARM_PROCESS_RESULT = 0
                    </if>
                    AND tna.NODE_DATA_TYPE IN
                    <foreach collection="alarmTypes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            )
        </if>
    </sql>

    <sql id="tabStateQueryCondition" >
        <if test="tabState!=0">
            <choose>
                <when test="tabState==4">
                    AND bt.STATE = 1
                </when>
                <when test="tabState==5">
                    AND bt.STATE = 2
                    AND NVL(bt.PAY_STATE,0) = 0
                </when>
                <when test="tabState==10">
                    AND bt.STATE = 2
                    AND bt.PAY_STATE NOT IN (0,2)
                </when>
                <when test="tabState==6">
                    AND bt.STATE = 2
                    AND bt.PAY_STATE = 2
                </when>
                <otherwise>
                    AND bt.STATE = 0
                    <if test="nodeIdQueryList !=null and nodeIdQueryList.size()>0">
                        AND bt.NODE_ID IN
                        <foreach collection="nodeIdQueryList" item="item" open="(" close=")" separator=",">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="getTransTaskListCollect" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            COUNT(*) taskCount,
            SUM(NVL(BT.ALL_FREIGHT, 0)) allFreight,
            SUM(NVL(BT.SERVICE_FEE, 0)) serviceFee,
            SUM(NVL(BT.DATA_SERVICE_FEE, 0)) dataServiceFee,
            SUM(NVL(BT.WITHHOLD_TAX_FEE, 0)) withholdTaxFee,
            SUM(
                CASE
                    WHEN BT.INS_STATE = 1 THEN NVL(BT.INS_FEE, 0)
                    ELSE 0
                END
            ) insFee,
            BT.GOODS_AMOUNT_TYPE goodsAmountType,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN 0
                    ELSE NVL(BT.GOODS_AMOUNT, 0)
                END
            ) goodsAmount,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN NVL(BT.LOADING_TONNAGE, 0)
                    ELSE 0
                END
            ) loadingTonnage,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN NVL(BT.UNLOADING_TONNAGE, 0)
                    ELSE 0
                END
            ) unloadingTonnage,
            COUNT(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN 1
                    ELSE NULL
                END
        ) dzTaskCount
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
        GROUP BY BT.GOODS_AMOUNT_TYPE
    </select>

    <select id="getTransTaskListCollectByIds" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            COUNT(*) taskCount,
            SUM(NVL(BT.ALL_FREIGHT, 0)) allFreight,
            SUM(NVL(BT.SERVICE_FEE, 0)) serviceFee,
            SUM(NVL(BT.DATA_SERVICE_FEE, 0)) dataServiceFee,
            SUM(NVL(BT.WITHHOLD_TAX_FEE, 0)) withholdTaxFee,
            SUM(
                CASE
                    WHEN BT.INS_STATE = 1 THEN NVL(BT.INS_FEE, 0)
                    ELSE 0
                END
            ) insFee,
            BT.GOODS_AMOUNT_TYPE goodsAmountType,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN 0
                    ELSE NVL(BT.GOODS_AMOUNT, 0)
                END
            ) goodsAmount,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN NVL(BT.LOADING_TONNAGE, 0)
                    ELSE 0
                END
            ) loadingTonnage,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN NVL(BT.UNLOADING_TONNAGE, 0)
                    ELSE 0
                END
            ) unloadingTonnage,
            COUNT(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN 1
                    ELSE NULL
                END
            ) dzTaskCount
        FROM T_BO_TRANS_TASK BT
        WHERE BT.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
            #{boTransTaskId}
        </foreach>
        <choose>
            <when test="isDel != null and isDel == '1'.toString()">
                AND BT.IS_DEL = 1
            </when>
            <otherwise>
                AND BT.IS_DEL = 0
            </otherwise>
        </choose>
        GROUP BY BT.GOODS_AMOUNT_TYPE
    </select>

    <select id="getTransTaskDispatchListCollect" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            COUNT(*) taskCount,
            SUM(NVL(TTA.ALL_FREIGHT, 0)) allFreight,
            SUM(
                CASE
                    WHEN TTA.INS_STATE = 1 THEN NVL(TTA.INS_FEE, 0)
                    ELSE 0
                END
            ) insFee,
            BT.GOODS_AMOUNT_TYPE goodsAmountType,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN 0
                    ELSE NVL(BT.GOODS_AMOUNT, 0)
                END
            ) goodsAmount,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN NVL(BT.LOADING_TONNAGE, 0)
                    ELSE 0
                END
            ) loadingTonnage,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN NVL(BT.UNLOADING_TONNAGE, 0)
                    ELSE 0
                END
            ) unloadingTonnage,
            COUNT(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN 1
                    ELSE NULL
                END
            ) dzTaskCount
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
        GROUP BY BT.GOODS_AMOUNT_TYPE
    </select>

    <select id="getTransTaskDispatchListCollectByIds" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            COUNT(*) taskCount,
            SUM(NVL(TTA.ALL_FREIGHT, 0)) allFreight,
            SUM(
                CASE
                    WHEN TTA.INS_STATE = 1 THEN NVL(TTA.INS_FEE, 0)
                    ELSE 0
                END
            ) insFee,
            BT.GOODS_AMOUNT_TYPE goodsAmountType,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN 0
                    ELSE NVL(BT.GOODS_AMOUNT, 0)
                END
            ) goodsAmount,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN NVL(BT.LOADING_TONNAGE, 0)
                    ELSE 0
                END
            ) loadingTonnage,
            SUM(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN NVL(BT.UNLOADING_TONNAGE, 0)
                    ELSE 0
                END
            ) unloadingTonnage,
            COUNT(
                CASE
                    WHEN BT.TRANSPORT_TYPE = 1 THEN 1
                    ELSE NULL
                END
            ) dzTaskCount
        FROM T_BO_TRANS_TASK_ALLOCATE TTA
        JOIN T_BO_TRANS_TASK BT
            ON BT.BO_TRANS_TASK_ID = TTA.BO_TRANS_TASK_ID
        WHERE TTA.IS_DEL = 0
        AND BT.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
            #{boTransTaskId}
        </foreach>
        <choose>
            <when test="isDel != null and isDel == '1'.toString()">
                AND BT.IS_DEL = 1
            </when>
            <otherwise>
                AND BT.IS_DEL = 0
            </otherwise>
        </choose>
        GROUP BY BT.GOODS_AMOUNT_TYPE
    </select>

    <select id="getTransTaskListTabCollect" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            BT.STATE state,
            BT.PAY_STATE payState,
            BT.NODE_ID nodeId,
            NVL(COUNT(*), 0) taskCount
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        GROUP BY BT.STATE, BT.PAY_STATE, BT.NODE_ID
    </select>

    <select id="getTransTaskListTabCollect_new" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            BT.STATE state,
            BT.PAY_STATE payState,
            BT.NODE_ID nodeId,
            BTE.TRANSPORT_NODE transportNode,
            NVL(COUNT(*), 0) taskCount
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        GROUP BY BT.STATE, BT.PAY_STATE, BT.NODE_ID, BTE.TRANSPORT_NODE
    </select>

    <select id="getTransTaskDispatchListTabCollect" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            BT.STATE state,
            TTA.PAY_STATE payState,
            TTA.NODE_ID nodeId,
            NVL(COUNT(*), 0) taskCount
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        GROUP BY BT.STATE, TTA.PAY_STATE, TTA.NODE_ID, BTE.TRANSPORT_NODE
    </select>

    <select id="getTransTaskDispatchListTabCollect_new" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            BT.STATE state,
            TTA.PAY_STATE payState,
            TTA.NODE_ID nodeId,
            BTE.TRANSPORT_NODE transportNode,
            NVL(COUNT(*), 0) taskCount
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        GROUP BY BT.STATE, TTA.PAY_STATE, TTA.NODE_ID, BTE.TRANSPORT_NODE
    </select>

    <sql id="transTaskListTable">
        T_BO_TRANS_TASK BT
        LEFT JOIN T_BO_TRANS_TASK_EXTRA BTE
            ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND BTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TTA
            ON TTA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND TTA.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR
            ON NR.BO_TRANS_NODE_RECORD_ID = BTE.DISPATCH_CAR_RECORD_ID
            AND NR.IS_DEL = 0
        LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE TDG
            ON TDG.BO_TASK_DRIVER_GUARANTEE_ID = BTE.BO_TASK_DRIVER_GUARANTEE_ID
            AND TDG.IS_DEL = 0
        <if test="cusSpecialConditionList != null and cusSpecialConditionList.size() > 0">
            <foreach collection="cusSpecialConditionList" item="condition" index="index">
                LEFT JOIN T_BO_TASK_CUS_FIELD CUS_${index}
                    ON CUS_${index}.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                    AND CUS_${index}.IS_DEL = 0
                    AND CUS_${index}.FIELD_KEY = #{condition.searchKey}
            </foreach>
        </if>
    </sql>

    <sql id="transTaskListTableNew">
        JOIN T_BO_TRANS_TASK BT
        LEFT JOIN T_BO_TRANS_TASK_EXTRA BTE
        ON BTE.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
        AND BTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TTA
        ON TTA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
        AND TTA.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD NR
        ON NR.BO_TRANS_NODE_RECORD_ID = BTE.DISPATCH_CAR_RECORD_ID
        AND NR.IS_DEL = 0
        LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE TDG
        ON TDG.BO_TASK_DRIVER_GUARANTEE_ID = BTE.BO_TASK_DRIVER_GUARANTEE_ID
        AND TDG.IS_DEL = 0
    </sql>

    <sql id="dataPermissionCondition">
        AND EXISTS (
            SELECT 1
            FROM T_BO_TRANS_TASK_GROUP_REL TGR
            INNER JOIN T_BO_TRANS_TASK_USERS TU
                ON TU.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID
                AND TU.IS_DEL = 0
            <if test="hasTransTaskCusCondition">
                INNER JOIN T_BO_TASK_CUS_FIELD CUS
                    ON CUS.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID
                    AND CUS.IS_DEL = 0
            </if>
            <if test="hasShippingListCondition">
                LEFT JOIN T_BO_SHIPPING_LIST BSL
                    ON BSL.BO_TRANS_TASK_ID = TGR.BO_TRANS_TASK_ID
                    AND BSL.IS_DEL = 0
            </if>
            WHERE TGR.IS_DEL = 0
            <if test="startCreatedTime != null and startCreatedTime.length > 0">
                AND TGR.CREATED_TIME >= TO_DATE(#{startCreatedTime}, 'yyyy-mm-dd hh24:mi:ss')
            </if>
            AND TGR.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            <if test="supplierTypeList != null and supplierTypeList.size() > 0">
                AND TGR.SUPPLIER_TYPE IN
                <foreach collection="supplierTypeList" item="supplierType" open="(" separator="," close=")">
                    #{supplierType}
                </foreach>
            </if>
            <if test="transTaskPermissionList != null and transTaskPermissionList.size() > 0">
                AND
                <foreach collection="transTaskPermissionList" item="transTaskPermission" open="(" separator="OR" close=")">
                    <foreach collection="transTaskPermission.scopeList" item="scope" open="(" separator="OR" close=")" index="index">
                        <if test="index == 0 and transTaskPermission.condition != null and transTaskPermission.condition.length > 0">
                            (
                        </if>
                        <choose>
                            <when test="scope.userId != null and scope.userId.length > 0">
                                (TGR.GROUP_ID = #{scope.groupId} AND TU.USER_ID =  #{scope.userId})
                            </when>
                            <otherwise>TGR.GROUP_ID = #{scope.groupId}</otherwise>
                        </choose>
                        <if test="index + 1 == transTaskPermission.scopeList.size() and transTaskPermission.condition != null and transTaskPermission.condition.length > 0">
                            ) AND ${transTaskPermission.condition}
                        </if>
                    </foreach>
                </foreach>
            </if>
            <if test="shippingListPermissionList != null and shippingListPermissionList.size() > 0">
                AND (BSL.BO_SHIPPING_LIST_ID IS NULL OR
                <foreach collection="shippingListPermissionList" item="shippingListPermission" open="(" separator="OR" close=")">
                    ${shippingListPermission.condition}
                </foreach>
                )
            </if>
            <if test="hasShippingListCondition and tabStatus == 9">
                AND BSL.PRINT_STATUS = 0
                AND BSL.RECEIPT_NO IS NOT NULL
                AND BSL.BO_SHIPPING_LIST_ID IS NOT NULL
            </if>
        )
    </sql>

    <sql id="transTaskListCondition">
        <choose>
            <when test="isDel != null and isDel == '1'.toString()">
                BT.IS_DEL = 1
            </when>
            <otherwise>
                BT.IS_DEL = 0
            </otherwise>
        </choose>

        <include refid="dataPermissionCondition"/>

        <if test="bslConditionList != null and bslConditionList.size() > 0">
            AND EXISTS (
                SELECT 1
                FROM T_BO_SHIPPING_LIST BSL
                WHERE BSL.IS_DEL = 0
                AND BSL.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                <foreach collection="bslConditionList" item="condition" open="AND (" separator=" OR " close=")">
                    (${condition})
                </foreach>
            )
        </if>

        <if test="cusConditionList != null and cusConditionList.size() > 0">
            AND #{cusConditionSize} = (
                SELECT COUNT(*)
                FROM T_BO_TASK_CUS_FIELD CUS
                WHERE CUS.IS_DEL = 0
                AND CUS.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                <foreach collection="cusConditionList" item="condition" open="AND (" separator=" OR " close=")">
                    (${condition})
                </foreach>
            )
        </if>

        <if test="cusSpecialConditionList != null and cusSpecialConditionList.size() > 0">
            <foreach collection="cusSpecialConditionList" item="condition">
                AND ${condition.searchSql}
            </foreach>
        </if>

        <if test="taskAlarmList != null and taskAlarmList.size() > 0">
            AND EXISTS (
                SELECT 1
                FROM T_BO_TRANS_NODE_ALARM BTNA
                WHERE BTNA.IS_DEL = 0
                AND BTNA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                <foreach collection="taskAlarmList" item="taskAlarm" open="AND (" separator=" OR " close=")">
                    (BTNA.NODE_DATA_TYPE = #{taskAlarm.nodeDataType} AND BTNA.ALARM_TYPE = #{taskAlarm.alarmType}
                    <if test="taskAlarm.alarmProcessResultList != null and taskAlarm.alarmProcessResultList.size() > 0">
                        AND BTNA.ALARM_PROCESS_RESULT IN
                        <foreach collection="taskAlarm.alarmProcessResultList" item="alarmProcessResult" open="(" separator="," close=")">
                            #{alarmProcessResult}
                        </foreach>
                    </if>
                    )
                </foreach>
            )
        </if>

        <if test="conditionList != null and conditionList.size() > 0">
            <foreach collection="conditionList" item="condition" open="AND" separator=" AND " close="">
                ${condition}
            </foreach>
        </if>

        <if test="(dispatchedIdList !=null and dispatchedIdList.size()>0) or (groupIdList !=null and groupIdList.size()>0)">
            AND (1 = 0
            <if test="dispatchedIdList !=null and dispatchedIdList.size()>0">
                OR NR.USER_ID IN
                <foreach collection="dispatchedIdList" item="dispatchedId" open="(" close=")" separator=",">
                    #{dispatchedId}
                </foreach>
            </if>

            <if test="groupIdList != null and groupIdList.size() > 0">
                OR EXISTS (
                    SELECT 1
                    FROM T_BO_TRANS_TASK_GROUP_REL TGR
                    WHERE TGR.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                    AND TGR.IS_DEL = 0
                    AND TGR.GROUP_ID IN
                <foreach collection="groupIdList" item="groupId" open="(" close=")" separator=",">
                    #{groupId}
                </foreach>
                )
            </if>
            )
        </if>

        <if test="loadingAddress != null">
            <if test="loadingAddress.placeName != null and loadingAddress.placeName.length > 0">
                AND INSTR(BT.LOADING_PLACE_NAME, #{loadingAddress.placeName}) > 0
            </if>
            <if test="loadingAddress.provinceName != null and loadingAddress.provinceName.length > 0">
                AND BT.START_PROVINCE_NAME = #{loadingAddress.provinceName}
            </if>
            <if test="loadingAddress.cityName != null and loadingAddress.cityName.length > 0">
                AND BT.START_CITY_NAME = #{loadingAddress.cityName}
            </if>
            <if test="loadingAddress.countyName != null and loadingAddress.countyName.length > 0">
                AND BT.START_COUNTY_NAME = #{loadingAddress.countyName}
            </if>
            <if test="loadingAddress.address != null and loadingAddress.address.length > 0">
                AND INSTR(BTE.LOADING_ADDRESS_NAME, #{loadingAddress.address}) > 0
            </if>
        </if>

        <if test="unLoadingAddress != null">
            <if test="unLoadingAddress.placeName != null and unLoadingAddress.placeName.length > 0">
                AND INSTR(BT.UNLOADING_PLACE_NAME, #{unLoadingAddress.placeName}) > 0
            </if>
            <if test="unLoadingAddress.provinceName != null and unLoadingAddress.provinceName.length > 0">
                AND BT.END_PROVINCE_NAME = #{unLoadingAddress.provinceName}
            </if>
            <if test="unLoadingAddress.cityName != null and unLoadingAddress.cityName.length > 0">
                AND BT.END_CITY_NAME = #{unLoadingAddress.cityName}
            </if>
            <if test="unLoadingAddress.countyName != null and unLoadingAddress.countyName.length > 0">
                AND BT.END_COUNTY_NAME = #{unLoadingAddress.countyName}
            </if>
            <if test="unLoadingAddress.address != null and unLoadingAddress.address.length > 0">
                AND INSTR(BTE.UNLOADING_ADDRESS_NAME, #{unLoadingAddress.address}) > 0
            </if>
        </if>

        <if test="searchKeywords != null and searchKeywords.length > 0">
            <choose>
                <when test="searchKeywordsType == '1'.toString()">
                    AND INSTR(BT.DRIVER_NAME, #{searchKeywords}) > 0
                </when>
                <when test="searchKeywordsType == '2'.toString()">
                    AND BT.MOBILE_NO = #{searchKeywords}
                </when>
                <when test="searchKeywordsType == '3'.toString()">
                    AND INSTR(BT.CART_BADGE_NO, #{searchKeywords}) > 0
                </when>
                <when test="searchKeywordsType == '1-3'.toString()">
                    AND (INSTR(BT.DRIVER_NAME, #{searchKeywords}) > 0
                        OR INSTR(BT.CART_BADGE_NO, #{searchKeywords}) > 0)
                </when>
                <otherwise >
                    AND (
                        INSTR(BT.CART_BADGE_NO, #{searchKeywords}) > 0
                        OR INSTR(BT.DRIVER_NAME, #{searchKeywords}) > 0
                        OR BT.MOBILE_NO = #{searchKeywords}
                    )
                </otherwise>
            </choose>
        </if>

        <if test="taxWaybillNoList != null and taxWaybillNoList.size() > 0">
            <choose>
                <when test="taxWaybillNoList.size() == 1">
                    AND BT.TAX_WAYBILL_NO LIKE #{taxWaybillNoList[0]} || '%'
                </when>
                <otherwise>
                    AND BT.TAX_WAYBILL_NO IN
                    <foreach collection="taxWaybillNoList" item="taxWaybillNo" open="(" close=")" separator=",">
                        #{taxWaybillNo}
                    </foreach>
                </otherwise>
            </choose>
        </if>

        <if test="receiveState != null and receiveState.length > 0 and receiveState != -1">
            <choose>
                <when test="receiveState == 0">
                    AND BTE.HYB_RECEIVED_TIME IS NULL AND BTE.DISPATCH_CAR_RECORD_ID IS NOT NULL
                </when>
                <when test="receiveState == 1">
                    AND BTE.HYB_RECEIVED_TIME IS NOT NULL
                </when>
            </choose>
        </if>

        <if test="capacityType != null and capacityType.length > 0">
            AND BT.CAPACITY_TYPE = #{capacityType}
            <if test="capacityTypeName != null and capacityTypeName.length > 0">
                AND BT.CAPACITY_TYPE_NAME = #{capacityTypeName}
            </if>
        </if>

        <if test="transportVoucherUploadStatus != null and transportVoucherUploadStatus.length > 0">
            <choose>
                <when test="transportVoucherUploadStatus == 1">
                    AND BT.FIRST_RECEIPT_TIME IS NULL
                </when>
                <when test="transportVoucherUploadStatus == 2">
                    AND BT.FIRST_RECEIPT_TIME IS NOT NULL
                </when>
            </choose>
        </if>

        <if test="confirmedArrivalStatus != null and confirmedArrivalStatus.length > 0">
            <choose>
                <when test="confirmedArrivalStatus == '0'.toString()">
                    AND BTE.DISPATCH_CAR_RECORD_ID IS NOT NULL
                    AND BTE.ARRIVE_CONFIRM_TIME IS NULL
                </when>
                <when test="confirmedArrivalStatus == '1'.toString()">
                    AND BTE.ARRIVE_CONFIRM_TIME IS NOT NULL
                </when>
            </choose>
        </if>

        <if test="upstreamPayState != null and upstreamPayState > 0">
            AND BT.SETTLE_MODE = 1
            <choose>
                <when test="upstreamPayState == 0">
                    AND NVL(BT.PAY_STATE, 0) = 0
                </when>
                <when test="upstreamPayState == 2">
                    AND BT.PAY_STATE = 2
                </when>
                <when test="upstreamPayState == 5">
                    AND BT.PAY_STATE = 5
                </when>
                <otherwise>
                    AND BT.PAY_STATE NOT IN (0, 2, 5)
                </otherwise>
            </choose>
        </if>

        <if test="downstreamSettleMode != null and downstreamSettleMode.length > 0">
            AND TTA.SETTLE_MODE = #{downstreamSettleMode}
        </if>

        <if test="downstreamPayState != null and downstreamPayState.length > 0">
            AND TTA.SETTLE_MODE = 1
            <choose>
                <when test="downstreamPayState == 0">
                    AND NVL(TTA.PAY_STATE, 0) = 0
                </when>
                <when test="downstreamPayState == 2">
                    AND TTA.PAY_STATE = 2
                </when>
                <when test="downstreamPayState == 5">
                    AND TTA.PAY_STATE = 5
                </when>
                <otherwise>
                    AND TTA.PAY_STATE NOT IN (0, 2, 5)
                </otherwise>
            </choose>
        </if>

        <if test="queryOrgIdList != null and queryOrgIdList.size() > 0">
            <choose>
                <when test="dispatch">
                    AND (TTA.ORG_ID IN
                    <foreach collection="queryOrgIdList" index="index" item="orgId" open="(" separator="," close=")">
                        <if test="(index % 999) == 998"> NULL) OR TTA.ORG_ID IN(</if>#{orgId}
                    </foreach>
                    )
                </when>
                <otherwise>
                    AND (BT.ORG_ID IN
                    <foreach collection="queryOrgIdList" index="index" item="orgId" open="(" separator="," close=")">
                        <if test="(index % 999) == 998"> NULL) OR BT.ORG_ID IN(</if>#{orgId}
                    </foreach>
                    )
                </otherwise>
            </choose>
        </if>

        <if test="cartLength != null and cartLength.length > 0">
            AND BTE.CART_LENGTH = #{cartLength}
        </if>

        <if test="cartType != null and cartType.length > 0">
            AND BTE.CART_TYPE = #{cartType}
        </if>
    </sql>

    <sql id="transTaskListTabStatusCondition" >
        <if test="tabStatus != 0">
            <choose>
                <when test="tabStatus == 4">
                    AND BT.STATE = 1
                </when>

                <when test="tabStatus == 5">
                    AND BT.STATE = 2
                    <choose>
                        <when test="dispatch">
                            AND NVL(TTA.PAY_STATE, 0) = 0
                        </when>
                        <otherwise>
                            AND NVL(BT.PAY_STATE, 0) = 0
                        </otherwise>
                    </choose>
                </when>

                <when test="tabStatus == 6">
                    AND BT.STATE = 2
                    <choose>
                        <when test="dispatch">
                            AND TTA.PAY_STATE = 2
                        </when>
                        <otherwise>
                            AND BT.PAY_STATE = 2
                        </otherwise>
                    </choose>
                </when>

                <when test="tabStatus == 7">
                    AND (
                        EXISTS (
                            SELECT 1
                            FROM T_BO_TRANS_NODE_ALARM A
                            WHERE A.IS_DEL = 0
                            AND BT.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
                            AND A.NODE_DATA_TYPE IN (
                                SELECT NODE_DATA_TYPE
                                FROM T_BO_ROLE_ALARM_TYPE_REL
                                WHERE IS_DEL = 0
                                AND ROLE_ID = 12000
                            )
                            AND A.ALARM_TYPE =1
                        )
                        OR
                        EXISTS (
                            SELECT 1
                            FROM T_BO_TRANS_NODE_RECORD R
                            WHERE R.IS_DEL = 0
                            AND BT.BO_TRANS_TASK_ID = R.BO_TRANS_TASK_ID
                            AND R.OVER_TIME > 0
                        )
                    )
                </when>

                <when test="tabStatus == 9">
                    <if test="nodeIdList != null and nodeIdList.size() > 0">
                        AND BT.NODE_ID IN
                        <foreach collection="nodeIdList" item="nodeId" open="(" separator="," close=")">
                            #{nodeId}
                        </foreach>
                    </if>
                </when>

                <when test="tabStatus == 10">
                    AND BT.STATE = 2
                    <choose>
                        <when test="dispatch">
                            AND TTA.PAY_STATE NOT IN (0, 2)
                        </when>
                        <otherwise>
                            AND BT.PAY_STATE NOT IN (0, 2)
                        </otherwise>
                    </choose>
                </when>

                <when test="tabStatus == 11">
                    AND BTE.USER_FREIGHT_APPROVAL_STATUS != 0
                </when>

                <when test="tabStatus == 12">
                    AND EXISTS (
                        SELECT 1
                        FROM T_BO_TRANS_NODE_ALARM TNA
                        WHERE TNA.IS_DEL = 0
                        AND TNA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                        <if test="trackStopType != null and trackStopType == 1">
                            AND TNA.ALARM_PROCESS_RESULT = 0
                        </if>
                        AND TNA.NODE_DATA_TYPE = 14
                    )
                    <if test="trackStopType != null and trackStopType == 0">
                        AND NOT EXISTS (
                            SELECT 1
                            FROM T_BO_TRANS_NODE_ALARM TNA
                            WHERE TNA.IS_DEL = 0
                            AND TNA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
                            AND TNA.ALARM_PROCESS_RESULT = 0
                            AND TNA.NODE_DATA_TYPE = 14
                        )
                    </if>
                </when>

                <when test="tabStatus == 13">
                </when>

                <when test="tabStatus == 14">
                    AND BTE.TRANSPORT_NODE = 600
                    AND NOT (
                        <choose>
                            <when test="dispatch">
                                (BT.STATE = 2 AND NVL(TTA.PAY_STATE, 0) = 2) OR (BT.STATE = 2 AND NVL(TTA.PAY_STATE, 0) NOT IN (0, 2))
                            </when>
                            <otherwise>
                                (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) = 2) OR (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) NOT IN (0, 2))
                            </otherwise>
                        </choose>
                    )
                </when>

                <when test="tabStatus == 15">
                    AND BTE.TRANSPORT_NODE = 650
                    AND NOT (
                    <choose>
                        <when test="dispatch">
                            (BT.STATE = 2 AND NVL(TTA.PAY_STATE, 0) = 2) OR (BT.STATE = 2 AND NVL(TTA.PAY_STATE, 0) NOT IN (0, 2))
                        </when>
                        <otherwise>
                            (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) = 2) OR (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) NOT IN (0, 2))
                        </otherwise>
                    </choose>
                    )
                </when>

                <when test="tabStatus == 16">
                    AND BTE.TRANSPORT_NODE = 800
                    AND NOT (
                    <choose>
                        <when test="dispatch">
                            (BT.STATE = 2 AND NVL(TTA.PAY_STATE, 0) = 2) OR (BT.STATE = 2 AND NVL(TTA.PAY_STATE, 0) NOT IN (0, 2))
                        </when>
                        <otherwise>
                            (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) = 2) OR (BT.STATE = 2 AND NVL(BT.PAY_STATE, 0) NOT IN (0, 2))
                        </otherwise>
                    </choose>
                    )
                </when>

                <otherwise>
                    AND BT.STATE = 0
                    <if test="nodeIdList !=null and nodeIdList.size() > 0">
                        <choose>
                            <when test="dispatch">
                                AND TTA.NODE_ID IN
                            </when>
                            <otherwise>
                                AND BT.NODE_ID IN
                            </otherwise>
                        </choose>
                        <foreach collection="nodeIdList" item="nodeId" open="(" separator="," close=")">
                            #{nodeId}
                        </foreach>
                        <if test="isNewTransportNode and (tabStatus == 2 or tabStatus == 3)">
                            AND BTE.TRANSPORT_NODE IS NULL
                        </if>
                    </if>
                </otherwise>

            </choose>
        </if>
    </sql>

    <select id="getTransTaskTimeoutCount" resultType="int">
        SELECT NVL(COUNT(*), 0)
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        AND (
            EXISTS (
                SELECT 1
                FROM T_BO_TRANS_NODE_ALARM A
                WHERE A.IS_DEL = 0
                AND BT.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
                AND A.NODE_DATA_TYPE IN (
                    SELECT NODE_DATA_TYPE
                    FROM T_BO_ROLE_ALARM_TYPE_REL
                    WHERE IS_DEL = 0
                    AND ROLE_ID = 12000
                )
                AND A.ALARM_TYPE = 1
            )
        OR EXISTS (
            SELECT 1
            FROM T_BO_TRANS_NODE_RECORD R
            WHERE R.IS_DEL = 0
            AND BT.BO_TRANS_TASK_ID = R.BO_TRANS_TASK_ID
            AND R.OVER_TIME > 0
            )
        )
    </select>

    <select id="getTransTaskCountByNodeList" resultType="int">
        SELECT NVL(COUNT(*), 0)
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        <if test="nodeIdList != null and nodeIdList.size() > 0">
            AND BT.NODE_ID IN
            <foreach collection="nodeIdList" item="nodeId" open="(" separator="," close=")">
                #{nodeId}
            </foreach>
        </if>
        AND BT.NODE_ID IS NOT NULL
    </select>

    <select id="getTransTaskApprovalCollect" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            BTE.USER_FREIGHT_APPROVAL_STATUS approvalStatus,
            NVL(COUNT(*), 0) taskCount
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        AND BTE.USER_FREIGHT_APPROVAL_STATUS != 0
        GROUP BY BTE.USER_FREIGHT_APPROVAL_STATUS
    </select>

    <select id="getTransTaskAlarmTotalCount" resultType="int">
        SELECT NVL(COUNT(*), 0)
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        AND EXISTS (
            SELECT 1
            FROM T_BO_TRANS_NODE_ALARM TNA
            WHERE TNA.IS_DEL = 0
            AND TNA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND TNA.NODE_DATA_TYPE = 14
        )
    </select>

    <select id="getTransTaskAlarmPendingCount" resultType="int">
        SELECT NVL(COUNT(*), 0)
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        AND EXISTS (
            SELECT 1
            FROM T_BO_TRANS_NODE_ALARM TNA
            WHERE TNA.IS_DEL = 0
            AND TNA.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND TNA.ALARM_PROCESS_RESULT = 0
            AND TNA.NODE_DATA_TYPE = 14
        )
    </select>

    <select id="getTransTaskDeletedCount" resultType="int">
        SELECT NVL(COUNT(*), 0)
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
    </select>

    <select id="getTransTaskListTotalRows" resultType="java.lang.Integer">
        SELECT NVL(COUNT(*), 0)
        FROM <include refid="transTaskListTable"/>
        LEFT JOIN T_BO_TRANS_TASK_RELATED BTR
            ON BTR.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND BTR.IS_DEL = 0
            AND BTR.IS_ALLOCATE = 0
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
    </select>

    <select id="getTransTaskList" resultType="java.util.Map">
        SELECT
            <foreach collection="columnList" item="column" separator=",">
                ${column}
            </foreach>
        FROM <include refid="transTaskListTable"/>
        LEFT JOIN T_BO_TRANS_TASK_RELATED BTR
            ON BTR.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND BTR.IS_DEL = 0
            AND BTR.IS_ALLOCATE = 0
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
        <include refid="taskListOrderBySql"/>
    </select>

    <sql id="taskListOrderBySql">
        <choose>
            <when test="orderBySql != null and orderBySql.length > 0">
                ORDER BY ${orderBySql}
            </when>
            <when test="tabStatus == 3">
                ORDER BY BTE.ARRIVE_TIME, BT.CREATED_TIME DESC
            </when>
            <when test="tabStatus == 11">
                ORDER BY DECODE(BTE.USER_FREIGHT_APPROVAL_STATUS, 1, 1, 2, 2, 3, 4, 4, 3), BT.CREATED_TIME DESC
            </when>
            <otherwise>
                ORDER BY BT.CREATED_TIME DESC
            </otherwise>
        </choose>
    </sql>

    <select id="getTransTaskDispatchListTotalRows" resultType="java.lang.Integer">
        SELECT NVL(COUNT(*), 0)
        FROM <include refid="transTaskListTable"/>
        LEFT JOIN T_BO_TRANS_TASK_RELATED BTR
            ON BTR.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND BTR.IS_DEL = 0
            AND BTR.IS_ALLOCATE = 1
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
    </select>

    <select id="getTransTaskDispatchList" resultType="java.util.Map">
        SELECT
            <foreach collection="columnList" item="column" separator=",">
                ${column}
            </foreach>
        FROM <include refid="transTaskListTable"/>
        LEFT JOIN T_BO_TRANS_TASK_RELATED BTR
            ON BTR.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND BTR.IS_DEL = 0
            AND BTR.IS_ALLOCATE = 1
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
        <include refid="taskListOrderBySql"/>
    </select>

    <select id="getSupplierCollect" resultType="com.wtyt.tt.bean.BoTransTaskCollectBean">
        SELECT
            TTGR.GROUP_ID supplierId,
            COUNT(*) taskCount
        FROM <include refid="transTaskListTable"/>
        INNER JOIN T_BO_TRANS_TASK_GROUP_REL TTGR
            ON TTGR.BO_TRANS_TASK_ID = BT.BO_TRANS_TASK_ID
            AND TTGR.IS_DEL = 0
            AND TTGR.SUPPLIER_TYPE IN (1, 2)
            AND TTGR.GROUP_ID IS NOT NULL
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
        GROUP BY TTGR.GROUP_ID
        ORDER BY TTGR.GROUP_ID
    </select>

    <select id="queryTaskListForExport" resultType="com.wtyt.bo.bean.response.BoTransTaskSubBean" fetchSize="1000">
        SELECT
        <include refid="taskExportField" />
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
        <include refid="taskListOrderBySql"></include>
    </select>

    <sql id="taskExportField">
        <include refid="taskExportCommonField"/>
        NVL(bt.TAX_WAYBILL_ID,tta.TAX_WAYBILL_ID) taxWaybillId,
        bt.ORG_ID orgId,
        TO_CHAR(NVL(bt.ALL_FREIGHT, 0), '**********0.00') allFreight,
        TO_CHAR(NVL(bt.SERVICE_FEE, 0), '**********0.00') serviceFee,
        TO_CHAR(NVL(bt.DATA_SERVICE_FEE, 0), '**********0.00') dataServiceFee,
        TO_CHAR(bt.WITHHOLD_TAX_FEE, '**********0.00') withholdTaxFee,
        TO_CHAR(NVL(bt.USER_FREIGHT, 0), '**********0.00') userFreight,
        TO_CHAR(NVL(bt.PREPAYMENTS, 0) , '**********0.00') prepayments,
        TO_CHAR(NVL(bt.BACK_FEE, 0) , '**********0.00') backFee,
        CASE
        WHEN BT.INS_STATE = 1
        THEN TO_CHAR(NVL(bt.ALL_FREIGHT, 0) + NVL(bt.SERVICE_FEE, 0) + NVL(bt.DATA_SERVICE_FEE, 0) + NVL(bt.INS_FEE, 0) + NVL(bt.WITHHOLD_TAX_FEE, 0), '**********0.00')
        ELSE TO_CHAR(NVL(bt.ALL_FREIGHT, 0) + NVL(bt.SERVICE_FEE, 0) + NVL(bt.DATA_SERVICE_FEE, 0) + NVL(bt.WITHHOLD_TAX_FEE, 0), '**********0.00')
        END payableFreight,
        TO_CHAR(NVL(bt.PREPAYMENTS_OILCARD , 0), '**********0.00') prepaymentsOilcard,
        TO_CHAR(NVL(bt.PREPAYMENTS_GASCARD , 0), '**********0.00') prepaymentsGascard,
        TO_CHAR(bt.PREPAYMENTS_BUY_OIL, '**********0.00') prepaymentsBuyOil,
        TO_CHAR(bt.PREPAYMENTS_BUY_GAS, '**********0.00') prepaymentsBuyGas,
        NVL(bt.PAY_STATE,0) payState,
        TO_CHAR(bt.UNIT_PRICE, '**********0.00') unitPrice,
        NVL(bt.ADVANCE_PAY_STATE, 0) advancePayState,
        TO_CHAR(NVL(bt.FREIGHT_INCR , 0), '**********0.00') freightIncr,
        TO_CHAR(NVL(bt.LOSS_FEE , 0), '**********0.00') lossFee,
        TO_CHAR(bt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') createdTime,
        TO_CHAR(NVL(bt.GOODS_COST, 0), '**********0.00') goodsCost,
        bt.NODE_ID nodeId,
        bt.SETTLE_MODE settleMode,
        NVL(bt.SETTLE_TYPE, bt.SETTLE_MODE) settleType,
        TO_CHAR(NVL(BT.INS_FEE , 0), '**********0.00') insFee,
        BTE.PAY_NAME payName,
        BTE.PAY_BANK_NAME payBankName,
        BTE.PROVINCE province,
        BTE.CITY_NAME cityName,
        BTE.PAY_MOBILE_NO payMobileNo,
        BTE.PAY_ID_CARD payIdCard,
        BTE.OIL_CARD_NO oilCardNo,
        BTE.GAS_CARD_NO gasCardNo,
        TO_CHAR(BTE.USER_FREIGHT_APPROVAL_TIME, 'yyyy-MM-dd Hh24:mi:ss') userFreightApprovalTime,
        TO_CHAR(BT.PAY_OVER_TIME, 'yyyy-MM-dd Hh24:mi:ss') payOverTime,
        tta.ORG_ID diffOrgId,
        tta.PAY_STATE diffPayState,
        tta.SETTLE_MODE diffSettleMode
    </sql>

    <sql id="taskExportCommonField">
        bt.TAX_WAYBILL_NO taxWaybillNo,
        bt.BO_TRANS_TASK_ID boTransTaskId,
        bt.TRANS_MODE transMode,
        bt.LOADING_PLACE_NAME loadingPlaceName,
        bt.UNLOADING_PLACE_NAME unloadingPlaceName,
        bt.MILEAGE mileage,
        bt.GOODS_NAME goodsName,
        bt.GOODS_AMOUNT goodsAmount,
        bt.GOODS_AMOUNT_TYPE goodsAmountType,
        bt.XCY_USER_ID xcyUserId,
        bt.DRIVER_NAME driverName,
        bt.DRIVER_ID_CARD driverIdCard,
        bt.MOBILE_NO mobileNo,
        bt.CART_BADGE_NO cartBadgeNo,
        bt.CART_BADGE_COLOR cartBadgeColor,
        bt.CAPACITY_TYPE_NAME capacityTypeName,
        TO_CHAR(bt.START_TIME, 'yyyy-MM-dd Hh24:mi:ss') startTime,
        TO_CHAR(bt.END_TIME, 'yyyy-MM-dd Hh24:mi:ss') endTime,
        bt.WB_ITEM wbItem,
        bt.START_PROVINCE_NAME startProvinceName,
        bt.START_CITY_NAME startCityName,
        bt.START_COUNTY_NAME startCountyName,
        bt.END_PROVINCE_NAME endProvinceName,
        bt.END_CITY_NAME endCityName,
        bt.END_COUNTY_NAME endCountyName,
        bt.TRANSPORT_TYPE transportType,
        bt.state state,
        NVL(bt.TRANS_PATTERN, 1) transPattern,
        bt.BO_BUSINESS_LINE_ID boBusinessLineId,
        bte.CART_TYPE cartType,
        bte.CART_LENGTH cartLength,
        bte.LOADING_ADDRESS_NAME loadingAddressName,
        bte.UNLOADING_ADDRESS_NAME unloadingAddressName,
        TO_CHAR(bte.ARRIVE_END_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveEndTime,
        TO_CHAR(bte.HYB_RECEIVED_TIME, 'yyyy-MM-dd Hh24:mi:ss') hybReceivedTime,
        bte.ARRIVE_END_TIME_FORMAT arriveEndTimeFormat,
        bte.UNLOAD_TIME_FORMAT unloadTimeFormat,
        TO_CHAR(bte.UNLOAD_TIME, 'YYYY-MM-DD HH24:MI') unLoadTime,
        TO_CHAR(bte.ARRIVE_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveTime,
        bte.PAPER_RECEIPT_STATUS paperReceiptStatus,
        TO_CHAR(bte.DISPATCH_CAR_TIME, 'yyyy-MM-dd Hh24:mi:ss') dispatchCarTime,
        bte.SERVICE_REQUIRE serviceRequire,
        bte.TRANS_VOUCHER transVoucher,
        bte.SALESMAN_NAME salesmanName,
        bte.SETTLE_ISSUE settleIssue,
        bte.BO_VOUCHER_CHECK_STATE boVoucherCheckState,
        NR.USER_ID dispatchCarUserId,
        CASE
        WHEN bt.LOADING_TONNAGE - TRUNC(bt.LOADING_TONNAGE) = 0 THEN
        TO_CHAR(TRUNC(bt.LOADING_TONNAGE), '**********0')
        ELSE
        TO_CHAR(bt.LOADING_TONNAGE, '**********0.9999')
        END loadingTonnage,
        CASE
        WHEN bt.UNLOADING_TONNAGE - TRUNC(bt.UNLOADING_TONNAGE) = 0 THEN
        TO_CHAR(TRUNC(bt.UNLOADING_TONNAGE), '**********0')
        ELSE
        TO_CHAR(bt.UNLOADING_TONNAGE, '**********0.9999')
        END unloadingTonnage,
        TO_CHAR(bte.LOSS_AMOUNT, '**********0.00') lossAmount,
        CASE
        WHEN tdg.GUARANTEE_AMOUNT - TRUNC(tdg.GUARANTEE_AMOUNT) = 0 THEN
        TO_CHAR(TRUNC(tdg.GUARANTEE_AMOUNT), '**********0')
        ELSE
        TO_CHAR(tdg.GUARANTEE_AMOUNT, '**********0.9999')
        END driverGuaranteeAmount,
        tdg.GUARANTEE_STATE driverGuaranteeState,
        tdg.GUARANTEE_CHANNEL driverGuaranteeChannel,
        BT.RECEIVER receiver,
        BT.RECEIVER_MOBILE receiverMobile,
        BT.TASK_NOTE taskNote,
        BT.CUSTOMER_REMARK customerRemark,
        BTE.CUSTOMER_ORDER_NO customerOrderNo,
        TO_CHAR(BTE.PAPER_ESTIMATED_DELIVERY_TIME, 'yyyy-MM-dd Hh24:mi:ss') paperEstimatedDeliveryTime,
        TO_CHAR(BTE.ELECTRONIC_RECEIPT_AUDIT_TIME, 'yyyy-MM-dd Hh24:mi:ss') electronicReceiptAuditTime,
        BTE.LINE_TIME_REQUIRE lineTimeRequire,
        BTE.TRAN_REQUIRE tranRequire,
        BTE.CART_TONNAGE cartTonnage,
        BTE.CUSTOMIZE_NO customizeNo,
        TO_CHAR(BTE.RECEIPT_RECEIVE_TIME, 'yyyy-MM-dd Hh24:mi:ss') receiptReceiveTime,
        BTE.EXPRESS_NUMBER expressNumber,
        BTE.LOADING_ROUGH_WEIGHT loadingRoughWeight,
        BTE.UNLOADING_ROUGH_WEIGHT unloadingRoughWeight,
        BTE.LOADING_TARE loadingTare,
        BTE.UNLOADING_TARE unloadingTare,
        TO_CHAR(BTE.ACTUAL_LOAD_TIME, 'yyyy-MM-dd Hh24:mi:ss') actualLoadTime,
        TO_CHAR(BTE.ACTUAL_UNLOAD_TIME, 'yyyy-MM-dd Hh24:mi:ss') actualUnloadTime,
        BTE.BOX_NO boxNo,
        BTE.DEDUCT_TONNAGE deductTonnage,
        TO_CHAR(BTE.LOADING_GOODS_TIME, 'yyyy-MM-dd Hh24:mi:ss') loadingGoodsTime,
        TO_CHAR(BTE.ARRIVE_CONFIRM_TIME, 'yyyy-MM-dd Hh24:mi:ss') arriveConfirmTime,
        TO_CHAR(BTE.CONTACT_DRIVER_TIME, 'yyyy-MM-dd Hh24:mi:ss') contactDriverTime,
        TO_CHAR(bte.LOSS_UNIT_PRICE, '**********0.00') lossUnitPrice,
        CASE WHEN bte.LOSS_ACTUAL_TONNAGE = FLOOR(bte.LOSS_ACTUAL_TONNAGE) THEN  TO_CHAR(bte.LOSS_ACTUAL_TONNAGE, '**********0') ELSE TO_CHAR(bte.LOSS_ACTUAL_TONNAGE, '**********0.9999') END lossActualTonnage,
        CASE WHEN bte.ALLOW_LOSS_WEIGHT = FLOOR(bte.ALLOW_LOSS_WEIGHT) THEN  TO_CHAR(bte.ALLOW_LOSS_WEIGHT, '**********0') ELSE TO_CHAR(bte.ALLOW_LOSS_WEIGHT, '**********0.9999') END allowLossWeight,
        bte.LOSS_CONFIG_VALUE lossConfigValue,
        bte.ALLOW_GAIN_FLAG allowGainFlag,
        bte.CPD_POOL_GROUP_NAME cpdPoolGroupName,
        bte.CPD_SECOND_POOL_GROUP_NAME cpdSecondPoolGroupName,
        TO_CHAR(BT.FIRST_RECEIPT_TIME, 'yyyy-MM-dd Hh24:mi:ss') firstReceiptTime,
        BTE.BELONG_DISPATCHER_ID belongDispatcherId,
        BTE.LOADING_CONTACT_NAME loadingContactName,
        BTE.LOADING_CONTACT_MOBILE_NO loadingContactMobileNo,
        BTE.UNLOADING_CONTACT_NAME unloadingContactName,
        BTE.UNLOADING_CONTACT_MOBILE_NO unloadingContactMobileNo,
        BTE.TRAILER_CART_BADGE_NO trailerCartBadgeNo,
        BTE.TRANSPORT_NODE transportNode,
        BTE.PAYER payer,
    </sql>

    <select id="queryDispatchTaskListForExport" resultType="com.wtyt.bo.bean.response.BoTransTaskSubBean" fetchSize="1000">
        SELECT
        <include refid="dispatchTaskExportField" />
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
        <include refid="taskListOrderBySql"></include>
    </select>

    <select id="getExportTransTaskListByTaskIds" resultType="com.wtyt.bo.bean.response.BoTransTaskSubBean" fetchSize="1000">
        SELECT
        <include refid="taskExportField" />
        FROM <include refid="transTaskListTable"/>
        WHERE BT.IS_DEL = 0
        <choose>
            <when test="boTransTaskIdList != null and boTransTaskIdList.size() > 0">
                AND BT.BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
                    #{boTransTaskId}
                </foreach>
            </when>
            <otherwise>
                AND BT.BO_TRANS_TASK_ID = -1
            </otherwise>
        </choose>
        ORDER BY BT.CREATED_TIME DESC
    </select>

    <sql id="dispatchTaskExportField">
        <include refid="taskExportCommonField"/>
        NVL(tta.TAX_WAYBILL_ID,bt.TAX_WAYBILL_ID) taxWaybillId,
        tta.ORG_ID orgId,
        TO_CHAR(NVL(tta.ALL_FREIGHT, 0), '**********0.00') allFreight,
        TO_CHAR(NVL(tta.USER_FREIGHT, 0), '**********0.00') userFreight,
        TO_CHAR(NVL(tta.PREPAYMENTS, 0) , '**********0.00') prepayments,
        TO_CHAR(NVL(tta.BACK_FEE, 0) , '**********0.00') backFee,
        CASE
        WHEN tta.INS_STATE = 1
        THEN TO_CHAR(NVL(tta.ALL_FREIGHT, 0) + NVL(tta.INS_FEE, 0), '**********0.00')
        ELSE TO_CHAR(NVL(tta.ALL_FREIGHT, 0), '**********0.00')
        END payableFreight,
        TO_CHAR(NVL(tta.PREPAYMENTS_OILCARD , 0), '**********0.00') prepaymentsOilcard,
        TO_CHAR(NVL(tta.PREPAYMENTS_GASCARD , 0), '**********0.00') prepaymentsGascard,
        TO_CHAR(tta.PREPAYMENTS_BUY_OIL, '**********0.00') prepaymentsBuyOil,
        TO_CHAR(tta.PREPAYMENTS_BUY_GAS, '**********0.00') prepaymentsBuyGas,
        NVL(tta.PAY_STATE,0) payState,
        TO_CHAR(tta.UNIT_PRICE, '**********0.00') unitPrice,
        TO_CHAR(NVL(tta.FREIGHT_INCR , 0), '**********0.00') freightIncr,
        TO_CHAR(NVL(tta.LOSS_FEE , 0), '**********0.00') lossFee,
        TO_CHAR(bt.CREATED_TIME, 'yyyy-MM-dd Hh24:mi:ss') createdTime,
        TO_CHAR(NVL(tta.GOODS_COST, 0), '**********0.00') goodsCost,
        tta.NODE_ID nodeId,
        tta.SETTLE_MODE settleMode,
        tta.SETTLE_MODE settleType,
        TO_CHAR(NVL(tta.INS_FEE , 0), '**********0.00') insFee,
        tta.PAY_NAME payName,
        tta.PAY_BANK_NAME payBankName,
        tta.PROVINCE province,
        tta.CITY_NAME cityName,
        tta.PAY_MOBILE_NO payMobileNo,
        tta.PAY_ID_CARD payIdCard,
        tta.OIL_CARD_NO oilCardNo,
        tta.GAS_CARD_NO gasCardNo,
        TO_CHAR(tta.PAY_OVER_TIME, 'yyyy-MM-dd Hh24:mi:ss') payOverTime,
        bt.ORG_ID diffOrgId,
        bt.PAY_STATE diffPayState,
        bt.SETTLE_MODE diffSettleMode,
        TO_CHAR(bt.ALL_FREIGHT, '**********0.00') diffAllFreight,
        TO_CHAR(bt.UNIT_PRICE, '**********0.00') diffUnitPrice,
        TO_CHAR(bt.PREPAYMENTS_OILCARD, '**********0.00') diffPrepaymentsOilcard,
        TO_CHAR(bt.PREPAYMENTS_GASCARD, '**********0.00') diffPrepaymentsGascard
    </sql>

    <resultMap id="CommonResultMap" type="com.wtyt.tt.bean.Resp5329298Bean">
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="taxWaybillId" column="TAX_WAYBILL_ID" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="mobileNo" column="MOBILE_NO" jdbcType="VARCHAR"/>
        <result property="driverName" column="DRIVER_NAME" jdbcType="VARCHAR"/>
        <result property="cartBadgeNo" column="CART_BADGE_NO" jdbcType="VARCHAR"/>
        <result property="cartBadgeColor" column="CART_BADGE_COLOR" jdbcType="VARCHAR"/>
        <result property="goodsName" column="GOODS_NAME" jdbcType="VARCHAR"/>
        <result property="goodsAmount" column="GOODS_AMOUNT" jdbcType="VARCHAR"/>
        <result property="taxWaybillNo" column="TAX_WAYBILL_NO" jdbcType="VARCHAR"/>
        <result property="goodsAmountType" column="GOODS_AMOUNT_TYPE" jdbcType="VARCHAR"/>
        <result property="loadingTonnage" column="LOADING_TONNAGE" jdbcType="VARCHAR"/>
        <result property="unloadingTonnage" column="UNLOADING_TONNAGE" jdbcType="VARCHAR"/>
        <result property="loadingPlaceName" column="LOADING_PLACE_NAME" jdbcType="VARCHAR"/>
        <result property="unloadingPlaceName" column="UNLOADING_PLACE_NAME" jdbcType="VARCHAR"/>
        <result property="startTime" column="START_TIME" jdbcType="VARCHAR"/>
        <result property="state" column="STATE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryOwnerMonitorList" resultMap="CommonResultMap">
        SELECT
        t.BO_TRANS_TASK_ID,
        NVL(t.TAX_WAYBILL_ID,ta.TAX_WAYBILL_ID) TAX_WAYBILL_ID,
        t.TAX_WAYBILL_NO,
        TO_CHAR(t.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') CREATED_TIME,
        t.MOBILE_NO,
        t.DRIVER_NAME,
        t.CART_BADGE_NO,
        t.CART_BADGE_COLOR,
        t.LOADING_PLACE_NAME ,
        t.UNLOADING_PLACE_NAME ,
        t.GOODS_NAME ,
        t.GOODS_AMOUNT,
        t.GOODS_AMOUNT_TYPE,
        TO_CHAR(t.START_TIME, 'YYYY-MM-DD HH24:MI:SS') START_TIME,
        TO_CHAR(t.LOADING_TONNAGE, '**********0.0000') LOADING_TONNAGE,
        TO_CHAR(t.UNLOADING_TONNAGE, '**********0.0000') UNLOADING_TONNAGE,
        t.STATE
        FROM
        T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA te ON
        te.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_ALLOCATE ta ON ta.BO_TRANS_TASK_ID =t.BO_TRANS_TASK_ID AND ta.IS_DEL =0
        WHERE
        t.IS_DEL = 0
        <if test="boTransTaskIds !=null and boTransTaskIds.size()>0">
            AND (
            t.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIds" index="index" open="(" close=")" item="id" separator=",">
                <if test="(index % 999) == 998"> NULL) OR t.BO_TRANS_TASK_ID IN(</if>#{id}
            </foreach>
            )
        </if>
        <if test="boBusinessLineIds !=null and boBusinessLineIds.size()>0">
            AND (
            t.BO_BUSINESS_LINE_ID IN
            <foreach collection="boBusinessLineIds" index="index" open="(" close=")" item="id" separator=",">
                <if test="(index % 999) == 998"> NULL) OR t.BO_BUSINESS_LINE_ID IN(</if>#{id}
            </foreach>
            )
        </if>
        <if test="state!=null and state!=''">
            AND t.STATE = #{state}
        </if>
        <if test="searchKeyword !=null and searchKeyword != ''">
            AND (t.TAX_WAYBILL_NO LIKE '%' || #{searchKeyword} || '%'
            OR t.CART_BADGE_NO LIKE '%' || #{searchKeyword} || '%'
            OR t.MOBILE_NO = #{searchKeyword}
            OR t.DRIVER_NAME = #{searchKeyword}
            OR t.START_PROVINCE_NAME || t.START_CITY_NAME || t.START_COUNTY_NAME || te.LOADING_ADDRESS_NAME || t.LOADING_PLACE_NAME
            || t.END_PROVINCE_NAME || t.END_CITY_NAME || t.END_COUNTY_NAME || te.UNLOADING_ADDRESS_NAME || t.UNLOADING_PLACE_NAME LIKE '%' || #{searchKeyword} || '%'
            )
        </if>
        ORDER BY t.START_TIME DESC,t.CREATED_TIME asc
    </select>

    <select id="batchQueryTaskList" resultMap="taskDetailResultMap">
        SELECT
            <include refid="taskDetailField"></include>
        FROM
        T_BO_TRANS_TASK bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
        bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
        WHERE
        bt.BO_TRANS_TASK_ID IN
        <foreach collection="taskIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="isDel!=null and isDel!=''">
            AND bt.IS_DEL = #{isDel}
        </if>

    </select>

    <!-- 主表+扩展表-->
    <sql id="taskDetailField">
        <include refid="taskDetailCommonField"/>
        bt.ORG_ID,
        bt.TAX_WAYBILL_ID,
        TO_CHAR(bt.ALL_FREIGHT, '**********0.00') ALL_FREIGHT,
        TO_CHAR(bt.SERVICE_FEE, '**********0.00') SERVICE_FEE,
        TO_CHAR(bt.DATA_SERVICE_FEE, '**********0.00') DATA_SERVICE_FEE,
        TO_CHAR(bt.USER_FREIGHT, '**********0.00') USER_FREIGHT,
        TO_CHAR(bt.PREPAYMENTS, '**********0.00') PREPAYMENTS,
        TO_CHAR(bt.BACK_FEE, '**********0.00') BACK_FEE,
        TO_CHAR(bt.PREPAYMENTS_OILCARD, '**********0.00') PREPAYMENTS_OILCARD,
        TO_CHAR(bt.PREPAYMENTS_GASCARD, '**********0.00') PREPAYMENTS_GASCARD,
        TO_CHAR(bt.PREPAYMENTS_BUY_OIL, '**********0.00') PREPAYMENTS_BUY_OIL,
        TO_CHAR(bt.PREPAYMENTS_BUY_GAS, '**********0.00') PREPAYMENTS_BUY_GAS,
        NVL(bt.PAY_STATE, 0) PAY_STATE,
        TO_CHAR(bt.UNIT_PRICE, '**********0.00') UNIT_PRICE,
        TO_CHAR(bt.FREIGHT_INCR, '**********0.00') FREIGHT_INCR,
        TO_CHAR(bt.LOSS_FEE, '**********0.00') LOSS_FEE,
        bt.SETTLE_MODE,
        bte.PAY_NAME,
        bte.PAY_ID_CARD,
        bte.PAY_BANK_NO,
        bte.PAY_BANK_NAME,
        bte.PROVINCE,
        bte.CITY_NAME,
        bte.PAY_MOBILE_NO,
        TO_CHAR(BT.NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') NODE_TIME,
        bt.NODE_ID
    </sql>

    <sql id="taskDetailCommonField">
        bt.TAX_WAYBILL_NO,
        bt.BO_TRANS_TASK_ID,
        bt.START_PROVINCE_NAME,
        bt.START_CITY_NAME,
        bt.START_COUNTY_NAME,
        bte.LOADING_ADDRESS_NAME,
        bt.START_LONGITUDE,
        bt.START_LATITUDE,
        bt.LOADING_PLACE_NAME,
        bt.END_PROVINCE_NAME,
        bt.END_CITY_NAME,
        bt.END_COUNTY_NAME,
        bte.UNLOADING_ADDRESS_NAME,
        bt.END_LONGITUDE,
        bt.END_LATITUDE,
        bt.UNLOADING_PLACE_NAME,
        bt.DRIVER_NAME,
        bt.MOBILE_NO,
        bt.CART_BADGE_NO,
        bt.CART_BADGE_COLOR,
        bte.CART_TYPE,
        bte.CART_LENGTH,
        bt.GOODS_NAME,
        CASE WHEN TRUNC(BT.GOODS_AMOUNT)=BT.GOODS_AMOUNT THEN TO_CHAR(BT.GOODS_AMOUNT,'**********90') ELSE TO_CHAR(BT.GOODS_AMOUNT,'**********90.0999') END GOODS_AMOUNT,
        bt.GOODS_AMOUNT_TYPE,
        TO_CHAR(bte.ARRIVE_END_TIME, 'YYYY-MM-DD HH24:MI:SS') ARRIVE_END_TIME,
        TO_CHAR(bte.UNLOAD_TIME, 'YYYY-MM-DD HH24:MI:SS') UNLOAD_TIME,
        CASE WHEN TRUNC(BT.LOADING_TONNAGE)=BT.LOADING_TONNAGE THEN TO_CHAR(BT.LOADING_TONNAGE,'**********90') ELSE TO_CHAR(BT.LOADING_TONNAGE,'**********90.0999') END LOADING_TONNAGE,
        CASE WHEN TRUNC(BT.UNLOADING_TONNAGE)=BT.UNLOADING_TONNAGE THEN TO_CHAR(BT.UNLOADING_TONNAGE,'**********90') ELSE TO_CHAR(BT.UNLOADING_TONNAGE,'**********90.0999') END UNLOADING_TONNAGE,
        TO_CHAR(bt.START_TIME, 'YYYY-MM-DD HH24:MI:SS') START_TIME,
        TO_CHAR(bt.END_TIME, 'YYYY-MM-DD HH24:MI:SS') END_TIME,
        bt.BO_BUSINESS_LINE_ID,
    </sql>

    <select id="getDispatchExportTransTaskList" resultType="com.wtyt.bo.bean.BoTaskExportBean" fetchSize="1000">
        SELECT <include refid="dispatchTaskExportField"/>
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
        <include refid="taskListOrderBySql"/>
    </select>

    <select id="getExportTransTaskList" resultType="com.wtyt.bo.bean.BoTaskExportBean" fetchSize="1000">
        SELECT <include refid="taskExportField"/>
        FROM <include refid="transTaskListTable"/>
        WHERE <include refid="transTaskListCondition"/>
        <include refid="transTaskListTabStatusCondition"/>
        <include refid="taskListOrderBySql"/>
    </select>

    <select id="getExportTransTaskListByIds" resultType="com.wtyt.bo.bean.BoTaskExportBean" fetchSize="1000">
        SELECT <include refid="taskExportField" />
        FROM <include refid="transTaskListTable"/>
        WHERE BT.IS_DEL = 0
        <choose>
            <when test="boTransTaskIdList != null and boTransTaskIdList.size() > 0">
                AND BT.BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
                    #{boTransTaskId}
                </foreach>
            </when>
            <otherwise>
                AND BT.BO_TRANS_TASK_ID = -1
            </otherwise>
        </choose>
        ORDER BY BT.CREATED_TIME DESC
    </select>

</mapper>
