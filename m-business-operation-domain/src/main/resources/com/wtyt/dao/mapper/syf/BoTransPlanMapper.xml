<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransPlanMapper">


    <insert id="proNewInsert">
    INSERT
        INTO
        T_BO_TRANS_PLAN (BO_TRANS_PLAN_ID,
        ORG_ID,
        USER_ID,
        FILE_URL,
        FILE_NAME,
        FILE_SIZE,
        FILE_TYPE,
        COMPLETE_TIME,
        IS_COMPLETE,
        ORDER_COUNT,
        EXCEPTION_INFO,
        AUTO_CREATE_FLAG,
        BO_TP_DH_CONFIG_ID,
        EXCEL_ROWS,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE)
    VALUES (#{boTransPlanId},
    #{orgId},
    #{userId},
    #{fileUrl},
    #{fileName},
    #{fileSize},
    #{fileType},
    TO_DATE(#{completeTime}, 'yyyy-mm-dd HH24:MI:ss'),
    #{isComplete},
    #{orderCount},
    #{exceptionInfo},
    #{autoCreateFlag},
    #{boTpDhConfigId},
    #{excelRows},
    0,
    SYSDATE,
    SYSDATE,
    NULL)
    </insert>
    <update id="proNewUpdatePlan">
        UPDATE
            T_BO_TRANS_PLAN
        SET
            IS_COMPLETE = #{isComplete},
            <if test="isComplete == 1">
                COMPLETE_TIME = SYSDATE ,
            </if>
            <if test="note!= null and note != ''">
                NOTE = #{note} ,
            </if>
            <if test="exceptionInfo!= null and exceptionInfo != ''">
                EXCEPTION_INFO = #{exceptionInfo},
            </if>
            <if test="orderCount!= null and orderCount != ''">
                ORDER_COUNT = #{orderCount},
            </if>
            <if test="waybillCount!= null and waybillCount != ''">
                WAYBILL_COUNT = #{waybillCount},
            </if>
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TRANS_PLAN_ID = #{boTransPlanId}
    </update>
    <select id="selectPlanByDate" resultType="java.lang.String">
    SELECT
        1
    FROM
        DUAL
    WHERE
        EXISTS (
        SELECT
            1
        FROM
            T_BO_TRANS_PLAN
        WHERE
            IS_DEL = 0
            AND ORG_ID = #{orgId}
            AND IS_COMPLETE = 1
            AND TO_CHAR(CREATED_TIME, 'YYYY-MM-DD') = TO_CHAR(SYSDATE, 'YYYY-MM-DD'))
    </select>
    <select id="selectOrderByDate" resultType="java.lang.String">
        SELECT
            1
        FROM
            DUAL
        WHERE
            EXISTS (
            SELECT
                1
            FROM
                T_BO_TRANS_ORDER
            WHERE
                ORG_ID = #{orgId}
                AND TO_CHAR(CREATED_TIME, 'YYYY-MM-DD') = TO_CHAR(SYSDATE, 'YYYY-MM-DD'))
    </select>
    <select id="selectById" resultType="com.wtyt.dao.bean.syf.BoTransPlanBean">
    SELECT
        BO_TRANS_PLAN_ID boTransPlanId,
        ORG_ID orgId,
        USER_ID userId,
        FILE_URL fileUrl,
        FILE_NAME fileName,
        FILE_SIZE fileSize,
        FILE_TYPE fileType,
        COMPLETE_TIME completeTime,
        IS_COMPLETE isComplete,
        ORDER_COUNT orderCount,
        EXCEPTION_INFO exceptionInfo,
        WAYBILL_COUNT waybillCount,
        AUTO_CREATE_FLAG autoCreateFlag
    FROM
        T_BO_TRANS_PLAN
    WHERE
        BO_TRANS_PLAN_ID = #{boTransPlanId}
    </select>
    <select id="selectTimeOutPlan" resultType="com.wtyt.dao.bean.syf.BoTransPlanBean">
        SELECT
        BO_TRANS_PLAN_ID boTransPlanId,
        IS_COMPLETE isComplete
        FROM
        T_BO_TRANS_PLAN
        WHERE
        IS_DEL = 0
        AND IS_COMPLETE = 2
        AND CREATED_TIME &lt;= SYSDATE - 1
    </select>
    <select id="getCompletePlanIdByOrgId" resultType="java.lang.String">
        SELECT
        *
        FROM
        (
        SELECT
        *
        FROM
        T_BO_TRANS_PLAN
        WHERE
        IS_DEL = 0
        AND IS_COMPLETE = 1
        AND ORG_ID = #{orgId}
        ORDER BY
        CREATED_TIME DESC )
        WHERE
        ROWNUM &lt;= #{num}
    </select>

</mapper>
