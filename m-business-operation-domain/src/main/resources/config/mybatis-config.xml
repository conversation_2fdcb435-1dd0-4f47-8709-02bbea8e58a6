<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

    <settings>
        <setting name="callSettersOnNulls" value="true"/>
        <setting name="logImpl" value="LOG4J2" />
<!--        <setting name="logImpl" value="STDOUT_LOGGING" />-->
        <setting name="jdbcTypeForNull" value="NULL" />
        <!--设置启用数据库字段下划线映射到java对象的驼峰式命名属性，默认为false-->
        <setting name="mapUnderscoreToCamelCase" value="false"/>
        <setting name="cacheEnabled" value="false" /><!-- 关闭二级缓存 -->
        <setting name="localCacheScope" value="STATEMENT" /><!-- 相当于关闭一级缓存 -->
    </settings>   
    
    <typeAliases>
        <package name="com.wtyt.dao.bean"/>
        <package name="com.wtyt.util.bean"/>
        <package name="com.wtyt.commons.bean"/>
        <package name="com.wtyt.bean"/>
        <package name="com.wtyt.bo.bean"/>
        <package name="com.wtyt.board.bean"/>
        <package name="com.wtyt.tt.bean"/>
    </typeAliases>
    
    
<!--     &lt;!&ndash; 插件 &ndash;&gt;-->
<!--    <plugins>-->
<!--        &lt;!&ndash; 分页插件 &ndash;&gt;-->
<!--        <plugin interceptor="com.github.pagehelper.PageInterceptor">-->
<!--            &lt;!&ndash; 分页插件会自动检测当前的数据库链接，自动选择合适的分页方式。 你可以配置helperDialect属性来指定分页插件使用哪种方言。配置时，可以使用下面的缩写值： oracle,mysql,mariadb,sqlite,hsqldb,postgresql,db2,sqlserver,informix,h2,sqlserver2012,derby 特别注意：使用-->
<!--                SqlServer2012 数据库时，需要手动指定为 sqlserver2012，否则会使用 SqlServer2005 的方式进行分页 &ndash;&gt;-->
<!--            <property name="helperDialect" value="oracle"/>-->
<!--            &lt;!&ndash; 默认值为 false，该参数对使用 RowBounds 作为分页参数时有效。 当该参数设置为 true 时，会将 RowBounds 中的 offset 参数当成 pageNum 使用，可以用页码和页面大小两个参数进行分页。 &ndash;&gt;-->
<!--            <property name="offsetAsPageNum" value="false" />-->
<!--            &lt;!&ndash; 默认值为false，该参数对使用 RowBounds 作为分页参数时有效。 当该参数设置为true时，使用 RowBounds 分页会进行 count 查询。 &ndash;&gt;-->
<!--            <property name="rowBoundsWithCount" value="true" />-->
<!--            &lt;!&ndash; 默认值为 false，当该参数设置为 true 时，如果 pageSize=0 或者 RowBounds.limit = 0 就会查询出全部的结果（相当于没有执行分页查询，但是返回结果仍然是 Page 类型）。 &ndash;&gt;-->
<!--            <property name="pageSizeZero" value="false" />-->
<!--            &lt;!&ndash; 分页合理化参数，默认值为false。当该参数设置为 true 时，pageNum<=0 时会查询第一页， pageNum>pages（超过总数时），会查询最后一页。默认false 时，直接根据参数进行查询。 &ndash;&gt;-->
<!--            <property name="reasonable" value="false" />-->
<!--            &lt;!&ndash; 为了支持startPage(Object params)方法，增加了该参数来配置参数映射，用于从对象中根据属性名取值， 可以配置 pageNum,pageSize,count,pageSizeZero,reasonable，不配置映射的用默认值 默认值为pageNum=pageNum;pageSize=pageSize;count=countSql;reasonable=reasonable;pageSizeZero=pageSizeZero。 &ndash;&gt;-->
<!--            &lt;!&ndash; <property name="params" value="pageNum=pageNum;pageSize=pageSize;count=countSql;reasonable=reasonable;pageSizeZero=pageSizeZero" /> &ndash;&gt;-->
<!--            &lt;!&ndash; 支持通过 Mapper 接口参数来传递分页参数，默认值false，分页插件会从查询方法的参数值中，自动根据上面 params 配置的字段中取值，查找到合适的值时就会自动分页。 使用方法可以参考测试代码中的 com.github.pagehelper.test.basic 包下的 ArgumentsMapTest 和 ArgumentsObjTest。 &ndash;&gt;-->
<!--            <property name="supportMethodsArguments" value="false" />-->
<!--            &lt;!&ndash; 默认值为 false。设置为 true 时，允许在运行时根据多数据源自动识别对应方言的分页 （不支持自动选择sqlserver2012，只能使用sqlserver） &ndash;&gt;-->
<!--            <property name="autoRuntimeDialect" value="true" />-->
<!--            &lt;!&ndash; 默认值为 true。当使用运行时动态数据源或没有设置 helperDialect 属性自动获取数据库类型时，会自动获取一个数据库连接， 通过该属性来设置是否关闭获取的这个连接 默认true关闭，设置为 false 后，不会关闭获取的连接，这个参数的设置要根据自己选择的数据源来决定。 &ndash;&gt;-->
<!--            <property name="closeConn" value="true" />-->
<!--        </plugin>-->
<!--    </plugins>-->

</configuration>  