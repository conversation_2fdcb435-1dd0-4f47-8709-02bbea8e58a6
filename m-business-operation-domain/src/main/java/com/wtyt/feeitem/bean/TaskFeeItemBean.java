package com.wtyt.feeitem.bean;

import java.io.Serializable;

public class TaskFeeItemBean implements Serializable {

    private String boTransTaskFeeItemId;//主键
    private String boTransTaskId;//运输任务id
    private String isAllocate;//是否是分配表 1是 0 否
    private String feeItemType;//类型 ,2:运费增补,1:运费扣减
    private String feeItemKey;//费用项,tarpaulin photoPenalty:苫布照片罚款,paperSealPhotoPenalty:封条照片罚款,leadSealPhotoPenalty:封铅照片罚款,underCarPhotoPenalty:车底照片罚款
    private String feeItemValue;//费用值
    private String feeItemName;//费用项名称
    private String feeItemNote;//费用备注

    public String getBoTransTaskFeeItemId() {
        return boTransTaskFeeItemId;
    }

    public void setBoTransTaskFeeItemId(String boTransTaskFeeItemId) {
        this.boTransTaskFeeItemId = boTransTaskFeeItemId;
    }

    public String getIsAllocate() {
        return isAllocate;
    }

    public void setIsAllocate(String isAllocate) {
        this.isAllocate = isAllocate;
    }

    public String getBoTransTaskId() {
        return boTransTaskId;
    }

    public void setBoTransTaskId(String boTransTaskId) {
        this.boTransTaskId = boTransTaskId;
    }

    public String getFeeItemType() {
        return feeItemType;
    }

    public void setFeeItemType(String feeItemType) {
        this.feeItemType = feeItemType;
    }

    public String getFeeItemKey() {
        return feeItemKey;
    }

    public void setFeeItemKey(String feeItemKey) {
        this.feeItemKey = feeItemKey;
    }

    public String getFeeItemValue() {
        return feeItemValue;
    }

    public void setFeeItemValue(String feeItemValue) {
        this.feeItemValue = feeItemValue;
    }

    public String getFeeItemName() {
        return feeItemName;
    }

    public void setFeeItemName(String feeItemName) {
        this.feeItemName = feeItemName;
    }

    public String getFeeItemNote() {
        return feeItemNote;
    }

    public void setFeeItemNote(String feeItemNote) {
        this.feeItemNote = feeItemNote;
    }

}
