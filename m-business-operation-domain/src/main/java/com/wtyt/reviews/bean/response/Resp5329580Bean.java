package com.wtyt.reviews.bean.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年03月15日 17时56分
 */
@Setter
@Getter
public class Resp5329580Bean implements Serializable {

    private static final long serialVersionUID = 9202943244502901360L;
    private String nextMonth;
    private Map<String, Object> page;
    private List<Reviews> reviewsList;

    @Setter
    @Getter
    public static class Reviews {
        private String reviewsMonth;
        private int totalCount;
        private int reviewsCount;
        private String avgScore;
        private List<Data> dataList;
    }

    @Setter
    @Getter
    public static class Data {

        private String subjectType;
        private String subjectId;
        private Map<String, ?> subjectExtend;
        private String reviewerName;
        private String reviewerTime;
        private String score;
        private String content;
    }
}
