<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.ReviewsMapper">

    <insert id="insertReviews">
        INSERT INTO T_PM_REVIEWS (
            PM_REVIEWS_ID,
            PM_REVIEWS_SUBJECT_ID,
            PARENT_ID,
            REVIEWER_IDENTITY,
            REVIEWER_ID,
            REVIEWER_NAME,
            REVIEWER_MOBILE,
            SCORE,
            CONTENT,
            IS_ANONYMITY
        ) VALUES (
            #{pmReviewsId},
            #{pmReviewsSubjectId},
            #{parentId},
            #{reviewerIdentity},
            #{reviewerId},
            #{reviewerName},
            #{reviewerMobile},
            #{score},
            #{content},
            #{isAnonymity}
        )
    </insert>

    <select id="getReviewsByReviewer" resultType="com.wtyt.reviews.bean.ReviewsBean">
        SELECT
            T.PM_REVIEWS_ID pmReviewsId,
            T.PM_REVIEWS_SUBJECT_ID pmReviewsSubjectId,
            T.PARENT_ID parentId,
            T.REVIEWER_IDENTITY reviewerIdentity,
            T.REVIEWER_ID reviewerId,
            T.REVIEWER_NAME reviewerName,
            T.REVIEWER_MOBILE reviewerMobile,
            T.SCORE score,
            T.CONTENT content,
            T.IS_ANONYMITY isAnonymity
        FROM T_PM_REVIEWS T
        WHERE T.REVIEWER_ID = #{reviewerId}
            AND T.REVIEWER_IDENTITY = #{reviewerIdentity}
            AND T.PM_REVIEWS_SUBJECT_ID = #{pmReviewsSubjectId}
    </select>

    <select id="getReviews" resultType="com.wtyt.reviews.bean.ReviewsBean">
        SELECT
            T.PM_REVIEWS_ID pmReviewsId,
            T.PM_REVIEWS_SUBJECT_ID pmReviewsSubjectId,
            T.PARENT_ID parentId,
            T.REVIEWER_IDENTITY reviewerIdentity,
            T.REVIEWER_ID reviewerId,
            T.REVIEWER_NAME reviewerName,
            T.REVIEWER_MOBILE reviewerMobile,
            T.SCORE score,
            T.CONTENT content,
            T.IS_ANONYMITY isAnonymity
        FROM T_PM_REVIEWS T
        WHERE T.IS_DEL = 0
            AND T.PM_REVIEWS_ID = #{pmReviewsId}
    </select>

    <update id="updateReviewsDelete">
        UPDATE T_PM_REVIEWS T
        SET T.IS_DEL = 1,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.PM_REVIEWS_ID = #{pmReviewsId}
    </update>

    <select id="getAllReviewsByReviewsSubjectId" resultType="com.wtyt.reviews.bean.ReviewsBean">
        SELECT
            T.PM_REVIEWS_ID pmReviewsId,
            T.PM_REVIEWS_SUBJECT_ID pmReviewsSubjectId,
            T.PARENT_ID parentId,
            T.REVIEWER_IDENTITY reviewerIdentity,
            T.REVIEWER_ID reviewerId,
            T.REVIEWER_NAME reviewerName,
            T.REVIEWER_MOBILE reviewerMobile,
            T.SCORE score,
            T.CONTENT content,
            T.IS_ANONYMITY isAnonymity
        FROM T_PM_REVIEWS T
        WHERE T.PM_REVIEWS_SUBJECT_ID = #{pmReviewsSubjectId}
        ORDER BY T.CREATED_TIME DESC
    </select>

</mapper>
