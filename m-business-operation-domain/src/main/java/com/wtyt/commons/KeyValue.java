package com.wtyt.commons;

import java.io.Serializable;

/**
 * Key Value 形式的数据
 * <AUTHOR>
 *
 */
public class KeyValue implements Serializable {

    private static final long serialVersionUID = -7928439757014854794L;
    
    private String key;
    private String value;
    
    public KeyValue(){}
    
    public KeyValue(String key, String value){
        this.key = key;
        this.value = value;
    }
    
    public String getKey() {
        return key;
    }
    public void setKey(String key) {
        this.key = key;
    }
    public String getValue() {
        return value;
    }
    public void setValue(String value) {
        this.value = value;
    }
    
    

}
