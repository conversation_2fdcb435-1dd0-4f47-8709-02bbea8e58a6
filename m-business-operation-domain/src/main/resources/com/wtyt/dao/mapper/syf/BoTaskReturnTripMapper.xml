<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskReturnTripMapper">

    <select id="getTaskReturnTrip" resultType="BoTaskReturnTripBean">
        SELECT
            T.BO_TASK_RETURN_TRIP_ID boTaskReturnTripId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.CREATED_USER_ID createdUserId,
            T.MODIFY_USER_ID modifyUserId,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.LOADING_ADDRESS_NAME loadingAddressName,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.UNLOADING_ADDRESS_NAME unloadingAddressName,
            T.MILEAGE mileage,
            T.MIDDLE_MILEAGE middleMileage,
            TO_CHAR(T.EXPECTED_START_TIME, 'yyyy-mm-dd hh24:mi:ss') expectedStartTime,
            TO_CHAR(T.EXPECTED_END_TIME, 'yyyy-mm-dd hh24:mi:ss') expectedEndTime,
            T.GOODS_NAME goodsName,
            T.GOODS_AMOUNT goodsAmount,
            T.GOODS_AMOUNT_TYPE goodsAmountType
        FROM T_BO_TASK_RETURN_TRIP T
        WHERE T.IS_DEL = 0
        AND T.BO_TASK_RETURN_TRIP_ID = #{boTaskReturnTripId}
    </select>

    <select id="getTaskReturnTripByTransTaskId" resultType="BoTaskReturnTripBean">
        SELECT
            T.BO_TASK_RETURN_TRIP_ID boTaskReturnTripId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.CREATED_USER_ID createdUserId,
            T.MODIFY_USER_ID modifyUserId,
            T.START_PROVINCE_NAME startProvinceName,
            T.START_CITY_NAME startCityName,
            T.START_COUNTY_NAME startCountyName,
            T.START_LONGITUDE startLongitude,
            T.START_LATITUDE startLatitude,
            T.LOADING_ADDRESS_NAME loadingAddressName,
            T.END_PROVINCE_NAME endProvinceName,
            T.END_CITY_NAME endCityName,
            T.END_COUNTY_NAME endCountyName,
            T.END_LONGITUDE endLongitude,
            T.END_LATITUDE endLatitude,
            T.UNLOADING_ADDRESS_NAME unloadingAddressName,
            T.MILEAGE mileage,
            T.MIDDLE_MILEAGE middleMileage,
            TO_CHAR(T.EXPECTED_START_TIME, 'yyyy-mm-dd hh24:mi:ss') expectedStartTime,
            TO_CHAR(T.EXPECTED_END_TIME, 'yyyy-mm-dd hh24:mi:ss') expectedEndTime,
            T.GOODS_NAME goodsName,
            T.GOODS_AMOUNT goodsAmount,
            T.GOODS_AMOUNT_TYPE goodsAmountType
        FROM T_BO_TASK_RETURN_TRIP T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND ROWNUM = 1
    </select>

    <insert id="insertTaskReturnTrip">
        INSERT INTO T_BO_TASK_RETURN_TRIP (
            BO_TASK_RETURN_TRIP_ID,
            BO_TRANS_TASK_ID,
            CREATED_USER_ID,
            START_PROVINCE_NAME,
            START_PROVINCE_CODE,
            START_CITY_NAME,
            START_CITY_CODE,
            START_COUNTY_NAME,
            START_COUNTY_CODE,
            START_LONGITUDE,
            START_LATITUDE,
            LOADING_ADDRESS_NAME,
            END_PROVINCE_NAME,
            END_PROVINCE_CODE,
            END_CITY_NAME,
            END_CITY_CODE,
            END_COUNTY_NAME,
            END_COUNTY_CODE,
            END_LONGITUDE,
            END_LATITUDE,
            UNLOADING_ADDRESS_NAME,
            MILEAGE,
            MIDDLE_MILEAGE,
            EXPECTED_START_TIME,
            EXPECTED_END_TIME,
            GOODS_NAME,
            GOODS_AMOUNT,
            GOODS_AMOUNT_TYPE
        ) VALUES (
            #{boTaskReturnTripId},
            #{boTransTaskId},
            #{createdUserId},
            #{startProvinceName},
            #{startProvinceCode},
            #{startCityName},
            #{startCityCode},
            #{startCountyName},
            #{startCountyCode},
            #{startLongitude},
            #{startLatitude},
            #{loadingAddressName},
            #{endProvinceName},
            #{endProvinceCode},
            #{endCityName},
            #{endCityCode},
            #{endCountyName},
            #{endCountyCode},
            #{endLongitude},
            #{endLatitude},
            #{unloadingAddressName},
            #{mileage},
            #{middleMileage},
            TO_DATE(#{expectedStartTime}, 'yyyy-mm-dd hh24:mi:ss'),
            TO_DATE(#{expectedEndTime}, 'yyyy-mm-dd hh24:mi:ss'),
            #{goodsName},
            #{goodsAmount},
            #{goodsAmountType}
        )
    </insert>

    <update id="updateTaskReturnTrip">
        UPDATE T_BO_TASK_RETURN_TRIP T
        SET T.MODIFY_USER_ID = #{modifyUserId},
            <if test="startProvinceName != null and startProvinceName.length > 0">
                T.START_PROVINCE_NAME = #{startProvinceName},
            </if>
            <if test="startProvinceCode != null and startProvinceCode.length > 0">
                T.START_PROVINCE_CODE = #{startProvinceCode},
            </if>
            <if test="startCityName != null and startCityName.length > 0">
                T.START_CITY_NAME = #{startCityName},
            </if>
            <if test="startCityCode != null and startCityCode.length > 0">
                T.START_CITY_CODE = #{startCityCode},
            </if>
            <if test="startCountyName != null and startCountyName.length > 0">
                T.START_COUNTY_NAME = #{startCountyName},
            </if>
            <if test="startCountyCode != null and startCountyCode.length > 0">
                T.START_COUNTY_CODE = #{startCountyCode},
            </if>
            <if test="startLongitude != null and startLongitude.length > 0">
                T.START_LONGITUDE = #{startLongitude},
            </if>
            <if test="startLatitude != null and startLatitude.length > 0">
                T.START_LATITUDE = #{startLatitude},
            </if>
            <if test="loadingAddressName != null and loadingAddressName.length > 0">
                T.LOADING_ADDRESS_NAME = #{loadingAddressName},
            </if>
            <if test="endProvinceName != null and endProvinceName.length > 0">
                T.END_PROVINCE_NAME = #{endProvinceName},
            </if>
            <if test="endProvinceCode != null and endProvinceCode.length > 0">
                T.END_PROVINCE_CODE = #{endProvinceCode},
            </if>
            <if test="endCityName != null and endCityName.length > 0">
                T.END_CITY_NAME = #{endCityName},
            </if>
            <if test="endCityCode != null and endCityCode.length > 0">
                T.END_CITY_CODE = #{endCityCode},
            </if>
            <if test="endCountyName != null and endCountyName.length > 0">
                T.END_COUNTY_NAME = #{endCountyName},
            </if>
            <if test="endCountyCode != null and endCountyCode.length > 0">
                T.END_COUNTY_CODE = #{endCountyCode},
            </if>
            <if test="endLongitude != null and endLongitude.length > 0">
                T.END_LONGITUDE = #{endLongitude},
            </if>
            <if test="endLatitude != null and endLatitude.length > 0">
                T.END_LATITUDE = #{endLatitude},
            </if>
            <if test="unloadingAddressName != null and unloadingAddressName.length > 0">
                T.UNLOADING_ADDRESS_NAME = #{unloadingAddressName},
            </if>
            <if test="mileage != null and mileage.length > 0">
                T.MILEAGE = #{mileage},
            </if>
            <if test="middleMileage != null and middleMileage.length > 0">
                T.MIDDLE_MILEAGE = #{middleMileage},
            </if>
            <if test="expectedStartTime != null">
                T.EXPECTED_START_TIME = TO_DATE(#{expectedStartTime}, 'yyyy-mm-dd hh24:mi:ss'),
            </if>
            <if test="expectedEndTime != null">
                T.EXPECTED_END_TIME = TO_DATE(#{expectedEndTime}, 'yyyy-mm-dd hh24:mi:ss'),
            </if>
            <if test="goodsName != null">
                T.GOODS_NAME = #{goodsName},
            </if>
            <if test="goodsAmount != null">
                T.GOODS_AMOUNT = #{goodsAmount},
            </if>
            <if test="goodsAmountType != null">
                T.GOODS_AMOUNT_TYPE = #{goodsAmountType},
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TASK_RETURN_TRIP_ID = #{boTaskReturnTripId}
    </update>

</mapper>