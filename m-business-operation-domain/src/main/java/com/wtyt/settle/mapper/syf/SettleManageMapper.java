package com.wtyt.settle.mapper.syf;

import com.wtyt.dao.bean.syf.BoTransTaskFeeBean;
import com.wtyt.settle.bean.*;
import com.wtyt.settle.bean.entity.TransFeeVerifyTaskEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2023/9/18 11:43
 */
@Repository
public interface SettleManageMapper {

    List<BoTransTaskFeeBean> querySettleList(@Param("list") List<String> queryOrgIdList, @Param("beginTime") String beginTime, @Param("endTime") String endTime, @Param("map") Map<String, Object> map);

    List<BoTransTaskFeeBean> queryNewSettleList(SettleQueryBean data);

    List<BoTransTaskFeeVrfBean> queryFeeVrfList(@Param("list") List<String> queryOrgIdList, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    List<BoTransTaskFeeVrfBean> queryNewFeeVrfList(SettleQueryBean data);

    List<FeeVerifyTaskDetail> queryFeeDetailList(SettleQueryBean data);

    /**
     * 查询项目下核销人员姓名分组的用户信息
     * @param queryOrgIdList
     * @return
     */
    List<VerifyUser> verifyUserList(@Param("list") List<String> queryOrgIdList);

    /**
     * 查询关联单号信息
     * @param boTransTaskFeeId
     * @return
     */
    String queryRelatedNo(String boTransTaskFeeId);

    /**
     * 新增或更新核销信息
     * @param taskFeeVrfBean
     * @return
     */
     int mergeIntoTaskFeeVaf(BoTransTaskFeeVrfBean taskFeeVrfBean);

     int  updateRelatedNo(BoTransTaskFeeVrfBean taskFeeVrfBean);

    /**
     * 新增核销记录信息
     * @param recordBean
     * @return
     */
     int insertFeeVerifyRecord(BoTransTaskFeeVrfReBean recordBean);

     int delFeeVrfRecordByFeeVrfId(@Param("boTransTaskFeeId")String boTransTaskFeeId);

    List<TabInfoBean> feeQueryStatusCount(Req5329313Bean req5329313Bean);

    /** 统计核销状态数 */
    List<TabInfoBean> queryVerifyStatusCount(Req5329313Bean req5329313Bean);

    /**
     * 统计核销任务费用状态数
     * @param req5329313Bean 请求信息
     * @return List<TabInfoBean> 
     * <AUTHOR> Qingcheng 
     * @since 2024/8/6
     */
    List<TabInfoBean> queryVerifyTaskFeeStatusCount(Req5329313Bean req5329313Bean);

    List<TaskFeeVerifyBean> feeVerifyList(Req5329313Bean req5329313Bean);

    /** 查询结算核销列表 */
    List<TaskFeeVerifyBean> querySettleVerifyList(Req5329313Bean req5329313Bean);

    /** 查询新版结算核销导出列表 */
    List<TaskFeeVerifyBean> querySettleVerifyExportList(Req5329313Bean req5329313Bean);

    BoTransTaskFeeVrfBean queryFeeVrfByFeeId(@Param("boTransTaskFeeId") String boTransTaskFeeId, @Param("freightRuleMap") Map<String, Object> freightRuleMap);

    int delFeeVrfByFeeVrfId(@Param("boTransTaskFeeVrfId")String boTransTaskFeeVrfId);

    /**
     * 核销记录数量批量查询
     * @param queryFeeIdList
     * @return
     */
    List<String> queryVerifyRecordCount(@Param("list") List<String> queryFeeIdList);
    //核销弹窗的款项汇总信息
    List<TaskFeeVerifyBean> queryVerifyPopSummaryInfo(Req5329318Bean data);
    //列表展示的款项汇总信息
    Resp5329318Bean queryVerifyListSummaryInfo(Req5329318Bean data);
    /** 新版列表核销弹框的款项汇总信息 */
    List<TaskFeeVerifyBean> queryNewVerifyPopSummaryInfo(Req5329318Bean data);
    /** 新版列表展示的款项汇总信息 */
    Resp5329318Bean queryNewVerifyListSummaryInfo(Req5329318Bean data);

    /** 根据运输任务id查询应收金额之和 */
    String queryReceivableFee(@Param("list") List<String> boTransTaskIds);

    /** 查询自动核销金额总和 */
    String queryTotalAutoVerify(String boTransTaskFeeId);

    /** 查询任务核销的总收入和总支出 */
    List<BoTaskVerifyBean> queryTaskVerifyList(@Param("boTransTaskIds") List<String> boTransTaskIds);

    /** 批量查询任务核销款项的核销状态 */
    List<BoTransTaskFeeBean> queryTaskFeeVerifyStatus(@Param("boTransTaskIds") List<String> boTransTaskIds, @Param("freightRuleMap") Map<String, Object> freightRuleMap);

    /** 查询审批通过的款项列表 */
    List<BoTransTaskFeeBean> queryApprovalTaskFeeList(Req5330283Bean req5330283Bean);

    /** 更新审批通过的款项 */
    int updateApprovalTaskFee(List<BoTransTaskFeeBean> feeList);

    /** 查询任务需要核销的款项列表 */
    List<BoTransTaskFeeBean> queryVerifyTaskFeeList(@Param("boTransTaskId")String boTransTaskId,@Param("freightRuleMap") Map<String, Object> freightRuleMap);

    /**
     * 查询核销任务列表
     * @param settleQueryPageBean 结算查询条件
     * @return List<TransFeeVerifyTaskEntity>
     * <AUTHOR> Qingcheng
     * @since 2024/8/2
     */
    List<TransFeeVerifyTaskEntity> queryVerifyTaskPage(SettleQueryPageBean settleQueryPageBean);

    /**
     * 查询核销任务费用
     * @param settleQueryPageBean 核销任务列表查询信息
     * @return List<TransFeeVerifyTaskEntity>
     * <AUTHOR> Qingcheng
     * @since 2024/8/5
     */
    List<TransFeeVerifyTaskEntity> queryVerifyTaskFeeInfo(SettleQueryPageBean settleQueryPageBean);

    List<BoTransTaskFeeBean> queryTaskFeeList(@Param("boTransTaskIds") List<String> boTransTaskIds, @Param("freightRuleMap") Map<String, Object> freightRuleMap);

    List<BoTransTaskFeeBean> queryCustomTaskFeeList(@Param("boTransFeeVerifyTaskIds") List<String> boTransFeeVerifyTaskIds);

    List<BoTaskFeeCountBean> queryTaskFeeStatistic(SettleQueryBean data);

    /**
     * 查询核销任务详细信息
     * @param settleQueryBean 核销任务详细信息查询条件
     * @return TransFeeVerifyTaskEntity
     * <AUTHOR> Qingcheng
     * @since 2024/8/5
     */
    TransFeeVerifyTaskEntity queryVerifyTaskInfo(SettleQueryBean settleQueryBean);

    /**
     * 查询核销任务费用信息
     * @param settleQueryBean 核销任务费用查询条件
     * @return Map<String,Object> 
     * <AUTHOR> Qingcheng 
     * @since 2024/8/6
     */
    List<TransFeeVerifyTaskEntity> queryVerifyTaskFee(SettleQueryBean settleQueryBean);

    List<TransFeeVerifyTaskEntity> queryVerifyTaskFeeDetail(SettleQueryBean data);

    List<BoTaskFeeCountBean> queryInvoiceFreight(SettleQueryBean data);
}
