<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dh.mapper.syf.DynamicHeaderOrderQueryMapper">
    <select id="selectHistoryTemplates" resultType="java.util.Map">
        SELECT
        *
        FROM (
        SELECT
        *
        FROM
        (SELECT
        TBTO.CREATED_TIME,
        <if test="queryType == '1'.toString()">
            COUNT(1) OVER(PARTITION BY TBTO.START_PROVINCE_NAME, TBTO.START_CITY_NAME, TBTO.START_COUNTY_NAME,
            TBTO.RAW_START_ADDRESS, TBTO.END_PROVINCE_NAME, TBTO.END_CITY_NAME, TBTO.END_COUNTY_NAME,
            TBTO.RAW_END_ADDRESS, TBTO.GOODS_NAME) NUM,
        </if>
        ROW_NUMBER() OVER(PARTITION BY TBTO.START_PROVINCE_NAME, TBTO.START_CITY_NAME, TBTO.START_COUNTY_NAME,
        TBTO.RAW_START_ADDRESS, TBTO.END_PROVINCE_NAME, TBTO.END_CITY_NAME, TBTO.END_COUNTY_NAME, TBTO.RAW_END_ADDRESS,
        TBTO.GOODS_NAME ORDER BY TBTO.CREATED_TIME DESC) RN,
        <if test="fieldList !=null and fieldList.size()>0">
            <foreach collection="fieldList" item="item" close="" open="" separator=",">
                ${item}
            </foreach>
        </if>
        <if test="columnList !=null and columnList.size()>0">
            <foreach collection="columnList" item="item" close="" open="," separator=",">
                ${item}
            </foreach>
        </if>
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTIA ON TBTO.BO_TRANS_ORDER_ID = TBTIA.BO_TRANS_ORDER_ID
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR on TBTO.BO_TRANS_ORDER_ID = TBOGR.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        WHERE TBTO.IS_DEL = 0
        <!-- 接入数据权限 -->
        <if test="permissionList != null and permissionList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionList" open="(" close=")" separator=" OR ">
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" open="(" close=")"
                             separator=" OR ">
                        TBOGR.GROUP_ID = #{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND TBTO.USER_ID = #{scopeItem.userId}
                        </if>
                    </foreach>
                    )
                </if>

                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
            </foreach>
            )
        </if>
        <if test="searchKeyWords != null and searchKeyWords.length > 0">
            AND (INSTR(TBTO.GOODS_NAME, #{searchKeyWords}) > 0
            OR INSTR(TBTO.START_PROVINCE_NAME || TBTO.START_CITY_NAME || TBTO.START_COUNTY_NAME ||
            TBTO.RAW_START_ADDRESS, #{searchKeyWords}) > 0
            OR INSTR(TBTO.END_PROVINCE_NAME || TBTO.END_CITY_NAME || TBTO.END_COUNTY_NAME || TBTO.RAW_END_ADDRESS,
            #{searchKeyWords}) > 0)
        </if>
        AND TBTO.START_PROVINCE_NAME IS NOT NULL
        AND TBTO.START_CITY_NAME IS NOT NULL
        AND TBTO.END_PROVINCE_NAME IS NOT NULL
        AND TBTO.END_CITY_NAME IS NOT NULL
        AND TBTO.GOODS_NAME IS NOT NULL
        AND TBTO.ORDER_TYPE = 2
        AND TBTO.CREATED_TIME > SYSDATE - 180) T
        WHERE T.RN = 1
        ORDER BY
        <choose>
            <when test="queryType == '1'.toString()">
                T.NUM DESC
            </when>
            <otherwise>
                T.CREATED_TIME DESC
            </otherwise>
        </choose>
        )
        WHERE ROWNUM &lt;= 30
    </select>

    <select id="selectLatestOrderUnitPrice" resultType="com.wtyt.dh.bean.BoDhOrderBean">
        SELECT
        boTpImportDataId boTpImportDataId,
        boTransOrderId boTransOrderId,
        boTpDhConfigId boTpDhConfigId,
        unitPrice unitPrice
        FROM (
        SELECT
        TBTO.UNIT_PRICE unitPrice,
        TBTO.BO_TRANS_ORDER_ID boTransOrderId,
        TPTID.BO_TP_DH_CONFIG_ID boTpDhConfigId,
        TPTID.BO_TP_IMPORT_DATA_ID boTpImportDataId
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TPTID on TBTO.BO_TRANS_ORDER_ID = TPTID.BO_TRANS_ORDER_ID
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR on TBTO.BO_TRANS_ORDER_ID = TBOGR.BO_TRANS_ORDER_ID
        WHERE TBTO.GOODS_NAME = #{goodsName}
        <!-- 接入数据权限 -->
        <if test="permissionList != null and permissionList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionList" open="(" close=")" separator=" OR ">
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" open="(" close=")"
                             separator=" OR ">
                        TBOGR.GROUP_ID = #{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND TBTO.USER_ID = #{scopeItem.userId}
                        </if>
                    </foreach>
                    )
                </if>

                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
            </foreach>
            )
        </if>
        AND TBTO.GOODS_NAME = #{goodsName}
        AND TBTO.START_PROVINCE_NAME = #{transLine.startProvinceName}
        AND TBTO.START_CITY_NAME = #{transLine.startCityName}
        <if test="transLine.startCountyName != null and transLine.startCountyName != ''">
            AND TBTO.START_COUNTY_NAME = #{transLine.startCountyName}
        </if>
        AND TBTO.END_PROVINCE_NAME = #{transLine.endProvinceName}
        AND TBTO.END_CITY_NAME = #{transLine.endCityName}
        <if test="transLine.endCountyName != null and transLine.endCountyName != ''">
            AND TBTO.END_COUNTY_NAME = #{transLine.endCountyName}
        </if>
        AND (TBTO.UNIT_PRICE IS NOT NULL OR TBTO.USER_FREIGHT IS NOT NULL)
        AND TBTO.ORDER_TYPE = 2  AND TBTO.IS_DEL = 0
        ORDER BY TBTO.CREATED_TIME DESC
        ) WHERE ROWNUM <![CDATA[ <= 1]]>
        UNION
        SELECT
        boTpImportDataId boTpImportDataId,
        boTransOrderId boTransOrderId,
        boTpDhConfigId boTpDhConfigId,
        unitPrice unitPrice
        FROM (
        SELECT
        TBTO.UNIT_PRICE unitPrice,
        TBTO.BO_TRANS_ORDER_ID boTransOrderId,
        TPTID.BO_TP_DH_CONFIG_ID boTpDhConfigId,
        TPTID.BO_TP_IMPORT_DATA_ID boTpImportDataId
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TPTID on TBTO.BO_TRANS_ORDER_ID = TPTID.BO_TRANS_ORDER_ID
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR on TBTO.BO_TRANS_ORDER_ID = TBOGR.BO_TRANS_ORDER_ID
        WHERE TBTO.GOODS_NAME = #{goodsName}
        <!-- 接入数据权限 -->
        <if test="permissionList != null and permissionList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionList" open="(" close=")" separator=" OR ">
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" open="(" close=")"
                             separator=" OR ">
                        TBOGR.GROUP_ID = #{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND TBTO.USER_ID = #{scopeItem.userId}
                        </if>
                    </foreach>
                    )
                </if>

                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
            </foreach>
            )
        </if>
        AND TBTO.START_PROVINCE_NAME = #{transLine.startProvinceName}
        AND TBTO.START_CITY_NAME = #{transLine.startCityName}
        AND TBTO.END_PROVINCE_NAME = #{transLine.endProvinceName}
        AND TBTO.END_CITY_NAME = #{transLine.endCityName}
        AND (TBTO.UNIT_PRICE IS NOT NULL OR TBTO.USER_FREIGHT IS NOT NULL)
        AND TBTO.ORDER_TYPE = 2 AND TBTO.IS_DEL = 0
        ORDER BY TBTO.CREATED_TIME DESC
        ) WHERE ROWNUM <![CDATA[ <= 1]]>
    </select>

    <select id="queryDynamicHeadOrderInfos" resultType="java.util.Map">
        SELECT
        <if test="fieldList !=null and fieldList.size()>0">
            <foreach collection="fieldList" item="item" close="" open="" separator=",">
                o.${item}
            </foreach>
        </if>
        <if test="columnList !=null and columnList.size()>0">
            <foreach collection="columnList" item="item" close="" open=","  separator=",">
                d.${item}
            </foreach>
        </if>
        FROM
        T_BO_TRANS_ORDER o
        <if test="columnList !=null and columnList.size()>0">
            INNER JOIN
        T_BO_TP_IMPORT_DATA d ON o.BO_TRANS_ORDER_ID = d.BO_TRANS_ORDER_ID AND d.is_del = 0
        </if>
        WHERE o.BO_TRANS_ORDER_ID in
            <foreach collection="boTransOrderIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>


    </select>
</mapper>
