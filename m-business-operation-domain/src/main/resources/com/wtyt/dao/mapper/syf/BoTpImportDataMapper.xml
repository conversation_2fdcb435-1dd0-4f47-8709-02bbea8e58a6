<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpImportDataMapper">
    <insert id="batchInsert">
        INSERT
            INTO
            T_BO_TP_IMPORT_DATA (BO_TP_IMPORT_DATA_ID,
            BO_TRANS_ORDER_ID,
            BO_TP_DH_CONFIG_ID,
            IS_DEL,
            CREATED_TIME,
            LAST_MODIFIED_TIME,
            NOTE,
            <foreach collection="columnNameList" item="each" close="" open="" separator=",">
                ${each}
            </foreach>
            )
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="dataList" separator="UNION ALL">
            SELECT
            #{item.boTpImportDataId} boTpImportDataId,
            #{item.boTransOrderId} boTransOrderId,
            #{item.boTpDhConfigId} boTpDhConfigId,
            0 isDel,
            SYSDATE createdTime,
            SYSDATE lastModifiedTime,
            NULL note,
            <foreach collection="columnNameList" item="each" close="" open="" separator=",">
               #{item.${each}}  ${each}
            </foreach>
            FROM DUAL
        </foreach>
        ) A
    </insert>
    <insert id="addTpImportData">
        INSERT INTO T_BO_TP_IMPORT_DATA(BO_TP_IMPORT_DATA_ID, BO_TRANS_ORDER_ID,BO_TP_DH_CONFIG_ID,
        <foreach collection="headerDataList" item="item" close="" open="" separator=",">
            ${item.dataColumnName}
        </foreach>
        )
        VALUES (#{boTpImportDataId}, #{boTransOrderId},#{boTpDhConfigId},
        <foreach collection="headerDataList" item="item" close="" open="" separator=",">
            #{item.inputValue}
        </foreach>
        )
    </insert>


    <select id="queryTpImportDataListByOrderId" resultType="java.util.HashMap">
        SELECT to_char(BO_TRANS_ORDER_ID) BO_TRANS_ORDER_ID ,
        <foreach collection="list" item="item" close="" open="" separator=",">
            ${item.dataColumnName}
        </foreach>
        FROM T_BO_TP_IMPORT_DATA
        WHERE IS_del = 0
        AND BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryTpImportDataListByOrderIdList" resultType="java.util.HashMap">
        SELECT to_char(BO_TRANS_ORDER_ID) BO_TRANS_ORDER_ID , ${queryDataColumn}
        FROM T_BO_TP_IMPORT_DATA
        WHERE IS_del = 0
        AND BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="queryTpImportDataList" resultType="java.util.Map">
        SELECT to_char(BO_TRANS_ORDER_ID) BO_TRANS_ORDER_ID ,
        <foreach collection="list" item="item" close="" open="" separator=",">
            ${item}
        </foreach>
        FROM T_BO_TP_IMPORT_DATA
        WHERE IS_del = 0
        AND BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="queryTpImportDataAndOrderList" resultType="java.util.Map">
        SELECT
            to_char(d.BO_TRANS_ORDER_ID) BO_TRANS_ORDER_ID
            <if test="dataColumns != null and dataColumns.size()>0">
                <foreach collection="dataColumns" item="item" close="" open="," separator=",">
                    d.${item}
                </foreach>
            </if>
            <if test="orderColumns != null and orderColumns.size()>0">
                <foreach collection="orderColumns" item="item" close="" open="," separator=",">
                    o.${item}
                </foreach>
            </if>
        FROM
            T_BO_TP_IMPORT_DATA d
        INNER JOIN T_BO_TRANS_ORDER o ON
            d.BO_TRANS_ORDER_ID = o.BO_TRANS_ORDER_ID
        WHERE
            d.is_del = 0
            AND o.IS_DEL = 0 AND O.ORDER_TYPE =1
            AND NOT EXISTS (
                SELECT 1 FROM T_BO_TRANS_ORDER_REL WHERE IS_DEL = 0 AND BO_TRANS_ORDER_ID = o.BO_TRANS_ORDER_ID
            )
           <choose>
                <when test="transTaskFlag != null and transTaskFlag == '1'.toString()">
                    AND  o.DT_INSIDE_ORDER_MARK =1
                </when>
                <otherwise>
                    AND  (o.DT_INSIDE_ORDER_MARK = 0 or o.DT_INSIDE_ORDER_MARK is null)
                </otherwise>
            </choose>
            AND o.ORG_ID = #{orgId}
            <if test="retrieval != null and retrieval != '' and searchFields != null and searchFields.size() > 0">
                AND
                <foreach collection="searchFields" item="item" close=")" open="(" separator=" OR ">
                    INSTR(d.${item}, #{retrieval}) > 0
                </foreach>
            </if>
        ORDER BY
            o.CREATED_TIME DESC
    </select>

    <update id="updateDataValue">
        UPDATE T_BO_TP_IMPORT_DATA SET ${dataColumnName} = #{dataValue},
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_ORDER_ID  =  #{boTransOrderId}
    </update>

    <update id="dynamicUpdateDataByOrderId">
        UPDATE T_BO_TP_IMPORT_DATA SET
        <foreach collection="columnDataMap.keys" item="k" separator=",">
            <if test="null != columnDataMap[k]">
                ${k} = #{columnDataMap[${k}]}
            </if>
        </foreach>
        ,LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_ORDER_ID  =  #{boTransOrderId} and is_del =0
    </update>

    <select id="queryDistinctImportDataList" resultType="String">
        SELECT
        distinct d.${column}
        FROM T_BO_TP_IMPORT_DATA d inner join T_BO_TRANS_ORDER TBTO on d.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID
        inner JOIN T_BO_ORDER_GROUP_REL tbogr ON TBTO.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        WHERE d.IS_del = 0 and TBTO.IS_DEL=0 and d.${column} is not null
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                        (
                        TBOGR.GROUP_ID =#{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND TBTO.USER_ID = #{scopeItem.userId}
                        </if>
                        )
                    </foreach>
                    )
                </if>
                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
                )
            </foreach>
            )
        </if>
    </select>


    <select id="queryDistinctOrderDataList" resultType="String">
        SELECT
        distinct TBTO.${column}
        from T_BO_TRANS_ORDER TBTO
        inner JOIN T_BO_ORDER_GROUP_REL tbogr ON TBTO.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        WHERE TBTO.IS_DEL=0 and TBTO.${column} is not null
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                        (
                        TBOGR.GROUP_ID =#{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND TBTO.USER_ID = #{scopeItem.userId}
                        </if>
                        )
                    </foreach>
                    )
                </if>
                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
                )
            </foreach>
            )
        </if>
    </select>

    <select id="queryDistinctOrderDataListFromOrdIdList" resultType="String">
        SELECT
        distinct TBTO.${column}
        from T_BO_TRANS_ORDER TBTO
        WHERE TBTO.IS_DEL=0 and TBTO.${column} is not null
        and (TBTO.ORG_ID IN
        <foreach collection="orgIds" index="index" item="orgId" open="(" separator="," close=")">
            <if test="(index % 999) == 998"> NULL) OR TBTO.ORG_ID IN(</if>#{orgId}
        </foreach>
        )
    </select>



    <select id="getCityListForAppQuery" resultType="com.wtyt.dao.bean.syf.BoPlaceBean">
            SELECT
	            provinceName,
	            cityName,
	            areaName
            FROM(SELECT
            <choose>
                <when test="searchKeywordsType == 1">
                    TBTO.START_PROVINCE_NAME provinceName,
                    TBTO.START_CITY_NAME cityName,
                    TBTO.START_COUNTY_NAME areaName
                </when>
                <otherwise>
                    TBTO.END_PROVINCE_NAME provinceName,
                    TBTO.END_CITY_NAME cityName,
                    TBTO.END_COUNTY_NAME areaName
                </otherwise>
            </choose>
        from T_BO_TRANS_ORDER TBTO
        LEFT JOIN T_BO_ORDER_GROUP_REL tbogr ON TBTO.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        WHERE TBTO.IS_DEL=0
         <choose>
            <when test="searchKeywordsType == 1">
             and   INSTR(TBTO.START_PROVINCE_NAME||TBTO.START_CITY_NAME||TBTO.START_COUNTY_NAME,#{searchKeyword}) > 0
            </when>
            <when test="searchKeywordsType == 2">
                and  INSTR(TBTO.END_PROVINCE_NAME||TBTO.END_CITY_NAME||TBTO.END_COUNTY_NAME,#{searchKeyword}) > 0
            </when>
            <otherwise>
                and  1 = 0
            </otherwise>
        </choose>
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                        (
                        TBOGR.GROUP_ID =#{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND TBTO.USER_ID = #{scopeItem.userId}
                        </if>
                        )
                    </foreach>
                    )
                </if>
                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
                )
            </foreach>
            )
        </if>)
         GROUP BY provinceName,cityName,areaName
    </select>

    <select id="queryTpImportDataById" resultType="java.util.Map">
        SELECT
        <foreach collection="list" item="item" close="" open="" separator=",">
            ${item}
        </foreach>
        FROM T_BO_TP_IMPORT_DATA d
        WHERE
        d.BO_TP_IMPORT_DATA_ID = #{importDataId}
    </select>

    <select id="selectConfigIdByOrderId" resultType="java.lang.String">
        SELECT BO_TP_DH_CONFIG_ID boTpDhConfigId FROM T_BO_TP_IMPORT_DATA WHERE BO_TRANS_ORDER_ID = #{boTransOrderId}
    </select>
</mapper>
