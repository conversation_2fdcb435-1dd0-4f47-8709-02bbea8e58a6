<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoStartWarningAlarmMapper">

    <select id="getNeedAlarmWaybillList" resultType="BoXxlJobAlarmBean">
        SELECT T.TAX_WAYBILL_ID taxWaybillId,
               T.TAX_WAYBILL_NO taxWaybillNo,
               T.BO_TRANS_TASK_ID boTransTaskId,
               T.ORG_ID orgId,
               T.CART_BADGE_NO cartBadgeNo,
               T.DRIVER_NAME driverName,
               T.MOBILE_NO mobileNo,
                tte.TRANS_VOUCHER transVoucher,
               TO_CHAR(<PERSON><PERSON>DEAD_<PERSON>INE_TIME, 'YYYY-MM-DD HH24:MI:SS') appointedArriveTime,
               TO_CHAR(L<PERSON>DEAD_LINE_TIME, 'YYYY-MM-DD HH24:MI:SS') comparedTime,
			   NVL(T.TRANS_PATTERN,1) transPattern,
               T.IS_PARTAKE_OPERATE isPartakeOperate,
               T.TRANS_MODE transMode,
               TA.TAX_WAYBILL_ID allocateTaxWaybillId
          FROM T_BO_TRANS_NODE_DEAD_LINE L
          LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TA
            ON L.BO_TRANS_TASK_ID = TA.BO_TRANS_TASK_ID and TA.IS_DEL = 0,
          T_BO_TRANS_TASK T,T_BO_TRANS_TASK_EXTRA tte
         WHERE L.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
           AND tte.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
           AND tte.IS_DEL =0
           AND (T.TRANSPORT_TYPE =0 OR T.TRANSPORT_TYPE IS NULL)
           AND T.CART_BADGE_NO IS NOT NULL
           AND L.NODE_ID = 500
           AND L.DEAD_LINE_TIME &lt; SYSDATE
           AND L.IS_DEL = 0
           AND T.START_TIME IS NULL
           AND T.IS_DEL = 0
           AND T.ORG_ID IN
           <foreach collection="list" item="item" close=")" open="(" separator=",">
               #{item}
           </foreach>
    </select>

    <select id="getArriveTimeForWaybillList" resultType="BoXxlJobAlarmBean">
        SELECT A.BO_TRANS_TASK_ID boTransTaskId,
               A.CREATED_TIME arriveTime
          FROM T_BO_TRANS_NODE_RECORD A,
               (SELECT BO_TRANS_TASK_ID, CREATED_TIME,TRANS_PATTERN
                FROM (SELECT R.BO_TRANS_TASK_ID,
                             R.CREATED_TIME,
                             NVL(T.TRANS_PATTERN,1) TRANS_PATTERN,
                             ROW_NUMBER() OVER(PARTITION BY R.BO_TRANS_TASK_ID ORDER BY R.CREATED_TIME DESC) RN
                      FROM T_BO_TRANS_NODE_RECORD R,T_BO_TRANS_TASK T
                      WHERE 
                        R.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
                        AND R.NODE_ID = 200
                        AND R.IS_DEL = 0
                        AND R.BO_TRANS_TASK_ID IN
                        <foreach collection="list" item="item" close=")" open="(" separator=",">
                            #{item}
                        </foreach>) T
                WHERE T.RN = 1) B
          WHERE A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID
            AND A.CREATED_TIME > B.CREATED_TIME
            AND ((B.TRANS_PATTERN =1 AND A.NODE_ID = 500)
            	OR
            	(B.TRANS_PATTERN =2 AND A.NODE_ID =400)
            )
            AND A.IS_DEL = 0
    </select>
    
    
    <select id="getKpiStartNeedAlarmWaybillList" resultType="BoXxlJobAlarmBean">
        SELECT
            W.BO_TRANS_TASK_ID boTransTaskId,
            L.TAX_WAYBILL_ID taxWaybillId,
            L.NODE_ID nodeId,
            TO_CHAR(L.KPI_DEAL_LINE_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
            SYSDATE realDeadLineTime,
            A.BO_TRANS_NODE_ALARM_ID boTransNodeAlarmId,
            NVL(A.NODE_DATA_TYPE,'4') nodeDataType,
            N.BOSS_NODE_CODE bossNodeCode
        FROM T_BO_TRANS_NODE_DEAD_LINE L
        LEFT JOIN T_BO_TRANS_NODE_ALARM A
            ON L.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
            AND L.NODE_ID = A.NODE_ID
            AND A.NODE_DATA_TYPE = 4
            AND A.ALARM_TYPE = #{alarmType}
            AND A.IS_DEL = 0
        LEFT JOIN T_BO_WAYBILL_BOSS_NODE N
            on L.BO_TRANS_TASK_ID = N.BO_TRANS_TASK_ID
            AND N.IS_DEL = 0
            ,T_BO_TRANS_TASK W,T_BO_TRANS_TASK_EXTRA TE
        WHERE L.BO_TRANS_TASK_ID = W.BO_TRANS_TASK_ID
            AND L.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
            AND L.NODE_ID = 600
        <if test="alarmType == '0'.toString()">
            AND L.KPI_DEAL_LINE_TIME >= SYSDATE
            AND L.KPI_DEAL_LINE_TIME &lt;= SYSDATE + #{lastKpiSendTimeBefore} / 24
        </if>
        <if test="alarmType == '1'.toString()">
            AND L.KPI_DEAL_LINE_TIME &lt; SYSDATE
        </if>
        <if test="dispatchedTime != null and dispatchedTime.length > 0">
            AND TE.DISPATCH_CAR_TIME <![CDATA[<]]> TO_DATE(#{dispatchedTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        AND L.IS_DEL = 0
        AND W.START_TIME IS NULL
        AND W.IS_DEL = 0
    </select>
</mapper>