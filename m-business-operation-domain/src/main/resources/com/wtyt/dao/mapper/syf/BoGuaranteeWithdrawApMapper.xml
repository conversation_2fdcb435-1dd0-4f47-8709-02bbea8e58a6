<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoGuaranteeWithdrawApMapper">

    <insert id="insertBoGuaranteeWithdrawAp">
        INSERT INTO T_BO_GUARANTEE_WITHDRAW_AP(BO_GUARANTEE_WITHDRAW_AP_ID,COMPANY_ID,COMPANY_NAME,APPLY_USER_ID,APPLY_USER_NAME,SYSROLE_TYPE,ORDER_NO,TOTAL_AMOUNT,SERVICE_FEE,BANK_CARD_NO,BANK_NAME)
        values(#{boGuaranteeWithdrawApId},#{companyId},#{companyName},#{applyUserId},#{applyUserName},#{sysroleType},#{orderNo},#{totalAmount},#{serviceFee},#{bankCardNo},#{bankName})
    </insert>

    <update id="updateBoGuaranteeWithdrawApInvalid">
        UPDATE T_BO_GUARANTEE_WITHDRAW_AP SET WITHDRAW_STATUS = 9, LAST_MODIFIED_TIME = SYSDATE WHERE BO_GUARANTEE_WITHDRAW_AP_ID = #{boGuaranteeWithdrawApId}
    </update>

    <update id="updateBoGuaranteeWithdrawApSuccess">
        UPDATE T_BO_GUARANTEE_WITHDRAW_AP SET WITHDRAW_STATUS = 1, LAST_MODIFIED_TIME = SYSDATE WHERE BO_GUARANTEE_WITHDRAW_AP_ID = #{boGuaranteeWithdrawApId}
    </update>

    <update id="updateBoGuaranteeWithdrawApFail">
        UPDATE T_BO_GUARANTEE_WITHDRAW_AP SET WITHDRAW_STATUS = 2, LAST_MODIFIED_TIME = SYSDATE WHERE BO_GUARANTEE_WITHDRAW_AP_ID = #{boGuaranteeWithdrawApId}
    </update>

    <select id="getAllWaitWithdrawList" resultType="com.wtyt.dao.bean.syf.BoGuaranteeWithdrawApBean">
        SELECT
        BO_GUARANTEE_WITHDRAW_AP_ID boGuaranteeWithdrawApId,
        ORDER_NO orderNo
        FROM T_BO_GUARANTEE_WITHDRAW_AP
        WHERE WITHDRAW_STATUS = 0 AND IS_DEL = 0
    </select>

</mapper>
