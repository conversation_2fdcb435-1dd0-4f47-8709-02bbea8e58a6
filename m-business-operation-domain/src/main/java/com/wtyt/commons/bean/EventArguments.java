package com.wtyt.commons.bean;

import com.wtyt.common.enums.ChannelEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023年07月12日 19时00分
 */
@Setter
@Getter
public class EventArguments implements Serializable {

    private static final long serialVersionUID = -1652896102087970887L;
    /**
     * 处理计数器
     */
    private int processCounter;
    /**
     * 重试计数器
     */
    private Integer retryCounter;
    /**
     * 是否开启重试
     * 初始化的时候设置、后续请勿随便修改、以免影响后续重试机制
     */
    private Boolean retry;
    /**
     * 重试次数、-1为无限次重试
     * 初始化的时候设置、后续请勿随便修改、以免影响后续重试机制
     */
    private Integer retryCount;
    /**
     * 重试间隔
     * 初始化的时候设置、后续请勿随便修改、以免影响后续重试机制
     */
    private Integer retryInterval;
    /**
     * 重新间隔单位、默认：分
     * 仅支持秒（SECONDS）、分钟（MINUTES）、小时（HOURS）。不设置为分、设置超过范围为小时
     */
    private String retryIntervalUnit;
    /**
     * 普通事件处理时间
     */
    private String generalProcessTime;
    /**
     * 告警事件处理时间
     */
    private String alarmProcessTime;
    /**
     * 消息事件处理时间
     */
    private String messageProcessTime;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 运输任务ID
     */
    private String boTransTaskId;
    /**
     * 运单ID
     */
    private String taxWaybillId;
    /**
     * 订单ID
     */
    private String boTransOrderId;
    /**
     * 随车清单ID
     */
    private String boShippingListId;
    private String boTaskOrderRemittanceId;
    private String boTaskDriverGuaranteeId;
    private String nodeId;
    private String nodeTime;
    /**
     * 节点异常扩展信息
     */
    private String nodeAlarmExtend;
    private List<String> messagePushConfigIdList;
    private ChannelEnum channel;
    /**
     * 定位类型
     * 0-到场前定位、1-到场后发车前定位、2-发车后定位
     * -1-取消定位、0-切换合作模式定位、1-启动定位(到场前&到场后发车前&发车后)、2-修改定位(到场前&到场后发车前&发车后)
     */
    private String lbsType;
    /**
     * 距离
     */
    private String distance;
    /**
     * 手机号
     * 如果未传司机信息这里默认把当前的司机信息带入的事件参数当中
     */
    private String mobileNo;
    /**
     * 车牌号
     * 如果未传司机信息这里默认把当前的司机信息带入的事件参数当中
     */
    private String cartBadgeNo;
    /**
     * 原因
     */
    private String cause;
    /**
     * 间隔时间
     */
    private String intervalTime;
    /**
     * 已完成的步骤、目前用于lbs服务
     */
    private Set<String> finishedStepList;
    /**
     * 截止时间
     */
    private String deadLineTime;
    /**
     * 自动触发节点上报的时间
     */
    private String nodeUploadTime;

    public void setRetry(Boolean retry) {
        this.retry = retry;
        if (Boolean.TRUE.equals(this.retry)) {
            if (this.retryCounter == null) {
                this.retryCounter = 0;
            }
            if (this.retryIntervalUnit == null) {
                this.retryIntervalUnit = TimeUnit.MINUTES.name();
            }
        }
    }
}