<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoAcceptedTimeoutDeleteMapper">
    
    <select id="getAcceptedTimeoutTaskList" resultType="BoTaskDetailBean" fetchSize="100">
        SELECT
            T1.BO_TRANS_TASK_ID boTransTaskId,
            T1.ORG_ID orgId,
            T1.TAX_WAYBILL_NO taxWaybillNo,
            T1.TAX_WAYBILL_ID taxWaybillId,
            T1.ORDER_CREATE_TYPE orderCreateType
        FROM T_BO_TRANS_TASK T1
        WHERE T1.IS_DEL = 0
        AND T1.HYB_STATE = 0
        AND T1.STATE = 0
        AND T1.CREATED_TIME <![CDATA[<]]> SYSDATE - (#{timeout} / 24 / 60)
        <if test="orgIdList != null and orgIdList.size() > 0">
            AND T1.ORG_ID IN
            <foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="orderCreateTypeList != null and orderCreateTypeList.size() > 0">
            AND T1.ORDER_CREATE_TYPE IN
            <foreach collection="orderCreateTypeList" item="orderCreateType" open="(" separator="," close=")">
                #{orderCreateType}
            </foreach>
        </if>
        ORDER BY T1.CREATED_TIME
    </select>

</mapper>