<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoBatchRecDetailMapper">

    <resultMap id="BaseResultMap" type="com.wtyt.dao.bean.syf.BoBatchRecDetailBean">
            <id property="boBatchRecDetailId" column="BO_BATCH_REC_DETAIL_ID" jdbcType="DECIMAL"/>
            <result property="boBatchRecId" column="BO_BATCH_REC_ID" jdbcType="DECIMAL"/>
            <result property="businessId" column="BUSINESS_ID" jdbcType="DECIMAL"/>
            <result property="optType" column="OPT_TYPE" jdbcType="DECIMAL"/>
            <result property="resultStatus" column="RESULT_STATUS" jdbcType="DECIMAL"/>
            <result property="resultInfo" column="RESULT_INFO" jdbcType="VARCHAR"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP"/>
            <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        BO_BATCH_REC_DETAIL_ID,BO_BATCH_REC_ID,BUSINESS_ID,
        RESULT_STATUS,RESULT_INFO,IS_DEL,
        TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') CREATED_TIME,TO_CHAR(LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI:SS') LAST_MODIFIED_TIME,
        OPT_TYPE,
        NOTE
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_BO_BATCH_REC_DETAIL
        where  BO_BATCH_REC_DETAIL_ID = #{boBatchRecDetailId,jdbcType=DECIMAL} 
    </select>


    <insert id="insert" keyColumn="BO_BATCH_REC_DETAIL_ID" keyProperty="boBatchRecDetailId" parameterType="com.wtyt.dao.bean.syf.BoBatchRecDetailBean" useGeneratedKeys="true">
        insert into T_BO_BATCH_REC_DETAIL
        ( BO_BATCH_REC_DETAIL_ID,BO_BATCH_REC_ID,BUSINESS_ID
        ,RESULT_STATUS,RESULT_INFO,BUSINESS_TYPE,IS_DEL
        ,CREATED_TIME,LAST_MODIFIED_TIME,NOTE
        )
        values (${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},#{boBatchRecId,jdbcType=DECIMAL},#{businessId,jdbcType=DECIMAL}
        ,#{resultStatus,jdbcType=DECIMAL},#{resultInfo,jdbcType=VARCHAR},#{businessType,jdbcType=DECIMAL},#{isDel,jdbcType=DECIMAL}
        ,#{createdTime,jdbcType=TIMESTAMP},#{lastModifiedTime,jdbcType=TIMESTAMP},#{note,jdbcType=VARCHAR}
        )
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wtyt.dao.bean.syf.BoBatchRecDetailBean">
        update T_BO_BATCH_REC_DETAIL
                <if test="boBatchRecId != null">
                    BO_BATCH_REC_ID = #{boBatchRecId,jdbcType=DECIMAL},
                </if>
                <if test="businessId != null">
                    BUSINESS_ID = #{businessId,jdbcType=DECIMAL},
                </if>
                <if test="businessType != null">
                    BUSINESS_TYPE = #{businessType,jdbcType=DECIMAL}
                </if>
                <if test="resultStatus != null">
                    RESULT_STATUS = #{resultStatus,jdbcType=DECIMAL},
                </if>
                <if test="resultInfo != null">
                    RESULT_INFO = #{resultInfo,jdbcType=VARCHAR},
                </if>
                <if test="isDel != null">
                    IS_DEL = #{isDel,jdbcType=DECIMAL},
                </if>
                <if test="createdTime != null">
                    CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="note != null">
                    NOTE = #{note,jdbcType=VARCHAR},
                </if>
        LAST_MODIFIED_TIME = #{lastModifiedTime,jdbcType=TIMESTAMP}
        where   BO_BATCH_REC_DETAIL_ID = #{boBatchRecDetailId,jdbcType=DECIMAL} 
    </update>

    <insert id="batchInsert" parameterType="com.wtyt.dao.bean.syf.BoBatchRecDetailBean">
        insert into T_BO_BATCH_REC_DETAIL
        ( BO_BATCH_REC_DETAIL_ID,BO_BATCH_REC_ID,BUSINESS_ID
        ,RESULT_STATUS,RESULT_INFO,BUSINESS_TYPE,OPT_TYPE
        ,IS_DEL,CREATED_TIME,LAST_MODIFIED_TIME
        )
        SELECT A.*
        FROM(
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT #{item.boBatchRecDetailId} BO_BATCH_REC_DETAIL_ID, #{item.boBatchRecId} BO_BATCH_REC_ID
            , #{item.businessId} BUSINESS_ID,#{item.resultStatus} RESULT_STATUS,
            #{item.resultInfo} RESULT_INFO,#{item.businessType} BUSINESS_TYPE,#{item.optType} OPT_TYPE
            ,0 IS_DEL,
            SYSDATE CREATED_TIME,
            SYSDATE LAST_MODIFIED_TIME
            FROM
            DUAL
        </foreach>
        ) A
    </insert>

    <select id="listByBoBatchRecId" resultMap="BaseResultMap">
        SELECT
        BUSINESS_ID,
        RESULT_STATUS,
        OPT_TYPE
        FROM
        T_BO_BATCH_REC_DETAIL
        WHERE
        IS_DEL = 0
        AND BO_BATCH_REC_ID = #{boBatchRecId}
    </select>

</mapper>
