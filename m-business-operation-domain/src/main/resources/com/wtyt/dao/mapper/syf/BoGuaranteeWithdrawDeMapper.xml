<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoGuaranteeWithdrawDeMapper">

    <insert id="insertBatchBoGuaranteeWithdrawDe">
        INSERT INTO T_BO_GUARANTEE_WITHDRAW_DE(BO_GUARANTEE_WITHDRAW_DE_ID, BO_GUARANTEE_WITHDRAW_AP_ID, BO_TASK_DRIVER_GUARANTEE_ID)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boGuaranteeWithdrawDeId} BO_BOARD_PROGRESS_ID,
            #{item.boGuaranteeWithdrawApId} BO_BUSINESS_PROJECT_ID,
            #{item.boTaskDriverGuaranteeId} BO_BUSINESS_LINE_ID
            FROM
            DUAL
        </foreach>
    </insert>

    <select id="queryBoTaskDriverGuaranteeIdList" resultType="java.lang.String">
        SELECT BO_TASK_DRIVER_GUARANTEE_ID FROM T_BO_GUARANTEE_WITHDRAW_DE WHERE IS_DEL = 0 AND BO_GUARANTEE_WITHDRAW_AP_ID = #{boGuaranteeWithdrawApId}
    </select>
</mapper>
