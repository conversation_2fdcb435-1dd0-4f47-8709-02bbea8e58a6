package com.wtyt.commons.context;

import com.wtyt.bo.bean.BoTaskDetailBean;
import com.wtyt.common.enums.*;
import com.wtyt.common.rpc.bean.Rpc22094OBean;
import com.wtyt.commons.bean.EventArguments;
import com.wtyt.dao.bean.syf.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年06月14日 22时32分
 */
@Setter
@Getter
public class ProcessContext implements Serializable {

    private static final long serialVersionUID = -6385933253162914700L;
    private EventProcessorEnum eventProcessor;
    private List<EventTypeEnum> eventTypeList;
    private NodeEventNameEnum nodeEventName;
    private NodeDataTypeEnum nodeDataType;
    private BossNodeEnum bossNode;
    private BoTaskDetailBean boTaskDetailBean;
    private AlarmTypeEnum alarmType;
    private EventEnum domainEvent;
    /**
     * 表示是否加载过运输任务详情
     */
    private boolean loadTaskDetail;
    /**
     * 表示是否加载过消息配置
     */
    private boolean loadMessageConfig;
    /**
     * 是否需要处理异常/消息
     */
    private boolean isNeedProcessAlarm;
    private boolean isNeedProcessMessage;
    /**
     * 用于存储上下文当中跨方法多次查询的内容
     */
    private Map<String, Object> cache;
    private List<Rpc22094OBean> messageConfigList;
    private String expectExecTime;
    private String messageLinkUrl;
    private EventArguments eventArguments;
    private BoTaskDriverGuaranteeBean boTaskDriverGuaranteeBean;
    private BoTransNodeAlarmBean boTransNodeAlarmBean;

    /**
     * 放入缓存
     * @param key
     * @param value
     */
    public <T> void putCache(CacheKeyEnum key, T value) {
        if (this.cache == null) {
            this.cache = new HashMap<>(16);
        }
        this.cache.put(key.name(), value);
    }

    /**
     * 获取缓存
     * @param key
     * @return
     */
    public <T> List<T> getCacheList(CacheKeyEnum key) {
        if (this.cache != null) {
            return (List<T>)this.cache.get(key.name());
        }
        return null;
    }

    /**
     * 获取缓存
     * @param key
     * @return
     */
    public <T> T getCache(CacheKeyEnum key) {
        if (this.cache != null) {
            return (T)this.cache.get(key.name());
        }
        return null;
    }
}