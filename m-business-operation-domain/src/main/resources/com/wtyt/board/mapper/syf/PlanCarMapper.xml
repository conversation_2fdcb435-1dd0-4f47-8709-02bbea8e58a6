<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.PlanCarMapper">

    <select id="queryLineUseCart" resultType="com.wtyt.board.plancar.bean.LineUseCartBean">
        SELECT
            TO_CHAR(O.START_TIME, 'YYYY-MM-DD') statDate,
            TO_CHAR(O.END_TIME, 'YYYY-MM-DD') endDate,
            P.BO_BUSINESS_LINE_ID boBusinessLineId,
            P.TOTAL_USE_CART_NUM  totalUseCartNum
        FROM
            T_BO_TRANS_ORDER O
        INNER JOIN T_BO_TRANS_ORDER_PROGRESS P ON
            P.BO_TRANS_ORDER_ID = O.BO_TRANS_ORDER_ID
        WHERE
            O.IS_DEL = 0 AND P.IS_DEL =0
            AND P.STATE !=0
            AND P.BO_BUSINESS_LINE_ID IN
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
    </select>


    <select id="todayReceiveTaskCount" resultType="Integer" parameterType="com.wtyt.board.plancar.bean.LineUseCartBean">
        SELECT count(1)
        FROM T_BO_TRANS_TASK tt
        left join T_BO_TRANS_TASK_EXTRA tte on tt.BO_TRANS_TASK_ID = tte.BO_TRANS_TASK_ID
        WHERE
         tt.is_del =0 and tte.is_del =0
        AND TT.BO_BUSINESS_LINE_ID =#{boBusinessLineId}
        AND tte.HYB_RECEIVED_TIME  &gt;= TO_DATE(#{today}||'00:00:00','YYYY-MM-DD HH24:MI:SS')
        AND tte.HYB_RECEIVED_TIME  &lt;= TO_DATE(#{today}||'23:59:59','YYYY-MM-DD HH24:MI:SS')
    </select>




</mapper>
