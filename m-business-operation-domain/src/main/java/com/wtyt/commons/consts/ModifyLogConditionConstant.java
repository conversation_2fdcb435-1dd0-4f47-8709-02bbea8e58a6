package com.wtyt.commons.consts;
/**
 * 
 * @ClassName: ModifyLogConditionConstant
 * @Description:日志修改记录条件常量表达式
 * <AUTHOR>
 * @date 2022年4月21日
 *
 */
public class ModifyLogConditionConstant {
	/*************************************WBBO*******************************************/
	//上下游服务配置
	public final static String WBBO_UDSERVICE_EXPRESSION="\"8\".equals(newInstance.getUdService())";
	
	public final static String WBBO_SHARE_UDSERVICE_EXPRESSION="\"8\".equals(oldInstance.getUdService())";
	
	public final static String WBBO_SHARE_BUSINESSITEMS_EXPRESSION="\"3\".equals(oldInstance.getBusinessItems())";
	
	/*************************************SWAI*******************************************/
	//上下游服务配置
	public final static String SWAI_UDSERVICE_EXPRESSION="\"8\".equals(newInstance.getUserBean().getUdService())";
	
	public final static String SWAI_SHARE_UDSERVICE_EXPRESSION="\"8\".equals(oldInstance.getUserBean().getUdService())";
	
	public final static String SWAI_SHARE_TRANSPORTTYPE_ZH_EXPRESSION="!\"1\".equals(oldInstance.getTransportType())";
	
}
