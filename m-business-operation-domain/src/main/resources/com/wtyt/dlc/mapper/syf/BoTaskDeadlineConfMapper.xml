<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dlc.mapper.syf.BoTaskDeadlineConfMapper">

    <resultMap type="com.wtyt.dlc.bean.BoTaskDeadlineConfBean" id="BoTaskDeadlineConfMap">
        <result property="boTaskDeadlineConfId" column="BO_TASK_DEADLINE_CONF_ID" jdbcType="VARCHAR"/>
        <result property="configType" column="CONFIG_TYPE" jdbcType="VARCHAR"/>
        <result property="state" column="STATE" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="configLevel" column="CONFIG_LEVEL" jdbcType="VARCHAR"/>
        <result property="timeFormat" column="TIME_FORMAT" jdbcType="VARCHAR"/>
        <result property="maxIntervalDays" column="MAX_INTERVAL_DAYS" jdbcType="VARCHAR"/>
        <result property="requiredConfigType" column="REQUIRED_CONFIG_TYPE" jdbcType="VARCHAR"/>
        <result property="requiredConfigRole" column="REQUIRED_CONFIG_ROLE" jdbcType="VARCHAR"/>
        <result property="modifyType" column="MODIFY_TYPE" jdbcType="VARCHAR"/>
        <result property="calculateType" column="CALCULATE_TYPE" jdbcType="VARCHAR"/>
        <result property="addDay" column="ADD_DAY" jdbcType="VARCHAR"/>
        <result property="timeHour" column="TIME_HOUR" jdbcType="VARCHAR"/>
        <result property="hourPerKm" column="HOUR_PER_KM" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="configItem" column="CONFIG_ITEM" jdbcType="VARCHAR"/>
        <result property="modifyRole" column="MODIFY_ROLE" jdbcType="VARCHAR"/>
        <result property="lastModifiedUserName" column="LAST_MODIFIED_USER_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="commonField">
        BO_TASK_DEADLINE_CONF_ID,
        ORG_ID,
        CONFIG_TYPE,
        STATE,
        CONFIG_LEVEL,
        CONFIG_ITEM,
        TIME_FORMAT,
        MAX_INTERVAL_DAYS,
        REQUIRED_CONFIG_TYPE,
        REQUIRED_CONFIG_ROLE,
        MODIFY_TYPE,
        MODIFY_ROLE,
        CALCULATE_TYPE,
        ADD_DAY,
        TIME_HOUR,
        CASE WHEN TRUNC(HOUR_PER_KM)=HOUR_PER_KM THEN TO_CHAR(HOUR_PER_KM, 'FM999999990') ELSE TO_CHAR(HOUR_PER_KM, 'FM999999990.9999') END HOUR_PER_KM
    </sql>

    <select id="list" resultMap="BoTaskDeadlineConfMap">
        SELECT
            <include refid="commonField"/>
        FROM
        T_BO_TASK_DEADLINE_CONF
        WHERE
        IS_DEL =0
        AND ORG_ID =#{orgId}
        AND CONFIG_TYPE =#{configType}
        ORDER BY DECODE(CONFIG_LEVEL,1,1,2) ASC,CREATED_TIME DESC
    </select>

    <select id="countExist" resultType="int">
        SELECT
            COUNT(*)
        FROM
        T_BO_TASK_DEADLINE_CONF
        WHERE
        IS_DEL =0
        AND ORG_ID =#{orgId}
        AND CONFIG_TYPE =#{configType}
        AND CONFIG_LEVEL =#{configLevel}
        <if test="configLevel!='1'.toString()">
            AND CONFIG_ITEM =#{configItem}
        </if>
    </select>

    <select id="countExistByOrgId" resultType="int">
        SELECT
        COUNT(*)
        FROM
        T_BO_TASK_DEADLINE_CONF
        WHERE
        IS_DEL =0
        AND ORG_ID =#{orgId}
        AND CONFIG_TYPE =#{configType}
    </select>

    <insert id="save">
        INSERT INTO T_BO_TASK_DEADLINE_CONF
        (BO_TASK_DEADLINE_CONF_ID, ORG_ID, CONFIG_TYPE, STATE, CONFIG_LEVEL, CONFIG_ITEM, TIME_FORMAT, MAX_INTERVAL_DAYS, REQUIRED_CONFIG_TYPE, REQUIRED_CONFIG_ROLE, MODIFY_TYPE, MODIFY_ROLE, CALCULATE_TYPE, ADD_DAY, TIME_HOUR, HOUR_PER_KM, LAST_MODIFIED_USER_NAME)
        VALUES(#{boTaskDeadlineConfId}, #{orgId}, #{configType}, #{state}, #{configLevel}, #{configItem}, #{timeFormat}, #{maxIntervalDays}, #{requiredConfigType}, #{requiredConfigRole}, #{modifyType}, #{modifyRole}, #{calculateType}, #{addDay}, #{timeHour}, #{hourPerKm}, #{lastModifiedUserName})
    </insert>

    <update id="delete">
        UPDATE
        T_BO_TASK_DEADLINE_CONF
        SET
        IS_DEL = 1,
        LAST_MODIFIED_TIME = SYSDATE,
        LAST_MODIFIED_USER_NAME = #{lastModifiedUserName}
        WHERE
        BO_TASK_DEADLINE_CONF_ID = #{boTaskDeadlineConfId}
        AND IS_DEL =0

    </update>

    <select id="getById" resultMap="BoTaskDeadlineConfMap">
        SELECT
            <include refid="commonField"/>
        FROM
        T_BO_TASK_DEADLINE_CONF
        WHERE
        IS_DEL =0
        AND BO_TASK_DEADLINE_CONF_ID =#{boTaskDeadlineConfId}
    </select>

    <update id="update">
        update T_BO_TASK_DEADLINE_CONF
        <set>
            <if test="state != null and state != ''">
                STATE = #{state},
            </if>
            <if test="timeFormat != null">
                TIME_FORMAT = #{timeFormat},
            </if>
            <if test="maxIntervalDays != null">
                MAX_INTERVAL_DAYS = #{maxIntervalDays},
            </if>
            <if test="requiredConfigType != null and requiredConfigType != ''">
                REQUIRED_CONFIG_TYPE = #{requiredConfigType},
            </if>
            <if test="requiredConfigRole != null">
                REQUIRED_CONFIG_ROLE = #{requiredConfigRole},
            </if>
            <if test="modifyType != null and modifyType != ''">
                MODIFY_TYPE = #{modifyType},
            </if>
            <if test="calculateType != null and calculateType != ''">
                CALCULATE_TYPE = #{calculateType},
            </if>
            <if test="addDay != null">
                ADD_DAY = #{addDay},
            </if>
            <if test="timeHour != null">
                TIME_HOUR = #{timeHour},
            </if>
            <if test="hourPerKm != null">
                HOUR_PER_KM = #{hourPerKm},
            </if>
            <if test="modifyRole != null">
                MODIFY_ROLE = #{modifyRole},
            </if>
            <if test="lastModifiedUserName != null and lastModifiedUserName != ''">
                LAST_MODIFIED_USER_NAME = #{lastModifiedUserName},
            </if>
            LAST_MODIFIED_TIME = SYSDATE
        </set>
        where
            BO_TASK_DEADLINE_CONF_ID = #{boTaskDeadlineConfId}
            and IS_DEL =0
    </update>

    <select id="queryOrgAndWbItemConfig" resultMap="BoTaskDeadlineConfMap">
        SELECT
        <include refid="commonField"/>
        FROM
        T_BO_TASK_DEADLINE_CONF
        WHERE
        IS_DEL = 0
        AND CONFIG_TYPE = #{configType}
        AND STATE = 1
        AND ORG_ID = #{orgId}
        AND (
        CONFIG_LEVEL = 1
        <if test="wbItem!=null and wbItem!=''">
            OR
            (CONFIG_LEVEL = 2 AND CONFIG_ITEM = #{wbItem})
        </if>
        )
        ORDER BY CONFIG_LEVEL DESC
    </select>

    <select id="listConfiguredOrgIds" resultType="java.lang.String">
        SELECT DISTINCT ORG_ID FROM T_BO_TASK_DEADLINE_CONF
    </select>


</mapper>

