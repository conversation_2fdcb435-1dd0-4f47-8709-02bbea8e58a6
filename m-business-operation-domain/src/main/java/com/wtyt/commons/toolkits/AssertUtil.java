package com.wtyt.commons.toolkits;

import com.wtyt.money.commons.exception.BaseTipException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Date;
import java.util.Objects;


/**
 * 
 * @ClassName: AssertUtil
 * @Description: 断言工具类
 * <AUTHOR>
 * @date 2021年10月22日
 *
 */
public class AssertUtil {
   private static Logger log= LoggerFactory.getLogger(AssertUtil.class);
   public static void notNull(Object obj,String message) throws BaseTipException {
		if(Objects.isNull(obj)) {
			throw new BaseTipException(message);
		}
	}
	
	public static void notBlank(String str,String message) throws BaseTipException{
		if(StringUtils.isBlank(str)) {
			throw new BaseTipException(message);
		}
	}
	
	public static void notEmpty(Collection collection,String message) throws BaseTipException{
		if(CollectionUtils.isEmpty(collection)) {
			throw new BaseTipException(message);
		}
	}
	
	public static void before(Date start,Date end,String message) throws BaseTipException{
		if(!start.before(end)) {
			throw new BaseTipException(message);
		}
	}
	
	public static void beforeEquals(Date start,Date end,String message) throws BaseTipException{
		if(start.after(end)) {
			throw new BaseTipException(message);
		}
	}
	
	public static void limit(Collection collection,int limit,String message) throws BaseTipException {
		if(collection !=null ) {
			if(collection.size()>limit) {
				throw new BaseTipException(message);
			}
		}
	}
	
	
	public static void equals(String origin,String expect,String throwMessage,String logMessage)throws BaseTipException{
		if((origin==null && expect!=null)||
				(origin!=null && expect==null)||
				 origin==null && expect==null) {
			log.error(logMessage);
			throw new BaseTipException(throwMessage);
		}
		if(!expect.equals(origin)) {
			log.error(logMessage);
			throw new BaseTipException(throwMessage);
		}
	}
	
	
	public static void assertTrue(boolean flag,String message)throws BaseTipException{
		if(!flag) {
			throw new BaseTipException(message);
		}
	}
	
	public static void assertFalse(boolean flag,String message)throws BaseTipException{
		if(flag) {
			throw new BaseTipException(message);
		}
	}
}
