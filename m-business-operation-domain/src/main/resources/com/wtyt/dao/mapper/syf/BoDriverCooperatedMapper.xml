<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoDriverCooperatedMapper">

    <insert id="insertDriverCooperated">
        INSERT INTO T_BO_DRIVER_COOPERATED (
            BO_DRIVER_COOPERATED_ID,
            TYPE,
            COMPANY_ID,
            ORG_ID,
            DRIVER_NAME,
            MOBILE_NO,
            CART_BADGE_NO,
            LAST_COOPERATED_TIME
        ) VALUES (
            #{boDriverCooperatedId},
            #{type},
            #{companyId},
            #{orgId},
            #{driverName},
            #{mobileNo},
            UPPER(#{cartBadgeNo}),
            TO_DATE(#{lastCooperatedTime}, 'yyyy-mm-dd hh24:mi:ss')
        )
    </insert>

    <insert id="batchInsertDriverCooperated">
        MER<PERSON> INTO T_BO_DRIVER_COOPERATED T1
        USING (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
                #{item.boDriverCooperatedId} AS BO_DRIVER_COOPERATED_ID,
                #{item.companyId} AS COMPANY_ID,
                #{item.orgId} AS ORG_ID,
                #{item.type} AS TYPE,
                #{item.driverName} AS DRIVER_NAME,
                #{item.mobileNo} AS MOBILE_NO,
                #{item.cartBadgeNo} AS CART_BADGE_NO,
                #{item.cartBadgeColor} AS CART_BADGE_COLOR,
                #{item.cartLength} AS CART_LENGTH,
                #{item.cartType} AS CART_TYPE,
                #{item.dataChannel} AS DATA_CHANNEL,
                #{item.requestSource} AS REQUEST_SOURCE
            FROM DUAL
        </foreach>
        ) T2 ON (T1.COMPANY_ID = T2.COMPANY_ID
            AND T1.TYPE = T2.TYPE
            AND ((T1.DRIVER_NAME IS NULL AND T2.DRIVER_NAME IS NULL) OR (T1.DRIVER_NAME = T2.DRIVER_NAME))
            AND T1.MOBILE_NO = T2.MOBILE_NO
            AND T1.CART_BADGE_NO = T2.CART_BADGE_NO
            AND T1.IS_DEL = 0)
        WHEN MATCHED THEN
            UPDATE SET
                T1.CART_LENGTH = TO_NUMBER(T2.CART_LENGTH),
                T1.CART_TYPE = T2.CART_TYPE,
                T1.CART_BADGE_COLOR = T2.CART_BADGE_COLOR,
                T1.LAST_MODIFIED_TIME =
                CASE WHEN T1.CART_LENGTH <![CDATA[<>]]> T2.CART_LENGTH
                          OR T1.CART_TYPE <![CDATA[<>]]> T2.CART_TYPE
                          OR T1.CART_BADGE_COLOR <![CDATA[<>]]> T2.CART_BADGE_COLOR THEN SYSDATE
                ELSE T1.LAST_MODIFIED_TIME END
        WHEN NOT MATCHED THEN
            INSERT (
                T1.BO_DRIVER_COOPERATED_ID,
                T1.COMPANY_ID,
                T1.ORG_ID,
                T1.TYPE,
                T1.DRIVER_NAME,
                T1.MOBILE_NO,
                T1.CART_BADGE_NO,
                T1.CART_BADGE_COLOR,
                T1.CART_LENGTH,
                T1.CART_TYPE,
                T1.DATA_CHANNEL,
                T1.REQUEST_SOURCE
            ) VALUES (
                T2.BO_DRIVER_COOPERATED_ID,
                T2.COMPANY_ID,
                T2.ORG_ID,
                T2.TYPE,
                T2.DRIVER_NAME,
                T2.MOBILE_NO,
                T2.CART_BADGE_NO,
                T2.CART_BADGE_COLOR,
                T2.CART_LENGTH,
                T2.CART_TYPE,
                T2.DATA_CHANNEL,
                T2.REQUEST_SOURCE
            )
    </insert>

    <select id="getTerminatePartnershipDriverByCompanyDriver" resultType="boDriverCooperatedBean">
        SELECT
            T.BO_DRIVER_COOPERATED_ID boDriverCooperatedId,
            T.TYPE type,
            T.COMPANY_ID companyId,
            T.ORG_ID orgId,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo
        FROM T_BO_DRIVER_COOPERATED T
        WHERE T.IS_DEL = 0
        AND T.TYPE = 1
        <choose>
            <when test="driverName != null and driverName.length > 0">
                AND T.DRIVER_NAME = #{driverName}
            </when>
            <otherwise>
                AND T.DRIVER_NAME IS NULL
            </otherwise>
        </choose>
        AND T.CART_BADGE_NO = UPPER(#{cartBadgeNo})
        AND T.MOBILE_NO = #{mobileNo}
        AND (T.COMPANY_ID = #{companyId} OR T.COMPANY_ID = -1)
    </select>

    <select id="getTerminatePartnershipDriverByCompanyId" resultType="boDriverCooperatedBean">
        SELECT
            T.BO_DRIVER_COOPERATED_ID boDriverCooperatedId,
            T.TYPE type,
            T.COMPANY_ID companyId,
            T.ORG_ID orgId,
            T.DRIVER_NAME driverName,
            T.MOBILE_NO mobileNo,
            T.CART_BADGE_NO cartBadgeNo
        FROM T_BO_DRIVER_COOPERATED T
        WHERE T.IS_DEL = 0
        AND T.TYPE = 1
        AND (T.COMPANY_ID = #{companyId} OR T.COMPANY_ID = -1)
    </select>

    <update id="updateDriverCooperatedDeletedByTerminatePartnership">
        UPDATE
            T_BO_DRIVER_COOPERATED T
        SET T.IS_DEL = 1,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.TYPE = 1
        <choose>
            <when test="driverName != null and driverName.length > 0">
                AND T.DRIVER_NAME = #{driverName}
            </when>
            <otherwise>
                AND T.DRIVER_NAME IS NULL
            </otherwise>
        </choose>
        AND T.CART_BADGE_NO = UPPER(#{cartBadgeNo})
        AND T.MOBILE_NO = #{mobileNo}
        AND T.COMPANY_ID = #{companyId}
    </update>

</mapper>