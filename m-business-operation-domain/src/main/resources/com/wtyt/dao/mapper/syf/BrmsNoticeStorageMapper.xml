<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BrmsNoticeStorageMapper">

    <resultMap type="BrmsNoticeStorageBean" id="baseResultMap">
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="storageName" column="STORAGE_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getNoticeStorageListByOrg" resultMap="baseResultMap">
        SELECT
            T.ORG_ID,
            T.STORAGE_NAME
        FROM T_BRMS_NOTICE_STORAGE T
        WHERE T.IS_DEL = 0
        AND T.CAN_PUSH_NOTICE = 1
        AND T.ORG_ID = #{orgId}
    </select>

    <select id="getNoticeStorageListByOrgList" resultMap="baseResultMap">
        SELECT
            T.ORG_ID,
            T.STORAGE_NAME
        FROM T_BRMS_NOTICE_STORAGE T
        WHERE T.IS_DEL = 0
        AND T.CAN_PUSH_NOTICE = 1
        AND T.ORG_ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>