<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskFeeMapper">
    <resultMap type="com.wtyt.dao.bean.syf.BoTransTaskFeeBean" id="TBoTransTaskFeeMap">
        <result property="boTransTaskFeeId" column="BO_TRANS_TASK_FEE_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="configKey" column="CONFIG_KEY" jdbcType="VARCHAR"/>
        <result property="configName" column="CONFIG_NAME" jdbcType="VARCHAR"/>
        <result property="configValue" column="CONFIG_VALUE" jdbcType="VARCHAR"/>
        <result property="expensiveType" column="EXPENSIVE_TYPE" jdbcType="VARCHAR"/>
        <result property="actualValue" column="ACTUAL_VALUE" jdbcType="DECIMAL"/>
        <result property="confirmStatus" column="CONFIRM_STATUS" jdbcType="DECIMAL"/>
        <result property="reason" column="REASON" jdbcType="VARCHAR"/>
        <result property="isApproval" column="IS_APPROVAL" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="isDisplay" column="IS_DISPLAY" jdbcType="VARCHAR"/>
        <result property="isVerify" column="IS_VERIFY" jdbcType="VARCHAR"/>
        <result property="isModify" column="IS_MODIFY" jdbcType="VARCHAR"/>
        <result property="settleVerifyStatus" column="SETTLE_VERIFY_STATUS" jdbcType="VARCHAR"/>
        <result property="isUploadVoucher" column="IS_UPLOAD_VOUCHER" jdbcType="VARCHAR"/>
        <result property="boTransFeeVerifyTaskId" column="BO_TRANS_FEE_VERIFY_TASK_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryByTransTaskId" resultMap="TBoTransTaskFeeMap">
        SELECT
            BO_TRANS_TASK_FEE_ID,
            CONFIG_KEY,
            CONFIG_NAME,
            TO_CHAR(CONFIG_VALUE, 'FM999999990.00') CONFIG_VALUE,
            TO_CHAR(ACTUAL_VALUE, 'FM999999990.00') ACTUAL_VALUE,
            EXPENSIVE_TYPE,
            CONFIRM_STATUS,
            REASON
        FROM T_BO_TRANS_TASK_FEE
        WHERE IS_DEL = 0
        AND BO_TRANS_TASK_ID = #{transTaskId}
    </select>

    <select id="queryByTransTaskIdAndOrgId" resultMap="TBoTransTaskFeeMap">
        SELECT
            BO_TRANS_TASK_FEE_ID,
            CONFIG_KEY,
            CONFIG_NAME,
            TO_CHAR(CONFIG_VALUE, 'FM999999990.00') CONFIG_VALUE,
            TO_CHAR(ACTUAL_VALUE, 'FM999999990.00') ACTUAL_VALUE,
            EXPENSIVE_TYPE,
            CONFIRM_STATUS,
            REASON
        FROM T_BO_TRANS_TASK_FEE
        WHERE IS_DEL = 0
          AND BO_TRANS_TASK_ID = #{transTaskId}
          AND ORG_ID = #{orgId}
    </select>

    <select id="queryByBoTransTaskId" resultMap="TBoTransTaskFeeMap">
        SELECT
            BO_TRANS_TASK_FEE_ID,
            CONFIG_KEY,
            CONFIG_NAME,
            CASE WHEN PRECISION >0
            THEN TO_CHAR(CONFIG_VALUE, substr('FM999999990.0000',1,12+PRECISION))
            WHEN PRECISION =0 THEN TO_CHAR(CONFIG_VALUE, 'FM999999990')
            ELSE
            TO_CHAR(CONFIG_VALUE, 'FM999999990.00')
            END CONFIG_VALUE,
            CASE WHEN PRECISION >0
            THEN TO_CHAR(ACTUAL_VALUE, substr('FM999999990.0000',1,12+PRECISION))
            WHEN PRECISION =0 THEN TO_CHAR(ACTUAL_VALUE, 'FM999999990')
            ELSE
            TO_CHAR(ACTUAL_VALUE, 'FM999999990.00')
            END ACTUAL_VALUE,
            PRECISION,
            EXPENSIVE_TYPE,
            CONFIRM_STATUS,
            IS_APPROVAL,
            IS_DISPLAY,
            IS_VERIFY,
            SETTLE_VERIFY_STATUS,
            IS_MODIFY,
            IS_UPLOAD_VOUCHER,
            REASON
        FROM T_BO_TRANS_TASK_FEE
        WHERE IS_DEL = 0
        AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>


    <insert id="batchSave">
        insert into
        T_BO_TRANS_TASK_FEE(BO_TRANS_TASK_FEE_ID,BO_TRANS_TASK_ID,CONFIG_KEY,CONFIG_NAME,CONFIG_VALUE,REASON, EXPENSIVE_TYPE,PRECISION,IS_DISPLAY,IS_VERIFY,IS_MODIFY ,IS_UPLOAD_VOUCHER,IS_APPROVAL,
        IS_CW_AUDIT, IS_REQUIRED, IS_NEED_REMARK, BO_TRANS_FEE_VERIFY_TASK_ID, DATA_SRC,RULE_PRICE,RULE_GOODS_AMOUNT_TYPE_NAME,ORG_ID,RELATION_FIELD,IS_UP_PAY_VERIFY,RULE_UP_RATE)
        SELECT
        A.*
        FROM (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            #{item.boTransTaskFeeId},
            #{item.boTransTaskId},
            #{item.configKey},
            #{item.configName},
            #{item.configValue},
            #{item.reason},
            #{item.expensiveType},
            #{item.precision},
            #{item.isDisplay},
            #{item.isVerify},
            #{item.isModify},
            #{item.isUploadVoucher},
            #{item.isApproval},
            #{item.isCwAudit},
            #{item.isRequired},
            #{item.isNeedRemark},
            #{item.boTransFeeVerifyTaskId,jdbcType=NUMERIC,javaType=string},
            #{item.dataSrc},
            #{item.rulePrice},
            #{item.ruleGoodsAmountTypeName},
            #{item.orgId},
            #{item.relationField},
            #{item.isUpPayVerify},
            #{item.ruleUpRate}
            FROM
            DUAL
        </foreach>) A
    </insert>

    <update id="deleteByTransTaskId">
        UPDATE T_BO_TRANS_TASK_FEE SET IS_DEL =1,LAST_MODIFIED_TIME =SYSDATE WHERE BO_TRANS_TASK_ID =#{transTaskId} AND IS_DEL =0
    </update>

    <update id="batchUpdateById">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_TRANS_TASK_FEE
            SET
            CONFIG_VALUE = #{item.configValue},
            REASON = #{item.reason},
            CONFIG_NAME = #{item.configName},
            DATA_SRC = #{item.dataSrc},
            RULE_PRICE = #{item.rulePrice},
            RULE_GOODS_AMOUNT_TYPE_NAME = #{item.ruleGoodsAmountTypeName},
            RULE_UP_RATE = #{item.ruleUpRate},
            LAST_MODIFIED_TIME = SYSDATE
            WHERE BO_TRANS_TASK_FEE_ID = #{item.boTransTaskFeeId}
        </foreach>
    </update>

    <update id="batchDel">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_TRANS_TASK_FEE
            SET
            IS_DEL = 1,
            LAST_MODIFIED_TIME = SYSDATE
            WHERE BO_TRANS_TASK_ID = #{item.boTransTaskId}
            and CONFIG_KEY = #{item.configKey}
            and is_del = 0
        </foreach>
    </update>


    <update id="updateOilFreight">
        UPDATE T_BO_TRANS_TASK_FEE SET CONFIG_VALUE =#{useOilFreight},LAST_MODIFIED_TIME =SYSDATE WHERE BO_TRANS_TASK_ID =#{boTransTaskId} AND IS_DEL =0 and CONFIG_KEY = #{configKey}
    </update>
    <update id="clearTaskFeeByFeeId">
        UPDATE T_BO_TRANS_TASK_FEE SET CONFIG_VALUE = NULL , ACTUAL_VALUE = NULL, LAST_MODIFIED_TIME = SYSDATE WHERE BO_TRANS_TASK_FEE_ID = #{boTransTaskFeeId}
    </update>

    <select id="queryActualValueByConfigKey" resultMap="TBoTransTaskFeeMap">
        SELECT
            f.BO_TRANS_TASK_FEE_ID,
            f.CONFIG_KEY,
            f.CONFIG_NAME,
            TO_CHAR(
            CASE
            <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                WHEN f.CONFIG_KEY = '${key}'
                THEN ${value}
            </foreach>
            ELSE NVL(f.CONFIG_VALUE, 0) END, 'FM999999990.00') CONFIG_VALUE,
            TO_CHAR(NVL(f.ACTUAL_VALUE,0), 'FM999999990.00') ACTUAL_VALUE,
            f.EXPENSIVE_TYPE,
            f.REASON
        FROM T_BO_TRANS_TASK_FEE f
        INNER JOIN T_BO_TRANS_TASK t ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID
        WHERE f.IS_DEL = 0
            AND t.IS_DEL = 0
            AND f.BO_TRANS_TASK_ID = #{transTaskId} and f.CONFIG_KEY =#{configKey}
    </select>

    <update id="updateActualValue">
        UPDATE T_BO_TRANS_TASK_FEE
        SET ACTUAL_VALUE = #{actualValue},
            SETTLE_VERIFY_STATUS = #{settleVerifyStatus},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_TASK_FEE_ID =#{boTransTaskFeeId} AND IS_DEL =0
    </update>
    <update id="updateConfirmState">
        UPDATE T_BO_TRANS_TASK_FEE SET CONFIRM_STATUS = 1,LAST_MODIFIED_TIME = SYSDATE WHERE BO_TRANS_TASK_FEE_ID =#{boTransTaskFeeId} AND IS_DEL =0
    </update>

    <update id="updateActualValueBatch">
        UPDATE T_BO_TRANS_TASK_FEE SET ACTUAL_VALUE = CONFIG_VALUE, LAST_MODIFIED_TIME =SYSDATE, SETTLE_VERIFY_STATUS = 2
        WHERE IS_DEL =0
        AND (CONFIRM_STATUS IS NULL OR CONFIRM_STATUS = 0)
        AND (ACTUAL_VALUE IS NULL OR CONFIG_VALUE > ACTUAL_VALUE)
        AND BO_TRANS_TASK_FEE_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateApprovalConfigValue">
        UPDATE T_BO_TRANS_TASK_FEE T
           SET T.CONFIG_VALUE = #{configValue},
               T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND (T.ACTUAL_VALUE IS NULL OR T.ACTUAL_VALUE <![CDATA[<=]]> #{configValue})
        AND T.BO_TRANS_TASK_FEE_ID = #{boTransTaskFeeId}
    </update>
    <update id="updateSettleVerifyStatus">
        UPDATE T_BO_TRANS_TASK_FEE
           SET SETTLE_VERIFY_STATUS = #{toStatus},
               LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0 AND BO_TRANS_TASK_FEE_ID = #{boTransTaskFeeId} AND SETTLE_VERIFY_STATUS = #{fromStatus}
    </update>

    <select id="queryByTransTaskIdList" resultMap="TBoTransTaskFeeMap">
        SELECT
            T.BO_TRANS_TASK_FEE_ID,
            T.BO_TRANS_TASK_ID,
            T.CONFIG_KEY,
            T.CONFIG_NAME,
            CASE WHEN PRECISION >0
            THEN TO_CHAR(CONFIG_VALUE, substr('FM999999990.0000',1,12+PRECISION))
            WHEN PRECISION =0 THEN TO_CHAR(CONFIG_VALUE, 'FM999999990')
            ELSE
            TO_CHAR(CONFIG_VALUE, 'FM999999990.00')
            END CONFIG_VALUE,
            CASE WHEN PRECISION >0
            THEN TO_CHAR(ACTUAL_VALUE, substr('FM999999990.0000',1,12+PRECISION))
            WHEN PRECISION =0 THEN TO_CHAR(ACTUAL_VALUE, 'FM999999990')
            ELSE
            TO_CHAR(ACTUAL_VALUE, 'FM999999990.00')
            END ACTUAL_VALUE,
            T.EXPENSIVE_TYPE,
            T.REASON
        FROM T_BO_TRANS_TASK_FEE T
        WHERE T.IS_DEL = 0
        <choose>
            <when test="transTaskIdList != null and transTaskIdList.size() > 0">
                AND T.BO_TRANS_TASK_ID IN
                <foreach collection="transTaskIdList" item="transTaskId" open="(" separator="," close=")">
                    #{transTaskId}
                </foreach>
            </when>
            <otherwise>AND T.BO_TRANS_TASK_ID = -1</otherwise>
        </choose>
        ORDER BY T.CREATED_TIME, T.BO_TRANS_TASK_FEE_ID
    </select>

    <select id="queryByTaskIds" resultMap="TBoTransTaskFeeMap">
        SELECT
            F.BO_TRANS_TASK_FEE_ID,
            T.BO_TRANS_TASK_ID,
            F.CONFIG_KEY,
            F.CONFIG_NAME,
            CASE
            <if test="freightRuleMap !=null and freightRuleMap.size()>0">
                <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                    WHEN F.EXPENSIVE_TYPE=2 AND F.CONFIG_KEY = '${key}'
                    THEN TO_CHAR(${value}, 'FM999999990.00')
                </foreach>
            </if>
            WHEN F.PRECISION >0
            THEN TO_CHAR(F.CONFIG_VALUE, substr('FM999999990.0000', 1, 12 + F.PRECISION))
            WHEN F.PRECISION = 0 THEN TO_CHAR(F.CONFIG_VALUE, 'FM999999990')
            ELSE
            TO_CHAR(F.CONFIG_VALUE, 'FM999999990.00')
            END CONFIG_VALUE,
            CASE
            WHEN F.PRECISION >0
            THEN TO_CHAR(F.ACTUAL_VALUE, substr('FM999999990.0000', 1, 12 + F.PRECISION))
            WHEN F.PRECISION = 0 THEN TO_CHAR(F.ACTUAL_VALUE, 'FM999999990')
            ELSE
            TO_CHAR(F.ACTUAL_VALUE, 'FM999999990.00')
            END ACTUAL_VALUE,
            F.EXPENSIVE_TYPE,
            F.IS_DISPLAY,
            F.IS_APPROVAL,
            F.IS_UPLOAD_VOUCHER,
            F.REASON
        FROM
            T_BO_TRANS_TASK T
        JOIN
            T_BO_TRANS_TASK_FEE F ON
            F.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
            AND F.IS_DEL = 0
        WHERE
            T.IS_DEL = 0
            AND T.BO_TRANS_TASK_ID IN
            <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
    </select>
    <select id="queryByTaskId" resultMap="TBoTransTaskFeeMap">
        SELECT
            F.BO_TRANS_TASK_FEE_ID,
            F.CONFIG_KEY,
            F.CONFIG_NAME,
            CASE
                <if test="freightRuleMap !=null and freightRuleMap.size()>0">
                    <foreach item="value" index="key" collection="freightRuleMap" separator=" ">
                        WHEN F.EXPENSIVE_TYPE=2 AND F.CONFIG_KEY = '${key}'
                        THEN TO_CHAR(${value}, 'FM999999990.00')
                    </foreach>
                </if>
                WHEN F.PRECISION >0
                THEN TO_CHAR(F.CONFIG_VALUE, substr('FM999999990.0000', 1, 12 + F.PRECISION))
                WHEN F.PRECISION = 0 THEN TO_CHAR(F.CONFIG_VALUE, 'FM999999990')
                ELSE
                TO_CHAR(F.CONFIG_VALUE, 'FM999999990.00')
                END CONFIG_VALUE,
                CASE
                WHEN F.PRECISION >0
                THEN TO_CHAR(F.ACTUAL_VALUE, substr('FM999999990.0000', 1, 12 + F.PRECISION))
                WHEN F.PRECISION = 0 THEN TO_CHAR(F.ACTUAL_VALUE, 'FM999999990')
                ELSE
                TO_CHAR(F.ACTUAL_VALUE, 'FM999999990.00')
                END ACTUAL_VALUE,
                F.EXPENSIVE_TYPE,
                F.IS_DISPLAY,
                F.IS_APPROVAL,
                F.IS_VERIFY,
                F.IS_MODIFY,
                F.IS_UPLOAD_VOUCHER,
                F.REASON
        FROM
            T_BO_TRANS_TASK T
        JOIN
            T_BO_TRANS_TASK_FEE F ON
            F.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
            AND F.IS_DEL = 0
        WHERE
            T.IS_DEL = 0
            AND T.BO_TRANS_TASK_ID = #{taskId}
    </select>

    <select id="list" resultMap="TBoTransTaskFeeMap">
        SELECT
            BO_TRANS_TASK_ID,
            TO_CHAR(NVL(CONFIG_VALUE, 0) , 'FM999999990.00') CONFIG_VALUE
        FROM
            T_BO_TRANS_TASK_FEE
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID IN
            <foreach collection="transTaskIdList" item="transTaskId" open="(" separator="," close=")">
                #{transTaskId}
            </foreach>
            AND CONFIG_KEY = #{configKey}
    </select>

    <select id="queryByFeeIdList" resultType="com.wtyt.dao.bean.syf.BoTransTaskFeeBean">
        SELECT
            T.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.CONFIG_KEY configKey,
            T.CONFIG_NAME configName,
            TO_CHAR(T.CONFIG_VALUE, 'FM999999990.00') configValue,
            TO_CHAR(NVL(T.ACTUAL_VALUE,0), 'FM999999990.00') actualValue,
            T.EXPENSIVE_TYPE expensiveType,
            T.REASON reason
        FROM T_BO_TRANS_TASK_FEE T
        WHERE T.IS_DEL = 0
        AND BO_TRANS_TASK_FEE_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectCustomerVerifyTaskFees" resultMap="TBoTransTaskFeeMap">
        SELECT BO_TRANS_TASK_FEE_ID, CONFIG_NAME, EXPENSIVE_TYPE, BO_TRANS_FEE_VERIFY_TASK_ID FROM T_BO_TRANS_TASK_FEE WHERE BO_TRANS_FEE_VERIFY_TASK_ID = #{boTransFeeVerifyTaskId} AND IS_DEL = 0 AND CONFIG_VALUE > 0
    </select>

    <select id="getTaskFeeListByConfigKeyList" resultMap="TBoTransTaskFeeMap">
        SELECT
            T.BO_TRANS_TASK_FEE_ID,
            T.BO_TRANS_TASK_ID,
            T.CONFIG_KEY,
            T.CONFIG_NAME,
            TO_CHAR(T.CONFIG_VALUE, 'FM999999990.00') CONFIG_VALUE,
            TO_CHAR(NVL(T.ACTUAL_VALUE,0), 'FM999999990.00') ACTUAL_VALUE,
            T.CONFIRM_STATUS,
            T.SETTLE_VERIFY_STATUS
        FROM T_BO_TRANS_TASK_FEE T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.CONFIG_KEY IN
        <foreach collection="configKeyList" item="configKey" open="(" separator="," close=")">
            #{configKey}
        </foreach>
    </select>

    <select id="queryPayMoneyByTaskFeeId" resultType="OperFeeRecRecordBean">
        SELECT t1.BO_TRANS_TASK_FEE_ID boTransTaskFeeId,
               TO_CHAR(NVL(sum(t1.PAYMENT_MONEY), 0) , 'FM999999990.00') paymentMoney
        FROM T_BO_OPER_FEE_REC_RECORD t1
        WHERE t1.BO_TRANS_TASK_FEE_ID IN
        <foreach collection="taskFeeIdList" item="taskFeeId" open="(" separator="," close=")">
            #{taskFeeId}
        </foreach>
          AND t1.IS_DEL = 0
        GROUP BY T1.BO_TRANS_TASK_FEE_ID
    </select>

</mapper>

