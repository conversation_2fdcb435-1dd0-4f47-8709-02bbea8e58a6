<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoOrgBiBoardMapper">


    <select id="queryOrgBiBoardList" resultType="com.wtyt.dao.bean.syf.BoOrgBiBoardBean">
        SELECT
            BO_ORG_BI_BOARD_ID boOrgBiBoardId,
            ORG_ID orgId,
            BI_BOARD_NAME biBoardName,
            BI_BOARD_URL biBoardUrl,
            BI_BOARD_PARAMS biBoardParams,
            BI_BOARD_TYPE biBoardType
        FROM
            T_BO_ORG_BI_BOARD
        WHERE
            IS_DEL = 0
            AND ORG_ID = #{orgId}
            AND BI_BOARD_TYPE = #{biBoardType}
    </select>

    <select id="queryGrpBiBoardList" resultType="com.wtyt.dao.bean.syf.BoOrgBiBoardBean">
        SELECT
            BO_ORG_BI_BOARD_ID boOrgBiBoardId,
            ORG_ID orgId,
            BI_BOARD_NAME biBoardName,
            BI_BOARD_URL biBoardUrl
        FROM
            T_BO_ORG_BI_BOARD
        WHERE
            IS_DEL = 0
        AND ORG_ID in
        <foreach collection="dataIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND BI_BOARD_TYPE = 1
        AND DATA_TYPE in (0,1)
    </select>

</mapper>
