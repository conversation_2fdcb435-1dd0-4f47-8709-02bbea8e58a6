<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoDhHeaderColumnMapper">

    <sql id="columnField">
        BO_DH_HEADER_COLUMN_ID boDhHeaderColumnId,
        BO_DH_FIELD_DICT_ID boDhFieldDictId,
        BO_DH_HEADER_IMPORT_ID boDhHeaderImportId,
        DEFAULT_VALUE defaultValue
    </sql>


    <insert id="batchInsert">
        INSERT INTO T_BO_DH_HEADER_COLUMN(BO_DH_HEADER_COLUMN_ID, BO_DH_FIELD_DICT_ID,
        BO_DH_HEADER_IMPORT_ID, DEFAULT_VALUE)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boDhHeaderColumnId} BO_DH_HEADER_COLUMN_ID,
            #{item.boDhFieldDictId} BO_DH_FIELD_DICT_ID,
            #{item.boDhHeaderImportId} BO_DH_HEADER_IMPORT_ID,
            #{item.defaultValue} DEFAULT_VALUE
            FROM
            DUAL
        </foreach>
    </insert>


    <select id="queryDhHeaderColumnListByImportIds" resultType="com.wtyt.dao.bean.syf.BoDhHeaderColumnBean">
        SELECT
        <include refid="columnField"/>
        FROM T_BO_DH_HEADER_COLUMN
        WHERE IS_DEL = 0
        AND BO_DH_HEADER_IMPORT_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        order by BO_DH_FIELD_DICT_ID asc
    </select>


</mapper>
