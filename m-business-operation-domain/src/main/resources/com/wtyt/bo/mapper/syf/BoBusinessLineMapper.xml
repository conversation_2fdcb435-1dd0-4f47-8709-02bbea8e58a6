<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoBusinessLineMapper">

    <select id="countLineGoods" resultType="com.wtyt.bo.bean.BoBusinessLineBean">
        SELECT
            TT.BO_BUSINESS_LINE_ID boBusinessLineId,
            TO_CHAR(SUM(TT.UNLOADING_TONNAGE), 'FM999999990.0000') totalGoodsAmount,
            TO_CHAR(SUM(TT.LOADING_TONNAGE), 'FM999999990.0000') totalLoadingTonnage
        FROM
            T_BO_TRANS_TASK TT
        WHERE
            TT.IS_DEL = 0
            AND TT.BO_BUSINESS_LINE_ID IN
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        GROUP BY
            TT.BO_BUSINESS_LINE_ID
    </select>
</mapper>
