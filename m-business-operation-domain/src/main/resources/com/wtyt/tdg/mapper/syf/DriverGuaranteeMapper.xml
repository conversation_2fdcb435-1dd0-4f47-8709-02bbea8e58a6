<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.tdg.mapper.syf.DriverGuaranteeMapper">

    <select id="queryDriverGuaranteeByTaxWaybillIds" resultType="com.wtyt.tdg.bean.Resp5330246Bean">
        SELECT
            T.TAX_WAYBILL_ID taxWaybillId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            CASE
                WHEN T.GUARANTEE_AMOUNT - TRUNC(T.GUARANTEE_AMOUNT) = 0 THEN TO_CHAR(TRUNC(T.GUARANTEE_AMOUNT), 'FM999999990')
                ELSE TO_CHAR(T.GUARANTEE_AMOUNT, 'FM999999990.9999')
                E<PERSON> driverGuaranteeAmount,
            CASE
                WHEN T.GUARANTEE_PAY_AMOUNT - TRUNC(T.GUARANTEE_PAY_AMOUNT) = 0 THEN TO_CHAR(TRUNC(T.GUARANTEE_PAY_AMOUNT), 'FM999999990')
                ELSE TO_CHAR(T.GUARANTEE_PAY_AMOUNT, 'FM999999990.9999')
                END driverGuaranteePayAmount,
            T.GUARANTEE_STATE driverGuaranteeState,
            T.GUARANTEE_CHANNEL driverGuaranteeChannel,
            T.GUARANTEE_DEAL_REASON driverGuaranteeDealReason,
            T.GUARANTEE_DISPATCH_CAR_RULE driverGuaranteeDispatchCarRule,
            TO_CHAR(T.GUARANTEE_SUBMIT_TIME, 'yyyy-mm-dd hh24:mi:ss') driverGuaranteeSubmitTime,
            T.DRIVER_CONFIRM_STATUS driverConfirmStatus,
            T.LOADING_REMARK loadingRemark,
            T.LOADING_CONTACT_NAME loadingContactName,
            T.LOADING_CONTACT_MOBILE_NO loadingContactMobileNo,
            T.UNLOADING_REMARK unloadingRemark,
            T.UNLOADING_CONTACT_NAME unloadingContactName,
            T.UNLOADING_CONTACT_MOBILE_NO unloadingContactMobileNo,
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            T.AUDIT_STATUS auditStatus,
            T.DELETE_STATUS deleteStatus,
            T.THIRD_TASK_NO thirdTaskNo,
            T.TRANS_TASK_FLAG transTaskFlag,
            T.BUSINESS_TYPE businessType,
            T.LOAD_TYPE loadType,
            T.VOUCHER_CONFIG_TYPE voucherConfigType,
            T.ORG_ID orgId
        FROM (
            SELECT
                NVL(T3.TAX_WAYBILL_ID, T1.TAX_WAYBILL_ID) TAX_WAYBILL_ID,
                T1.BO_TRANS_TASK_ID,
                T4.GUARANTEE_AMOUNT,
                T4.GUARANTEE_PAY_AMOUNT,
                T4.GUARANTEE_STATE,
                T4.GUARANTEE_CHANNEL,
                T4.GUARANTEE_DEAL_REASON,
                T4.GUARANTEE_DISPATCH_CAR_RULE,
                T4.GUARANTEE_SUBMIT_TIME,
                T4.DRIVER_CONFIRM_STATUS,
                T2.LOADING_REMARK,
                T2.LOADING_CONTACT_NAME,
                T2.LOADING_CONTACT_MOBILE_NO,
                T2.UNLOADING_REMARK,
                T2.UNLOADING_CONTACT_NAME,
                T2.UNLOADING_CONTACT_MOBILE_NO,
                T2.AUDIT_STATUS,
                T2.DELETE_STATUS,
                T2.THIRD_TASK_NO,
                T2.TRANS_TASK_FLAG,
                T2.BUSINESS_TYPE,
                T2.LOAD_TYPE,
                T2.VOUCHER_CONFIG_TYPE,
                T1.BO_BUSINESS_LINE_ID,
                T1.ORG_ID
            FROM T_BO_TRANS_TASK T1
            INNER JOIN T_BO_TRANS_TASK_EXTRA T2
                ON T2.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID AND T2.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE T3
                ON T3.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID AND T3.IS_DEL = 0
            LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE T4
                ON T4.BO_TASK_DRIVER_GUARANTEE_ID = T2.BO_TASK_DRIVER_GUARANTEE_ID AND T4.IS_DEL = 0
            WHERE T1.IS_DEL = 0
            AND T1.TAX_WAYBILL_ID IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

            UNION

            SELECT
                T5.TAX_WAYBILL_ID,
                T5.BO_TRANS_TASK_ID,
                T8.GUARANTEE_AMOUNT,
                T8.GUARANTEE_PAY_AMOUNT,
                T8.GUARANTEE_STATE,
                T8.GUARANTEE_CHANNEL,
                T8.GUARANTEE_DEAL_REASON,
                T8.GUARANTEE_DISPATCH_CAR_RULE,
                T8.GUARANTEE_SUBMIT_TIME,
                T8.DRIVER_CONFIRM_STATUS,
                T7.LOADING_REMARK,
                T7.LOADING_CONTACT_NAME,
                T7.LOADING_CONTACT_MOBILE_NO,
                T7.UNLOADING_REMARK,
                T7.UNLOADING_CONTACT_NAME,
                T7.UNLOADING_CONTACT_MOBILE_NO,
                T7.AUDIT_STATUS,
                T7.DELETE_STATUS,
                T7.THIRD_TASK_NO,
                T7.TRANS_TASK_FLAG,
                T7.BUSINESS_TYPE,
                T7.LOAD_TYPE,
                T7.VOUCHER_CONFIG_TYPE,
                T6.BO_BUSINESS_LINE_ID,
                T6.ORG_ID
            FROM T_BO_TRANS_TASK_ALLOCATE T5
            INNER JOIN T_BO_TRANS_TASK T6
                ON T6.BO_TRANS_TASK_ID = T5.BO_TRANS_TASK_ID AND T6.IS_DEL = 0
            INNER JOIN T_BO_TRANS_TASK_EXTRA T7
                ON T7.BO_TRANS_TASK_ID = T5.BO_TRANS_TASK_ID AND T7.IS_DEL = 0
            LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE T8
                ON T8.BO_TASK_DRIVER_GUARANTEE_ID = T7.BO_TASK_DRIVER_GUARANTEE_ID AND T8.IS_DEL = 0
            WHERE T5.IS_DEL = 0
            AND T5.TAX_WAYBILL_ID IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        )T ORDER BY BO_TRANS_TASK_ID DESC
    </select>

    <select id="getOrgTransTaskGuaranteeChannelList" resultType="java.lang.String">
        SELECT GUARANTEE_CHANNEL
        FROM (
            SELECT T4.GUARANTEE_CHANNEL,COUNT(*) COUNTER FROM (
                SELECT
                    ST1.BO_TRANS_TASK_ID
                FROM T_BO_TRANS_TASK ST1
                WHERE ST1.IS_DEL = 0
                AND ST1.ORG_ID = #{orgId}
                UNION
                SELECT
                    ST2.BO_TRANS_TASK_ID
                FROM T_BO_TRANS_TASK_ALLOCATE ST2
                WHERE ST2.IS_DEL = 0
                AND ST2.ORG_ID = #{orgId}
            )T1
            LEFT JOIN T_BO_TRANS_TASK_EXTRA T3
                ON T3.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
                AND T3.IS_DEL = 0
            LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE T4
                ON T4.BO_TASK_DRIVER_GUARANTEE_ID = T3.BO_TASK_DRIVER_GUARANTEE_ID
                AND T4.IS_DEL = 0
            WHERE T3.BO_TASK_DRIVER_GUARANTEE_ID IS NOT NULL
            AND T4.GUARANTEE_CHANNEL IS NOT NULL
            GROUP BY T4.GUARANTEE_CHANNEL
        ) ORDER BY COUNTER DESC, GUARANTEE_CHANNEL DESC
    </select>

</mapper>
