<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info">

    <properties>
        <property name="FILE_NAME">m-business-operation-domain</property>
    </properties>

    <!-- appender配置 -->
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout charset="utf-8" pattern="[%d][%traceId][%p][%t][%c:%M:%L] %m%n" />
        </Console>
        <RollingFile name="RollingFile" fileName="/data/app/log/${FILE_NAME}.log" filePattern="/data/app/log/${FILE_NAME}_%d{yyyy-MM-dd}.log" append="true">
            <!-- 输出格式 -->
            <PatternLayout charset="utf-8" pattern="[%d][%traceId][%p][%t][%c:%M:%L] %m%n" />
            <!-- 设置策略 -->
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" />
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="log">
                    <!-- glob 项为需要自动清理日志的pattern -->
    
                    <IfFileName glob="*.log"/>
                    <!-- 1d 表示自动清理掉1天以前的日志文件 -->
                    <IfLastModified age="90d"/>
    
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

    </Appenders>
    <Loggers>
        <!--过滤掉spring和mybatis的一些无用的DEBUG信息 -->
        <logger name="org.springframework" level="INFO"></logger>
        <logger name="org.mybatis" level="debug"></logger>
        <logger name="org.apache.ibatis" level="debug"></logger>
        <logger name="com.wtyt.pmq.rabbitmq.client.thread" level="INFO"></logger>
        <logger name="org.apache.http" level="INFO"></logger>
        <logger name="com.ctrip.framework.apollo" level="INFO"></logger>
        <logger name="com.netflix.discovery" level="INFO"></logger>

        <Root level="debug">
            <AppenderRef ref="Console" />
            <AppenderRef ref="RollingFile"/>
        </Root>

    </Loggers>
</Configuration>