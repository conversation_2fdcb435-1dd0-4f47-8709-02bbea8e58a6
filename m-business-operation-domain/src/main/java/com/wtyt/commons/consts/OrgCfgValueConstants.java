package com.wtyt.commons.consts;

/**
 * 
 * @ClassName: OrgCfgValueConstants
 * @Description: 企业配置值的常量类
 * <AUTHOR>
 * @date 2021年10月18日
 *
 */
public class OrgCfgValueConstants {

    // 司机认证级别-平台级
    public static final String ORG_CFG_VALUE_82 = "82";

    // 司机认证级别-项目级
    public static final String ORG_CFG_VALUE_83 = "83";

    /**
     * 开通
     */
    public static final String ORG_CFG_VALUE_8 = "8";
    /**
     * 关闭
     */
    public static final String ORG_CFG_VALUE_9 = "9";
    /**
     * 有免赔额
     */
    public static final String ORG_CFG_VALUE_91 = "91";
    /**
     * 无免赔额
     */
    public static final String ORG_CFG_VALUE_92 = "92";
    /**
     * 强制每单购买
     */
    public static final String ORG_CFG_VALUE_93 = "93";
    /**
     * 不强制每单购买
     */
    public static final String ORG_CFG_VALUE_94 = "94";

    /**
     * 路歌付费
     */
    public static final String ORG_CFG_VALUE_68 = "68";
    /**
     * 司机付费购买
     */
    public static final String ORG_CFG_VALUE_54 = "54";

    /**
     * 822：运输凭证——回单
     */
    public static final String ORG_CFG_VALUE_71 = "71";
    /**
     * 822：运输凭证——装卸货照
     */
    public static final String ORG_CFG_VALUE_72 = "72";
    /**
     * 822：运输凭证——回单或装卸照
     */
    public static final String ORG_CFG_VALUE_73 = "73";
    /**
     * 822：运输凭证——出库单加回单或卸货照
     */
    public static final String ORG_CFG_VALUE_79 = "79";
    /**
     * 822：运输凭证——装货照加回单
     */
    public static final String ORG_CFG_VALUE_99 = "99";
    /**
     * 822：运输凭证——出库单加卸货照
     */
    public static final String ORG_CFG_VALUE_100 = "100";
    /**
     * 822：运输凭证——出库单+回单
     */
    public static final String ORG_CFG_VALUE_103 = "103";
}
