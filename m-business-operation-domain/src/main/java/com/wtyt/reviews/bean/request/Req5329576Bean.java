package com.wtyt.reviews.bean.request;

import com.wtyt.common.rpc.bean.base.BoTokenBean;
import com.wtyt.common.toolkits.NumberToolkit;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年03月15日 17时51分
 */
@Setter
@Getter
public class Req5329576Bean extends BoTokenBean implements Serializable {

    private static final long serialVersionUID = 2071514000951998707L;
    private String pageSize;
    private String pageNo;
    private String reviewerIdentity;
    private String searchType;

    public int getPageSize() {
        return Math.min(NumberToolkit.toInt(this.pageSize, 20), 50);
    }

    public int getPageNo() {
        return NumberToolkit.toInt(this.pageNo, 1);
    }
}
