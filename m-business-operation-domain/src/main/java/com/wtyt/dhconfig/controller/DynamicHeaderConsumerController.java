package com.wtyt.dhconfig.controller;

import com.wtyt.common.annotation.ExcludeAppComponent;
import com.wtyt.common.annotation.ExcludeJobComponent;
import com.wtyt.commons.annotation.Logger;
import com.wtyt.dhconfig.service.DynamicHeadConsumerService;
import com.wtyt.dhconfig.bean.Req5330317IBean;
import com.wtyt.dhconfig.bean.Req5330317OBean;
import com.wtyt.money.commons.bean.BaseBean;
import com.wtyt.money.commons.bean.ResDataBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@ExcludeAppComponent
@ExcludeJobComponent
@RequestMapping("/dh/component/consumer")
public class DynamicHeaderConsumerController {

    @Autowired
    private DynamicHeadConsumerService dynamicHeadConsumerService;

    @Logger("5330317-动态表头-查询动态表头配置信息")
    @PostMapping(value = "/queryDhConfig")
    public ResDataBean<Req5330317OBean> queryDhConfig(@RequestBody BaseBean<Req5330317IBean> baseBean) {
        return new ResDataBean<Req5330317OBean>().success(dynamicHeadConsumerService.queryDhConfig(baseBean.getData()));
    }


}
