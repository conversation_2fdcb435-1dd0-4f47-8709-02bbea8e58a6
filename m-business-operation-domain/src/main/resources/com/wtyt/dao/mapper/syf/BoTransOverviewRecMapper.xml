<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransOverviewRecMapper">

    <insert id="insertBoardBpOverviewRec">
        INSERT INTO T_BO_TRANS_OVERVIEW_REC(BO_TRANS_OVERVIEW_REC_ID, BO_TRANS_OVERVIEW_ID, BO_BUSINESS_LINE_ID, LOADING_ADDRESS_NAME, UNLOADING_ADDRESS_NAME, PLAN_CAR_NUM, RECEIVED_NOT_ARRIVE_NUM, ARRIVE_NOT_START_NUM, START_NOT_ARRIVE_NUM, ARRIVE_NOT_UNLOAD_NUM, UNSETTLED_NUM, COMPLETED_NUM)
        <foreach item="item" index="index" collection="lineList" separator="union all">
            (
            SELECT
            #{item.boTransOverviewRecId},
            #{item.boTransOverviewId},
            #{item.boBusinessLineId},
            #{item.loadingAddressName},
            #{item.unloadingAddressName},
            #{item.planCarNum},
            #{item.receivedNotArriveNum},
            #{item.arriveNotStartNum},
            #{item.startNotArriveNum},
            #{item.arriveNotUnloadNum},
            #{item.unsettledNum},
            #{item.completedNum}
            FROM DUAL
            )
        </foreach>
    </insert>

    <select id="queryBoTransOverviewRecList" resultType="com.wtyt.board.bean.BoardBpOverviewLineBean">
        SELECT
            BO_TRANS_OVERVIEW_REC_ID boTransOverviewRecId,
            BO_TRANS_OVERVIEW_ID boTransOverviewId,
            BO_BUSINESS_LINE_ID boBusinessLineId,
            LOADING_ADDRESS_NAME loadingAddressName,
            UNLOADING_ADDRESS_NAME unloadingAddressName,
            PLAN_CAR_NUM planCarNum,
            RECEIVED_NOT_ARRIVE_NUM receivedNotArriveNum,
            ARRIVE_NOT_START_NUM arriveNotStartNum,
            START_NOT_ARRIVE_NUM startNotArriveNum,
            ARRIVE_NOT_UNLOAD_NUM arriveNotUnloadNum,
            UNSETTLED_NUM unsettledNum,
            COMPLETED_NUM completedNum
        FROM T_BO_TRANS_OVERVIEW_REC
        WHERE IS_DEL = 0
        AND BO_TRANS_OVERVIEW_ID = #{boTransOverviewId}
        ORDER BY BO_BUSINESS_LINE_ID DESC
    </select>

    <select id="queryBatchBoTransOverviewRecList" resultType="com.wtyt.board.bean.BoardBpOverviewLineBean">
        SELECT
            BO_TRANS_OVERVIEW_REC_ID boTransOverviewRecId,
            BO_TRANS_OVERVIEW_ID boTransOverviewId,
            BO_BUSINESS_LINE_ID boBusinessLineId,
            LOADING_ADDRESS_NAME loadingAddressName,
            UNLOADING_ADDRESS_NAME unloadingAddressName,
            PLAN_CAR_NUM planCarNum,
            RECEIVED_NOT_ARRIVE_NUM receivedNotArriveNum,
            ARRIVE_NOT_START_NUM arriveNotStartNum,
            START_NOT_ARRIVE_NUM startNotArriveNum,
            ARRIVE_NOT_UNLOAD_NUM arriveNotUnloadNum,
            UNSETTLED_NUM unsettledNum,
            COMPLETED_NUM completedNum
        FROM T_BO_TRANS_OVERVIEW_REC
        WHERE IS_DEL = 0
        AND BO_TRANS_OVERVIEW_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        ORDER BY BO_BUSINESS_LINE_ID DESC
    </select>
</mapper>
