<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoNodeMapper">
    <sql id="nodeInfo">
                A.NODE_ID,
                B.KLB_NODE_NAME,
                B.PC_NODE_NAME,
                A.OPT_SOURCE,
                A.USER_ID,
                A.DRIVER_ID,
                A.OVER_TIME,
                A.TAX_POS_INFO_ID,
                A.CREATED_TIME,
                A.SYS_ROLE_TYPE,
                A.<PERSON>EA<PERSON>,
                A.ORG_ID,
                A.GROUP_ID,
                A.JOB_NAME
    </sql>
    <sql id="nodeInfoApp">
                A.NODE_ID,
                CASE
                    WHEN T.TRANSPORT_TYPE = 1 THEN
                        B.DZ_NODE_NAME
                    ELSE
                        B.NODE_NAME
                        END
                    NODE_NAME,
                <PERSON><PERSON>_<PERSON>URCE,
                A.<PERSON>ER_ID,
                A<PERSON>DRIVER_ID,
                <PERSON><PERSON>_TIME,
                A.TAX_POS_INFO_ID,
                A.CREATED_TIME,
                A.REASON,
                A.SYS_ROLE_TYPE,
                A.ORG_ID,
                A.GROUP_ID,
                A.JOB_NAME
    </sql>
    
    <sql id="nodeBaseInfoApp">
        A.BO_TRANS_NODE_RECORD_ID,
        A.NODE_ID,
        A.OPT_SOURCE,
        A.USER_ID,
        A.DRIVER_ID,
        A.OVER_TIME,
        A.TAX_POS_INFO_ID,
        A.CREATED_TIME,
        A.REASON,
        A.SYS_ROLE_TYPE,
        A.ORG_ID,
        A.GROUP_ID,
        A.JOB_NAME,
        A.EXTEND,
        A.ADDRESS,
        A.NODE_SUB_OBJECT_TYPE,
        A.NODE_SUB_OBJECT_VALUE
    </sql>
    
    <!-- 获取节点过程数据 -->
    <select id="getNodeRecordList" parameterType="Req1735208Bean" resultType="com.wtyt.bo.bean.response.Resp1735208Bean$NodeRecordInfo">
        SELECT
            TEMP.NODE_ID nodeId,
            CASE WHEN #{entType} = 1 THEN TEMP.KLB_NODE_NAME
            ELSE TEMP.PC_NODE_NAME END nodeName,
            TEMP.OPT_SOURCE optSource,
            TEMP.USER_ID userId,
            TEMP.DRIVER_ID driverId,
            TEMP.OVER_TIME overTime,
            TEMP.TAX_POS_INFO_ID taxPosInfoId,
            TO_CHAR(TEMP.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            TEMP.SYS_ROLE_TYPE sysRoleType,
            TEMP.REASON reason,
            TEMP.ORG_ID orgId,
            TEMP.GROUP_ID groupId,
            TEMP.JOB_NAME jobName
        FROM (
        SELECT <include refid="nodeInfo" />
            FROM T_BO_TRANS_NODE_RECORD A
            LEFT JOIN T_BO_TRANS_NODE_DICT B
                ON B.NODE_ID = A.NODE_ID
                AND B.IS_DEL = 0
            WHERE A.BO_TRANS_TASK_ID = #{boTransTaskId}
                AND A.IS_DEL = 0 AND A.NODE_ID NOT IN (900,910,1200)
            UNION
            SELECT <include refid="nodeInfo" />
            FROM T_BO_TRANS_NODE_RECORD A
            LEFT JOIN T_BO_TRANS_NODE_DICT B
                ON B.NODE_ID = A.NODE_ID
                AND B.IS_DEL = 0
            WHERE A.BO_TRANS_TASK_ID = #{boTransTaskId} AND A.ORG_ID = #{orgId}
                AND A.IS_DEL = 0 AND A.NODE_ID IN (900,910,1200)
        ) TEMP WHERE 1 = 1
            <if test="filterNodeList!= null and filterNodeList.size() > 0">
                AND (
                    TEMP.NODE_ID NOT IN
                    <foreach collection="filterNodeList" index="index" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    OR (
                        TEMP.NODE_ID=510 AND EXISTS (
                        SELECT
                        1
                        FROM
                        T_BO_TRANS_NODE_RECORD S1
                        WHERE
                        S1.IS_DEL = 0
                        AND S1.BO_TRANS_TASK_ID = #{boTransTaskId}
                        AND S1.NODE_ID = 600
                        AND S1.OPT_SOURCE =-1
                        )
                    )
                )
            </if>
            <choose>
                <when test="sortFlag == 1">
                    ORDER BY TEMP.CREATED_TIME DESC,TEMP.NODE_ID DESC
                </when>
                <otherwise>
                    ORDER BY TEMP.CREATED_TIME, TEMP.NODE_ID
                </otherwise>
            </choose>
    </select>

    <select id="getNextNodeInfo" parameterType="String" resultType="com.wtyt.bo.bean.response.Resp1735208Bean$NodeRecordInfo">
        SELECT CASE WHEN #{entType} = 1 THEN A.KLB_NODE_NAME
                    ELSE A.PC_NODE_NAME END nodeName,
               TO_CHAR(B.DEAD_LINE_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime
        FROM T_BO_TRANS_NODE_DICT A
             LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE B
               ON B.NODE_ID = A.NODE_ID
               AND B.BO_TRANS_TASK_ID = #{boTransTaskId}
               AND B.IS_DEL = 0
        WHERE A.NODE_ID = #{nodeId }
        AND A.IS_DEL = 0
    </select>
    <select id="getNodeRecordListV2" resultType="com.wtyt.bo.bean.response.NodeInfo"
            parameterType="map">
        SELECT TEMP.NODE_ID nodeId,
        TEMP.NODE_NAME nodeName,
        TEMP.OPT_SOURCE optSource,
        TEMP.USER_ID userId,
        TEMP.DRIVER_ID driverId,
        TEMP.OVER_TIME overTime,
        TEMP.TAX_POS_INFO_ID taxPosInfoId,
        TO_CHAR(TEMP.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
        TEMP.REASON reason,
        TEMP.SYS_ROLE_TYPE sysRoleType,
        TEMP.ORG_ID orgId,
        TEMP.GROUP_ID groupId,
        TEMP.JOB_NAME jobName
        FROM (
        SELECT <include refid="nodeInfoApp" />
        FROM T_BO_TRANS_NODE_RECORD A
        LEFT JOIN T_BO_TRANS_NODE_DICT B
        ON B.NODE_ID = A.NODE_ID
        AND B.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK T ON T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND T.IS_DEL = 0
        WHERE A.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND A.IS_DEL = 0 AND A.NODE_ID NOT IN (900,910,1200)
        UNION
        SELECT <include refid="nodeInfoApp" />
        FROM T_BO_TRANS_NODE_RECORD A
        LEFT JOIN T_BO_TRANS_NODE_DICT B
        ON B.NODE_ID = A.NODE_ID
        AND B.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK T ON T.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND T.IS_DEL = 0
        WHERE A.BO_TRANS_TASK_ID = #{boTransTaskId} AND A.ORG_ID = #{orgId}
        AND A.IS_DEL = 0 AND A.NODE_ID IN (900,910,1200)
        ) TEMP

        WHERE 1=1
        <if test="queryNodeIdList != null and queryNodeIdList.size() > 0">
            AND TEMP.NODE_ID IN
            <foreach collection="queryNodeIdList" index="index" item="item" open="(" close=")" separator=",">
                 #{item}
            </foreach>
        </if>
        <if test="filterNodeList != null and filterNodeList.size > 0">
            AND TEMP.NODE_ID NOT IN
            <foreach collection="filterNodeList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="sortFlag == 1">
                ORDER BY TEMP.CREATED_TIME DESC, TEMP.NODE_ID DESC
            </when>
            <otherwise>
                ORDER BY TEMP.CREATED_TIME, TEMP.NODE_ID
            </otherwise>
        </choose>
    </select>
    <resultMap id="listNodeDeadLineMap" type="com.wtyt.bo.bean.response.Resp1735218Bean">
        <id property="boTransTaskId" column="BO_TRANS_TASK_ID" />
        <result property="taxWaybillId" column="TAX_WAYBILL_ID" />
        <collection property="nodeDeadLineList" ofType="com.wtyt.bo.bean.response.Resp1735218Bean$NodeDeadLineInfo">
            <id property="nodeId" column="NODE_ID" />
            <result property="deadLineTime" column="DEAD_LINE_TIME"/>
        </collection>
    </resultMap>
    <select id="getNodeDeadLineByTaskIds" resultMap="listNodeDeadLineMap" >
        SELECT
            BO_TRANS_TASK_ID,
            TAX_WAYBILL_ID,
            NODE_ID,
            TO_CHAR(DEAD_LINE_TIME, 'YYYY-MM-DD HH24:MI:SS') DEAD_LINE_TIME
        FROM
            T_BO_TRANS_NODE_DEAD_LINE
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID IN
            <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                #{item.boTransTaskId}
            </foreach>
            ORDER BY NODE_ID
    </select>
    
    
    <select id="getSpecialNodeRecordList" resultType="com.wtyt.bo.bean.response.NodeInfo">
        SELECT
            TEMP.BO_TRANS_NODE_RECORD_ID boTransNodeRecordId,
            TEMP.NODE_ID nodeId,
            TEMP.OPT_SOURCE optSource,
            TEMP.USER_ID userId,
            TEMP.DRIVER_ID driverId,
            TEMP.OVER_TIME overTime,
            TEMP.TAX_POS_INFO_ID taxPosInfoId,
            TO_CHAR(TEMP.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            TEMP.REASON reason,
            TEMP.SYS_ROLE_TYPE sysRoleType,
            TEMP.ORG_ID orgId,
            TEMP.GROUP_ID groupId,
            TEMP.JOB_NAME jobName,
            TEMP.ADDRESS address,
            TEMP.NODE_SUB_OBJECT_TYPE nodeSubObjectType,
            TEMP.NODE_SUB_OBJECT_VALUE nodeSubObjectValue
        FROM (
        SELECT
         <include refid="nodeBaseInfoApp" />
        FROM T_BO_TRANS_NODE_RECORD A
        WHERE A.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND A.IS_DEL = 0 AND A.NODE_ID NOT IN (900,910,1200)
        UNION ALL
        SELECT
         <include refid="nodeBaseInfoApp" />
        FROM T_BO_TRANS_NODE_RECORD A
        WHERE
        A.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND A.ORG_ID = #{orgId}
        AND A.IS_DEL = 0 AND A.NODE_ID IN (1200)
        ) TEMP
        <choose>
            <when test="sortFlag == 1">
                ORDER BY TEMP.CREATED_TIME DESC, TEMP.NODE_ID DESC
            </when>
            <otherwise>
                ORDER BY TEMP.CREATED_TIME, TEMP.NODE_ID
            </otherwise>
        </choose>
    </select>

    <select id="getNodeRecordListByNodeIds" resultType="com.wtyt.bo.bean.response.NodeInfo">
        SELECT
            T.NODE_ID nodeId,
            T.OPT_SOURCE optSource,
            T.USER_ID userId,
            T.DRIVER_ID driverId,
            T.OVER_TIME overTime,
            T.TAX_POS_INFO_ID taxPosInfoId,
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            T.REASON reason,
            T.SYS_ROLE_TYPE sysRoleType,
            T.ORG_ID orgId,
            T.GROUP_ID groupId,
            T.JOB_NAME jobName,
            T.EXTEND extend
        FROM T_BO_TRANS_NODE_RECORD T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.NODE_ID IN
        <foreach collection="nodeIdList" item="nodeId" open="(" separator="," close=")">
            #{nodeId}
        </foreach>
        <choose>
            <when test="sortFlag == 1">
                ORDER BY T.CREATED_TIME DESC, T.NODE_ID DESC
            </when>
            <otherwise>
                ORDER BY T.CREATED_TIME, T.NODE_ID
            </otherwise>
        </choose>
    </select>

    <select id="batchGetNodeUploadCountAndMaxNode" resultType="com.wtyt.bo.bean.response.NodeInfo">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            COUNT(CASE WHEN NODE_ID = #{nodeId} THEN 1 ELSE NULL END) nodeUploadCount,
            MAX(T.NODE_ID) maxNodeId
        FROM T_BO_TRANS_NODE_RECORD T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
            #{boTransTaskId}
        </foreach>
        GROUP BY T.BO_TRANS_TASK_ID
    </select>

    <select id="batchGetNodeList" resultType="com.wtyt.bo.bean.response.NodeInfo">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.NODE_ID nodeId
        FROM T_BO_TRANS_NODE_RECORD T
        WHERE T.IS_DEL = 0
            AND T.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
                #{boTransTaskId}
            </foreach>
            AND T.NODE_ID IN
            <foreach collection="nodeIdList" item="nodeId" open="(" separator="," close=")">
                #{nodeId}
            </foreach>
        ORDER BY T.BO_TRANS_TASK_ID, T.NODE_ID
    </select>

</mapper>
