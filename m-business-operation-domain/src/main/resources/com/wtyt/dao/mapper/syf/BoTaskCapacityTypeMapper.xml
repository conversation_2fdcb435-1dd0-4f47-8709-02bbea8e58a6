<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskCapacityTypeMapper">

    <select id="getTaskCapacityTypeByCartBadgeNo" resultType="com.wtyt.dao.bean.syf.BoTaskCapacityTypeBean">
        SELECT * FROM (
            SELECT
                T.COMPANY_ID companyId,
                T.CART_BADGE_NO cartBadgeNo,
                T.CAPACITY_TYPE capacityType,
                T.CAPACITY_TYPE_NAME capacityTypeName
            FROM T_BO_TASK_CAPACITY_TYPE T
            WHERE T.IS_DEL = 0
            AND T.CART_BADGE_NO = #{cartBadgeNo}
            AND T.COMPANY_ID = #{companyId}
            ORDER BY T.CREATED_TIME DESC
        ) WHERE ROWNUM = 1
    </select>
    <select id="listTaskCapacityTypeByCartBadgeNos" resultType="com.wtyt.dao.bean.syf.BoTaskCapacityTypeBean">
        SELECT
            T.COMPANY_ID companyId,
            T.CART_BADGE_NO cartBadgeNo,
            T.CAPACITY_TYPE capacityType,
            T.CAPACITY_TYPE_NAME capacityTypeName
        FROM T_BO_TASK_CAPACITY_TYPE T
        WHERE
            T.IS_DEL = 0
            AND T.COMPANY_ID = #{companyId}
            AND T.CART_BADGE_NO IN
        <foreach collection="cartBadgeNos" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY T.CREATED_TIME DESC
    </select>

</mapper>