<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.SearchMapper">

    <select id="getSearchListByCategory" resultType="com.wtyt.dl.bean.SearchBean">
        SELECT
            T.DL_SEARCH_ID dlSearchId,
            T.DL_CATEGORY_ID dlCategoryId,
            T.SEARCH_KEY searchKey,
            T.SEARCH_NAME searchName,
            T.SEARCH_TYPE searchType,
            T.FORCE_SELECT forceSelect,
            T.SELECTED_MODE selectedMode,
            T.SEARCH_SQL searchSql,
            T.PAGES pages,
            T.SCOPES scopes,
            T.RENDER_CONFIG renderConfig
        FROM T_DL_SEARCH T
        WHERE T.IS_DEL = 0
        AND (T.PAGES IS NULL OR INSTR(',' || T.PAGES || ',', ',' || #{page} || ',') > 0)
        AND T.DL_CATEGORY_ID IN
        <foreach collection="categoryIdList" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        ORDER BY SORT_NO
    </select>

    <select id="getSelectedSearchList" resultType="com.wtyt.dl.bean.SearchBean">
        SELECT
            T1.DL_SEARCH_ID dlSearchId,
            T1.DL_CATEGORY_ID dlCategoryId,
            T1.SEARCH_KEY searchKey,
            T1.SEARCH_NAME searchName,
            T1.SEARCH_TYPE searchType,
            T1.FORCE_SELECT forceSelect,
            T1.SELECTED_MODE selectedMode,
            T1.SEARCH_SQL searchSql,
            T1.PAGES pages,
            T1.SCOPES scopes,
            T1.RENDER_CONFIG renderConfig
        FROM T_DL_SEARCH T1
        INNER JOIN T_DL_USER_SEARCH T2
            ON T2.DL_SEARCH_ID = T1.DL_SEARCH_ID
            AND T2.IS_DEL = 0
        WHERE T1.IS_DEL = 0
        AND T2.BUSINESS_TYPE = #{businessType}
        AND T2.PAGE = #{page}
        AND T2.UNIQUE_KEY = #{uniqueKey}
        ORDER BY T2.SORT_NO
    </select>

    <select id="getSearchListBySearchKeyList" resultType="com.wtyt.dl.bean.SearchBean">
        SELECT
            T1.DL_SEARCH_ID dlSearchId,
            T1.DL_CATEGORY_ID dlCategoryId,
            T1.SEARCH_KEY searchKey,
            T1.SEARCH_NAME searchName,
            T1.SEARCH_TYPE searchType,
            T1.FORCE_SELECT forceSelect,
            T1.SELECTED_MODE selectedMode,
            T1.SEARCH_SQL searchSql,
            T1.PAGES pages,
            T1.SCOPES scopes,
            T1.RENDER_CONFIG renderConfig
        FROM T_DL_SEARCH T1
        INNER JOIN T_DL_CATEGORY T2
            ON T2.DL_CATEGORY_ID = T1.DL_CATEGORY_ID
            AND T2.IS_DEL = 0
        WHERE T1.IS_DEL = 0
        AND T2.BUSINESS_TYPE = #{businessType}
        AND (T1.PAGES IS NULL OR INSTR(',' || T1.PAGES || ',', ',' || #{page} || ',') > 0)
        AND T1.SEARCH_KEY IN
        <foreach collection="searchKeyList" item="searchKey" open="(" separator="," close=")">
            #{searchKey}
        </foreach>
        ORDER BY T1.SORT_NO
    </select>

</mapper>