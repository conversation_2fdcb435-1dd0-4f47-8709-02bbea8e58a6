<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransNodeFlowMapper">

    <insert id="insertBoTransNodeFlow" parameterType="BoTransNodeFlowBean">
        INSERT INTO T_BO_TRANS_NODE_FLOW(
                        BO_TRANS_NODE_FLOW_ID,
                        NODE_ID,
                        NEXT_NODE_ID,
                        IS_DEL,
                        CREATED_TIME,
                        LAST_MODIFIED_TIME,
                        <if test="note != null and note !=''">
            NOTE
        </if>

        )values(
                            #{boTransNodeFlowId},

                            #{nodeId},

                            #{nextNodeId},

                            #{isDel},

                            #{createdTime},

                            #{lastModifiedTime},

                        <if test="note != null and note !=''">
        #{note}
        </if>


        )
    </insert>

    <select id="getCountByNodeIdAndNextNodeId" resultType="int">
        SELECT COUNT(1)
        FROM T_BO_TRANS_NODE_FLOW
        WHERE NODE_ID = #{nodeId}
        AND NEXT_NODE_ID = #{nextNodeId}
        AND TRANS_PATTERN = #{transPattern}
        AND IS_DEL = 0
    </select>
    <select id="queryUpNodeIdListByNextNodeId" resultType="java.lang.String">
        SELECT NODE_ID FROM T_BO_TRANS_NODE_FLOW WHERE IS_DEL =0 AND NEXT_NODE_ID = #{nextNodeId} AND TRANS_PATTERN = #{transPattern}
    </select>
</mapper>
