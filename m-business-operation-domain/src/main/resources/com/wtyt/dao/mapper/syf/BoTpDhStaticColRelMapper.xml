<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhStaticColRelMapper">

    <insert id="insertBatch">
        INSERT INTO T_BO_TP_DH_STATIC_COL_REL(BO_TP_DH_STATIC_COL_REL_ID, BO_TP_DH_CONFIG_ID, BO_TP_DH_STATIC_COL_ID, SEARCH_TYPE, IS_PRECISE, IS_SHOW,SHIPPING_IS_SHOW,SORT_NUM)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTpDhStaticColRelId} BO_TP_DH_STATIC_COL_REL_ID,
            #{item.boTpDhConfigId} BO_TP_DH_CONFIG_ID,
            #{item.boTpDhStaticColId} BO_TP_DH_STATIC_COL_ID,
            #{item.searchType} SEARCH_TYPE,
            #{item.isPrecise} IS_PRECISE,
            #{item.isShow} IS_SHOW,
            #{item.shippingIsShow} SHIPPING_IS_SHOW,
            #{item.sortNum} SORT_NUM
            FROM
            DUAL
        </foreach>
    </insert>

    <insert id="batchInsert">
        INSERT INTO T_BO_TP_DH_STATIC_COL_REL(BO_TP_DH_STATIC_COL_REL_ID, BO_TP_DH_CONFIG_ID, BO_TP_DH_STATIC_COL_ID,
        SEARCH_TYPE, IS_PRECISE, IS_SHOW,SHIPPING_IS_SHOW, COLUMN_LENGTH,SORT_NUM)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTpDhStaticColRelId} BO_TP_DH_STATIC_COL_REL_ID,
            #{item.boTpDhConfigId} BO_TP_DH_CONFIG_ID,
            #{item.boTpDhStaticColId} BO_TP_DH_STATIC_COL_ID,
            #{item.searchType} SEARCH_TYPE,
            #{item.isPrecise} IS_PRECISE,
            #{item.isShow} IS_SHOW,
            #{item.shippingIsShow} SHIPPING_IS_SHOW,
            #{item.columnLength} COLUMN_LENGTH,
            #{item.sortNum} SORT_NUM
            FROM
            DUAL
        </foreach>
    </insert>
    <select id="queryStaticColRelList" resultType="com.wtyt.dao.bean.syf.BoTpDhStaticColRelBean">
        SELECT
        BO_TP_DH_STATIC_COL_REL_ID boTpDhStaticColRelId,
        BO_TP_DH_CONFIG_ID boTpDhConfigId,
        BO_TP_DH_STATIC_COL_ID boTpDhStaticColId,
        SEARCH_TYPE searchType,
        IS_PRECISE isPrecise,
        COLUMN_LENGTH columnLength,
        IS_SHOW isShow,
        SHIPPING_IS_SHOW shippingIsShow,
        SORT_NUM sortNum
        FROM T_BO_TP_DH_STATIC_COL_REL
        WHERE IS_DEL = 0
        AND BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        ORDER BY SORT_NUM ASC,BO_TP_DH_STATIC_COL_REL_ID asc
    </select>

    <select id="queryAllStaticColRel" resultType="com.wtyt.dao.bean.syf.BoTpDhStaticColRelBean">
        SELECT
            BO_TP_DH_STATIC_COL_REL_ID boTpDhStaticColRelId,
            BO_TP_DH_CONFIG_ID boTpDhConfigId,
            BO_TP_DH_STATIC_COL_ID boTpDhStaticColId,
            SEARCH_TYPE searchType,
            IS_PRECISE isPrecise,
            IS_SHOW isShow,
            SHIPPING_IS_SHOW shippingIsShow,
            COLUMN_LENGTH columnLength,
            SORT_NUM sortNum
        FROM T_BO_TP_DH_STATIC_COL_REL
        WHERE IS_DEL = 0
          AND BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
    </select>
</mapper>
