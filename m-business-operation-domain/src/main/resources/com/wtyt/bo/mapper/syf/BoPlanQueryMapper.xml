<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoPlanQueryMapper">

 <sql id="queryDistributeOrderPlanListWithAsSql">
        WITH RankedResults AS
        (
        SELECT ROWNUM RN, TT.* FROM (
        SELECT
        TBTO.MERGE_ID,
        TBTO.BO_TRANS_ORDER_ID,
        TBTO.CUSTOMER_ORDER_NO,
        TBTO.CREATED_TIME,
        TBTO.LAST_MODIFIED_TIME,
        TBTO.TRANS_DATE,
        TBTNDL.DEAD_LINE_TIME,
        TBTT.BO_TRANS_TASK_ID,
        DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) DISPATCH_STATE,
        TBTT.NODE_ID,
        TBTTE.DISPATCH_CAR_TIME,
        TBTT.TAX_WAYBILL_NO,
        NVL2(TBTO.CARRIER, 1, 0) DISTRIBUTE_STATE,
        CASE
        WHEN TBTT.OWNER_ORG_ID != TBTNR.ORG_ID THEN '2'
        WHEN TBTT.OWNER_ORG_ID = TBTNR.ORG_ID AND TBTNR.SYS_ROLE_TYPE = 12005 THEN '2'
        ELSE '1'
        END disPatchType,
        TBTNR.USER_ID disPatcherUserId,
        TBTNR.GROUP_ID disPatcherSupplierId

        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE TBTNDL ON TBTT.BO_TRANS_TASK_ID = TBTNDL.BO_TRANS_TASK_ID AND TBTNDL.IS_DEL = 0  AND TBTNDL.NODE_ID = 200
        <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
            INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0 AND GROUP_TYPE = 4
        </if>
        LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        WHERE <include refid="distributeModeAdapter"/>

        AND TBTO.MERGE_ID IN
        (
        SELECT
        DISTINCT TBTO.MERGE_ID
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TBTTA ON TBTT.BO_TRANS_TASK_ID = TBTTA.BO_TRANS_TASK_ID AND TBTTA.IS_DEL = 0
        </if>
     <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
     <if test="dispatcherList != null and dispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        </if>
        WHERE <include refid="distributeModeAdapter"/>
        <include refid="tabConditionForDistributeOrder" />
        <include refid="stateConditionForDistributeOrder" />
        <include refid="searchConditionForDistributeOrder" />
        )
     <!-- 处理已删除情况的条件 -->
     <choose>
            <!-- 已删除TAB -->
         <when test="tabState != null and tabState == '100'.toString()">
                AND TBTO.IS_DEL = 1
            </when>
            <otherwise>
                AND TBTO.IS_DEL = 0
            </otherwise>
        </choose>
        <choose>
            <when test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
                ORDER BY TBOGR.CREATED_TIME DESC
            </when>
            <otherwise>
                ORDER BY ${orderByStr}
            </otherwise>
        </choose>
        ) TT
        )
    </sql>

    <sql id="queryPlanListWithAsSql">
       WITH RankedResults AS
        (
        SELECT ROWNUM RN, TT.* FROM (
        SELECT
        TBTO.MERGE_ID,
        TBTO.BO_TRANS_ORDER_ID,
        TBTO.CUSTOMER_ORDER_NO,
        TBTO.CREATED_TIME,
        TBTO.LAST_MODIFIED_TIME,
        TBTO.TRANS_DATE,
        TBTNDL.DEAD_LINE_TIME,
        TBTT.BO_TRANS_TASK_ID,
        DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) DISPATCH_STATE,
        TBTT.NODE_ID,
        TBTTE.DISPATCH_CAR_TIME,
        TBTT.TAX_WAYBILL_NO,
        NVL2(TBTO.CARRIER, 1, 0) DISTRIBUTE_STATE,
        CASE
        WHEN TBTT.OWNER_ORG_ID != TBTNR.ORG_ID THEN '2'
        WHEN TBTT.OWNER_ORG_ID = TBTNR.ORG_ID AND TBTNR.SYS_ROLE_TYPE = 12005 THEN '2'
        ELSE '1'
        END disPatchType,
        TBTNR.USER_ID disPatcherUserId,
        TBTNR.GROUP_ID disPatcherSupplierId
        FROM (SELECT
        DISTINCT TBTO.MERGE_ID
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TBTTA ON TBTT.BO_TRANS_TASK_ID = TBTTA.BO_TRANS_TASK_ID AND TBTTA.IS_DEL = 0
        </if>
        <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
        <if test="dispatcherList != null and dispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        </if>
        WHERE <include refid="distributeModeAdapter"/>
        <include refid="tabConditionForDistributeOrder" />
        <include refid="stateConditionForDistributeOrder" />
        <include refid="searchConditionForDistributeOrder" /> ) MIR INNER JOIN T_BO_TRANS_ORDER TBTO  ON MIR.MERGE_ID =TBTO.MERGE_ID
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
            INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0 AND GROUP_TYPE = 4
        </if>
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE TBTNDL ON TBTT.BO_TRANS_TASK_ID = TBTNDL.BO_TRANS_TASK_ID AND TBTNDL.IS_DEL = 0  AND TBTNDL.NODE_ID = 200
        LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        WHERE <include refid="distributeModeAdapter"/>
        <!-- 处理已删除情况的条件 -->
        <choose>
            <!-- 已删除TAB -->
            <when test="tabState != null and tabState == '100'.toString()">
                AND TBTO.IS_DEL = 1
            </when>
            <otherwise>
                AND TBTO.IS_DEL = 0
            </otherwise>
        </choose>
        <choose>
            <when test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
                ORDER BY TBOGR.CREATED_TIME DESC
            </when>
            <otherwise>
                ORDER BY ${orderByStr}
            </otherwise>
        </choose>
        ) TT
        )
    </sql>

    <sql id="tabConditionForDistributeOrder">
        <choose>
            <!-- 已删除TAB -->
            <when test="tabState != null and tabState == '100'.toString()">
                AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,1)
            </when>
            <otherwise>
                AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,2)
            </otherwise>
        </choose>
        <!-- tab状态筛选 -->
        <choose>
            <!-- 已删除TAB -->
            <when test="tabState != null and tabState == '100'.toString()">
                AND  TBTO.IS_DEL = 1
            </when>
            <!-- 我的TAB -->
            <when test="tabState != null and tabState == '1'.toString()">
                AND  TBTO.IS_DEL = 0 AND TBTO.USER_ID = #{userId}
            </when>
            <!-- 超时（预警/告警）TAB -->
            <when test="tabState != null and tabState == '3'.toString()">
                <if test="dispatchAlarmFromOrder!= null and dispatchAlarmFromOrder == '0'.toString()">
                      <!-- 异常TAB时，需要关联异常表 只获取有异常的数据 -->
                    AND  TBTO.IS_DEL = 0 AND EXISTS(SELECT 1  FROM T_BO_TRANS_NODE_ALARM tna WHERE tna.NODE_ID = 200 and tna.NODE_DATA_TYPE = 1 AND (tna.ALARM_TYPE = 1 OR (tna.ALARM_TYPE=0 AND tna.ALARM_PROCESS_RESULT = 0)) and tna.IS_DEL = 0 AND TBTT.BO_TRANS_TASK_ID = tna.BO_TRANS_TASK_ID)
                </if>
                <if test="dispatchAlarmFromOrder!= null and dispatchAlarmFromOrder == '1'.toString()">
                      <!-- 异常TAB时，需要关联订单异常表只获取有异常的数据 -->
                    AND  TBTO.IS_DEL = 0 AND EXISTS(
                    SELECT 1  FROM T_BO_TRANS_ORDER_ALARM toa
                    WHERE (toa.ALARM_TYPE = 1 OR (toa.ALARM_TYPE=0 AND toa.ALARM_PROCESS_RESULT = 0))
                      and toa.IS_DEL = 0 AND TBTO.BO_TRANS_ORDER_ID = toa.BO_TRANS_ORDER_ID)
                </if>
            </when>
            <!-- 未分发TAB -->
            <when test="tabState != null and tabState == '8'.toString()">
                AND  TBTO.IS_DEL = 0 AND TBTO.CARRIER IS NULL
            </when>
            <!-- 已分发TAB -->
            <when test="tabState != null and tabState == '9'.toString()">
                AND  TBTO.IS_DEL = 0 AND TBTO.CARRIER IS NOT NULL
            </when>
            <!-- 未派车TAB -->
            <when test="tabState != null and tabState == '10'.toString()">
                AND  TBTO.IS_DEL = 0 AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0
                <if test="cpd != null and cpd == '2'.toString()">
                    AND not exists(select 1 from T_BO_ORDER_CPD_SHUNT_REL tbc where tbc.MERGE_ID = TBTO.MERGE_ID and tbc.IS_DEL = 0)
                </if>
            </when>
            <!-- 已派车TAB -->
            <when test="tabState != null and tabState == '11'.toString()">
                AND  TBTO.IS_DEL = 0 AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 1
            </when>
            <!-- 待摇号TAB -->
            <when test="tabState != null and tabState == '12'.toString()">
                AND  TBTO.IS_DEL = 0 AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0  AND NVL(TBTO.LOTTERY_STATE,0) =0   <include refid="cpdLotteryLineId"/>
            </when>
            <!-- 已摇号TAB -->
            <when test="tabState != null and tabState == '13'.toString()">
                AND  TBTO.IS_DEL = 0   AND TBTO.LOTTERY_STATE =1  <include refid="cpdLotteryLineId"/>
            </when>
            <!-- 摇号中TAB -->
            <when test="tabState != null and tabState == '14'.toString()">
                AND  TBTO.IS_DEL = 0 AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0   AND TBTO.LOTTERY_STATE =2  <include refid="cpdLotteryLineId"/>
            </when>
            <otherwise>
                AND TBTO.IS_DEL = 0
            </otherwise>
        </choose>
    </sql>

    <sql id="cpdLotteryLineId">
        <if test="lotteryLineList != null and lotteryLineList.size() > 0">
            AND(
            <foreach item="item" index="index" collection="lotteryLineList" separator="or">
                (
                <if test="item.startProvinceName != null and item.startProvinceName != ''">
                    TBTO.START_PROVINCE_NAME = #{item.startProvinceName}
                </if>
                <if test="item.startCityName != null and item.startCityName != ''">
                    and TBTO.START_CITY_NAME = #{item.startCityName}
                </if>
                <if test="item.startCountyName != null and item.startCountyName != ''">
                    and TBTO.START_COUNTY_NAME = #{item.startCountyName}
                </if>
                <if test="item.endProvinceName != null and item.endProvinceName != ''">
                    and TBTO.END_PROVINCE_NAME = #{item.endProvinceName}
                </if>
                <if test="item.endCityName != null and item.endCityName != ''">
                    and TBTO.END_CITY_NAME = #{item.endCityName}
                </if>
                <if test="item.endCountyName != null and item.endCountyName != ''">
                    and TBTO.END_COUNTY_NAME = #{item.endCountyName}
                </if>
                <include refid="shieldConsignee"/>
                <if test="item.boTransOrderIds != null and item.boTransOrderIds.size() > 0">
                    TBTO.BO_TRANS_ORDER_ID IN
                    <foreach collection="item.boTransOrderIds" index="index" open="(" close=")" item="id" separator=",">
                        <if test="(index % 999) == 998"> NULL) OR TBTO.BO_TRANS_ORDER_ID IN(</if>#{id}
                    </foreach>
                </if>
                )
            </foreach>)
        </if>
    </sql>

    <sql id="shieldConsignee">
        <if test="item.shieldConsigneeList != null and item.shieldConsigneeList.size() > 0">
            AND NOT (
            <foreach item="item1" index="index" collection="item.shieldConsigneeList" separator="or">
                (
                TBTO.CONSIGNEE_UNIT = #{item1.consigneeName} and TBTO.CONSIGNEE_UNIT is not null
                <if test="item1.endProvinceName != null and item1.endProvinceName != ''">
                    and TBTO.END_PROVINCE_NAME = #{item1.endProvinceName}
                </if>
                <if test="item1.endCityName != null and item1.endCityName != ''">
                    and TBTO.END_CITY_NAME = #{item1.endCityName}
                </if>
                <if test="item1.endCountyName != null and item1.endCountyName != ''">
                    and TBTO.END_COUNTY_NAME = #{item1.endCountyName}
                </if>
                <if test="item1.endAddress != null and item1.endAddress != ''">
                    and TBTO.END_ADDRESS = #{item1.endAddress}
                </if>
                )
            </foreach>)
        </if>
    </sql>

    <sql id="stateConditionForDistributeOrder">
        <!-- 任务状态筛选 -->
        <choose>
            <when test="state != null and state == '0'.toString()">
                <!--0：全部  查询全部时，不做节点限制
                <choose>
                    <when test="configId != null and configId == '104'.toString()">
                        AND TBTT.NODE_ID IN (0,100,200,300,400,500,600,650,700,800,1000,1100,1200)
                    </when>
                    <when test="configId != null and configId == '105'.toString()">
                        AND TBTT.NODE_ID IN (0,100,200,300,400,501,600,650,700,800,1000,1100,1200)
                    </when>
                </choose>
                -->
            </when>
            <!-- 1：待派车   -->
            <when test="state != null and state == '1'.toString()">
                AND (TBTT.NODE_ID IN (0, 100, 300) or TBTT.NODE_ID IS NULL)
                 <if test="queryFhOrderMark != null and queryFhOrderMark != ''">
                         AND NOT EXISTS(
                             SELECT 1 FROM T_BO_TO_FH_ORDER_REL TBTFOR
                                 WHERE TBTFOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID
                                AND TBTFOR.IS_DEL = 0)
                 </if>
            </when>
            <!-- 2：待到场    -->
            <when test="state != null and state == '2'.toString()">
                <choose>
                    <when test="configId != null and configId == '104'.toString()">
                        AND TBTT.NODE_ID IN (200, 400)
                    </when>
                    <when test="configId != null and configId == '105'.toString()">
                        AND TBTT.NODE_ID IN(200,501)
                    </when>
                </choose>
            </when>
            <!--3：待发车  -->
            <when test="state != null and state == '3'.toString()">
                <choose>
                    <when test="configId != null and configId == '104'.toString()">
                        AND TBTT.NODE_ID = 500
                    </when>
                    <when test="configId != null and configId == '105'.toString()">
                        AND TBTT.NODE_ID = 400
                    </when>
                </choose>
            </when>
            <!-- 4：运输中   -->
            <when test="state != null and state == '4'.toString()">
                AND TBTT.NODE_ID IN (600,650)
            </when>
            <!-- 5：运输完成 -->
            <when test="state != null and state == '5'.toString()">
                <choose>
                    <when test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
                        AND TBTT.STATE = 2 AND NVL(TBTTA.PAY_STATE, 0) != 2
                    </when>
                    <otherwise>
                        AND TBTT.NODE_ID IN (700,800,1000,1100)
                    </otherwise>
                </choose>
            </when>
            <!-- 6：已结算   -->
            <when test="state != null and state == '6'.toString()">
                <choose>
                    <when test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
                        AND TBTT.STATE = 2 AND NVL(TBTTA.PAY_STATE, 0) = 2
                    </when>
                    <otherwise>
                        AND TBTT.NODE_ID = 1200
                    </otherwise>
                </choose>
            </when>

            <!-- 7待派车-一手货找车中   -->
            <when test="state != null and state == '7'.toString()">
                AND (TBTT.NODE_ID IN (0, 100, 300) or TBTT.NODE_ID IS NULL)
                AND EXISTS(
                SELECT 1 FROM T_BO_TO_FH_ORDER_REL TBTFOR
                WHERE TBTFOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID
                AND TBTFOR.IS_DEL = 0 AND TBTFOR.FH_STATE = 0
                )
            </when>
            <!-- 8：待派车-一手货已订车   -->
            <when test="state != null and state == '8'.toString()">
                AND (TBTT.NODE_ID IN (0, 100, 300) or TBTT.NODE_ID IS NULL)
                AND EXISTS(
                SELECT 1 FROM T_BO_TO_FH_ORDER_REL TBTFOR
                WHERE TBTFOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID
                AND TBTFOR.IS_DEL = 0 AND TBTFOR.FH_STATE = 1
                )
            </when>
        </choose>
    </sql>

    <sql id="searchConditionForDistributeOrder">
        <!--询价状态inquireState-->
        <if test="inquireState != null and inquireState != ''">
            AND EXISTS(
            SELECT 1 FROM T_BO_INQUIRE_USER_REL TBIUR
            INNER JOIN T_BO_INQUIRE TBI ON TBIUR.BO_TRANS_ORDER_ID = TBI.BO_TRANS_ORDER_ID AND TBI.IS_DEL = 0
            WHERE TBIUR.IS_DEL = 0
            AND TBIUR.USER_ID = #{userId}
            AND TBIUR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID
            <if test="inquireState !='2'.toString()">
                AND TBI.INQUIRE_STATE = #{inquireState}
            </if>
            <if test="inquireState =='2'.toString()">
                AND TBI.INQUIRE_STATE in (2,3)
            </if>
            )
        </if>
        <!--配载状态stowageState-->
        <if test="stowageState != null and stowageState != ''">
            <if test="stowageState == 1">
                <!-- 未配载，则不存在已配载数据+不存在运输计划 -->
                AND NOT EXISTS(
                SELECT 1 FROM T_BO_TP_STOWAGE_ORDER_REL TBTSOR
                WHERE TBTSOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID
                AND TBTSOR.IS_DEL = 0
                AND DECODE(TBTSOR.STOWAGE_STATE,1,1,2,2,3,2,4,1,NULL,1) = 2
                )
                and TBTOR.BO_TRANS_ORDER_ID is null
            </if>
            <if test="stowageState == 2">
                <!-- 已配载，则存在已配载数据 -->
                AND EXISTS(
                SELECT 1 FROM T_BO_TP_STOWAGE_ORDER_REL TBTSOR
                WHERE TBTSOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID
                AND TBTSOR.IS_DEL = 0
                AND DECODE(TBTSOR.STOWAGE_STATE,1,1,2,2,3,2,4,1,NULL,1) = 2
                )
            </if>
        </if>
        <!--回单签收状态-->
        <if test="receiptReceiveState != null and receiptReceiveState != ''">
            AND nvl(TBTO.RECEIPT_RECEIVE_STATE,0)=#{receiptReceiveState}
        </if>
        <!--里程区间maxMileage-->
        <if test="minMileage != null and minMileage != ''">
            AND TBTO.MILEAGE >= #{minMileage}
        </if>
        <if test="maxMileage != null and maxMileage != ''">
            AND TBTO.MILEAGE &lt;= #{maxMileage}
        </if>

        <!--目的地endPlace-->
        <if test="endPlace != null and endPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="endPlace" separator="OR">
                (
                <if test="item.provinceName != null and item.provinceName != ''">
                    TBTO.END_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    AND TBTO.END_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    AND TBTO.END_COUNTY_NAME = #{item.areaName}
                </if>
                )
            </foreach>)
        </if>
        <!--装货地endPlace-->
        <if test="startPlace != null and startPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="startPlace" separator="OR">
                (
                <if test="item.provinceName != null and item.provinceName != ''">
                    TBTO.START_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    AND TBTO.START_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    AND TBTO.START_COUNTY_NAME = #{item.areaName}
                </if>
                )
            </foreach>)
        </if>

        <!--发车日期transDateStart-->
        <if test="transDateStart != null and transDateStart !=''">
            AND TBTO.TRANS_DATE &gt;= TO_DATE(#{transDateStart},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!--发车日期transDateEnd-->
        <if test="transDateEnd != null and transDateEnd !=''">
            AND TBTO.TRANS_DATE &lt;= TO_DATE(#{transDateEnd},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!--创建日期createdDateStart-->
        <if test="createdDateStart != null and createdDateStart !=''">
            AND TBTO.CREATED_TIME &gt;= TO_DATE(#{createdDateStart},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!--创建日期createdDateEnd-->
        <if test="createdDateEnd != null and createdDateEnd !=''">
            AND TBTO.CREATED_TIME &lt;= TO_DATE(#{createdDateEnd},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!--分发状态distributeStatus-->
        <if test="distributeStatus != null and distributeStatus !=''">
            AND NVL2(TBTO.CARRIER, 1,  0) = #{distributeStatus}
        </if>
        <!--拆分状态spiltState-->
        <if test="spiltState != null and spiltState !=''">
            <choose>
                <when test="spiltState==1">
                    AND TBTO.SPILT_PROGRESS > 0 AND TBTO.SPILT_PROGRESS &lt; 100
                </when>
                <when test="spiltState==2">
                    AND TBTO.SPILT_PROGRESS >= 100
                </when>
                <otherwise>
                    AND (nvl(TBTO.GOODS_WEIGHT_REMAIN,0) > 0 OR nvl(TBTO.GOODS_VOLUME_REMAIN,0) > 0 OR nvl(TBTO.GOODS_CASE_PACK_REMAIN,0) > 0)
                    AND TBTOR.BO_TRANS_ORDER_REL_ID IS NULL
                </otherwise>
            </choose>
        </if>
        <if test="ddDispatcherList != null and ddDispatcherList.size() > 0">
            and TBTNR.USER_ID in
            <foreach collection="ddDispatcherList" open="(" close=")" separator="," item="item">
                #{item.userId}
            </foreach>
        </if>
        <if test="gysDispatcherList != null and gysDispatcherList.size() > 0">
            and TBTNR.GROUP_ID in
            <foreach collection="gysDispatcherList" open="(" close=")" separator="," item="item">
                #{item.supplierId}
            </foreach>
        </if>
        <!-- 自定义条件搜索 -->
        <include refid="queryParamCondition" />
        <!-- 业务运作PC运力池订货证 未匹配司机且未派车的计划 -->
        <if test="mergeIdList != null and mergeIdList.size() > 0">
                AND TBTO.MERGE_ID IN
                <foreach collection="mergeIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
        </if>

        <!-- 数据权限 -->
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                        (
                        TBOGR.GROUP_ID =#{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND TBTO.USER_ID = #{scopeItem.userId}
                        </if>
                        )
                    </foreach>
                    )
                </if>
                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
                )
            </foreach>
            )
        </if>

    </sql>

    <!-- 自定义的动态查询条件 -->
    <sql id="queryParamCondition">
        <if test="queryParamStr != null">
            <if test="queryParamStr.mixQuery != null and queryParamStr.mixQuery.size() > 0">
                AND (
                <foreach item="item" index="index" collection="queryParamStr.mixQuery" separator=" OR ">
                    <!-- 精确搜索 -->
                    <if test="item.isPrecise != null and item.isPrecise == '1'.toString()">
                        ${item.paramKey} IN
                        <foreach item="labelValue" index="index" collection="item.labelValue" close=")" open="(" separator=",">
                            #{labelValue}
                        </foreach>
                    </if>
                    <if test="item.isPrecise != null and item.isPrecise == '0'.toString()">
                        <foreach item="labelValue" index="index" collection="item.labelValue" close=")" open="(" separator=" OR ">
                            ${item.paramKey} LIKE '%' || #{labelValue} || '%'
                        </foreach>
                    </if>
                </foreach>
                )
            </if>
            <if test="queryParamStr.singleQuery != null and queryParamStr.singleQuery.size() > 0">
                AND (
                <foreach item="item" index="index" collection="queryParamStr.singleQuery" separator=" AND ">
                    <choose>
                        <when test="item.labelType == '2'.toString()">
                            <!--日期格式查询-->
                                 ${item.paramKey} &gt;= TO_DATE(#{item.labelValue[0]},'YYYY-MM-DD HH24:MI:SS')
                            AND  ${item.paramKey} &lt;= TO_DATE(#{item.labelValue[1]},'YYYY-MM-DD HH24:MI:SS')
                        </when>
                        <otherwise>
                            <!-- 精确搜索 -->
                            <if test="item.isPrecise != null and item.isPrecise == '1'.toString()">
                                ${item.paramKey} IN
                                <foreach item="labelValue" index="index" collection="item.labelValue" close=")" open="(" separator=",">
                                    #{labelValue}
                                </foreach>
                            </if>
                            <if test="item.isPrecise != null and item.isPrecise == '0'.toString()">
                                <foreach item="labelValue" index="index" collection="item.labelValue" close=")" open="(" separator=" OR ">
                                    ${item.paramKey} LIKE '%' || #{labelValue} || '%'
                                </foreach>
                            </if>

                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
        </if>
    </sql>

    <sql id="distributeModeAdapter">
        <choose>
            <when test="queryOwnerOrgId != null and queryOwnerOrgId != ''">
                TBTO.org_id = #{queryOwnerOrgId}
            </when>
            <when test="isAdminView">
                TBTO.ORG_ID IN
                <foreach collection="queryOrgIdList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                TBTO.org_id = #{orgId}
            </otherwise>
        </choose>
        AND TBTO.ORDER_TYPE =1
    </sql>


    <sql id="queryDistributeOrderPlanListByMergeIdsWithAsSql">
        WITH RankedResults AS
        (
        SELECT ROWNUM RN, TT.* FROM (
        SELECT
        TBTO.MERGE_ID,
        TBTO.BO_TRANS_ORDER_ID,
        TBTO.CUSTOMER_ORDER_NO,
        TBTO.CREATED_TIME,
        TBTO.LAST_MODIFIED_TIME,
        TBTO.TRANS_DATE,
        TBTNDL.DEAD_LINE_TIME,
        TBTT.BO_TRANS_TASK_ID,
        DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) DISPATCH_STATE,
        TBTT.NODE_ID,
        TBTTE.DISPATCH_CAR_TIME,
        TBTT.TAX_WAYBILL_NO,
        NVL2(TBTO.CARRIER, 1, 0) DISTRIBUTE_STATE,
        CASE
        WHEN TBTT.OWNER_ORG_ID != TBTNR.ORG_ID THEN '2'
        WHEN TBTT.OWNER_ORG_ID = TBTNR.ORG_ID AND TBTNR.SYS_ROLE_TYPE = 12005 THEN '2'
        ELSE '1'
        END disPatchType,
        TBTNR.USER_ID disPatcherUserId,
        TBTNR.GROUP_ID disPatcherSupplierId

        FROM T_BO_TRANS_ORDER TBTO
        <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
            INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0 AND GROUP_TYPE = 4
        </if>
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE TBTNDL ON TBTT.BO_TRANS_TASK_ID = TBTNDL.BO_TRANS_TASK_ID AND TBTNDL.IS_DEL = 0  AND TBTNDL.NODE_ID = 200
        LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        WHERE TBTO.MERGE_ID IN
        <foreach collection="pageMergeIdList" open="(" close=")" separator="," item="mergeId">
            #{mergeId}
        </foreach>
        <!-- 处理已删除情况的条件 -->
        <choose>
            <!-- 已删除TAB -->
            <when test="tabState != null and tabState == '100'.toString()">
                AND TBTO.IS_DEL = 1
            </when>
            <otherwise>
                AND TBTO.IS_DEL = 0
            </otherwise>
        </choose>
        ) TT
        )
    </sql>

    <select id="queryDghPlanBaseMergeList" resultType="String" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        SELECT mergeId from (
        SELECT mergeId ,MIN(RNN) groupOrder FROM (
            SELECT TBTO.MERGE_ID mergeId,
                    ROWNUM RNN
            FROM T_BO_TRANS_ORDER TBTO
            INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
            INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
            <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
                AND TBOGR.GROUP_TYPE = 4
            </if>
            LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
            <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
                LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TBTTA ON TBTT.BO_TRANS_TASK_ID = TBTTA.BO_TRANS_TASK_ID AND TBTTA.IS_DEL = 0
            </if>
            <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
            <if test="dispatcherList != null and dispatcherList.size() > 0">
                LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and
                TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
            </if>
            LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE TBTNDL ON TBTT.BO_TRANS_TASK_ID = TBTNDL.BO_TRANS_TASK_ID AND TBTNDL.IS_DEL
            = 0 AND TBTNDL.NODE_ID = 200
            WHERE <include refid="distributeModeAdapter"/>
            <include refid="tabConditionForDistributeOrder"/>
            <include refid="stateConditionForDistributeOrder"/>
            <include refid="searchConditionForDistributeOrder"/>
            <choose>
                <!-- 已删除TAB -->
                <when test="tabState != null and tabState == '100'.toString()">
                    AND TBTO.IS_DEL = 1
                </when>
                <otherwise>
                    AND TBTO.IS_DEL = 0
                </otherwise>
            </choose>
            <choose>
                <when test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
                    ORDER BY TBOGR.CREATED_TIME DESC
                </when>
                <otherwise>
                    ORDER BY ${orderByStr}
                </otherwise>
            </choose>
        ) TEMP GROUP BY mergeId ) ORDER BY groupOrder
    </select>



    <select id="queryDhPlanBaseListByMergeIdsForDistributeOrder" resultType="com.wtyt.bo.bean.DhPlanBaseBean" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        <include refid="queryDistributeOrderPlanListByMergeIdsWithAsSql" />
        SELECT
        T.MERGE_ID mergeId,
        T.BO_TRANS_ORDER_ID boTransOrderId,
        T.CUSTOMER_ORDER_NO customerOrderNo,
        O.THIRD_ORDER_NO thirdOrderNo,
        TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        T.TRANS_DATE transDate,
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.TAX_WAYBILL_NO taxWaybillNo,
        T.DISTRIBUTE_STATE distributeState,
        D.disPatchType dispatchType,
        D.disPatcherUserId dispatcherUserId,
        D.disPatcherSupplierId dispatcherSupplierId,
        O.GOODS_NAME goodsName,
        NVL(O.GOODS_CASE_PACK, 0) goodsCasePack,
        NVL(O.GOODS_WEIGHT, 0) goodsWeight,
        NVL(O.GOODS_VOLUME, 0) goodsVolume,
        NVL(O.LOADING_CAR_NUM, 0) loadingCarNum,
        NVL(O.GOODS_CASE_PACK_REMAIN, 0) goodsCasePackRemain,
        NVL(O.GOODS_WEIGHT_REMAIN, 0) goodsWeightRemain,
        NVL(O.GOODS_VOLUME_REMAIN, 0) goodsVolumeRemain,
        NVL(O.LOADING_CAR_NUM, 0) - T.DISPATCH_CAR_NUM  loadingCarNumRemain,
        O.START_PROVINCE_NAME startProvinceName,
        O.START_CITY_NAME startCityName,
        O.START_COUNTY_NAME startCountyName,
        O.START_ADDRESS startAddress,
        O.RAW_START_ADDRESS rawStartAddress,
        O.END_PROVINCE_NAME endProvinceName,
        O.END_CITY_NAME endCityName,
        O.END_COUNTY_NAME endCountyName,
        O.END_ADDRESS endAddress,
        O.RAW_END_ADDRESS rawEndAddress,
        O.URGENT_MARK  urgentMark,
        O.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        O.GOODS_WEIGHT_UNIT goodsWeightUnit,
        O.GOODS_VOLUME_UNIT goodsVolumeUnit,
        O.ACCOUNT_GROUP_ID accountGroupId,
        decode( O.UPSTREAM_STATE,0,1,1,2,0) upstreamState,
        TO_CHAR(O.SPILT_PROGRESS, 'FM9999999990') spiltProgress
        <if test="terminalType == 'APP-XDL'">
            ,O.SERVICE_REQUIRE serviceRequire,
            O.CUSTOMER_NAME customerName,
            O.START_CITY_NAME || O.START_COUNTY_NAME loadingArea,
            O.END_CITY_NAME || O.END_COUNTY_NAME unloadingArea,
            O.GOODS_SPEC goodsSpec,
            O.CONSIGNEE_UNIT consigneeUnit
        </if>
        FROM
        (
        (SELECT
        MIN(RN) RN,
        MERGE_ID,
        BO_TRANS_ORDER_ID,
        CUSTOMER_ORDER_NO,
        CREATED_TIME,
        TRANS_DATE,
        MIN(BO_TRANS_TASK_ID) BO_TRANS_TASK_ID,
        COUNT(BO_TRANS_TASK_ID) DISPATCH_CAR_NUM,
        listagg(CASE WHEN rnn&lt;=200 THEN TAX_WAYBILL_NO ELSE NULL END , ',') WITHIN GROUP (ORDER BY RN)  TAX_WAYBILL_NO,
        DISTRIBUTE_STATE
        FROM
        (
            SELECT RN ,
            MERGE_ID,
            BO_TRANS_ORDER_ID,
            CUSTOMER_ORDER_NO,
            CREATED_TIME,
            TRANS_DATE,
            BO_TRANS_TASK_ID,
            TAX_WAYBILL_NO,
            ROW_NUMBER() OVER(PARTITION BY BO_TRANS_ORDER_ID  ORDER BY rn ) rnn,
            DISTRIBUTE_STATE
            FROM RankedResults RRS
        )
        GROUP BY MERGE_ID, BO_TRANS_ORDER_ID, CUSTOMER_ORDER_NO, CREATED_TIME, TRANS_DATE, DISTRIBUTE_STATE) T
        INNER JOIN (SELECT MERGE_ID, MIN(RNN) groupOrder FROM (SELECT RR.*, ROWNUM RNN FROM RankedResults RR) GROUP BY MERGE_ID) R ON R.MERGE_ID = T.MERGE_ID
        LEFT JOIN (SELECT DISTINCT BO_TRANS_TASK_ID, disPatchType, disPatcherUserId, disPatcherSupplierId FROM RankedResults RR) D ON D.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_ORDER O ON  O.BO_TRANS_ORDER_ID = T.BO_TRANS_ORDER_ID
        )
        ORDER BY R.groupOrder ASC, T.RN ASC
    </select>


    <sql id="queryPlanListByMergeIdsWithAsSql">
        WITH RankedResults AS
        (
        SELECT ROWNUM RN, TT.* FROM (
        SELECT
        TBTO.MERGE_ID,
        TBTO.BO_TRANS_ORDER_ID,
        TBTO.CUSTOMER_ORDER_NO,
        TBTO.CREATED_TIME,
        TBTO.LAST_MODIFIED_TIME,
        TBTO.TRANS_DATE,
        TBTNDL.DEAD_LINE_TIME,
        TBTT.BO_TRANS_TASK_ID,
        DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) DISPATCH_STATE,
        TBTT.NODE_ID,
        TBTTE.DISPATCH_CAR_TIME,
        TBTT.TAX_WAYBILL_NO,
        NVL2(TBTO.CARRIER, 1, 0) DISTRIBUTE_STATE,
        CASE
        WHEN TBTT.OWNER_ORG_ID != TBTNR.ORG_ID THEN '2'
        WHEN TBTT.OWNER_ORG_ID = TBTNR.ORG_ID AND TBTNR.SYS_ROLE_TYPE = 12005 THEN '2'
        ELSE '1'
        END disPatchType,
        TBTNR.USER_ID disPatcherUserId,
        TBTNR.GROUP_ID disPatcherSupplierId
        FROM T_BO_TRANS_ORDER TBTO
        <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
            INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0 AND GROUP_TYPE = 4
        </if>
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE TBTNDL ON TBTT.BO_TRANS_TASK_ID = TBTNDL.BO_TRANS_TASK_ID AND TBTNDL.IS_DEL = 0  AND TBTNDL.NODE_ID = 200
        LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        WHERE TBTO.MERGE_ID IN
        <foreach collection="pageMergeIdList" open="(" close=")" separator="," item="mergeId">
            #{mergeId}
        </foreach>
        <!-- 处理已删除情况的条件 -->
        <choose>
            <!-- 已删除TAB -->
            <when test="tabState != null and tabState == '100'.toString()">
                AND TBTO.IS_DEL = 1
            </when>
            <otherwise>
                AND TBTO.IS_DEL = 0
            </otherwise>
        </choose>
        ) TT
        )
    </sql>


    <select id="queryDhPlanBaseListByMergeIds" resultType="com.wtyt.bo.bean.DhPlanBaseBean" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        <include refid="queryPlanListByMergeIdsWithAsSql" />
        SELECT
        T.MERGE_ID mergeId,
        T.BO_TRANS_ORDER_ID boTransOrderId,
        T.CUSTOMER_ORDER_NO customerOrderNo,
        O.THIRD_ORDER_NO thirdOrderNo,
        TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        T.TRANS_DATE transDate,
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.TAX_WAYBILL_NO taxWaybillNo,
        T.DISTRIBUTE_STATE distributeState,
        D.disPatchType dispatchType,
        D.disPatcherUserId dispatcherUserId,
        D.disPatcherSupplierId dispatcherSupplierId,
        O.ORG_ID orgId,
        O.ACCOUNT_GROUP_ID accountGroupId,
        O.GOODS_NAME goodsName,
        NVL(O.GOODS_CASE_PACK, 0) goodsCasePack,
        NVL(O.GOODS_WEIGHT, 0) goodsWeight,
        NVL(O.GOODS_VOLUME, 0) goodsVolume,
        NVL(O.LOADING_CAR_NUM, 0) loadingCarNum,
        NVL(O.GOODS_CASE_PACK_REMAIN, 0) goodsCasePackRemain,
        NVL(O.GOODS_WEIGHT_REMAIN, 0) goodsWeightRemain,
        NVL(O.GOODS_VOLUME_REMAIN, 0) goodsVolumeRemain,
        NVL(O.LOADING_CAR_NUM, 0) - T.DISPATCH_CAR_NUM  loadingCarNumRemain,
        O.START_PROVINCE_NAME startProvinceName,
        O.START_CITY_NAME startCityName,
        O.START_COUNTY_NAME startCountyName,
        O.START_ADDRESS startAddress,
        O.RAW_START_ADDRESS rawStartAddress,
        O.END_PROVINCE_NAME endProvinceName,
        O.END_CITY_NAME endCityName,
        O.END_COUNTY_NAME endCountyName,
        O.END_ADDRESS endAddress,
        O.RAW_END_ADDRESS rawEndAddress,
        O.URGENT_MARK  urgentMark,
        O.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        O.GOODS_WEIGHT_UNIT goodsWeightUnit,
        O.GOODS_VOLUME_UNIT goodsVolumeUnit,
        nvl(O.HIERARCHY_TYPE,0) hierarchyType,
        O.DT_INSIDE_ORDER_MARK dtInsideOrderMark,
        decode( O.UPSTREAM_STATE,0,1,1,2,0) upstreamState,
        TO_CHAR(O.SPILT_PROGRESS, 'FM9999999990') spiltProgress
        <if test="terminalType == 'APP-XDL'">
            ,O.SERVICE_REQUIRE serviceRequire,
            O.CUSTOMER_NAME customerName,
            O.START_CITY_NAME || O.START_COUNTY_NAME loadingArea,
            O.END_CITY_NAME || O.END_COUNTY_NAME unloadingArea,
            O.GOODS_SPEC goodsSpec,
            O.CONSIGNEE_UNIT consigneeUnit
        </if>
        FROM
        (
        (SELECT
        MIN(RN) RN,
        MERGE_ID,
        BO_TRANS_ORDER_ID,
        CUSTOMER_ORDER_NO,
        CREATED_TIME,
        TRANS_DATE,
        MIN(BO_TRANS_TASK_ID) BO_TRANS_TASK_ID,
        COUNT(BO_TRANS_TASK_ID) DISPATCH_CAR_NUM,
        listagg(CASE WHEN rnn&lt;=200 THEN TAX_WAYBILL_NO ELSE NULL END , ',') WITHIN GROUP (ORDER BY RN)  TAX_WAYBILL_NO,
        DISTRIBUTE_STATE
        FROM
        (
            SELECT RN ,
            MERGE_ID,
            BO_TRANS_ORDER_ID,
            CUSTOMER_ORDER_NO,
            CREATED_TIME,
            TRANS_DATE,
            BO_TRANS_TASK_ID,
            TAX_WAYBILL_NO,
            ROW_NUMBER() OVER(PARTITION BY BO_TRANS_ORDER_ID  ORDER BY rn ) rnn,
            DISTRIBUTE_STATE
            FROM RankedResults RRS
        )
        GROUP BY MERGE_ID, BO_TRANS_ORDER_ID, CUSTOMER_ORDER_NO, CREATED_TIME, TRANS_DATE, DISTRIBUTE_STATE) T
        INNER JOIN (SELECT MERGE_ID, MIN(RNN) groupOrder FROM (SELECT RR.*, ROWNUM RNN FROM RankedResults RR) GROUP BY MERGE_ID) R ON R.MERGE_ID = T.MERGE_ID
        LEFT JOIN (SELECT DISTINCT BO_TRANS_TASK_ID, disPatchType, disPatcherUserId, disPatcherSupplierId FROM RankedResults RR) D ON D.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_ORDER O ON  O.BO_TRANS_ORDER_ID = T.BO_TRANS_ORDER_ID
        )
        ORDER BY R.groupOrder ASC, T.RN ASC
    </select>


    <select id="queryDhPlanBaseListForDistributeOrder" resultType="com.wtyt.bo.bean.DhPlanBaseBean" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        <include refid="queryDistributeOrderPlanListWithAsSql" />
        SELECT
        T.MERGE_ID mergeId,
        T.BO_TRANS_ORDER_ID boTransOrderId,
        T.CUSTOMER_ORDER_NO customerOrderNo,
        O.THIRD_ORDER_NO thirdOrderNo,
        TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        T.TRANS_DATE transDate,
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.TAX_WAYBILL_NO taxWaybillNo,
        T.DISTRIBUTE_STATE distributeState,
        D.disPatchType dispatchType,
        D.disPatcherUserId dispatcherUserId,
        D.disPatcherSupplierId dispatcherSupplierId,
        O.GOODS_NAME goodsName,
        NVL(O.GOODS_CASE_PACK, 0) goodsCasePack,
        NVL(O.GOODS_WEIGHT, 0) goodsWeight,
        NVL(O.GOODS_VOLUME, 0) goodsVolume,
        NVL(O.LOADING_CAR_NUM, 0) loadingCarNum,
        NVL(O.GOODS_CASE_PACK_REMAIN, 0) goodsCasePackRemain,
        NVL(O.GOODS_WEIGHT_REMAIN, 0) goodsWeightRemain,
        NVL(O.GOODS_VOLUME_REMAIN, 0) goodsVolumeRemain,
        NVL(O.LOADING_CAR_NUM, 0) - T.DISPATCH_CAR_NUM  loadingCarNumRemain,
        O.START_PROVINCE_NAME startProvinceName,
        O.START_CITY_NAME startCityName,
        O.START_COUNTY_NAME startCountyName,
        O.START_ADDRESS startAddress,
        O.RAW_START_ADDRESS rawStartAddress,
        O.END_PROVINCE_NAME endProvinceName,
        O.END_CITY_NAME endCityName,
        O.END_COUNTY_NAME endCountyName,
        O.END_ADDRESS endAddress,
        O.RAW_END_ADDRESS rawEndAddress,
        O.URGENT_MARK  urgentMark,
        O.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        O.GOODS_WEIGHT_UNIT goodsWeightUnit,
        O.GOODS_VOLUME_UNIT goodsVolumeUnit,
        O.ACCOUNT_GROUP_ID accountGroupId,
        decode( O.UPSTREAM_STATE,0,1,1,2,0) upstreamState,
        TO_CHAR(O.SPILT_PROGRESS, 'FM9999999990') spiltProgress
        <if test="terminalType == 'APP-XDL'">
            ,O.SERVICE_REQUIRE serviceRequire,
            O.CUSTOMER_NAME customerName,
            O.START_CITY_NAME || O.START_COUNTY_NAME loadingArea,
            O.END_CITY_NAME || O.END_COUNTY_NAME unloadingArea,
            O.GOODS_SPEC goodsSpec,
            O.CONSIGNEE_UNIT consigneeUnit
        </if>
        FROM
        (
        (SELECT
        MIN(RN) RN,
        MERGE_ID,
        BO_TRANS_ORDER_ID,
        CUSTOMER_ORDER_NO,
        CREATED_TIME,
        TRANS_DATE,
        MIN(BO_TRANS_TASK_ID) BO_TRANS_TASK_ID,
        COUNT(BO_TRANS_TASK_ID) DISPATCH_CAR_NUM,
        listagg(CASE WHEN rnn&lt;=200 THEN TAX_WAYBILL_NO ELSE NULL END , ',') WITHIN GROUP (ORDER BY RN)  TAX_WAYBILL_NO,
        DISTRIBUTE_STATE
        FROM
        (
            SELECT RN ,
            MERGE_ID,
            BO_TRANS_ORDER_ID,
            CUSTOMER_ORDER_NO,
            CREATED_TIME,
            TRANS_DATE,
            BO_TRANS_TASK_ID,
            TAX_WAYBILL_NO,
            ROW_NUMBER() OVER(PARTITION BY BO_TRANS_ORDER_ID  ORDER BY rn ) rnn,
            DISTRIBUTE_STATE
            FROM RankedResults RRS
        )
        GROUP BY MERGE_ID, BO_TRANS_ORDER_ID, CUSTOMER_ORDER_NO, CREATED_TIME, TRANS_DATE, DISTRIBUTE_STATE) T
        INNER JOIN (SELECT MERGE_ID, MIN(RNN) groupOrder FROM (SELECT RR.*, ROWNUM RNN FROM RankedResults RR) GROUP BY MERGE_ID) R ON R.MERGE_ID = T.MERGE_ID
        LEFT JOIN (SELECT DISTINCT BO_TRANS_TASK_ID, disPatchType, disPatcherUserId, disPatcherSupplierId FROM RankedResults RR) D ON D.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_ORDER O ON  O.BO_TRANS_ORDER_ID = T.BO_TRANS_ORDER_ID
        )
        ORDER BY R.groupOrder ASC, T.RN ASC
    </select>


    <select id="queryDhPlanMergeIdList" resultType="String" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        <include refid="queryPlanListWithAsSql" />
        SELECT
        R.MERGE_ID mergeId
        FROM(SELECT MERGE_ID, MIN(RNN) groupOrder FROM (SELECT RR.*, ROWNUM RNN FROM RankedResults RR) GROUP BY MERGE_ID) R
        ORDER BY R.groupOrder ASC
    </select>

    <select id="queryDhPlanBaseList" resultType="com.wtyt.bo.bean.DhPlanBaseBean" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        <include refid="queryPlanListWithAsSql" />
        SELECT
        T.MERGE_ID mergeId,
        T.BO_TRANS_ORDER_ID boTransOrderId,
        T.CUSTOMER_ORDER_NO customerOrderNo,
        O.THIRD_ORDER_NO thirdOrderNo,
        TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        T.TRANS_DATE transDate,
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.TAX_WAYBILL_NO taxWaybillNo,
        T.DISTRIBUTE_STATE distributeState,
        D.disPatchType dispatchType,
        D.disPatcherUserId dispatcherUserId,
        D.disPatcherSupplierId dispatcherSupplierId,
        O.ORG_ID orgId,
        O.ACCOUNT_GROUP_ID accountGroupId,
        O.GOODS_NAME goodsName,
        NVL(O.GOODS_CASE_PACK, 0) goodsCasePack,
        NVL(O.GOODS_WEIGHT, 0) goodsWeight,
        NVL(O.GOODS_VOLUME, 0) goodsVolume,
        NVL(O.LOADING_CAR_NUM, 0) loadingCarNum,
        NVL(O.GOODS_CASE_PACK_REMAIN, 0) goodsCasePackRemain,
        NVL(O.GOODS_WEIGHT_REMAIN, 0) goodsWeightRemain,
        NVL(O.GOODS_VOLUME_REMAIN, 0) goodsVolumeRemain,
        NVL(O.LOADING_CAR_NUM, 0) - T.DISPATCH_CAR_NUM  loadingCarNumRemain,
        O.START_PROVINCE_NAME startProvinceName,
        O.START_CITY_NAME startCityName,
        O.START_COUNTY_NAME startCountyName,
        O.START_ADDRESS startAddress,
        O.RAW_START_ADDRESS rawStartAddress,
        O.END_PROVINCE_NAME endProvinceName,
        O.END_CITY_NAME endCityName,
        O.END_COUNTY_NAME endCountyName,
        O.END_ADDRESS endAddress,
        O.RAW_END_ADDRESS rawEndAddress,
        O.URGENT_MARK  urgentMark,
        O.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        O.GOODS_WEIGHT_UNIT goodsWeightUnit,
        O.GOODS_VOLUME_UNIT goodsVolumeUnit,
        nvl(O.HIERARCHY_TYPE,0) hierarchyType,
        O.DT_INSIDE_ORDER_MARK dtInsideOrderMark,
        decode( O.UPSTREAM_STATE,0,1,1,2,0) upstreamState,
        TO_CHAR(O.SPILT_PROGRESS, 'FM9999999990') spiltProgress
        <if test="terminalType == 'APP-XDL'">
            ,O.SERVICE_REQUIRE serviceRequire,
            O.CUSTOMER_NAME customerName,
            O.START_CITY_NAME || O.START_COUNTY_NAME loadingArea,
            O.END_CITY_NAME || O.END_COUNTY_NAME unloadingArea,
            O.GOODS_SPEC goodsSpec,
            O.CONSIGNEE_UNIT consigneeUnit
        </if>
        FROM
        (
        (SELECT
        MIN(RN) RN,
        MERGE_ID,
        BO_TRANS_ORDER_ID,
        CUSTOMER_ORDER_NO,
        CREATED_TIME,
        TRANS_DATE,
        MIN(BO_TRANS_TASK_ID) BO_TRANS_TASK_ID,
        COUNT(BO_TRANS_TASK_ID) DISPATCH_CAR_NUM,
        listagg(CASE WHEN rnn&lt;=200 THEN TAX_WAYBILL_NO ELSE NULL END , ',') WITHIN GROUP (ORDER BY RN)  TAX_WAYBILL_NO,
        DISTRIBUTE_STATE
        FROM
        (
            SELECT RN ,
            MERGE_ID,
            BO_TRANS_ORDER_ID,
            CUSTOMER_ORDER_NO,
            CREATED_TIME,
            TRANS_DATE,
            BO_TRANS_TASK_ID,
            TAX_WAYBILL_NO,
            ROW_NUMBER() OVER(PARTITION BY BO_TRANS_ORDER_ID  ORDER BY rn ) rnn,
            DISTRIBUTE_STATE
            FROM RankedResults RRS
        )
        GROUP BY MERGE_ID, BO_TRANS_ORDER_ID, CUSTOMER_ORDER_NO, CREATED_TIME, TRANS_DATE, DISTRIBUTE_STATE) T
        INNER JOIN (SELECT MERGE_ID, MIN(RNN) groupOrder FROM (SELECT RR.*, ROWNUM RNN FROM RankedResults RR) GROUP BY MERGE_ID) R ON R.MERGE_ID = T.MERGE_ID
        LEFT JOIN (SELECT DISTINCT BO_TRANS_TASK_ID, disPatchType, disPatcherUserId, disPatcherSupplierId FROM RankedResults RR) D ON D.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_ORDER O ON  O.BO_TRANS_ORDER_ID = T.BO_TRANS_ORDER_ID
        )
        ORDER BY R.groupOrder ASC, T.RN ASC
    </select>

    <select id="getStatCountForDistributeOrder" resultType="com.wtyt.dh.bean.TabStatBean" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        SELECT
        sum(myCount)  myCount, <!-- 我的总数 -->
        sum(allCount) allCount, <!-- 全部总数 -->
        sum(delCount) delCount, <!-- 已删除总数 -->
        sum(unDistributedCount) unDistributedCount,  <!-- 未分发总数 -->
        sum(distributedCount) distributedCount,  <!-- 已分发总数 -->
        sum(unDispatchedCount) unDispatchedCount, <!-- 未派车总数 -->
        sum(dispatchedCount) dispatchedCount,  <!-- 已派车总数 -->
        sum(upLotteryCount) upLotteryCount,  <!-- 待摇号总数 -->
        sum(lotteryCount) lotteryCount, <!-- 已摇号总数 -->
        sum(lotteryIngCount) lotteryIngCount  <!-- 摇号中总数 -->
        FROM (
        SELECT
        max(myCount) myCount,
        max(allCount) allCount,
        max(delCount) delCount,
        max(unDistributedCount) unDistributedCount,
        max(distributedCount) distributedCount,
        max(unDispatchedCount) unDispatchedCount,
        max(dispatchedCount) dispatchedCount,
        max(upLotteryCount) upLotteryCount,
        max(lotteryCount) lotteryCount,
        max(lotteryIngCount) lotteryIngCount
        FROM
        (
        SELECT
        TBTO.BO_TRANS_ORDER_ID,
        CASE WHEN TBTO.IS_DEL = 0 AND TBTO.USER_ID = #{userId} THEN 1 ELSE 0 END AS myCount,
        CASE WHEN TBTO.IS_DEL = 0 THEN 1 ELSE 0 END AS allCount,
        CASE WHEN TBTO.IS_DEL = 1 THEN 1 ELSE 0 END  AS delCount,
        CASE WHEN TBTO.IS_DEL = 0 AND NVL2(TBTO.CARRIER, 1, 0) = 0 THEN 1 ELSE 0 END AS unDistributedCount,
        CASE WHEN TBTO.IS_DEL = 0 AND NVL2(TBTO.CARRIER, 1, 0) = 1 THEN 1 ELSE 0 END AS distributedCount,
        CASE WHEN TBTO.IS_DEL = 0 AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0 THEN 1 ELSE 0 END AS unDispatchedCount,
        CASE WHEN TBTO.IS_DEL = 0 AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 1 THEN 1 ELSE 0 END AS dispatchedCount,
        CASE WHEN TBTO.IS_DEL = 0 AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0 AND NVL(TBTO.LOTTERY_STATE,0) =0   <include refid="cpdLotteryLineId"/>  THEN 1 ELSE 0 END AS upLotteryCount,
        CASE WHEN TBTO.IS_DEL = 0 AND TBTO.LOTTERY_STATE =1  <include refid="cpdLotteryLineId"/>  THEN 1 ELSE 0 END AS lotteryCount,
        CASE WHEN TBTO.IS_DEL = 0 AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0   AND TBTO.LOTTERY_STATE =2  <include refid="cpdLotteryLineId"/> THEN 1 ELSE 0 END AS lotteryIngCount
        FROM
        (
        SELECT
        DISTINCT TBTO.MERGE_ID
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
        <if test="dispatcherList != null and dispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        </if>
        WHERE <include refid="distributeModeAdapter"/>
        <include refid="searchConditionForDistributeOrder" />
        ) T
        LEFT JOIN T_BO_TRANS_ORDER TBTO ON T.merge_id = TBTO.merge_id
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        ) GROUP BY BO_TRANS_ORDER_ID
        )
    </select>




     <select id="getStatCount" resultType="com.wtyt.dh.bean.TabStatBean" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        SELECT
        sum(myCount)  myCount, <!-- 我的总数 -->
         sum(allCount) allCount, <!-- 全部总数 -->
         sum(delCount) delCount, <!-- 已删除总数 -->
         sum(unDistributedCount) unDistributedCount,  <!-- 未分发总数 -->
         sum(distributedCount) distributedCount,  <!-- 已分发总数 -->
         sum(unDispatchedCount) unDispatchedCount, <!-- 未派车总数 -->
         sum(dispatchedCount) dispatchedCount,  <!-- 已派车总数 -->
         sum(upLotteryCount) upLotteryCount,  <!-- 待摇号总数 -->
         sum(lotteryCount) lotteryCount, <!-- 已摇号总数 -->
         sum(lotteryIngCount) lotteryIngCount  <!-- 摇号中总数 -->
         FROM (
        SELECT
        TBTO.BO_TRANS_ORDER_ID,
        ROW_NUMBER() over (partition by TBTO.BO_TRANS_ORDER_ID ORDER BY TBTOR.BO_TRANS_TASK_ID ) as RN ,
        CASE WHEN TBTO.IS_DEL = 0 AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,2) AND TBTO.USER_ID = #{userId}  THEN 1 ELSE 0 END AS myCount,
        CASE WHEN TBTO.IS_DEL = 0 AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,2) THEN 1 ELSE 0 END AS allCount,
        CASE WHEN TBTO.IS_DEL = 1 AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,1) THEN 1 ELSE 0 END  AS delCount,
        CASE WHEN TBTO.IS_DEL = 0 AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,2) AND NVL2(TBTO.CARRIER, 1, 0) = 0 THEN 1 ELSE 0 END AS unDistributedCount,
        CASE WHEN TBTO.IS_DEL = 0 AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,2) AND NVL2(TBTO.CARRIER, 1, 0) = 1 THEN 1 ELSE 0 END AS distributedCount,
        CASE WHEN TBTO.IS_DEL = 0 AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,2) AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0 THEN 1 ELSE 0 END AS unDispatchedCount,
        CASE WHEN TBTO.IS_DEL = 0 AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,2) AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 1 THEN 1 ELSE 0 END AS dispatchedCount,
        CASE WHEN TBTO.IS_DEL = 0 AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,2) AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0 AND NVL(TBTO.LOTTERY_STATE,0) =0   <include refid="cpdLotteryLineId"/>  THEN 1 ELSE 0 END AS upLotteryCount,
        CASE WHEN TBTO.IS_DEL = 0 AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,2) AND TBTO.LOTTERY_STATE =1  <include refid="cpdLotteryLineId"/>  THEN 1 ELSE 0 END AS lotteryCount,
        CASE WHEN TBTO.IS_DEL = 0 AND nvl(TBTO.HIERARCHY_TYPE,0) in(0,2) AND DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0   AND TBTO.LOTTERY_STATE =2  <include refid="cpdLotteryLineId"/> THEN 1 ELSE 0 END AS lotteryIngCount
        FROM
        (
        SELECT
        DISTINCT TBTO.MERGE_ID
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
         <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
         <if test="dispatcherList != null and dispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        </if>
        WHERE <include refid="distributeModeAdapter"/>
        <include refid="searchConditionForDistributeOrder" />
        ) T
        INNER JOIN T_BO_TRANS_ORDER TBTO ON T.merge_id = TBTO.merge_id
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        ) where RN =1
    </select>

    <select id="getTaskStatCountForDistributeOrder" resultType="com.wtyt.dh.bean.TaskStateStatisticsBean" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        SELECT
        sum(allCount)  allCount,
        sum(disPatchCount) disPatchCount,
        <if test="queryFhOrderMark != null and queryFhOrderMark != ''">
        sum(dpFHLookingNum) dpFHLookingNum,
        sum(dpFHBookingNum) dpFHBookingNum,
        </if>
        sum(arrivedCount) arrivedCount,
        sum(departCount) departCount,
        sum(transportCount) transportCount,
        sum(finishCount) finishCount,
        sum(payCount) payCount,
        sum(partSpiltNum) partSpiltNum,
        sum(finishSpiltNum) finishSpiltNum,
        sum(noSpiltNum) noSpiltNum
        from
        (
        SELECT
        max(allCount) allCount,
        max(disPatchCount) disPatchCount,
        <if test="queryFhOrderMark != null and queryFhOrderMark != ''">
        max(dpFHLookingNum) dpFHLookingNum,
        max(dpFHBookingNum) dpFHBookingNum,
        </if>
        max(arrivedCount) arrivedCount,
        max(departCount) departCount,
        max(transportCount) transportCount,
        max(finishCount) finishCount,
        max(payCount) payCount,
        max(partSpiltNum) partSpiltNum,
        max(finishSpiltNum) finishSpiltNum,
        max(noSpiltNum) noSpiltNum
        FROM(
        SELECT
        TBTO.BO_TRANS_ORDER_ID,
        CASE WHEN TBTO.IS_DEL = 0 THEN 1 ELSE 0 END AS allCount, <!-- 全部总数 -->
        CASE WHEN  DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0 THEN 1 ELSE 0 END AS disPatchCount,<!-- 待派车总数 -->
        <if test="queryFhOrderMark != null and queryFhOrderMark != ''">
            CASE WHEN  DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0 AND  DECODE(tfor.FH_STATE, 0, 1, 0) = 1 THEN 1 ELSE 0 END AS dpFHLookingNum, <!--  待派车-一手货找车中 -->
            CASE WHEN  DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0 AND  DECODE(tfor.FH_STATE, 1, 1, 0) = 1 THEN 1 ELSE 0 END AS dpFHBookingNum, <!--  待派车-一手货已订车 -->
        </if>
        <choose>
            <when test="configId != null and configId == '104'.toString()">
                CASE WHEN  DECODE(TBTT.NODE_ID, 200, 1, 400, 1, 0) = 1 THEN 1 ELSE 0 END AS arrivedCount,<!-- 待到场 -->
                CASE WHEN  DECODE(TBTT.NODE_ID, 500, 1, 0) = 1 THEN 1 ELSE 0 END AS departCount,<!-- 待发车 -->
            </when>
            <when test="configId != null and configId == '105'.toString()">
                CASE WHEN  DECODE(TBTT.NODE_ID, 200, 1, 501, 1, 0) = 1 THEN 1 ELSE 0 END AS arrivedCount,<!-- 待到场 -->
                CASE WHEN  DECODE(TBTT.NODE_ID, 400, 1, 0) = 1 THEN 1 ELSE 0 END AS departCount,<!-- 待发车 -->
            </when>
        </choose>
        CASE WHEN  DECODE(TBTT.NODE_ID, 600,1,650,1, 0) = 1 THEN 1 ELSE 0 END AS transportCount,<!-- 运输中 -->
        <choose>
            <when test="queryOwnerOrgId == null or queryOwnerOrgId == ''">
                CASE WHEN  DECODE(TBTT.NODE_ID, 700,1,800,1,1000,1,1100,1, 0) = 1 THEN 1 ELSE 0 END AS finishCount,<!-- 运输完成 -->
                CASE WHEN  DECODE(TBTT.NODE_ID, 1200,1, 0) = 1 THEN 1 ELSE 0 END AS payCount,<!-- 已结算 -->
            </when>
            <otherwise>
                CASE WHEN  NVL(TBTT.STATE, 0) = 2 AND NVL(TBTTA.PAY_STATE, 0) != 2 THEN 1 ELSE 0 END AS finishCount,<!-- 运输完成 -->
                CASE WHEN  NVL(TBTT.STATE, 0) = 2 AND NVL(TBTTA.PAY_STATE, 0) = 2 THEN 1 ELSE 0 END AS payCount,<!-- 已结算 -->
            </otherwise>
        </choose>
        CASE WHEN TBTO.SPILT_PROGRESS > 0 AND TBTO.SPILT_PROGRESS &lt; 100 THEN 1 ELSE 0 END AS partSpiltNum,<!-- 部分拆分数 -->
        CASE WHEN TBTO.SPILT_PROGRESS >= 100 THEN 1 ELSE 0 END AS finishSpiltNum,<!-- 拆分完成 -->
        CASE WHEN (nvl(TBTO.GOODS_WEIGHT_REMAIN,0) > 0 OR nvl(TBTO.GOODS_VOLUME_REMAIN,0) > 0 OR nvl(TBTO.GOODS_CASE_PACK_REMAIN,0) > 0 OR nvl(TBTO.LOADING_CAR_NUM, 0) > 0)
        AND (SELECT COUNT(1) FROM T_BO_TRANS_ORDER_REL R WHERE R.IS_DEL=0 AND R.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID ) = 0 THEN 1 ELSE 0 END AS noSpiltNum<!-- 未拆分数 -->
        FROM
        (
        SELECT
        DISTINCT TBTO.MERGE_ID
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
        <if test="dispatcherList != null and dispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        </if>
        WHERE <include refid="distributeModeAdapter"/>
        <include refid="tabConditionForDistributeOrder" />
        <include refid="searchConditionForDistributeOrder" />
        ) T
        LEFT JOIN T_BO_TRANS_ORDER TBTO ON T.merge_id = TBTO.merge_id
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        <if test="queryOwnerOrgId != null and queryOwnerOrgId != ''">
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TBTTA ON TBTT.BO_TRANS_TASK_ID = TBTTA.BO_TRANS_TASK_ID AND TBTTA.IS_DEL = 0
        </if>
        <if test="queryFhOrderMark != null and queryFhOrderMark != ''">
            LEFT JOIN T_BO_TO_FH_ORDER_REL tfor ON TBTO.BO_TRANS_ORDER_ID = tfor.BO_TRANS_ORDER_ID AND tfor.IS_DEL = 0
        </if>
        where <include refid="distributeModeAdapter"/>
        <include refid="tabConditionForDistributeOrder" />
        ) GROUP BY BO_TRANS_ORDER_ID
        )
    </select>

     <select id="getTaskStatCount" resultType="com.wtyt.dh.bean.TaskStateStatisticsBean" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        SELECT
        sum(allCount)  allCount,
        sum(disPatchCount) disPatchCount,
        <if test="queryFhOrderMark != null and queryFhOrderMark != ''">
        sum(dpFHLookingNum) dpFHLookingNum,
        sum(dpFHBookingNum) dpFHBookingNum,
        </if>
        sum(arrivedCount) arrivedCount,
        sum(departCount) departCount,
        sum(transportCount) transportCount,
        sum(finishCount) finishCount,
        sum(payCount) payCount,
        sum(partSpiltNum) partSpiltNum,
        sum(finishSpiltNum) finishSpiltNum,
        sum(noSpiltNum) noSpiltNum
        FROM(
        SELECT
        TBTO.BO_TRANS_ORDER_ID,
        ROW_NUMBER() over (partition by TBTO.BO_TRANS_ORDER_ID ORDER BY TBTOR.BO_TRANS_TASK_ID ) as RN ,
        CASE WHEN TBTO.IS_DEL = 0 THEN 1 ELSE 0 END AS allCount, <!-- 全部总数 -->
         CASE WHEN  DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0 THEN 1 ELSE 0 END AS disPatchCount,<!-- 待派车总数 -->
         <if test="queryFhOrderMark != null and queryFhOrderMark != ''">
            CASE WHEN  DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0 AND  DECODE(tfor.FH_STATE, 0, 1, 0) = 1 THEN 1 ELSE 0 END AS dpFHLookingNum, <!--  待派车-一手货找车中 -->
             CASE WHEN  DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) = 0 AND  DECODE(tfor.FH_STATE, 1, 1, 0) = 1 THEN 1 ELSE 0 END AS dpFHBookingNum, <!--  待派车-一手货已订车 -->
        </if>
        <choose>
            <when test="configId != null and configId == '104'.toString()">
                CASE WHEN  DECODE(TBTT.NODE_ID, 200, 1, 400, 1, 0) = 1 THEN 1 ELSE 0 END AS arrivedCount,<!-- 待到场 -->
                CASE WHEN  DECODE(TBTT.NODE_ID, 500, 1, 0) = 1 THEN 1 ELSE 0 END AS departCount,<!-- 待发车 -->
            </when>
            <when test="configId != null and configId == '105'.toString()">
                CASE WHEN  DECODE(TBTT.NODE_ID, 200, 1, 501, 1, 0) = 1 THEN 1 ELSE 0 END AS arrivedCount,<!-- 待到场 -->
                CASE WHEN  DECODE(TBTT.NODE_ID, 400, 1, 0) = 1 THEN 1 ELSE 0 END AS departCount,<!-- 待发车 -->
            </when>
        </choose>
        CASE WHEN  DECODE(TBTT.NODE_ID, 600,1,650,1, 0) = 1 THEN 1 ELSE 0 END AS transportCount,<!-- 运输中 -->
         <choose>
            <when test="queryOwnerOrgId == null or queryOwnerOrgId == ''">
                CASE WHEN  DECODE(TBTT.NODE_ID, 700,1,800,1,1000,1,1100,1, 0) = 1 THEN 1 ELSE 0 END AS finishCount,<!-- 运输完成 -->
                CASE WHEN  DECODE(TBTT.NODE_ID, 1200,1, 0) = 1 THEN 1 ELSE 0 END AS payCount,<!-- 已结算 -->
            </when>
            <otherwise>
                CASE WHEN  NVL(TBTT.STATE, 0) = 2 AND NVL(TBTTA.PAY_STATE, 0) != 2 THEN 1 ELSE 0 END AS finishCount,<!-- 运输完成 -->
                CASE WHEN  NVL(TBTT.STATE, 0) = 2 AND NVL(TBTTA.PAY_STATE, 0) = 2 THEN 1 ELSE 0 END AS payCount,<!-- 已结算 -->
            </otherwise>
        </choose>
        CASE WHEN TBTO.SPILT_PROGRESS > 0 AND TBTO.SPILT_PROGRESS &lt; 100 THEN 1 ELSE 0 END AS partSpiltNum,<!-- 部分拆分数 -->
         CASE WHEN TBTO.SPILT_PROGRESS >= 100 THEN 1 ELSE 0 END AS finishSpiltNum,<!-- 拆分完成 -->
         CASE WHEN (nvl(TBTO.GOODS_WEIGHT_REMAIN,0) > 0 OR nvl(TBTO.GOODS_VOLUME_REMAIN,0) > 0 OR nvl(TBTO.GOODS_CASE_PACK_REMAIN,0) > 0 OR nvl(TBTO.LOADING_CAR_NUM, 0) > 0)
        AND (SELECT COUNT(1) FROM T_BO_TRANS_ORDER_REL R WHERE R.IS_DEL=0 AND R.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID ) = 0 THEN 1 ELSE 0 END AS noSpiltNum<!-- 未拆分数 -->
         FROM
        (SELECT
           DISTINCT TBTO.MERGE_ID
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
         <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
         <if test="dispatcherList != null and dispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        </if>
        WHERE <include refid="distributeModeAdapter"/>
        <include refid="tabConditionForDistributeOrder"/>
        <include refid="searchConditionForDistributeOrder" />) T
        INNER JOIN T_BO_TRANS_ORDER TBTO ON T.merge_id = TBTO.merge_id
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        <if test="queryOwnerOrgId != null and queryOwnerOrgId != ''">
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TBTTA ON TBTT.BO_TRANS_TASK_ID = TBTTA.BO_TRANS_TASK_ID AND TBTTA.IS_DEL = 0
        </if>
        <if test="queryFhOrderMark != null and queryFhOrderMark != ''">
            LEFT JOIN T_BO_TO_FH_ORDER_REL tfor ON TBTO.BO_TRANS_ORDER_ID = tfor.BO_TRANS_ORDER_ID AND tfor.IS_DEL = 0
        </if>
       ) WHERE RN =1
    </select>

    <select id="getOverTimeStatCountForDistributeOrder" resultType="int"  parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        SELECT COUNT(1) FROM
        (
        SELECT
        DISTINCT TBTT.BO_TRANS_TASK_ID
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE TBTNDL ON TBTT.BO_TRANS_TASK_ID = TBTNDL.BO_TRANS_TASK_ID AND TBTNDL.IS_DEL = 0  AND TBTNDL.NODE_ID = 200
        <if test="dispatcherList != null and dispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        </if>
        WHERE <include refid="distributeModeAdapter"/>
        <include refid="tabConditionForDistributeOrder" />
        <include refid="stateConditionForDistributeOrder" />
        <include refid="searchConditionForDistributeOrder" />
        ) T
        WHERE EXISTS(SELECT 1 FROM T_BO_TRANS_NODE_ALARM TBTNA WHERE TBTNA.NODE_ID = 200
        AND (TBTNA.NODE_DATA_TYPE = 1 OR TBTNA.ALARM_TYPE = 1) AND TBTNA.IS_DEL = 0
        AND  TBTNA.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID)
    </select>

    <select id="getOverTimeStatCount" resultType="int"  parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
      SELECT
        COUNT(DISTINCT TBTT.BO_TRANS_TASK_ID)
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        <if test="dispatcherList != null and dispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        </if>
        WHERE <include refid="distributeModeAdapter"/>
        <include refid="tabConditionForDistributeOrder" />
        <include refid="stateConditionForDistributeOrder" />
        <include refid="searchConditionForDistributeOrder" />
    </select>

    <select id="getOverTimeOrderCount" resultType="int"  parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        SELECT
        COUNT(1)
        FROM
        (
        SELECT
        DISTINCT TBTO.MERGE_ID
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
        <if test="dispatcherList != null and dispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        </if>
        WHERE <include refid="distributeModeAdapter"/>
        <include refid="tabConditionForDistributeOrder" />
        <include refid="stateConditionForDistributeOrder" />
        <include refid="searchConditionForDistributeOrder" />
        ) T
        INNER JOIN T_BO_TRANS_ORDER TBTO ON T.merge_id = TBTO.merge_id
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        WHERE EXISTS(
                    SELECT 1  FROM T_BO_TRANS_ORDER_ALARM toa
                    WHERE (toa.ALARM_TYPE = 1 OR (toa.ALARM_TYPE=0 AND toa.ALARM_PROCESS_RESULT = 0))
                      and toa.IS_DEL = 0 AND TBTO.BO_TRANS_ORDER_ID = toa.BO_TRANS_ORDER_ID)
    </select>

    <!-- 查询列表静态展示/查询项数据 -->
    <select id="queryDhStaticColInfoList" resultType="com.wtyt.dh.bean.DhStaticColumnBean" >
       SELECT
        TBTDSCC.HEADER_NAME headerName,
        TBTDSCR.BO_TP_DH_STATIC_COL_REL_ID boTpDhStaticColRelId,
        TBTDSC.COLUMN_TYPE columnType,
        TBTDSC.COLUMN_NAME columnName,
        TBTDSC.FIELD_TYPE fieldType,
        TBTDSCR.SEARCH_TYPE searchType,
        TBTDSCR.IS_PRECISE isPrecise,
        TBTDSCR.IS_SHOW isShow,
        TBTDSCR.SHIPPING_IS_SHOW shippingIsShow,
        TBTDSCR.COLUMN_LENGTH columnLength,
        TBTDSCR.SORT_NUM  sortNum
        FROM
        T_BO_TP_DH_STATIC_COL TBTDSC
        INNER JOIN T_BO_TP_DH_STATIC_COL_REL TBTDSCR ON TBTDSCR.BO_TP_DH_STATIC_COL_ID = TBTDSC.BO_TP_DH_STATIC_COL_ID
        INNER JOIN T_BO_TP_DH_STATIC_COL_CFG  TBTDSCC ON TBTDSCC.BO_TP_DH_STATIC_COL_ID = TBTDSC.BO_TP_DH_STATIC_COL_ID
        WHERE TBTDSC.IS_DEL = 0
        AND TBTDSCR.IS_DEL = 0
        AND TBTDSCC.IS_DEL = 0
        AND TBTDSCC.ORG_ID = #{orgId}
        AND TBTDSCR.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        Order by TBTDSCR.SORT_NUM ,TBTDSCR.BO_TP_DH_STATIC_COL_REL_ID
    </select>
    <select id="adminViewQueryDhStaticColInfoList" resultType="com.wtyt.dh.bean.DhStaticColumnBean" >
        <include refid="queryDhStaticColInfoList" />
    </select>
    <sql id="queryDhStaticColInfoList">
       SELECT
        TBTDSCC.HEADER_NAME headerName,
        TBTDSCR.BO_TP_DH_STATIC_COL_REL_ID boTpDhStaticColRelId,
        TBTDSC.COLUMN_TYPE columnType,
        TBTDSC.COLUMN_NAME columnName,
        TBTDSC.FIELD_TYPE fieldType,
        TBTDSCR.SEARCH_TYPE searchType,
        TBTDSCR.IS_PRECISE isPrecise,
        TBTDSCR.IS_SHOW isShow,
        TBTDSCR.SHIPPING_IS_SHOW shippingIsShow,
        TBTDSCR.COLUMN_LENGTH columnLength,
        TBTDSCR.SORT_NUM  sortNum
        FROM
        T_BO_TP_DH_STATIC_COL TBTDSC
        INNER JOIN T_BO_TP_DH_STATIC_COL_REL TBTDSCR ON TBTDSCR.BO_TP_DH_STATIC_COL_ID = TBTDSC.BO_TP_DH_STATIC_COL_ID
        INNER JOIN T_BO_TP_DH_STATIC_COL_CFG  TBTDSCC ON TBTDSCC.BO_TP_DH_STATIC_COL_ID = TBTDSC.BO_TP_DH_STATIC_COL_ID
        WHERE TBTDSC.IS_DEL = 0
        AND TBTDSCR.IS_DEL = 0
        AND TBTDSCC.IS_DEL = 0
        AND TBTDSCC.ORG_ID = #{orgId}
        <choose>
            <when test="accountGroupId != null and accountGroupId != ''">
                AND TBTDSCC.ACCOUNT_GROUP_ID = #{accountGroupId}
            </when>
            <otherwise>
                AND TBTDSCC.ORG_ID = #{orgId}
            </otherwise>
        </choose>
        AND TBTDSCR.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        Order by TBTDSCR.SORT_NUM ,TBTDSCR.BO_TP_DH_STATIC_COL_REL_ID
    </sql>

    <select id="goodsCount" resultType="com.wtyt.dh.bean.GoodsCountBean"
            parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        select
            count(TBTO.BO_TRANS_ORDER_ID) orderCountNum,
            nvl(sum(case
                when #{goodsCount.goodsCasePackUnit} is not null and TBTO.GOODS_CASE_PACK_UNIT=#{goodsCount.goodsCasePackUnit} then NVL(tbto.GOODS_CASE_PACK, 0) else 0  end
                ),0) goodsCasePackNum,
            nvl(sum(case
                when #{goodsCount.goodsVolumeUnit}  is not null and TBTO.GOODS_VOLUME_UNIT=#{goodsCount.goodsVolumeUnit} then NVL(tbto.GOODS_VOLUME, 0) else 0  end
                ),0) goodsVolumeNum,
            nvl(sum(case
                when #{goodsCount.goodsWeightUnit} is not null  and TBTO.GOODS_WEIGHT_UNIT in('KG','千克') then TRUNC(NVL(tbto.GOODS_WEIGHT, 0)/1000,4)
                when #{goodsCount.goodsWeightUnit} is not null  and TBTO.GOODS_WEIGHT_UNIT=#{goodsCount.goodsWeightUnit} then NVL(tbto.GOODS_WEIGHT, 0)
                else 0  end
               ),0) goodsWeightNum
        from (
            SELECT
                DISTINCT TBTO.MERGE_ID
            FROM T_BO_TRANS_ORDER TBTO
            INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
            INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
            <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
                LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TBTTA ON TBTT.BO_TRANS_TASK_ID = TBTTA.BO_TRANS_TASK_ID AND TBTTA.IS_DEL = 0
            </if>
            <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
            <if test="dispatcherList != null and dispatcherList.size() > 0">
                LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
                LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
            </if>
            WHERE <include refid="distributeModeAdapter"/>
            <include refid="tabConditionForDistributeOrder" />
            <include refid="stateConditionForDistributeOrder" />
            <include refid="searchConditionForDistributeOrder" />
        )MER
        inner join t_bo_trans_order tbto on mer.merge_id = tbto.merge_id and tbto.is_del = 0
    </select>
    <select id="stowageCount" resultType="java.lang.Long"
            parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        select
            count(distinct tbtor.BO_TRANS_TASK_ID)+
            count(distinct tbtsor.BO_TP_STOWAGE_ORDER_REL_ID )stowageCountNum
        from (
            SELECT
                DISTINCT TBTO.MERGE_ID
            FROM T_BO_TRANS_ORDER TBTO
            INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
            INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
            <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
                LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TBTTA ON TBTT.BO_TRANS_TASK_ID = TBTTA.BO_TRANS_TASK_ID AND TBTTA.IS_DEL = 0
            </if>
            <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
            <if test="dispatcherList != null and dispatcherList.size() > 0">
                LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
                LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
            </if>
            WHERE <include refid="distributeModeAdapter"/>
            <include refid="tabConditionForDistributeOrder" />
            <include refid="stateConditionForDistributeOrder" />
            <include refid="searchConditionForDistributeOrder" />
        )MER
        inner join t_bo_trans_order tbto on mer.merge_id = tbto.merge_id and tbto.is_del = 0
        left join t_bo_trans_order_rel tbtor on tbtor.BO_TRANS_ORDER_ID = tbto.BO_TRANS_ORDER_ID and tbtor.is_del = 0 and tbtor.BO_TRANS_TASK_ID is not null
        left join T_BO_TP_STOWAGE_ORDER_REL tbtsor on tbtsor.BO_TRANS_ORDER_ID = mer.merge_id and tbtsor.is_del = 0 and tbtsor.STOWAGE_STATE in(2,3) and tbtor.BO_TRANS_TASK_ID is  null
    </select>
    <select id="stowageCountByMergeIds" resultType="com.wtyt.dh.bean.BoTpStowageCountBean">
        SELECT
            tbto.MERGE_ID mergeId ,count(DISTINCT tbtor.BO_TRANS_TASK_ID)+ count(DISTINCT tbtsor.BO_TP_STOWAGE_ORDER_REL_ID )stowageCount
        FROM t_bo_trans_order tbto
        LEFT JOIN t_bo_trans_order_rel tbtor ON
            tbtor.BO_TRANS_ORDER_ID = tbto.BO_TRANS_ORDER_ID
                AND tbtor.is_del = 0
                AND tbtor.BO_TRANS_TASK_ID IS NOT NULL
        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL tbtsor ON
            tbtsor.BO_TRANS_ORDER_ID = tbto.merge_id
                AND tbtsor.is_del = 0
                AND tbtsor.STOWAGE_STATE IN(2, 3)
                AND tbtor.BO_TRANS_TASK_ID IS NULL
        where tbto.merge_id in
        <foreach collection="mergeIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY tbto.MERGE_ID
    </select>

    <sql id="queryDhPlanParentIdWithAsSql">
        WITH RankedResults AS
        (
        SELECT
        DISTINCT TBTO.BO_TRANS_ORDER_ID
        FROM T_BO_TRANS_ORDER TBTO
        INNER JOIN T_BO_TP_IMPORT_DATA TBTID ON TBTO.BO_TRANS_ORDER_ID  = TBTID.BO_TRANS_ORDER_ID AND TBTID.IS_DEL = 0
        INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0
        <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
            AND GROUP_TYPE = 4
        </if>
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TBTTA ON TBTT.BO_TRANS_TASK_ID = TBTTA.BO_TRANS_TASK_ID AND TBTTA.IS_DEL = 0
        </if>
        <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
        <if test="dispatcherList != null and dispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTT.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        </if>
        WHERE <include refid="distributeModeAdapter"/>
        <include refid="stateConditionForDistributeOrder" />
        <include refid="searchConditionForDistributeOrder" />
        AND TBTO.IS_DEL = 0 and TBTO.HIERARCHY_TYPE in(1,2)
        )
    </sql>
    <select id="queryDhPlanParentIdList" resultType="java.lang.String" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        <include refid="queryDhPlanParentIdWithAsSql"/>
        select TBTO.BO_TRANS_ORDER_ID from (
            select  tohr.BO_TRANS_ORDER_ID from RankedResults RS
            inner join T_BO_ORDER_HIERARCHY_REL tohr on tohr.SUB_BO_TRANS_ORDER_ID = RS.BO_TRANS_ORDER_ID and tohr.IS_DEL = 0
            union
            select  tohr.BO_TRANS_ORDER_ID from RankedResults RS
            inner join T_BO_ORDER_HIERARCHY_REL tohr on  tohr.BO_TRANS_ORDER_ID = RS.BO_TRANS_ORDER_ID and tohr.IS_DEL = 0
        )T inner join  T_BO_TRANS_ORDER TBTO ON TBTO.BO_TRANS_ORDER_ID = T.BO_TRANS_ORDER_ID AND TBTO.IS_DEL = 0
        order by TBTO.CREATED_TIME DESC ,T.BO_TRANS_ORDER_ID
    </select>

    <sql id="queryPlanListByParentIdsWithAsSql">
        WITH RankedResults AS
        (
        SELECT
        TBTO.PARENT_ID,
        TBTO.BO_TRANS_ORDER_ID,
        TBTO.CUSTOMER_ORDER_NO,
        TBTO.CREATED_TIME,
        TBTO.LAST_MODIFIED_TIME,
        TBTO.TRANS_DATE,
        TBTT.BO_TRANS_TASK_ID,
        DECODE(NVL(TBTT.NODE_ID, 0), 0, 0, 100, 0, 300, 0, 1) DISPATCH_STATE,
        TBTT.NODE_ID,
        TBTTE.DISPATCH_CAR_TIME,
        TBTT.TAX_WAYBILL_NO,
        NVL2(TBTO.CARRIER, 1, 0) DISTRIBUTE_STATE,
        CASE
        WHEN TBTT.OWNER_ORG_ID != TBTNR.ORG_ID THEN '2'
        WHEN TBTT.OWNER_ORG_ID = TBTNR.ORG_ID AND TBTNR.SYS_ROLE_TYPE = 12005 THEN '2'
        ELSE '1'
        END disPatchType,
        TBTNR.USER_ID disPatcherUserId,
        TBTNR.GROUP_ID disPatcherSupplierId
        FROM (
        SELECT t1.BO_TRANS_ORDER_ID,
        t1.BO_TRANS_ORDER_ID PARENT_ID,
        t1.CUSTOMER_ORDER_NO,
        t1.CREATED_TIME,
        t1.LAST_MODIFIED_TIME,t1.CARRIER,
        t1.TRANS_DATE
        FROM T_BO_TRANS_ORDER t1 WHERE t1.IS_DEL = 0 and t1.HIERARCHY_TYPE =1
        AND t1.BO_TRANS_ORDER_ID IN
        <foreach collection="pageParentIdList" open="(" close=")" separator="," item="parentId">
            #{parentId}
        </foreach>
        UNION ALL
        SELECT t2.BO_TRANS_ORDER_ID,
        TBOHR.BO_TRANS_ORDER_ID PARENT_ID,
        t2.CUSTOMER_ORDER_NO,
        t2.CREATED_TIME,
        t2.LAST_MODIFIED_TIME,t2.CARRIER,
        t2.TRANS_DATE
        FROM T_BO_TRANS_ORDER t2
        INNER JOIN T_BO_ORDER_HIERARCHY_REL TBOHR on TBOHR.SUB_BO_TRANS_ORDER_ID = t2.BO_TRANS_ORDER_ID AND TBOHR.IS_DEL = 0
        WHERE t2.IS_DEL = 0 and t2.HIERARCHY_TYPE =2
        AND TBOHR.BO_TRANS_ORDER_ID IN
        <foreach collection="pageParentIdList" open="(" close=")" separator="," item="parentId">
            #{parentId}
        </foreach>
        ) TBTO
        <if test="queryDistributePlan != null and queryDistributePlan == '1'.toString()">
            INNER JOIN T_BO_ORDER_GROUP_REL TBOGR ON TBOGR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBOGR.IS_DEL = 0 AND GROUP_TYPE = 4
        </if>
        LEFT JOIN T_BO_TRANS_ORDER_REL TBTOR ON TBTOR.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID AND TBTOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK TBTT ON TBTT.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTT.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA TBTTE ON TBTTE.BO_TRANS_TASK_ID = TBTOR.BO_TRANS_TASK_ID AND TBTTE.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD TBTNR ON TBTTE.DISPATCH_CAR_RECORD_ID = TBTNR.BO_TRANS_NODE_RECORD_ID and TBTNR.IS_DEL = 0 AND TBTNR.NODE_ID = 200
        )
    </sql>
    <select id="queryDhPlanBaseListByParentIds" resultType="com.wtyt.bo.bean.DhPlanBaseBean"  parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        <include refid="queryPlanListByParentIdsWithAsSql" />
        SELECT
        T.PARENT_ID parentId,
        O.MERGE_ID mergeId,
        T.BO_TRANS_ORDER_ID boTransOrderId,
        T.CUSTOMER_ORDER_NO customerOrderNo,
        O.THIRD_ORDER_NO thirdOrderNo,
        TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        T.TRANS_DATE transDate,
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.TAX_WAYBILL_NO taxWaybillNo,
        T.DISTRIBUTE_STATE distributeState,
        D.disPatchType dispatchType,
        D.disPatcherUserId dispatcherUserId,
        D.disPatcherSupplierId dispatcherSupplierId,
        O.ORG_ID orgId,
        O.ACCOUNT_GROUP_ID accountGroupId,
        O.GOODS_NAME goodsName,
        NVL(O.GOODS_CASE_PACK, 0) goodsCasePack,
        NVL(O.GOODS_WEIGHT, 0) goodsWeight,
        NVL(O.GOODS_VOLUME, 0) goodsVolume,
        NVL(O.LOADING_CAR_NUM, 0) loadingCarNum,
        NVL(O.GOODS_CASE_PACK_REMAIN, 0) goodsCasePackRemain,
        NVL(O.GOODS_WEIGHT_REMAIN, 0) goodsWeightRemain,
        NVL(O.GOODS_VOLUME_REMAIN, 0) goodsVolumeRemain,
        NVL(O.LOADING_CAR_NUM, 0) - T.DISPATCH_CAR_NUM  loadingCarNumRemain,
        O.START_PROVINCE_NAME startProvinceName,
        O.START_CITY_NAME startCityName,
        O.START_COUNTY_NAME startCountyName,
        O.START_ADDRESS startAddress,
        O.RAW_START_ADDRESS rawStartAddress,
        O.END_PROVINCE_NAME endProvinceName,
        O.END_CITY_NAME endCityName,
        O.END_COUNTY_NAME endCountyName,
        O.END_ADDRESS endAddress,
        O.RAW_END_ADDRESS rawEndAddress,
        O.URGENT_MARK  urgentMark,
        O.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        O.GOODS_WEIGHT_UNIT goodsWeightUnit,
        O.GOODS_VOLUME_UNIT goodsVolumeUnit,
        nvl(O.HIERARCHY_TYPE,0) hierarchyType,
        O.DT_INSIDE_ORDER_MARK dtInsideOrderMark,
        decode( O.UPSTREAM_STATE,0,1,1,2,0) upstreamState,
        case when tbori.BO_TRANS_ORDER_ID is null then 0 else 1 end remarkFlag,
        TO_CHAR(O.SPILT_PROGRESS, 'FM9999999990') spiltProgress
        <if test="terminalType == 'APP-XDL'">
            ,O.SERVICE_REQUIRE serviceRequire,
            O.CUSTOMER_NAME customerName,
            O.START_CITY_NAME || O.START_COUNTY_NAME loadingArea,
            O.END_CITY_NAME || O.END_COUNTY_NAME unloadingArea,
            O.GOODS_SPEC goodsSpec,
            O.CONSIGNEE_UNIT consigneeUnit
        </if>
        FROM
        (
        SELECT
        PARENT_ID,
        BO_TRANS_ORDER_ID,
        CUSTOMER_ORDER_NO,
        CREATED_TIME,
        TRANS_DATE,
        MIN(BO_TRANS_TASK_ID) BO_TRANS_TASK_ID,
        COUNT(BO_TRANS_TASK_ID) DISPATCH_CAR_NUM,
        listagg(CASE WHEN rnn&lt;=200 THEN TAX_WAYBILL_NO ELSE NULL END , ',') WITHIN GROUP (ORDER BY BO_TRANS_TASK_ID)  TAX_WAYBILL_NO,
        DISTRIBUTE_STATE
        FROM
        (
        SELECT
        PARENT_ID ,
        BO_TRANS_ORDER_ID,
        CUSTOMER_ORDER_NO,
        CREATED_TIME,
        TRANS_DATE,
        BO_TRANS_TASK_ID,
        TAX_WAYBILL_NO,
        ROW_NUMBER() OVER(PARTITION BY BO_TRANS_ORDER_ID  ORDER BY BO_TRANS_TASK_ID ) rnn,
        DISTRIBUTE_STATE
        FROM RankedResults RRS
        )GROUP BY PARENT_ID, BO_TRANS_ORDER_ID, CUSTOMER_ORDER_NO, CREATED_TIME, TRANS_DATE, DISTRIBUTE_STATE
        ) T
        LEFT JOIN (SELECT DISTINCT BO_TRANS_TASK_ID, disPatchType, disPatcherUserId, disPatcherSupplierId FROM RankedResults RR) D ON D.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_ORDER O ON  O.BO_TRANS_ORDER_ID = T.BO_TRANS_ORDER_ID
        LEFT JOIN(SELECT BO_TRANS_ORDER_ID FROM T_BO_ORDER_REMARK_INFO  WHERE is_del=0  GROUP BY BO_TRANS_ORDER_ID) tbori on tbori.BO_TRANS_ORDER_ID = T.BO_TRANS_ORDER_ID
        INNER JOIN(
        <foreach collection="pageParentIdList" open="(" close=")" separator="union all" item="parentId" index="ind">
           select  #{parentId} PARENT_ID,#{ind} sort_no from dual
        </foreach>
        )S on s.PARENT_ID = T.PARENT_ID
        ORDER BY S.sort_no ASC, O.THIRD_ORDER_NO  ASC NULLS FIRST
    </select>

    <select id="getSegmentedStatCount" resultType="int" parameterType="com.wtyt.dh.bean.request.Req5329171IBean">
        <include refid="queryDhPlanParentIdWithAsSql"/>
        select count(BO_TRANS_ORDER_ID) from (
             select  tohr.BO_TRANS_ORDER_ID from RankedResults RS
             inner join T_BO_ORDER_HIERARCHY_REL tohr on tohr.SUB_BO_TRANS_ORDER_ID = RS.BO_TRANS_ORDER_ID and tohr.IS_DEL = 0
             union
             select  tohr.BO_TRANS_ORDER_ID from RankedResults RS
             inner join T_BO_ORDER_HIERARCHY_REL tohr on  tohr.BO_TRANS_ORDER_ID = RS.BO_TRANS_ORDER_ID and tohr.IS_DEL = 0
         )T
    </select>


</mapper>
