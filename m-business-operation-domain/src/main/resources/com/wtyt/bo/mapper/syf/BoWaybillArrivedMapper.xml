<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoWaybillArrivedMapper">
    
    <select id="queryAllDispatchedWaybillByOrgIds" resultType="BoXxlJobAlarmBean">
        SELECT
            T.TAX_WAYBILL_ID taxWaybillId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            L.NODE_ID nodeId,
            TO_CHAR(L.DEAD_LINE_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
            T.ORG_ID orgId,
            T.CART_BADGE_NO cartBadgeNo,
            T.DRIVER_NAME driverName,
            T.START_PROVINCE_NAME startProvinceName,
            T.END_PROVINCE_NAME endProvinceName,
            T.START_CITY_NAME startCityName,
            T.END_CITY_NAME endCityName,
            T.START_COUNTY_NAME startCountyName,
            T.END_COUNTY_NAME endCountyName,
            T.MOBILE_NO mobileNo,
            T.MILEAGE mileage,
            T.CREATED_USER_ID klbUserId,
            T.XCY_USER_ID xcyUserId,
            T.IS_PARTAKE_OPERATE isPartakeOperate,
            NVL(T.TRANS_PATTERN,1) transPattern
        FROM T_BO_TRANS_NODE_DEAD_LINE L, T_BO_TRANS_TASK T
        WHERE L.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        AND L.NODE_ID = '500'
        AND T.NODE_ID = '200'
        AND L.DEAD_LINE_TIME IS NOT NULL
        AND L.IS_DEL = 0
        AND T.CART_BADGE_NO IS NOT NULL
        AND T.IS_DEL = 0
        AND T.ORG_ID in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>