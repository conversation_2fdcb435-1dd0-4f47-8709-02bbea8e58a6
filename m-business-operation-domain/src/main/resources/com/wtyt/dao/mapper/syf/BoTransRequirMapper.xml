<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransRequirMapper">

    <insert id="insertBoTransRequir" parameterType="BoTransRequirBean">
        INSERT INTO T_BO_TRANS_REQUIR(
                        BO_TRANS_REQUIR_ID,                
                        TAX_WAYBILL_ID,                
                        CATEGORY_CODE,                
                        CATEGORY_NAME,                
                        IS_DEL,                
                        CREATED_TIME,                
                        LAST_MODIFIED_TIME,                
                        <if test="note != null and note !=''">
            NOTE
        </if>
        
        )values(
                            #{boTransRequirId},                

                            #{taxWaybillId},                

                            #{categoryCode},                

                            #{categoryName},                

                            #{isDel},                

                            #{createdTime},                

                            #{lastModifiedTime},                

                        <if test="note != null and note !=''">
        #{note}
        </if>
        

        )
    </insert>
</mapper>