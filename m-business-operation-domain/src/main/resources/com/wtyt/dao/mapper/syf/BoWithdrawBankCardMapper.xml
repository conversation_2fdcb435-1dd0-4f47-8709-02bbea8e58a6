<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoWithdrawBankCardMapper">
    <insert id="saveBankCard">
        INSERT INTO T_BO_WITHDRAW_BANK_CARD(BO_WITHDRAW_BANK_CARD_ID, COMPANY_ID, BANK_CARD_NO, BANK_NAME)
        SELECT
        ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},
        #{companyId},
        #{bankCardNo},
        #{bankName}
        FROM DUAL WHERE NOT EXISTS(SELECT 1 FROM T_BO_WITHDRAW_BANK_CARD WHERE COMPANY_ID = #{companyId} AND BANK_CARD_NO = #{bankCardNo} AND BANK_NAME = #{bankName})
    </insert>

    <select id="queryBankCardList" resultType="com.wtyt.dao.bean.syf.BoWithdrawBankCardBean">
        SELECT
            BO_WITHDRAW_BANK_CARD_ID boWithdrawBankCardId,
            COMPANY_ID companyId,
            BANK_CARD_NO bankCardNo,
            BANK_NAME bankName
        FROM T_BO_WITHDRAW_BANK_CARD
        WHERE IS_DEL = 0
        AND COMPANY_ID = #{companyId}
        ORDER BY CREATED_TIME DESC
    </select>
</mapper>
