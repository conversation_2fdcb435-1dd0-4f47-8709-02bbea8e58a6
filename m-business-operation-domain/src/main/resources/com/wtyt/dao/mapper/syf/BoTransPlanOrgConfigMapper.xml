<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransPlanOrgConfigMapper">
    <insert id="insert">
        INSERT
            INTO
            T_BO_TRANS_PLAN_ORG_CONFIG (
            BO_TRANS_PLAN_ORG_CONFIG_ID,
            ORG_ID,
            HEADER_START_LINE,
            IS_DEL,
            CREATED_TIME,
            LAST_MODIFIED_TIME,
            NOTE,
            MERGE_FIELD,
            MERGE_NO_FIELD,
            KG_FLAG,
            G<PERSON>UP_FIELD,
            FIRST_GOODS_AMOUNT_TYPE,
            SMART_STOWAGE_PLAN_NUM,
            IMPORT_MAX_ROW_NUM,
            OLD_CONFIG_FLAG
            )
        VALUES (
            #{boTransPlanOrgConfigId},
            #{orgId},
            #{headerStartLine},
            0,
            <PERSON>Y<PERSON><PERSON><PERSON>,
            <PERSON>Y<PERSON><PERSON><PERSON>,
            NULL,
            #{mergeField},
            #{mergeNoField},
            #{kgFlag},
            #{groupField},
            #{firstGoodsAmountType},
            #{smartStowagePlanNum},
            #{importMaxRowNum},
            #{oldConfigFlag}
        )
    </insert>
    <update id="update">
        UPDATE
            T_BO_TRANS_PLAN_ORG_CONFIG
        SET
            HEADER_START_LINE = #{headerStartLine},
            MERGE_FIELD = #{mergeField},
            MERGE_NO_FIELD = #{mergeNoField},
            KG_FLAG = #{kgFlag},
            GROUP_FIELD = #{groupField},
            FIRST_GOODS_AMOUNT_TYPE = #{firstGoodsAmountType},
            OLD_CONFIG_FLAG = #{oldConfigFlag},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TRANS_PLAN_ORG_CONFIG_ID = #{boTransPlanOrgConfigId}
    </update>

    <select id="selectByOrgId" resultType="com.wtyt.dao.bean.syf.BoTransPlanOrgConfigBean">
    SELECT
        BO_TRANS_PLAN_ORG_CONFIG_ID boTransPlanOrgConfigId,
        ORG_ID orgId,
        HEADER_START_LINE headerStartLine,
        MERGE_FIELD mergeField,
        MERGE_NO_FIELD mergeNoField,
        KG_FLAG kgFlag,
        GROUP_FIELD groupField,
        FIRST_GOODS_AMOUNT_TYPE firstGoodsAmountType,
        SMART_STOWAGE_PLAN_NUM smartStowagePlanNum,
        IMPORT_MAX_ROW_NUM importMaxRowNum,
        OLD_CONFIG_FLAG oldConfigFlag
    FROM
        T_BO_TRANS_PLAN_ORG_CONFIG
    WHERE
        IS_DEL = 0
        AND ORG_ID = #{orgId}
    </select>
</mapper>