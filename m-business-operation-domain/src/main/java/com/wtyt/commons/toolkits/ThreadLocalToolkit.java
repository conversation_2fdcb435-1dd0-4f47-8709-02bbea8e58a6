package com.wtyt.commons.toolkits;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

public class ThreadLocalToolkit {

    private ThreadLocalToolkit() {
    }
    
    private static final ThreadLocal<Map<Integer, Gson>> GSON_CACHE = new ThreadLocal<Map<Integer, Gson>>() {
        @Override
        protected Map<Integer, Gson> initialValue() {
            Map<Integer, Gson> map = new HashMap<>();
            map.put(1, new GsonBuilder().disableHtmlEscaping().create());
            // someFieldName ---> SomeFieldName
            map.put(2, new GsonBuilder().disableHtmlEscaping().setFieldNamingPolicy(FieldNamingPolicy.UPPER_CAMEL_CASE).create());
            return map;
        }
    };

    public static Gson gson() {
        return gson(1);
    }

    public static Gson gson(int num) {
        return GSON_CACHE.get().get(num);
    }

    
    
    private static final ThreadLocal<Map<Integer, DateFormat>> FORMAT_CACHE = new ThreadLocal<Map<Integer, DateFormat>>() {
        @Override
        protected Map<Integer, DateFormat> initialValue() {
            Map<Integer, DateFormat> map = new HashMap<>();
            map.put(1, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
            map.put(2, new SimpleDateFormat("yyyyMMddHHmmss"));
            map.put(3, new SimpleDateFormat("yyyy-MM-dd"));
            map.put(4, new SimpleDateFormat("yyyyMMdd"));
            return map;
        }
    };

    public static DateFormat formatType() {
        return formatType(1);
    }

    public static DateFormat formatType(int num) {
        return FORMAT_CACHE.get().get(num);
    }



}
