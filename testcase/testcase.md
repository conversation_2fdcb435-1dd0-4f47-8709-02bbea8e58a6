# 目标：生成接口测试代码

## 固定信息
- dbType: oracle
- schema: syf

## 处理流程：

* 1. 调用mcp工具获取涉及到接口文档信息和rpc接口文档信息
* 2. 检查接口实现代码涉及到数据库表和JOIN关系，调用mcp工具获取数据库表结构信息
* 3. 检查接口实现代码是否使用Rpc、mq、apollo，如需进行mock且对请求参数进行断言
* 4. 若对应功能测试用例md文件存在，必须严格按md文件用例生成，不要参考文件中**测试步骤**和**预期结果**
* 5. 若对应功能测试用例md文件不存在，根据接口文档和表结构再结合接口功能描述生成全面且有效的测试用例
* 6. 生成测试类和对应的清除数据库表数据注解
     1. 生成Controller测试时，mock请求参数必须严格按接口文档参数设值，切勿使用请求Bean
* 7. 生成测试单元excel数据库表数据
     1. 必须获取数据库表结构信息且严格遵守**Mock数据库值规范**mock字段值，
     2. 生成数据时，每次只生成一个用例数据，再调用一次mcp工具写入excel数据，每次最多写入3个sheet页
	 3. 如果准备空数据，则不需要生成excel数据
* 8. 生成接口返回值期望结果，必须严格按接口文档响应参数进行期望,仅期望result对象中参数，若result中存在数组字段，则单独验证数组对象
* 9. 调用mcp工具-记录AI生成测试用例代码

## 常用的注解和类与对应的包引用

- ApplicationXlsBaseTests：import com.wtyt.ApplicationXlsBaseTests;
- MvcExpectedDatabase：import com.wtyt.commons.base.test.annotation.MvcExpectedDatabase;
- AssertionMode：import com.wtyt.commons.base.test.consts.AssertionMode;
- AssertionMode：import import com.wtyt.commons.base.test.consts.AssertionMode;
- JsonObject：import com.google.gson.JsonObject;
- ResDataBean：import com.wtyt.money.commons.bean.ResDataBean;
- MockToolkit：import com.wtyt.commons.base.test.toolkit.MockToolkit;
- GsonToolkit：import com.wtyt.commons.base.test.toolkit.GsonToolkit;
- CheckToolkit: import com.wtyt.commons.base.test.toolkit.CheckToolkit;
- ReCodeConsts: import com.wtyt.lg.commons.consts;

## 命名规范

### 测试类

- 接口测试: `{被测类名}{接口ID}Test`（被测类名去掉 `Controller`，如：`CarQuery2001Test`）
- 后台任务: `{被测方法名}Test`（如：`TaskTimeUpdateConsumerTest`）
- 业务逻辑: `{功能模块名}Test`（如：`ExcelCompareTest`）

### 测试方法

- 格式: `test_{功能描述}_{场景}_{序号}`
- 场景: `nor`(正常)、`ex`(异常)、`limit`(边界)
- 序号：`001`

### 测试数据文件

- 清理数据: `{测试类名}.xlsx:clean`
- 准备数据: `{测试类名}.xlsx:{序号}`
- 数据库期望结果: `{测试类名}.xlsx:{序号}expect`
- 接口返回值期望结果: `{测试类名}.xlsx:{序号}mvc`

## 测试类模板

```java
package com.wtyt.{模块路径}.controller;

import com.github.springtestdbunit.annotation.*;
import com.wtyt.{对应的ApplicationTests基类};

/**
 * {功能描述}测试类
 */
public class {TestClassName} extends {ApplicationTestsBase} {

    @Autowired
    private {TestedService} {testedService};

    /**
     * 测试功能点：{具体测试功能描述}
     * 数据预置：{数据描述}【表{table1}、表{table2}】
     * mock逻辑：{mock的关键点}
     * 返参验证：{验证返参中的关键点}
     * DB验证：{验证涉及的表及关键字段}
     */
    @DatabaseSetup(value="{TestClassName}.xlsx:001",
                   connection="xxxDataSourceFactory",
                   type=DatabaseOperation.CLEAN_INSERT)
    @Test
    public void test{FunctionName}_{Scenario}() throws Exception {
    }
}
```

## 断言规范

- rpc接口mock时，入参必须断言
- 接口类统一异常处理，断言ReCode和ReInfo
  - ReCodeConsts.TIP
  - ReCodeConsts.SUCCESS
- 数据库断言，主键字段无需断言或期望
- @DatabaseSetup 或 @ExpectedDatabase 中connection值=test目录下数据源配置类中DatabaseDataSourceConnectionFactoryBean对象

## Mock 配置

### RPC Mock

```java
MockToolkit.getInstance().addMock({sid}, f -> {
	// 入参校验
    Assert.assertEquals("8214333", f.get("orgId").getAsString());
    // rpc接口返回结果
    String resultStr = "{\"reCode\":\"0\",\"reInfo\":\"处理成功\",\"result\":{...}}";
    return GsonToolkit.fromJson(resultStr, JsonObject.class);
});
```

### Apollo Mock

```java
MockToolkit.getInstance().addMock("legal.holiday.rest", "formatterDate");
```

### RabbitMQ Mock

```java
MockToolkit.getInstance().addPmqMock("topic.push.pos.confirm.tdtf", f->{
        // 对MQ消息体进行校验
        Assert.assertEquals("8214333", f.get("orgId").getAsString());
		}
```

### Mock方法实现
```java
    // Mock 服务类方法
    new MockUp<{服务类}>() {
        @Mock
        public {返回类型} {方法名}({参数列表}) {
            // Mock实现
            return {模拟返回值};
        }
    }
```

## 数据验证

### 返回参数验证(存在result)

```java
    @Test
    @MvcExpectedDatabase(value = "{TestClassName}.xlsx:001mvc", assertionMode = AssertionMode.NO_SORTED)
    public void testGetCarInfo_normal() throws Exception {
	Assert.assertEquals("1", result.getReCode());
	Assert.assertEquals("处理成功", result.getReInfo());
    // 验证result
    this.verifyMvcActualData(result.getResult());
    // result中存在数组字段，则单独验证数组对象
	CheckToolkit.check(this, "{TestClassName}.xlsx:001mvc_UnRepaymentList", resDataBean.getResult()
    .getUnRepaymentList(), AssertionMode.NO_SORTED);
    }
```

### 数据库验证

- 操作数据库表，必须验证，如：insert、update、delete

```java
@ExpectedDatabase(value="TestClassName.xlsx:001expect",
                  connection="xxxDataSourceFactory",
                  assertionMode=DatabaseAssertionMode.NON_STRICT_UNORDERED)
```

## 测试用例示例

### Controller 测试

注意不要使用mockMvc,请使用this.sendRequest(URL, reqData, ResponseResult.class);

```java
public class CarQueryControllerTest extends {ApplicationTestsBase} {
    private static final String URL = "/car/query/getCarInfo";
    //ResDataBean<接口响应bean>是接口返回值
    private class Result extends ResDataBean<{接口响应bean}> {
        private static final long serialVersionUID = 1L;
    }

    @Test
    public void testGetCarInfo_normal() throws Exception {
        JsonObject reqData = new JsonObject();
        reqData.addProperty("cartBadgeNo", "测A12345");

        Result result = this.sendRequest(URL, reqData, Result.class);
        Assert.assertEquals(ReCodeConsts.SUCCESS ,resDataBean.getReCode());
        Assert.assertEquals("处理成功" ,resDataBean.getReInfo());
        this.verifyMvcActualData(result.getResult());
    }
}
```

### MQ Consumer 测试

```java
public class TaskTimeUpdateConsumerTest extends {ApplicationTestsBase} {
    @Autowired
    TaskTimeUpdateConsumer taskTimeUpdateConsumer;

    @DatabaseSetup(value="TaskTimeUpdateConsumerTest.xlsx:001",
                   connection="testOaiDataSourceFactory",
                   type=DatabaseOperation.CLEAN_INSERT)
    @Test
    public void testSyncConsume_UploadTimeUpdate() throws Exception {
        String msg = "{\"boTransTaskId\":\"982942085004722221\"}";
        byte[] body = msg.getBytes();

        taskTimeUpdateConsumer.syncConsume(null, null, null, body, BAI_BO_TASK_UPDATE_QUEUE);
    }
}
```

### Service 测试

```java
public class CarServiceTest extends {ApplicationTestsBase} {
    @Autowired
    private CarService carService;

    @DatabaseSetup(value="CarServiceTest.xlsx:001",
                   connection="xxxDataSourceFactory",
                   type=DatabaseOperation.CLEAN_INSERT)
    @Test
    public void testUpdateCarInfo_normal() throws Exception {
        CarBean carBean = new CarBean();
        carBean.setCartBadgeNo("测A12345");
        carBean.setCartLength("12.5");

        carService.updateCarInfo(carBean);
    }
}
```

### Excel 格式详细规范

#### Mock数据库值规范

##### 1. 数字类型，必须是数字

- INT,BIGINT,TINYINT,DECIMAL,NUMBER

##### 2. 字符串类型，必须是字符串

- VARCHAR,TEXT,LONGTEXT,VARCHAR2

##### 3. 时间类型，必须是时间

- DATE,DATETIME

##### 4. NOT NULL约束字段，必须是mock字段值

- 其他数据库字段类型

##### 5. 插入准备表数据页
- 如果准备空数据，则不需要生成excel数据
- 需要分析业务代码和mapper.xml中出现相关表、表之间关联关系、where条件字段
- 可以忽略代码和xml未出现的表字段，主键不可忽略
- 如果表结构字段约束为NOT NULL，必须mock字段值
- 如果时间字段设置当前时间，请使用SYSDATE。
- 当前时间支持加减 Y(年) M(月) D(日) H(时) MI(分)。如SYSDATE -1Y+1M  - 2D + 3MI  即为当前时间减1年加1月减2天加3分钟。
- 非当前时间必须使用格式yyyy-MM-dd HH:mm:ss

##### 6. 插入数据库期望结果页

- 需要分析业务代码和mapper.xml中操作的相关表
- 如果时间字段期望当前时间，请使用CURMINUTE

#### 文件命名规范

- 文件名格式：`{测试类名}Tests.xlsx`
- 示例：`OaiChat6438001Tests.xlsx`
- 存放路径：`src/test/resources/com/wtyt/app/{模块名}/controller/`

#### Sheet页命名规范

##### 1. clean页（数据清理）

- **用途**：清理测试相关表数据
- **命名**：`clean`
- **内容**：测试功能涉及到的所有表

##### 2. 准备表数据页

- **命名格式**：`{序号}`（如：`001`、`002`、`003`）
- **用途**：存放测试用例的初始化数据
- **对应关系**：与测试方法中的序号一致

##### 3. 数据库期望结果页

- **命名格式**：`{序号}expect`（如：`001expect`、`002expect`）
- **用途**：存放测试用例执行后的数据库表数据期望状态
- **验证方式**：通过 `@ExpectedDatabase`注解进行数据库状态验证

##### 4. 接口返回值期望结果页

- **result命名格式**：`{序号}mvc`（如：`001mvc`、`002mvc`）
- **用途**：存放测试用例执行后的接口返回结果
- **验证方式**：通过 `@MVCExpectedDatabase`注解进行接口返回值验证
- **result中存在数组字段**：需要拆分期望
  - **命名格式**：`{序号}mvc_{字段名}`（如：`001mvc_UnRepaymentList`）
  - **拆分规则**：
           - result中存在数组字段,注意result中验证该数组字段的期望值= `ignore`
		   - 若result只有该数组字段且无其他字段期望，则无需期望result
  - **示例**：

  ```json
  {
      "total": 2,
      "pageSize": 10,
      "UnRepaymentList": [
          {
              "UnRepaymentId": "123456",
              "UnRepaymentType": "1",
              "UnRepaymentAmount": "1000.00"
          }
  }

  ```

#### excel结构规范

- 字段说明 ：
    - sheetName：sheet名称
    - sheetType：sheet类型，0：清除表数据，1：准备表数据，2：期望表数据，3：期望mvc接口返回结果
    - values：数据列表
    - tableName：数据库表名
- 数据库期望结果 ：只包含变更表和关键验证字段


##### 写入excel参数示例，必须是json格式

```json
{
 "dataPackage": [
	{
		"sheetName":"clean",
		"sheetType":"0",
		"values":[
			{
				"tableName":"T_XXX_XXX",
			}
		]
	},
	{
		"sheetName":"001",
		"sheetType":"1",// 表数据准备
		"values":[
			{
			   "tableName":"T_XXX_XXX",
			   "rowData":[{
				   "ID":"1",
				   "CREATED_TIME":"2025-12-25 10:00:00"
			   }]
			}
		]
	},
	{
	    "sheetName":"001expect",
		"sheetType":"2", // 期望表数据
		"values":[
			{
			   "tableName":"T_XXX_XXX",
			   "rowData":[{
				   "ID":"1",
				   "CREATED_TIME":"2025-12-25 10:00:00"
			   }]
			}
		]
	},
	{
	    "sheetName":"mvc",
		"sheetType":"3", // 期望mvc接口返回结果
		"values":[
			{
			   "rowData":[{
				   "id":"1",
				   "createdTime":"2025-12-25 10:00:00"
			   }]
			}
		]
	}
  ]
}
```

#### 接口返回参数结构规范

##### 写入excel参数示例

```json
{
 "dataPackage": [
    {
	    "sheetName":"mvc",
		"sheetType":"3", // 期望mvc接口返回结果
		"values":[
			{
			   "rowData":[{
				   "id":"1",
				   "createdTime":"2025-12-25 10:00:00"
			   }]
			}
		]
	}
   ]
}
```

#### 数据内容规范

##### 字段值设置原则

1. **主键字段**：使用有意义的测试ID（如：1、2、999等）
2. **关联字段**：保持数据一致性，确保外键关联正确
3. **时间字段**：使用固定的测试时间（如：`2025-06-30 15:56:00`）

##### 必填字段规范

- `IS_DEL`：默认为 `0`（未删除）
- `CREATED_TIME`：设置为测试执行时间
- `LAST_MODIFIED_TIME`：设置为测试执行时间
- `NOTE`：可设置为测试说明或留空

#### 期望结果数据规范

##### expect页设计原则

0. **接口新增数据**：数据库表或接口返回结果主键无需效验，去掉主键字段
1. **只包含变更的表**：仅包含测试执行后会发生变化的表
2. **只包含关键字段**：仅包含需要验证的关键业务字段
3. **变更字段优先放前面**：变更字段放前面，便于对比。
4. **数据完整性**：确保期望数据与测试逻辑一致

##### 验证字段选择

- 业务主键字段
- 状态变更字段
- 核心业务字段
- 时间戳字段（如需验证）