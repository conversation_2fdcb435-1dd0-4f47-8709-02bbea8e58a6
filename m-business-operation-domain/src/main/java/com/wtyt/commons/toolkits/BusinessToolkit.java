package com.wtyt.commons.toolkits;

import cn.hutool.core.bean.BeanUtil;
import com.wtyt.bo.bean.BoTaskDetailBean;
import com.wtyt.bo.bean.request.Req1735201Bean;
import com.wtyt.common.beans.config.InterfaceCallFieldConfig;
import com.wtyt.common.enums.NodeChannelCodeEnum;
import com.wtyt.common.toolkits.ConfigToolkit;
import com.wtyt.lg.commons.toolkits.DateToolkit;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.*;

/**
 * 业务使用的一些公共方法工具类
 *
 * <AUTHOR>
 */
public class BusinessToolkit {

    private static Map<String, Integer> CONVERT_PUSH_WAY_MAP = new HashMap<>();

    static {
        // 请进入NodeChannelCodeEnum修改
        CONVERT_PUSH_WAY_MAP.put("0", 4);
        CONVERT_PUSH_WAY_MAP.put("1", 1);
        CONVERT_PUSH_WAY_MAP.put("2", 2);
        CONVERT_PUSH_WAY_MAP.put("3", 8);
        CONVERT_PUSH_WAY_MAP.put("4", 16);
        //chatGpt通道
        CONVERT_PUSH_WAY_MAP.put("99", 64);
    }

    /**
     * pushWay
     * 0：企微
     * 1：短信
     * 2：app
     *
     * pushChannel 是按照二进制累计的，所以直接累加转换
     *  99是CHATGPT渠道
     * @param pushWay
     * @return
     */
    public static String convertPushWayToChannel(String pushWay) {
        if (StringUtils.isBlank(pushWay)) {
            return StringUtils.EMPTY;
        }
        List<String> list = Arrays.asList(StringUtils.split(pushWay, ","));
        int pushChannel = 0;
        for (String p : list) {
            Integer i = CONVERT_PUSH_WAY_MAP.get(p);
            if (i == null) {
                continue;
            }
            pushChannel += i;
        }
        return String.valueOf(pushChannel);
    }

    /**
     * pushWay
     * 0：企微
     * 1：短信
     * 2：app
     * <p>
     * type 1 好运宝 2 快路宝
     *
     * @param pushWay
     * @return
     */
    public static String getBaseMsgChannelCode(String pushWay, String type) {
        if (StringUtils.isBlank(pushWay)) {
            return StringUtils.EMPTY;
        }
        List<String> list = Arrays.asList(StringUtils.split(pushWay, ","));
        List<String> msgChannelCodeList = new ArrayList<>();
        for (String p : list) {
            if (StringUtils.equals("0", p)) {
                msgChannelCodeList.add(NodeChannelCodeEnum.QW.getName());
            } else if (StringUtils.equals("1", p)) {
                msgChannelCodeList.add(NodeChannelCodeEnum.DX.getName());
            } else if (StringUtils.equals("2", p) && "1".equals(type)) {
                msgChannelCodeList.add(NodeChannelCodeEnum.HYB.getName());
            } else if (StringUtils.equals("2", p) && "2".equals(type)) {
                msgChannelCodeList.add(NodeChannelCodeEnum.KLB.getName());
            }
        }
        return String.join(",", msgChannelCodeList);
    }


    /**
     * 校验pushWay是否包含短信
     * @param pushWay
     * @return
     */
    public static boolean checkPushWayHasDx(String pushWay) {
        if (StringUtils.isBlank(pushWay)) {
            return false;
        }
        return Arrays.asList(StringUtils.split(pushWay, ",")).contains("1");
    }


    /**
     * 95：当天
     * 99：近两天
     * 100：近三天
     * 101：近五天
     * 102：近一周
     *
     * @param timeScope
     * @return
     */
    public static Date getTimeScopeBeforeDate(String timeScope) {
        Date date = DateToolkit.getOfDayFirst(new Date());
        switch (timeScope) {
            case "95":
                break;
            case "99":
            case "100":
            case "101":
            case "102":
                date = DateUtils.addDays(date, -getTimeScopeSize(timeScope));
                break;
            default:
                break;
        }
        return date;
    }

    /**
     * 95：当天
     * 99：近两天
     * 100：近三天
     * 101：近五天
     * 102：近一周
     *
     * @param timeScope
     * @return
     */
    public static int getTimeScopeSize(String timeScope) {
        int size = 0;
        switch (timeScope) {
            case "95":
                break;
            case "99":
                size = 1;
                break;
            case "100":
                size = 2;
                break;
            case "101":
                size = 4;
                break;
            case "102":
                size = 6;
                break;
            default:
                break;
        }
        return size;
    }

    /**
     * 约定到场时间和约定送达时间格式化
     * @param arriveEndTimeFormat
     * @param originTime
     * @return
     */
    public static String getArriveEndTimeFormat(String arriveEndTimeFormat,String originTime){
        if(StringUtils.isNotBlank(originTime)){
            if("2".equals(arriveEndTimeFormat)){
                //日期级
                originTime = originTime.substring(0,10);
            }else {
                //小时级
                originTime = originTime.substring(0,16);
            }
        }

        return originTime;
    }


    public static boolean isOnlyBusinessOperation(String settleMode,String allocateSettleMode){
        return !"1".equals(settleMode) && (StringUtils.isEmpty(allocateSettleMode) || !"1".equals(allocateSettleMode));
    }
}
