<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskExportRecordMapper">

    <insert id="insertTaskExportRecord">
        INSERT INTO T_BO_TASK_EXPORT_RECORD (
            BO_TASK_EXPORT_RECORD_ID,
            EXPORT_TYPE,
            ORG_ID,
            USER_ID,
            START_TIME,
            END_TIME,
            EXPIRES_TIME,
            TOTAL_ROWS,
            FILE_NAME,
            FILE_SUFFIX,
            FILE_DIRECTORY,
            FILE_STATUS,
            FILE_URL
        ) VALUES (
            #{boTaskExportRecordId},
            #{exportType},
            #{orgId},
            #{userId},
            TO_DATE(#{startTime}, 'yyyy-mm-dd hh24:mi:ss'),
            TO_DATE(#{endTime}, 'yyyy-mm-dd hh24:mi:ss'),
            TO_DATE(#{expiresTime}, 'yyyy-mm-dd hh24:mi:ss'),
            #{totalRows},
            #{fileName},
            #{fileSuffix},
            #{fileDirectory},
            #{fileStatus},
            #{fileUrl}
        )
    </insert>

    <update id="updateTaskExportRecord">
        UPDATE T_BO_TASK_EXPORT_RECORD T
        SET <if test="totalRows != null and totalRows.length > 0">
                T.TOTAL_ROWS = #{totalRows},
            </if>
            <if test="fileStatus != null and fileStatus.length > 0">
                T.FILE_STATUS = #{fileStatus},
            </if>
            <if test="endTime != null and endTime.length > 0">
                T.END_TIME = TO_DATE(#{endTime}, 'yyyy-mm-dd hh24:mi:ss'),
            </if>
            <if test="expiresTime != null and expiresTime.length > 0">
                T.EXPIRES_TIME = TO_DATE(#{expiresTime}, 'yyyy-mm-dd hh24:mi:ss'),
            </if>
            <if test="fileUrl != null and fileUrl.length > 0">
                T.FILE_URL = #{fileUrl},
            </if>
            <if test="failureReason != null">
                T.FAILURE_REASON = #{failureReason},
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TASK_EXPORT_RECORD_ID = #{boTaskExportRecordId}
    </update>

    <select id="getTaskExportRecord" resultType="com.wtyt.dao.bean.syf.BoTaskExportRecordBean">
        SELECT
            T.BO_TASK_EXPORT_RECORD_ID boTaskExportRecordId,
            T.EXPORT_TYPE exportType,
            T.ORG_ID orgId,
            T.USER_ID userId,
            TO_CHAR(T.START_TIME, 'yyyy-mm-dd hh24:mi:ss') startTime,
            TO_CHAR(T.END_TIME, 'yyyy-mm-dd hh24:mi:ss') endTime,
            TO_CHAR(T.EXPIRES_TIME, 'yyyy-mm-dd hh24:mi:ss') expiresTime,
            T.TOTAL_ROWS totalRows,
            T.FILE_NAME fileName,
            T.FILE_SUFFIX fileSuffix,
            T.FILE_DIRECTORY fileDirectory,
            T.FILE_STATUS fileStatus,
            T.FILE_URL fileUrl,
            T.FAILURE_REASON failureReason
        FROM T_BO_TASK_EXPORT_RECORD T
        WHERE T.IS_DEL = 0
        AND T.BO_TASK_EXPORT_RECORD_ID = #{boTaskExportRecordId}
    </select>

    <select id="getTaskExportRecordList" resultType="com.wtyt.dao.bean.syf.BoTaskExportRecordBean">
        SELECT
            T.BO_TASK_EXPORT_RECORD_ID boTaskExportRecordId,
            T.EXPORT_TYPE exportType,
            T.ORG_ID orgId,
            T.USER_ID userId,
            TO_CHAR(T.START_TIME, 'yyyy-mm-dd hh24:mi:ss') startTime,
            TO_CHAR(T.END_TIME, 'yyyy-mm-dd hh24:mi:ss') endTime,
            TO_CHAR(T.EXPIRES_TIME, 'yyyy-mm-dd hh24:mi:ss') expiresTime,
            T.TOTAL_ROWS totalRows,
            T.FILE_NAME fileName,
            T.FILE_SUFFIX fileSuffix,
            T.FILE_DIRECTORY fileDirectory,
            T.FILE_STATUS fileStatus,
            T.FILE_URL fileUrl,
            T.FILE_DOWNLOADS fileDownloads,
            T.FAILURE_REASON failureReason
        FROM T_BO_TASK_EXPORT_RECORD T
        WHERE T.IS_DEL = 0
        AND T.ORG_ID = #{orgId}
        AND T.USER_ID = #{userId}
        <if test="exportTypeList != null and exportTypeList.size() > 0">
            AND T.EXPORT_TYPE IN
            <foreach collection="exportTypeList" item="exportType" open="(" separator="," close=")">
                #{exportType}
            </foreach>
        </if>
        ORDER BY T.CREATED_TIME DESC
    </select>

    <update id="incTaskExportFileDownloads">
        UPDATE T_BO_TASK_EXPORT_RECORD T
        SET T.FILE_DOWNLOADS = T.FILE_DOWNLOADS + 1,
            T.REMARK = #{remark},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TASK_EXPORT_RECORD_ID = #{boTaskExportRecordId}
    </update>

</mapper>
