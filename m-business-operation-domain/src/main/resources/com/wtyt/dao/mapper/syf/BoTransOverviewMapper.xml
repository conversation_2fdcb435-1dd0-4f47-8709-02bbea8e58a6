<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransOverviewMapper">
    <insert id="insertBoardBpOverview">
        INSERT INTO T_BO_TRANS_OVERVIEW(BO_TRANS_OVERVIEW_ID, OVERVIEW_DATE, BO_BUSINESS_PROJECT_ID, PLAN_CAR_NUM, RECEIVED_NOT_ARRIVE_NUM, ARRIVE_NOT_START_NUM, START_NOT_ARRIVE_NUM, ARRIVE_NOT_UNLOAD_NUM, UNSETTLED_NUM, COMPLETED_NUM)
        VALUES(#{boTransOverviewId}, #{overviewDate}, #{boBusinessProjectId}, #{planCarNum}, #{receivedNotArriveNum}, #{arriveNotStartNum}, #{startNotArriveNum}, #{arriveNotUnloadNum}, #{unsettledNum}, #{completedNum})
    </insert>

    <select id="queryBoTransOverviewList" resultType="com.wtyt.board.bean.BoardBpOverviewBean">
        SELECT
        BO_TRANS_OVERVIEW_ID boTransOverviewId,
        OVERVIEW_DATE overviewDate,
        PLAN_CAR_NUM planCarNum,
        RECEIVED_NOT_ARRIVE_NUM receivedNotArriveNum,
        ARRIVE_NOT_START_NUM arriveNotStartNum,
        START_NOT_ARRIVE_NUM startNotArriveNum,
        ARRIVE_NOT_UNLOAD_NUM arriveNotUnloadNum,
        UNSETTLED_NUM unsettledNum,
        COMPLETED_NUM completedNum,
        (SELECT count(1) FROM T_BO_TRANS_OVERVIEW_REC c WHERE c.IS_DEL = 0 AND o.BO_TRANS_OVERVIEW_ID= c.BO_TRANS_OVERVIEW_ID) lineNum
        FROM T_BO_TRANS_OVERVIEW o
        WHERE  BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId}
        AND IS_DEL = 0
        AND OVERVIEW_DATE >= #{startIndexDate}
        AND OVERVIEW_DATE &lt;= #{endIndexDate}
        ORDER BY OVERVIEW_DATE DESC
    </select>

    <select id="queryBoTransOverviewById" resultType="com.wtyt.board.bean.BoardBpOverviewBean">
        SELECT
            BO_TRANS_OVERVIEW_ID boTransOverviewId,
            OVERVIEW_DATE overviewDate,
            PLAN_CAR_NUM planCarNum,
            RECEIVED_NOT_ARRIVE_NUM receivedNotArriveNum,
            ARRIVE_NOT_START_NUM arriveNotStartNum,
            START_NOT_ARRIVE_NUM startNotArriveNum,
            ARRIVE_NOT_UNLOAD_NUM arriveNotUnloadNum,
            UNSETTLED_NUM unsettledNum,
            COMPLETED_NUM completedNum
        FROM T_BO_TRANS_OVERVIEW
        WHERE  BO_TRANS_OVERVIEW_ID = #{boTransOverviewId}
        AND IS_DEL = 0
    </select>

    <select id="queryCountByBpId" resultType="java.lang.Integer">
        SELECT
            COUNT(BO_TRANS_OVERVIEW_ID)
        FROM T_BO_TRANS_OVERVIEW
        WHERE  BO_BUSINESS_PROJECT_ID = #{boBusinessProjectId}
        AND OVERVIEW_DATE = #{overviewDate}
        AND IS_DEL = 0
    </select>

</mapper>
