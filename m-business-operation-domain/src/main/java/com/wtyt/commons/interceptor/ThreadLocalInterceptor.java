package com.wtyt.commons.interceptor;

import com.wtyt.dh.util.DynamicHeadFieldDictThreadLocal;
import com.wtyt.dh.util.DynamicHeadRuleThreadLocal;
import com.wtyt.dh.util.OrgConfigCache;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 *
 *
 */
@Aspect
@Component
public class ThreadLocalInterceptor {



    @Pointcut("execution(public * com.wtyt..controller..*(..))")
    public void log() {
    }

    @After("log()")
    public void after(JoinPoint joinPoint) {
        DynamicHeadFieldDictThreadLocal.remove();
        DynamicHeadRuleThreadLocal.remove();
        OrgConfigCache.remove();
    }



}
