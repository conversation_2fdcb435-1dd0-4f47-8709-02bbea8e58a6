package com.wtyt.reviews.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wtyt.bean.Page;
import com.wtyt.common.constants.RedisKeyConstants;
import com.wtyt.common.constants.SidConstants;
import com.wtyt.common.enums.DefaultValueEnum;
import com.wtyt.common.rpc.bean.Rpc19001IBean;
import com.wtyt.common.rpc.bean.Rpc19001OBean;
import com.wtyt.common.rpc.bean.Rpc19049IBean;
import com.wtyt.common.rpc.bean.Rpc19049OBean;
import com.wtyt.common.rpc.bean.sq.Rpc1800IBean;
import com.wtyt.common.rpc.bean.sq.Rpc1800OBean;
import com.wtyt.common.rpc.service.RpcGwayService;
import com.wtyt.common.rpc.service.RpcSqService;
import com.wtyt.common.toolkits.*;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.reviews.bean.*;
import com.wtyt.reviews.bean.request.*;
import com.wtyt.reviews.bean.response.*;
import com.wtyt.dao.mapper.syf.ReviewsMapper;
import com.wtyt.dao.mapper.syf.ReviewsScopeMapper;
import com.wtyt.dao.mapper.syf.ReviewsSubjectMapper;
import com.wtyt.reviews.mapper.syf.ReviewsCollectMapper;
import com.wtyt.reviews.service.ReviewsFactory;
import com.wtyt.reviews.service.ReviewsService;
import com.wtyt.reviews.service.ReviewsSubjectExtendService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年03月15日 15时21分
 */
@Service
public class ReviewsServiceImpl implements ReviewsService {

    private RedissonClient redissonClient;
    private RpcGwayService rpcGwayService;
    private RpcSqService rpcSqService;
    private ReviewsSubjectMapper reviewsSubjectMapper;
    private ReviewsScopeMapper reviewsScopeMapper;
    private ReviewsMapper reviewsMapper;
    private ReviewsCollectMapper reviewsCollectMapper;

    /**
     * 创建评价主体
     * @param reqCreate
     */
    @Override
    public void createReviewsSubject(ReqCreateBean reqCreate) {
        String reviewsTarget = reqCreate.getReviewsTarget();
        String reviewsTargetId = reqCreate.getReviewsTargetId();
        ReviewsTargetBean target = this.getReviewsTarget(reviewsTarget, reviewsTargetId);

        ReviewsSubjectBean reviewsSubject = new ReviewsSubjectBean();
        BeanUtils.copyProperties(reqCreate, reviewsSubject);
        reviewsSubject.setPmReviewsSubjectId(UidToolkit.generateUidString());
        if (target != null) {
            reviewsSubject.setReviewsTargetName(target.getName());
            reviewsSubject.setReviewsTargetMobile(target.getMobile());
        }
        this.reviewsSubjectMapper.insertReviewsSubject(reviewsSubject);

        List<ReqCreateBean.Scope> scopeList = reqCreate.getScopeList();
        List<ReviewsScopeBean> reviewsScopeList = new ArrayList<>(scopeList.size());
        for (ReqCreateBean.Scope scope : scopeList) {
            ReviewsScopeBean reviewsScope = new ReviewsScopeBean();
            BeanUtils.copyProperties(scope, reviewsScope);
            reviewsScope.setPmReviewsScopeId(UidToolkit.generateUidString());
            reviewsScope.setPmReviewsSubjectId(reviewsSubject.getPmReviewsSubjectId());
            reviewsScopeList.add(reviewsScope);
        }
        this.reviewsScopeMapper.batchInsertReviewsScope(reviewsScopeList);
    }

    /**
     * 获取评价对象
     * @param reviewsTarget
     * @param reviewsTargetId
     * @return
     */
    private ReviewsTargetBean getReviewsTarget(String reviewsTarget, String reviewsTargetId) {
        if (StringUtils.equals(reviewsTarget, "0")) {
            if (VerifyToolkit.isPositiveNumber(reviewsTargetId)) {
                Rpc19001IBean rpc19001IBean = new Rpc19001IBean();
                rpc19001IBean.setUserId(reviewsTargetId);
                Rpc19001OBean rpc19001OBean = this.rpcGwayService.rpcResult(rpc19001IBean, SidConstants.SID_19001, Rpc19001OBean.class);
                if (rpc19001OBean != null) {
                    ReviewsTargetBean reviewsTargetBean = new ReviewsTargetBean();
                    reviewsTargetBean.setName(rpc19001OBean.getRealName());
                    reviewsTargetBean.setMobile(rpc19001OBean.getMobileNo());
                    return reviewsTargetBean;
                }
            }
        }
        return null;
    }

    /**
     * 删除评价主体
     * @param reqDelete
     */
    @Override
    public void deleteReviewsSubject(ReqDeleteBean reqDelete) {
        String subjectType = reqDelete.getSubjectType();
        String subjectId = reqDelete.getSubjectId();
        ReviewsSubjectBean reviewsSubject = this.reviewsSubjectMapper.getReviewsSubjectByOrgSubject(subjectType, subjectId);
        if (reviewsSubject != null) {
            String pmReviewsSubjectId = reviewsSubject.getPmReviewsSubjectId();
            this.reviewsSubjectMapper.updateReviewsSubjectDelete(pmReviewsSubjectId, reqDelete.getRemark());
        }
    }

    /**
     * 获取评价得分
     * @param reqDetail
     * @return
     */
    @Override
    public List<ReviewsCollectBean> getReviewsScoreList(ReqDetailBean reqDetail) {
        String subjectType = reqDetail.getSubjectType();
        List<String> subjectIdList = reqDetail.getSubjectIdList();
        if (CollectionUtils.isNotEmpty(subjectIdList)) {
            return this.reviewsCollectMapper.getReviewsScoreListBySubject(subjectType, subjectIdList);
        }
        return Collections.emptyList();
    }

    /**
     * 恢复评价主体
     * @param reqRecovery
     */
    @Override
    public void recoveryReviewsSubject(ReqRecoveryBean reqRecovery) {
        String subjectType = reqRecovery.getSubjectType();
        List<String> subjectIdList = reqRecovery.getSubjectIdList();
        if (CollectionUtils.isNotEmpty(subjectIdList)) {
            this.reviewsSubjectMapper.updateReviewsSubjectRecovery(subjectType, subjectIdList);
        }
    }

    /**
     * 替换评价对象
     * @param reqReplace
     */
    @Override
    public void replaceReviewsTarget(ReqReplaceBean reqReplace) {
        String subjectType = reqReplace.getSubjectType();
        String subjectId = reqReplace.getSubjectId();
        String reviewsTarget = reqReplace.getReviewsTarget();
        String reviewsTargetId = StringUtils.defaultIfBlank(reqReplace.getReviewsTargetId(), "-1");
        ReviewsSubjectBean reviewsSubject = this.reviewsSubjectMapper.getReviewsSubjectByOrgSubject(subjectType, subjectId);
        if (reviewsSubject != null) {
            String pmReviewsSubjectId = reviewsSubject.getPmReviewsSubjectId();
            String oldReviewsTarget = reviewsSubject.getReviewsTarget();
            String oldReviewsTargetId = reviewsSubject.getReviewsTargetId();
            if (!StringUtils.equals(reviewsTarget, oldReviewsTarget) || !StringUtils.equals(reviewsTargetId, oldReviewsTargetId)) {
                List<ReviewsBean> reviewsList = this.reviewsMapper.getAllReviewsByReviewsSubjectId(pmReviewsSubjectId);
                if (CollectionUtils.isEmpty(reviewsList)) {
                    // 未产生评价数据的可以进行修改评价对象
                    reviewsSubject.setReviewsTarget(reviewsTarget);
                    reviewsSubject.setReviewsTargetId(reviewsTargetId);
                    reviewsSubject.setReviewsTargetName(null);
                    reviewsSubject.setReviewsTargetMobile(null);
                    ReviewsTargetBean target = this.getReviewsTarget(reviewsTarget, reviewsTargetId);
                    if (target != null) {
                        reviewsSubject.setReviewsTargetName(target.getName());
                        reviewsSubject.setReviewsTargetMobile(target.getMobile());
                    }
                    this.reviewsSubjectMapper.updateReviewsSubjectTarget(reviewsSubject);
                }
            }
        }
    }

    /**
     * 5329573-绩效管理-评价主体详情
     * @param data
     * @return
     */
    @Override
    public Resp5329573Bean reviewsSubjectDetail(Req5329573Bean data) {
        String subjectType = data.getSubjectType();
        String subjectId = data.getSubjectId();
        if (VerifyToolkit.isNonNegativeIntegers(subjectType) && VerifyToolkit.isPositiveNumber(subjectId)) {
            ReviewsSubjectBean reviewsSubject = this.reviewsSubjectMapper.getReviewsSubjectByOrgSubject(subjectType, subjectId);
            if (reviewsSubject != null) {
                ReviewsSubjectExtendService reviewsSubjectExtendService = ReviewsFactory.getSubjectExtendService(subjectType);
                Map<String, ?> subjectExtend = reviewsSubjectExtendService.getSubjectExtend(subjectId);
                Resp5329573Bean resp5329573Bean = new Resp5329573Bean();
                BeanUtils.copyProperties(reviewsSubject, resp5329573Bean);
                resp5329573Bean.setSubjectExtend(subjectExtend);
                return resp5329573Bean;
            }
        }
        return null;
    }

    /**
     * 5329574-绩效管理-提交评价
     * @param data
     * @return
     */
    @Override
    public void submitReviews(Req5329574Bean data) {
        // 验证参数
        this.verifySubmitReviews(data);
        // 获取评价主体
        ReviewsSubjectBean reviewsSubject = this.getReviewsSubject(data);
        // 获取redis锁
        RLock rLock = this.getRedissonRLock(reviewsSubject);
        try {
            if (rLock.tryLock()) {
                // 验证是否已评价
                this.verifyIsReviews(data);
                // 新增评价
                this.insertReviews(data);
            }
        } finally {
            if (rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    /**
     * 获取redis锁
     * @param reviewsSubject
     * @return
     */
    private RLock getRedissonRLock(ReviewsSubjectBean reviewsSubject) {
        String subjectType = reviewsSubject.getSubjectType();
        String subjectId = reviewsSubject.getSubjectId();
        String lockKey = RedisKeyToolkit.getLockKey(RedisKeyConstants.REVIEWS_LOCK, subjectType + subjectId);
        return this.redissonClient.getLock(lockKey);
    }

    /**
     * 新增评价
     * @param data
     */
    private void insertReviews(Req5329574Bean data) {
        String reviewerMobile = data.getLgMobileNo();
        // 获取评价者姓名
        String reviewerName = this.getReviewerName(reviewerMobile);
        ReviewsBean reviews = new ReviewsBean();
        reviews.setPmReviewsId(UidToolkit.generateUidString());
        reviews.setPmReviewsSubjectId(data.getPmReviewsSubjectId());
        reviews.setParentId("-1");
        // 固定司机
        reviews.setReviewerIdentity("0");
        reviews.setReviewerId(data.getLgDriverId());
        reviews.setReviewerName(reviewerName);
        reviews.setReviewerMobile(reviewerMobile);
        reviews.setScore(data.getScore());
        reviews.setContent(data.getContent());
        reviews.setIsAnonymity(data.getIsAnonymity());
        this.reviewsMapper.insertReviews(reviews);
    }

    /**
     * 验证是否已评价
     * @param data
     */
    private void verifyIsReviews(Req5329574Bean data) {
        String pmReviewsSubjectId = data.getPmReviewsSubjectId();
        String reviewerIdentity = "0";
        String reviewerId = data.getLgDriverId();
        // 验证是否已评价、评价过后进行删除也不能再次进行评价、所以这里需要查询全部的
        ReviewsBean reviews = this.reviewsMapper.getReviewsByReviewer(pmReviewsSubjectId, reviewerIdentity, reviewerId);
        if (reviews != null) {
            throw new UnifiedBusinessException("已评价、请勿重复操作");
        }
    }

    /**
     * 获取评价主体
     * @param object
     * @return
     */
    private ReviewsSubjectBean getReviewsSubject(Object object) {
        ReviewsSubjectBean reviewsSubject = null;
        String pmReviewsSubjectId = (String)ReflectUtil.getFieldValue(object, "pmReviewsSubjectId");
        if (VerifyToolkit.isPositiveInteger(pmReviewsSubjectId)) {
            reviewsSubject = this.reviewsSubjectMapper.getReviewsSubject(pmReviewsSubjectId);
        }
        return Optional.ofNullable(reviewsSubject)
                .orElseThrow(() -> new UnifiedBusinessException("评价主体不存在"));
    }

    /**
     * 获取评价者姓名
     * @param reviewerMobile
     * @return
     */
    private String getReviewerName(String reviewerMobile) {
        String reviewerName = null;
        try {
            Rpc1800IBean rpc1800IBean = new Rpc1800IBean();
            rpc1800IBean.setMobile_no(reviewerMobile);
            Rpc1800OBean rpc1800OBean = this.rpcSqService.rpcResult(rpc1800IBean, SidConstants.SID_1800, Rpc1800OBean.class, false);
            if (rpc1800OBean != null) {
                reviewerName = rpc1800OBean.getReal_name();
            }
        } catch (Exception e) {
            throw new UnifiedBusinessException("系统繁忙、请稍后重试");
        }
        return reviewerName;
    }

    /**
     * 验证参数
     * @param data
     */
    private void verifySubmitReviews(Req5329574Bean data) {
        String pmReviewsSubjectId = data.getPmReviewsSubjectId();
        String lgDriverId = data.getLgDriverId();
        String lgMobileNo = data.getLgMobileNo();
        String score = data.getScore();
        String isAnonymity = data.getIsAnonymity();
        VerifyToolkit.verifyEmpty(lgDriverId, "登陆失效、请重新登陆[1]");
        VerifyToolkit.verifyPositiveNumber(lgDriverId, "登陆失效、请重新登陆[2]");
        VerifyToolkit.verifyEmpty(lgMobileNo, "登陆失效、请重新登陆[3]");
        VerifyToolkit.verifyPositiveNumber(lgMobileNo, "登陆失效、请重新登陆[4]");
        VerifyToolkit.verifyEmpty(pmReviewsSubjectId, "评价主体ID不可为空");
        VerifyToolkit.verifyPositiveNumber(pmReviewsSubjectId, "评价主体ID格式非法");
        VerifyToolkit.verifyEmpty(score, "评分不可为空");
        VerifyToolkit.verifyPositiveNumber(score, "评分格式非法");
        VerifyToolkit.verifyRangeContains(score, "1,2,3,4,5","评分格式非法");
        VerifyToolkit.verifyEmpty(isAnonymity, "是否匿名不可为空");
        VerifyToolkit.verifyRangeContains(isAnonymity, "0,1","是否匿名格式非法");
    }

    /**
     * 5329575-绩效管理-用户评价列表（汇总）
     * @param data
     * @return
     */
    @Override
    public Resp5329575Bean userReviewsCollect(Req5329575Bean data) {
        String reviewerIdentity = data.getReviewerIdentity();
        String reviewerId = data.getLgDriverId();
        int finishedCount = 0, unfinishedCount = 0;
        if (VerifyToolkit.isNonNegativeIntegers(reviewerIdentity) && VerifyToolkit.isPositiveNumber(reviewerId)) {
            // 已评价总数
            finishedCount = this.reviewsCollectMapper.getFinishedReviewsCount(reviewerIdentity, reviewerId);
            // 未评价总数
            unfinishedCount = this.reviewsCollectMapper.getUnfinishedReviewsCount(reviewerIdentity, reviewerId);
        }
        Resp5329575Bean resp5329575Bean = new Resp5329575Bean();
        resp5329575Bean.setFinishedCount(finishedCount);
        resp5329575Bean.setUnfinishedCount(unfinishedCount);
        return resp5329575Bean;
    }

    /**
     * 5329576-绩效管理-用户评价列表
     * @param data
     * @return
     */
    @Override
    public Resp5329576Bean userReviewsList(Req5329576Bean data) {
        int pageSize = data.getPageSize();
        int pageNo = data.getPageNo();
        String reviewerIdentity = data.getReviewerIdentity();
        String reviewerId = data.getLgDriverId();
        String searchType = data.getSearchType();

        List<Resp5329576Bean.Reviews> reviewsList = Collections.emptyList();
        List<ReviewsCollectBean> reviewsCollectList = Collections.emptyList();
        if (VerifyToolkit.isNonNegativeIntegers(reviewerIdentity, searchType) && VerifyToolkit.isPositiveNumber(reviewerId)) {
            PageHelper.startPage(pageNo, pageSize);
            if (StringUtils.equals(searchType, "0")) {
                // 未评价
                reviewsCollectList = this.reviewsCollectMapper.getUnfinishedReviewsList(reviewerIdentity, reviewerId);
            } else if (StringUtils.equals(searchType, "1")) {
                // 已评价
                reviewsCollectList = this.reviewsCollectMapper.getFinishedReviewsList(reviewerIdentity, reviewerId);
            }
        }
        // 通过工厂获取评价主体的信息
        if (CollectionUtils.isNotEmpty(reviewsCollectList)) {
            Map<String, Map<String, ?>> subjectExtendMap = new HashMap<>(reviewsCollectList.size());
            Map<String, List<ReviewsCollectBean>> listMap = reviewsCollectList.stream()
                    .collect(Collectors.groupingBy(ReviewsCollectBean::getSubjectType));

            for (Map.Entry<String, List<ReviewsCollectBean>> next : listMap.entrySet()) {
                String subjectType = next.getKey();
                List<ReviewsCollectBean> reviewsCollectBeans = next.getValue();

                List<String> subjectIdList = reviewsCollectBeans.stream()
                        .map(ReviewsCollectBean::getSubjectId)
                        .collect(Collectors.toList());
                ReviewsSubjectExtendService subjectExtendService = ReviewsFactory.getSubjectExtendService(subjectType);
                subjectExtendMap.putAll(subjectExtendService.getSubjectExtend(subjectIdList));
            }

            reviewsList = new ArrayList<>(reviewsCollectList.size());
            for (ReviewsCollectBean reviewsCollect : reviewsCollectList) {
                String subjectId = reviewsCollect.getSubjectId();
                Map<String, ?> subjectExtend = subjectExtendMap.get(subjectId);

                Resp5329576Bean.Reviews reviews = new Resp5329576Bean.Reviews();
                BeanUtils.copyProperties(reviewsCollect, reviews);
                reviews.setSubjectExtend(subjectExtend);
                reviewsList.add(reviews);
            }
        }
        PageInfo<ReviewsCollectBean> pageInfo = new PageInfo<>(reviewsCollectList);
        Resp5329576Bean resp5329576Bean = new Resp5329576Bean();
        resp5329576Bean.setTotalRecords(pageInfo.getTotal());
        resp5329576Bean.setReviewsList(reviewsList);
        return resp5329576Bean;
    }

    /**
     * 5329577-绩效管理-用户评价删除
     * @param data
     */
    @Override
    public void deleteReviews(Req5329577Bean data) {
        String driverId = data.getLgDriverId();
        String pmReviewsId = data.getPmReviewsId();
        if (VerifyToolkit.isPositiveNumber(driverId, pmReviewsId)) {
            ReviewsBean reviews = this.reviewsMapper.getReviews(pmReviewsId);
            if (reviews != null) {
                String reviewerId = reviews.getReviewerId();
                if (StringUtils.equals(driverId, reviewerId)) {
                    this.reviewsMapper.updateReviewsDelete(pmReviewsId);
                }
            }
        }
    }

    /**
     * 5329578-绩效管理-评价中心列表
     * @param data
     * @return
     */
    @Override
    public Resp5329578Bean centerReviewsList(Req5329578Bean data) {
        // 设置默认值
        this.setCenterReviewsListDefault(data);
        List<Resp5329578Bean.ReviewsTarget> reviewsTargetList = Collections.emptyList();
        String orgId = data.getOrgId();
        List<String> roleIdList = ConfigToolkit.getReviewsOrgRoleConfig(orgId);
        String roleIds = String.join(", ", roleIdList);
        Rpc19049IBean rpc19049IBean = new Rpc19049IBean();
        rpc19049IBean.setOrgId(orgId);
        rpc19049IBean.setRoleIds(roleIds);
        rpc19049IBean.setIsOnlyXdl("1");
        Rpc19049OBean rpc19049OBean = this.rpcGwayService.rpcResult(rpc19049IBean, SidConstants.SID_19049, Rpc19049OBean.class);
        if (rpc19049OBean != null) {
            List<Rpc19049OBean> userList = rpc19049OBean.getUserList();
            List<String> roleUserIdList = userList.stream()
                    .map(Rpc19049OBean::getUserId)
                    .collect(Collectors.toList());
            String startDate = data.getStartDate();
            String endDate = data.getEndDate();
            List<ReviewsCollectBean> reviewsCollectList = Collections.emptyList();
            if (!StringUtils.equalsAny("-1", startDate, endDate)) {
                ReviewsDbParameterBean reviewsDbParameterBean = new ReviewsDbParameterBean();
                reviewsDbParameterBean.setOrgId(data.getOrgId());
                reviewsDbParameterBean.setReviewsTarget("0");
                reviewsDbParameterBean.setReviewsTargetIdList(roleUserIdList);
                reviewsDbParameterBean.setStartDate(startDate);
                reviewsDbParameterBean.setEndDate(endDate);
                reviewsCollectList = this.reviewsCollectMapper.getReviewsSummaryData1(reviewsDbParameterBean);
                for (ReviewsCollectBean reviewsCollect : reviewsCollectList) {
                    int reviewsCount = reviewsCollect.getReviewsCount();
                    double totalScore = reviewsCollect.getTotalScore();
                    if (reviewsCount > 0) {
                        double avgScore = NumberToolkit.divide(totalScore, reviewsCount);
                        reviewsCollect.setAvgScore(avgScore);
                    }
                }
            }

            reviewsTargetList = new ArrayList<>(userList.size());
            for (Rpc19049OBean oBean : userList) {
                String userId = oBean.getUserId();
                ReviewsCollectBean reviewsCollect = reviewsCollectList.stream()
                        .filter(rc -> {
                            String reviewsTargetId = rc.getReviewsTargetId();
                            return StringUtils.equals(reviewsTargetId, userId);
                        })
                        .findFirst().orElse(new ReviewsCollectBean());
                Resp5329578Bean.ReviewsTarget reviewsTarget = new Resp5329578Bean.ReviewsTarget();
                reviewsTarget.setReviewsTarget("0");
                reviewsTarget.setReviewsTargetId(oBean.getUserId());
                reviewsTarget.setReviewsTargetName(oBean.getRealName());
                reviewsTarget.setTotalCount(reviewsCollect.getTotalCount());
                reviewsTarget.setReviewsCount(reviewsCollect.getReviewsCount());
                reviewsTarget.setAvgScore(NumberToolkit.format(reviewsCollect.getAvgScore(),"#0.#", RoundingMode.HALF_UP));
                reviewsTargetList.add(reviewsTarget);
            }
        }
        Resp5329578Bean resp5329578Bean = new Resp5329578Bean();
        resp5329578Bean.setReviewsTargetList(reviewsTargetList);
        return resp5329578Bean;
    }

    /**
     * 设置默认值
     * @param data
     */
    private void setCenterReviewsListDefault(Req5329578Bean data) {
        String startDate = data.getStartDate();
        String endDate = data.getEndDate();
        if (StringUtils.isNotBlank(startDate)) {
            try {
                DateUtil.parse(startDate, DateToolkit.YYYY_MM_DD);
            } catch (Exception e) {
                data.setStartDate("-1");
            }
        }
        if (StringUtils.isNotBlank(endDate)) {
            try {
                DateUtil.parse(endDate, DateToolkit.YYYY_MM_DD);
            } catch (Exception e) {
                data.setEndDate("-1");
            }
        }
    }

    /**
     * 5329579-绩效管理-评价中心-个人评价列表（汇总）
     * @param data
     * @return
     */
    @Override
    public Resp5329579Bean centerPersonalReviewsCollect(Req5329579Bean data) {
        // 设置默认值
        this.setCenterPersonalReviewsCollectDefaultValue(data);
        String reviewsTarget = data.getReviewsTarget();
        String reviewsTargetId = data.getReviewsTargetId();
        List<ReviewsCollectBean> reviewsCollectList = null;
        if (VerifyToolkit.isNonNegativeIntegers(reviewsTarget) && VerifyToolkit.isPositiveNumber(reviewsTargetId)) {
            ReviewsDbParameterBean reviewsDbParameterBean = new ReviewsDbParameterBean();
            reviewsDbParameterBean.setOrgId(data.getOrgId());
            reviewsDbParameterBean.setReviewsTarget(reviewsTarget);
            reviewsDbParameterBean.setReviewsTargetId(reviewsTargetId);
            reviewsDbParameterBean.setSearchType(data.getSearchType());
            reviewsCollectList = this.reviewsCollectMapper.getReviewsSummaryData2(reviewsDbParameterBean);
        }
        int scoreTotal = 0;
        Map<String, Integer> scoreCollect = new HashMap<>(5);
        if (CollectionUtils.isNotEmpty(reviewsCollectList)) {
            scoreTotal = reviewsCollectList.stream()
                    .mapToInt(ReviewsCollectBean::getTotalCount)
                    .sum();
            scoreCollect = reviewsCollectList.stream()
                    .collect(Collectors.toMap(ReviewsCollectBean::getScore, ReviewsCollectBean::getTotalCount));
        }
        Resp5329579Bean resp5329579Bean = new Resp5329579Bean();
        for (int i = 1; i <= 5; i++) {
            String key = String.valueOf(i);
            scoreCollect.putIfAbsent(key, 0);
        }
        resp5329579Bean.setScoreTotal(scoreTotal);
        resp5329579Bean.setScoreCollect(scoreCollect);
        return resp5329579Bean;
    }

    /**
     * 设置默认值
     * @param data
     */
    private void setCenterPersonalReviewsCollectDefaultValue(Req5329579Bean data) {
        if (StringUtils.isBlank(data.getSearchType())) {
            data.setSearchType(DefaultValueEnum.ZERO.getValue());
        }
    }

    /**
     * 5329580-绩效管理-评价中心-个人评价列表
     * @param data
     * @return
     */
    @Override
    public Resp5329580Bean centerPersonalReviewsList(Req5329580Bean data) {
        // 设置默认值
        this.setCenterPersonalReviewsListDefaultValue(data);
        List<Resp5329580Bean.Reviews> reviewsList = Collections.emptyList();
        List<ReviewsCollectBean> reviewsCollectList = Collections.emptyList();
        String reviewsTarget = data.getReviewsTarget();
        String reviewsTargetId = data.getReviewsTargetId();
        String month = data.getMonth();
        String score = data.getScore();

        DateTime minDateTime = data.getMinDateTime();
        DateTime maxDateTime = data.getMaxDateTime();
        DateTime monthDateTime = null;
        if (!StringUtils.equalsAny("-1", reviewsTarget, reviewsTargetId, month, score)
                && minDateTime != null && maxDateTime != null) {
            monthDateTime = DateUtil.beginOfMonth(DateUtil.parseDate(month + "-01"));
            if (DateUtil.isIn(monthDateTime, minDateTime, maxDateTime)) {
                String startDate = DateUtil.format(monthDateTime, DateToolkit.YYYY_MM_DD_HH_MM_SS);
                String endDate = DateUtil.format(DateUtil.endOfMonth(monthDateTime), DateToolkit.YYYY_MM_DD_HH_MM_SS);
                PageHelper.startPage(data.getPageNo(), data.getPageSize());
                ReviewsDbParameterBean reviewsDbParameterBean = new ReviewsDbParameterBean();
                reviewsDbParameterBean.setOrgId(data.getOrgId());
                reviewsDbParameterBean.setReviewsTarget(reviewsTarget);
                reviewsDbParameterBean.setReviewsTargetId(reviewsTargetId);
                reviewsDbParameterBean.setStartDate(startDate);
                reviewsDbParameterBean.setEndDate(endDate);
                reviewsDbParameterBean.setScore(score);
                reviewsDbParameterBean.setSearchType(data.getSearchType());
                reviewsCollectList = this.reviewsCollectMapper.getReviewsListByReviewsTarget(reviewsDbParameterBean);
                if (CollectionUtils.isNotEmpty(reviewsCollectList)) {
                    // 获取评价主体扩展
                    Map<String, Map<String, ?>> subjectExtendMap = this.getReviewsSubjectExtendMap(reviewsCollectList);
                    // 构建评价列表的数据分组
                    Map<String, List<Resp5329580Bean.Data>> dataGroupMap = this.buildReviewsListDataGroup(reviewsCollectList, subjectExtendMap);
                    reviewsList = new ArrayList<>(16);
                    for (Map.Entry<String, List<Resp5329580Bean.Data>> next : dataGroupMap.entrySet()) {
                        String createdMonth = next.getKey() + "-01";
                        List<Resp5329580Bean.Data> dataList = next.getValue();
                        DateTime startDateTime = DateUtil.parseDate(createdMonth);
                        // 月统计
                        String endDates = DateUtil.formatDate(DateUtil.endOfMonth(startDateTime));
                        reviewsDbParameterBean = new ReviewsDbParameterBean();
                        reviewsDbParameterBean.setOrgId(data.getOrgId());
                        reviewsDbParameterBean.setStartDate(createdMonth);
                        reviewsDbParameterBean.setEndDate(endDates);
                        reviewsDbParameterBean.setReviewsTarget(data.getReviewsTarget());
                        reviewsDbParameterBean.setReviewsTargetIdList(Collections.singletonList(data.getReviewsTargetId()));
                        List<ReviewsCollectBean> reviewsCollectLists = this.reviewsCollectMapper.getReviewsSummaryData1(reviewsDbParameterBean);
                        int totalCount = 0, reviewsCount = 0;
                        double avgScore = 0D;
                        if (CollectionUtils.isNotEmpty(reviewsCollectLists)) {
                            ReviewsCollectBean reviewsCollect = reviewsCollectLists.get(0);
                            totalCount = reviewsCollect.getTotalCount();
                            reviewsCount = reviewsCollect.getReviewsCount();
                            double totalScore = reviewsCollect.getTotalScore();
                            if (reviewsCount > 0) {
                                avgScore = NumberToolkit.divide(totalScore, reviewsCount);
                            }
                        }
                        createdMonth = DateUtil.format(startDateTime, DateToolkit.YYYY_MM_ZH);
                        Resp5329580Bean.Reviews reviews = new Resp5329580Bean.Reviews();
                        reviews.setReviewsCount(reviewsCount);
                        // 前端暂时不改了、
                        reviews.setReviewsMonth(createdMonth);
                        reviews.setTotalCount(totalCount);
                        reviews.setAvgScore(NumberToolkit.format(avgScore,"#0.#", RoundingMode.HALF_UP));
                        reviews.setDataList(dataList);
                        reviewsList.add(reviews);
                    }
                }
            }
        }
        String nextMonth = null;
        Page page = new Page(new PageInfo<>(reviewsCollectList));
        if (page.isHasNextPage()) {
            nextMonth = month;
        } else {
            if (monthDateTime != null) {
                DateTime nextMonthDateTime = DateUtil.offsetMonth(monthDateTime, -1);
                if (DateUtil.compare(nextMonthDateTime, minDateTime) >= 0) {
                    nextMonth = DateUtil.format(nextMonthDateTime, DateToolkit.YYYY_MM);
                }
            }
        }
        Resp5329580Bean resp5329580Bean = new Resp5329580Bean();
        resp5329580Bean.setPage(page.getResult());
        resp5329580Bean.setReviewsList(reviewsList);
        resp5329580Bean.setNextMonth(nextMonth);
        return resp5329580Bean;
    }

    /**
     * 构建评价列表的数据分组
     * 根据创建主题月份进行分组
     * @param reviewsCollectList
     * @param subjectExtendMap
     * @return
     */
    private Map<String, List<Resp5329580Bean.Data>> buildReviewsListDataGroup(List<ReviewsCollectBean> reviewsCollectList,
                                                                              Map<String, Map<String, ?>> subjectExtendMap) {
        Map<String, List<Resp5329580Bean.Data>> dataGroupMap = new LinkedHashMap<>(16);
        for (ReviewsCollectBean reviewsCollect : reviewsCollectList) {
            // 评价主体创建时间
            String createdTime = reviewsCollect.getCreatedTime();
            String createdMonth;
            try {
                createdMonth = DateToolkit.formate(createdTime, DateToolkit.YYYY_MM);
            } catch (ParseException e) {
                continue;
            }

            String subjectId = reviewsCollect.getSubjectId();
            Map<String, ?> subjectExtend = subjectExtendMap.get(subjectId);
            // 评价数据
            Resp5329580Bean.Data data = new Resp5329580Bean.Data();
            data.setSubjectType(reviewsCollect.getSubjectType());
            data.setSubjectId(reviewsCollect.getSubjectId());
            data.setReviewerName(reviewsCollect.getReviewerName());
            data.setReviewerTime(reviewsCollect.getReviewsTime());
            data.setScore(reviewsCollect.getScore());
            data.setContent(reviewsCollect.getContent());
            // 评价主体扩展
            data.setSubjectExtend(subjectExtend);

            if (dataGroupMap.containsKey(createdMonth)) {
                dataGroupMap.get(createdMonth).add(data);
            } else {
                List<Resp5329580Bean.Data> dataList = new LinkedList<>();
                dataList.add(data);
                dataGroupMap.put(createdMonth, dataList);
            }
        }
        return dataGroupMap;
    }

    /**
     * 获取评价主体扩展
     * @param reviewsCollectList
     * @return
     */
    private Map<String, Map<String,?>> getReviewsSubjectExtendMap(List<ReviewsCollectBean> reviewsCollectList) {
        Map<String, Map<String, ?>> subjectExtendMap = new HashMap<>(reviewsCollectList.size());
        Map<String, List<ReviewsCollectBean>> listMap = reviewsCollectList.stream()
                .collect(Collectors.groupingBy(ReviewsCollectBean::getSubjectType));
        for (Map.Entry<String, List<ReviewsCollectBean>> next : listMap.entrySet()) {
            String subjectType = next.getKey();
            List<ReviewsCollectBean> reviewsCollectBeans = next.getValue();

            List<String> subjectIdList = reviewsCollectBeans.stream()
                    .map(ReviewsCollectBean::getSubjectId)
                    .collect(Collectors.toList());
            ReviewsSubjectExtendService subjectExtendService = ReviewsFactory.getSubjectExtendService(subjectType);
            subjectExtendMap.putAll(subjectExtendService.getSubjectExtend(subjectIdList));
        }
        return subjectExtendMap;
    }

    /**
     * 设置默认值
     * @param data
     */
    private void setCenterPersonalReviewsListDefaultValue(Req5329580Bean data) {
        String month = data.getMonth();
        String reviewsTarget = data.getReviewsTarget();
        String reviewsTargetId = data.getReviewsTargetId();
        String score = data.getScore();
        if (StringUtils.isNotBlank(month)) {
            try {
                DateUtil.parse(month, DateToolkit.YYYY_MM);
            } catch (Exception e) {
                data.setMonth("-1");
            }
        }
        if (StringUtils.isNotBlank(reviewsTarget)) {
            if (!VerifyToolkit.isNonNegativeIntegers(reviewsTarget)) {
                data.setReviewsTarget("-1");
            }
        }
        if (StringUtils.isNotBlank(reviewsTargetId)) {
            if (!VerifyToolkit.isPositiveInteger(reviewsTargetId)) {
                data.setReviewsTargetId("-1");
            }
        }
        if (StringUtils.isNotBlank(score)) {
            if (!VerifyToolkit.isPositiveNumber(score)) {
                data.setScore("-1");
            }
        }
        if (StringUtils.isBlank(data.getSearchType())) {
            data.setSearchType(DefaultValueEnum.ZERO.getValue());
        }
        month = data.getMonth();
        reviewsTarget = data.getReviewsTarget();
        reviewsTargetId = data.getReviewsTargetId();
        score = data.getScore();
        if (!StringUtils.equalsAny("-1", reviewsTarget, reviewsTargetId, month, score)) {
            ReviewsDbParameterBean reviewsDbParameterBean = new ReviewsDbParameterBean();
            reviewsDbParameterBean.setOrgId(data.getOrgId());
            reviewsDbParameterBean.setReviewsTarget(reviewsTarget);
            reviewsDbParameterBean.setReviewsTargetId(reviewsTargetId);
            reviewsDbParameterBean.setScore(score);
            reviewsDbParameterBean.setSearchType(data.getSearchType());
            ReviewsCollectBean reviewsCollect = this.reviewsCollectMapper.getReviewsLimitByReviewsTarget(reviewsDbParameterBean);
            if (reviewsCollect != null) {
                String minTime = reviewsCollect.getMinTime();
                String maxTime = reviewsCollect.getMaxTime();
                if (StringUtils.isNotBlank(minTime)) {
                    DateTime minDateTime = DateUtil.beginOfMonth(DateUtil.parseDate(minTime));
                    data.setMinDateTime(minDateTime);
                }
                if (StringUtils.isNotBlank(maxTime)) {
                    DateTime maxDateTime = DateUtil.endOfMonth(DateUtil.parseDate(maxTime));
                    data.setMaxDateTime(maxDateTime);
                }
            }
        }
        if (StringUtils.isBlank(month)) {
            DateTime maxDateTime = data.getMaxDateTime();
            if (maxDateTime != null) {
                month = DateUtil.format(maxDateTime, DateToolkit.YYYY_MM);
                data.setMonth(month);
            } else {
                data.setMonth("-1");
            }
        }
    }

    /**
     * 5330279-绩效管理-用户评价数据汇总
     * @param data
     * @return
     */
    @Override
    public Resp5330279Bean userReviewsCollect(Req5330279Bean data) {
        String reviewerIdentity = data.getReviewerIdentity();
        String reviewerId = data.getReviewerId();
        int unfinishedCount = 0, finishedCount = 0, historyFinishedCount = 0;
        if (VerifyToolkit.isNonNegativeIntegers(reviewerIdentity) && VerifyToolkit.isPositiveNumber(reviewerId)) {
            // 未评价总数
            unfinishedCount = this.reviewsCollectMapper.getUnfinishedReviewsCount(reviewerIdentity, reviewerId);
            // 已评价总数
            finishedCount = this.reviewsCollectMapper.getFinishedReviewsCount(reviewerIdentity, reviewerId);
            // 历史已评价总数
            historyFinishedCount = this.reviewsCollectMapper.getHistoryFinishedReviewsCount(reviewerIdentity, reviewerId);
        }
        Resp5330279Bean resp5330279Bean = new Resp5330279Bean();
        resp5330279Bean.setUnfinishedCount(unfinishedCount);
        resp5330279Bean.setFinishedCount(finishedCount);
        resp5330279Bean.setHistoryFinishedCount(historyFinishedCount);
        return resp5330279Bean;
    }

    @Autowired
    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Autowired
    public void setRpcGwayService(RpcGwayService rpcGwayService) {
        this.rpcGwayService = rpcGwayService;
    }

    @Autowired
    public void setRpcSqService(RpcSqService rpcSqService) {
        this.rpcSqService = rpcSqService;
    }

    @Autowired
    public void setReviewsSubjectMapper(ReviewsSubjectMapper reviewsSubjectMapper) {
        this.reviewsSubjectMapper = reviewsSubjectMapper;
    }

    @Autowired
    public void setReviewsScopeMapper(ReviewsScopeMapper reviewsScopeMapper) {
        this.reviewsScopeMapper = reviewsScopeMapper;
    }

    @Autowired
    public void setReviewsMapper(ReviewsMapper reviewsMapper) {
        this.reviewsMapper = reviewsMapper;
    }

    @Autowired
    public void setReviewsCollectMapper(ReviewsCollectMapper reviewsCollectMapper) {
        this.reviewsCollectMapper = reviewsCollectMapper;
    }
}
