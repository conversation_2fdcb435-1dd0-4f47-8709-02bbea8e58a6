<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.UserColumnFieldMapper">
    
    
    <delete id="delUserTemplateColumnField">
         delete from T_DL_USER_COLUMN_FIELD t where exists
         (select c.DL_USER_COLUMN_ID
              from T_DL_USER_COLUMN c where t.DL_USER_COLUMN_ID = c.DL_USER_COLUMN_ID and c.UNIQUE_KEY = #{uniqueKey}
             and c.PAGE = #{page} and c.BUSINESS_TYPE = #{businessType}
          )
    </delete>
    
    <insert id="insert">
        insert into T_DL_USER_COLUMN_FIELD(DL_USER_COLUMN_FIELD_ID,DL_USER_COLUMN_ID,DL_COLUMN_FIELD_ID,SORT_NO)
        values (#{dlUserColumnFieldId},#{dlUserColumnId},#{dlColumnFieldId},#{sortNo})
    </insert>


</mapper>