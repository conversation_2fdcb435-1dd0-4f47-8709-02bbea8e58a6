<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskUsersMapper">
    <insert id="save" parameterType="com.wtyt.dao.bean.syf.BoTransTaskUsersBean">
        INSERT INTO T_BO_TRANS_TASK_USERS(BO_TRANS_TASK_USERS_ID,BO_TRANS_TASK_ID, USER_ID,SYS_ROLE_TYPE)
        VALUES(${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()}, #{boTransTaskId}, #{userId},#{sysRoleType})
    </insert>

    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO T_BO_TRANS_TASK_USERS(BO_TRANS_TASK_USERS_ID,BO_TRANS_TASK_ID, USER_ID,SYS_ROLE_TYPE,JOB_NAME)
    <foreach item="item" index="index" collection="list" separator="UNION ALL">
        SELECT
            #{item.boTransTaskUsersId} boTransTaskUsersId,
            #{item.boTransTaskId} boTransTaskId,
            #{item.userId} userId,
            #{item.sysRoleType} sysRoleType,
            #{item.jobName} jobName
        FROM
            DUAL
    </foreach>
    </insert>
    <update id="deleteTaskUserByTaskId">
        UPDATE T_BO_TRANS_TASK_USERS SET IS_DEL =1,LAST_MODIFIED_TIME =SYSDATE WHERE BO_TRANS_TASK_ID =#{taskId} AND IS_DEL =0
    </update>

    <select id="queryTaskUsersByBoTransTaskIdOrTaxWaybillId" resultType="com.wtyt.dao.bean.syf.BoTransTaskUsersBean">
        SELECT B.USER_ID userId,B.SYS_ROLE_TYPE sysRoleType FROM T_BO_TRANS_TASK A JOIN T_BO_TRANS_TASK_USERS B ON A.BO_TRANS_TASK_ID =B.BO_TRANS_TASK_ID AND B.IS_DEL =0
        WHERE A.IS_DEL =0
        <choose>
            <when test="boTransTaskId != null and boTransTaskId !=''">
                AND A.BO_TRANS_TASK_ID = #{boTransTaskId}
            </when>
            <when test="taxWaybillId != null and taxWaybillId !=''">
                AND A.TAX_WAYBILL_ID =#{taxWaybillId}
            </when>
            <otherwise>
                AND 1 = 0
            </otherwise>
        </choose>
    </select>
    <insert id="saveOrUpdate" parameterType="com.wtyt.dao.bean.syf.BoTransTaskUsersBean">
        MERGE INTO T_BO_TRANS_TASK_USERS A
        USING (
            <foreach collection="list" index="index" item="item" open=""
                     close="" separator="union">
                SELECT #{item.boTransTaskUsersId} BO_TRANS_TASK_USERS_ID,#{item.boTransTaskId} BO_TRANS_TASK_ID,#{item.userId} USER_ID,#{item.sysRoleType} SYS_ROLE_TYPE FROM DUAL
            </foreach>
        ) B
        ON (A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID AND A.USER_ID = B.USER_ID)
        WHEN MATCHED THEN
        UPDATE SET
            A.SYS_ROLE_TYPE = B.SYS_ROLE_TYPE,
            A.IS_DEL = 0,
            A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
            INSERT (BO_TRANS_TASK_USERS_ID,
            BO_TRANS_TASK_ID,
            USER_ID,
            SYS_ROLE_TYPE)
            VALUES
            (B.BO_TRANS_TASK_USERS_ID,
            B.BO_TRANS_TASK_ID,
            B.USER_ID ,
            B.SYS_ROLE_TYPE)
    </insert>

    <update id="batchTransferTaskUsers" parameterType="java.util.List">
        <foreach collection="boTaskDetailBeans" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_TRANS_TASK_USERS T
            <set>
                T.LAST_MODIFIED_TIME = SYSDATE,
                <if test="item.toUserId != null and item.toUserId != ''">
                    T.USER_ID = #{item.toUserId},
                    <if test="item.createdUserJobName != null and item.createdUserJobName != ''">
                        T.JOB_NAME = #{item.createdUserJobName},
                    </if>
                    <if test="item.createdUserSysRoleType != null and item.createdUserSysRoleType != ''">
                        T.SYS_ROLE_TYPE = #{item.createdUserSysRoleType},
                    </if>
                </if>
            </set>
            WHERE T.BO_TRANS_TASK_ID = #{item.boTransTaskId} AND T.USER_ID = #{item.createdUserId} AND T.USER_ID IS NOT NULL AND T.IS_DEL = 0
        </foreach>
    </update>

</mapper>
