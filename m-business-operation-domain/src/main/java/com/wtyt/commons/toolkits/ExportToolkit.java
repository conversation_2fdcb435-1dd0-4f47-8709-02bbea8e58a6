package com.wtyt.commons.toolkits;

import com.thoughtworks.xstream.InitializationException;
import com.wtyt.common.beans.config.ExportConfig;
import com.wtyt.common.constants.RedisKeyConstants;
import com.wtyt.common.enums.DefaultValueEnum;
import com.wtyt.common.enums.StepEnum;
import com.wtyt.common.toolkits.*;
import com.wtyt.commons.bean.ReqExportBean;
import com.wtyt.commons.bean.UploadResult;
import com.wtyt.commons.context.ExportContext;
import com.wtyt.dao.bean.syf.BoTaskExportRecordBean;
import com.wtyt.dao.mapper.syf.BoTaskExportRecordMapper;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RList;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月29日 09时36分
 */
@Slf4j
public class ExportToolkit {

    private ExportToolkit() {
        throw new InitializationException("ExportToolkit Cannot initialize");
    }

    /**
     * 附件导出类型
     */
    public static final List<String> ATTACHMENT_EXPORT_TYPE_LIST = Arrays.asList(
            DefaultValueEnum.THREE.getValue(),
            DefaultValueEnum.FOUR.getValue(),
            DefaultValueEnum.FIVE.getValue()
    );

    /**
     * 验证是否允许导出
     * @param data
     */
    public static void verifyAllowExport(ReqExportBean data) {
        String userLockKey = RedisKeyToolkit.getLockKey(RedisKeyConstants.GLOBAL_EXPORT, "user:" + data.getUserId());
        RedissonClient redissonClient = SpringContextToolkit.getBean(RedissonClient.class);
        RLock userLock = redissonClient.getLock(userLockKey);
        if (userLock.isLocked()) {
            throw new UnifiedBusinessException("当前有导出/下载任务正在处理中、请耐心等待");
        }
        ExportConfig exportConfig = ConfigToolkit.getExportConfig();
        String underwayCacheKey = RedisKeyToolkit.getCacheKey(RedisKeyConstants.GLOBAL_EXPORT, "underway");
        RList<String> underwayList = redissonClient.getList(underwayCacheKey);
        if (underwayList.size() >= exportConfig.getUnderwayLimit()) {
            for (String key : underwayList) {
                RLock otherLock = redissonClient.getLock(key);
                if (!otherLock.isLocked()) {
                    underwayList.remove(key);
                }
            }
            if (underwayList.size() >= exportConfig.getUnderwayLimit()) {
                throw new UnifiedBusinessException("当前服务器导出/下载任务繁忙、请稍后重试");
            }
        }
        underwayList.add(userLockKey);
        userLock.lock();
        String exportType = data.getExportType();
        if (ATTACHMENT_EXPORT_TYPE_LIST.contains(exportType)) {
            exportConfig.setDirectory("--");
            exportConfig.setFileSuffix("zip");
        } else {
            exportConfig.setFileSuffix("xlsx");
        }
        data.setExportConfig(exportConfig);
    }


    /**
     * 生成导出编码
     * @param data
     * @return
     */
    public static void createExportCode(ReqExportBean data) {
        data.setExportCode(UidToolkit.generateUidString());
    }

    /**
     * 写入导出记录
     * @param context
     * @param stepEnum
     * @param extendParams
     */
    public static void writerExportRecord(ExportContext context, StepEnum stepEnum, Object ... extendParams) {
        ReqExportBean data = context.getData();
        if (log.isDebugEnabled()) {
            log.debug("[{}]开始写入导出记录", data.getExportCode());
        }
        ExportConfig exportConfig = data.getExportConfig();
        BoTaskExportRecordBean exportRecord = new BoTaskExportRecordBean();
        exportRecord.setBoTaskExportRecordId(data.getExportCode());

        BoTaskExportRecordMapper boTaskExportRecordMapper = SpringContextToolkit.getBean(BoTaskExportRecordMapper.class);
        if (StepEnum.STEP_ERROR.equals(stepEnum)) {
            String failureReason = CommonToolkit.subStringByByte((String)extendParams[0], 512);
            exportRecord.setFailureReason(failureReason);
            boTaskExportRecordMapper.updateTaskExportRecord(exportRecord);
        } else if (StepEnum.STEP_1.equals(stepEnum)) {
            String orgId = data.getOrgId();
            orgId = StringUtils.defaultIfBlank(orgId, DefaultValueEnum.MINUS_ONE.getValue());
            String fileName;
            if (StringUtils.isNotBlank(data.getExportFileName())) {
                fileName = data.getExportFileName();
            } else {
                fileName = data.getExportCode() + "." + exportConfig.getFileSuffix();
            }
            exportRecord.setExportType(data.getExportType());
            exportRecord.setOrgId(orgId);
            exportRecord.setUserId(data.getUserId());
            exportRecord.setStartTime(DateToolkit.getCurDateTime());
            exportRecord.setFileName(fileName);
            exportRecord.setFileSuffix(exportConfig.getFileSuffix());
            exportRecord.setFileDirectory(exportConfig.getDirectory());
            exportRecord.setFileStatus(DefaultValueEnum.ONE.getValue());
            boTaskExportRecordMapper.insertTaskExportRecord(exportRecord);
        } else if (StepEnum.STEP_2.equals(stepEnum)) {
            exportRecord.setTotalRows(String.valueOf(data.getTotalRows()));
            exportRecord.setFileStatus(DefaultValueEnum.TWO.getValue());
            boTaskExportRecordMapper.updateTaskExportRecord(exportRecord);
        } else if (StepEnum.STEP_3.equals(stepEnum)) {
            UploadResult uploadResult = (UploadResult)extendParams[0];
            exportRecord.setTotalRows(String.valueOf(data.getTotalRows()));
            exportRecord.setFileStatus(DefaultValueEnum.THREE.getValue());
            exportRecord.setEndTime(DateToolkit.getCurDateTime());
            exportRecord.setExpiresTime(uploadResult.getExpiresTime());
            exportRecord.setFileUrl(uploadResult.getUrl());
            boTaskExportRecordMapper.updateTaskExportRecord(exportRecord);
        }
    }

    /**
     * 复制写入导出记录
     * @param exportRecord
     * @param exportCode
     */
    public static void copyWriterExportRecord(BoTaskExportRecordBean exportRecord, String exportCode) {
        if (exportRecord != null && StringUtils.isNotBlank(exportCode)) {
            BoTaskExportRecordMapper boTaskExportRecordMapper = SpringContextToolkit.getBean(BoTaskExportRecordMapper.class);
            exportRecord.setBoTaskExportRecordId(exportCode);
            exportRecord.setStartTime(DateToolkit.getCurDateTime());
            exportRecord.setEndTime(exportRecord.getStartTime());
            boTaskExportRecordMapper.insertTaskExportRecord(exportRecord);
        }
    }

    /**
     * 解锁
     * @param context
     */
    public static void unlock(ExportContext context) {
        // 解锁
        unlock(context.getData());
    }

    /**
     * 解锁
     * @param data
     */
    public static void unlock(ReqExportBean data) {
        // 解锁用户
        unlockUser(data);
        // 解锁进行中的数量
        unlockUnderway(data);
    }


    /**
     * 解锁进行中的数量
     */
    public static void unlockUnderway(ReqExportBean data) {
        String underwayCacheKey = RedisKeyToolkit.getCacheKey(RedisKeyConstants.GLOBAL_EXPORT, "underway");
        RedissonClient redissonClient = SpringContextToolkit.getBean(RedissonClient.class);
        RList<String> underwayList = redissonClient.getList(underwayCacheKey);
        if (underwayList.isExists()) {
            if (log.isDebugEnabled()) {
                log.debug("[{}]开始更新进行中的数量、当前还存在{}个进行中任务", data.getExportCode(), underwayList.size());
            }
            String userLockKey = RedisKeyToolkit.getLockKey(RedisKeyConstants.GLOBAL_EXPORT, "user:" + data.getUserId());
            underwayList.remove(userLockKey);
        } else {
            if (log.isDebugEnabled()) {
                log.debug("[{}]开始更新进行中的数量、当前无进行中任务", data.getExportCode());
            }
        }
    }

    /**
     * 解锁用户
     * @param data
     */
    public static void unlockUser(ReqExportBean data) {
        if (log.isDebugEnabled()) {
            log.debug("[{}]开始对用户进行强制解锁", data.getExportCode());
        }
        String userLockKey = RedisKeyToolkit.getLockKey(RedisKeyConstants.GLOBAL_EXPORT, "user:" + data.getUserId());
        RedissonClient redissonClient = SpringContextToolkit.getBean(RedissonClient.class);
        RLock userLock = redissonClient.getLock(userLockKey);
        userLock.forceUnlock();
    }
}
