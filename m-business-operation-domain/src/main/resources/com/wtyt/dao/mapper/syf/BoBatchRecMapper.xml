<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoBatchRecMapper">

    <resultMap id="BaseResultMap" type="com.wtyt.dao.bean.syf.BoBatchRecBean">
            <id property="boBatchRecId" column="BO_BATCH_REC_ID" jdbcType="DECIMAL"/>
            <result property="batchNo" column="BATCH_NO" jdbcType="VARCHAR"/>
            <result property="orgId" column="ORG_ID" jdbcType="DECIMAL" />
            <result property="userId" column="USER_ID" jdbcType="DECIMAL"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="batchType" column="BATCH_TYPE" jdbcType="DECIMAL"/>
            <result property="resultInfo" column="RESULT_INFO" jdbcType="VARCHAR"/>
            <result property="successNum" column="SUCCESS_NUM" jdbcType="DECIMAL"/>
            <result property="failNum" column="FAIL_NUM" jdbcType="DECIMAL"/>
            <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        BO_BATCH_REC_ID boBatchRecId,BATCH_NO batchNo,ORG_ID orgId,
        USER_ID userId,STATUS status,BATCH_TYPE batchType,
        RESULT_INFO resultInfo,SUCCESS_NUM successNum,FAIL_NUM failNum,
        IS_DEL isDel,
        TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,TO_CHAR(LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI:SS') lastModifiedTime,
        NOTE
    </sql>


    <select id="getByBatchNo" resultType="com.wtyt.dao.bean.syf.BoBatchRecBean">
        select
        <include refid="Base_Column_List" />
        from T_BO_BATCH_REC
        where BATCH_NO = #{batchNo}
        and IS_DEL=0
    </select>


    <insert id="insert" keyColumn="BO_BATCH_REC_ID" keyProperty="boBatchRecId" parameterType="com.wtyt.dao.bean.syf.BoBatchRecBean" useGeneratedKeys="true">
        insert into T_BO_BATCH_REC
        ( BO_BATCH_REC_ID,BATCH_NO,ORG_ID
        ,USER_ID,USER_NAME,STATUS,BATCH_TYPE
        ,RESULT_INFO,SUCCESS_NUM,FAIL_NUM
        ,IS_DEL,CREATED_TIME,LAST_MODIFIED_TIME
        ,NOTE)
        values (${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},#{batchNo,jdbcType=VARCHAR},#{orgId,jdbcType=DECIMAL}
        ,#{userId,jdbcType=DECIMAL},#{userName},#{status,jdbcType=DECIMAL},#{batchType,jdbcType=DECIMAL}
        ,#{resultInfo,jdbcType=VARCHAR},#{successNum,jdbcType=DECIMAL},#{failNum,jdbcType=DECIMAL}
        ,'0',SYSDATE,SYSDATE
        ,#{note,jdbcType=VARCHAR})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wtyt.dao.bean.syf.BoBatchRecBean">
        update T_BO_BATCH_REC
        SET
                <if test="batchNo != null">
                    BATCH_NO = #{batchNo,jdbcType=VARCHAR},
                </if>
                <if test="orgId != null">
                    ORG_ID = #{orgId,jdbcType=DECIMAL},
                </if>
                <if test="userId != null">
                    USER_ID = #{userId,jdbcType=DECIMAL},
                </if>
                <if test="status != null">
                    STATUS = #{status,jdbcType=DECIMAL},
                </if>
                <if test="batchType != null">
                    BATCH_TYPE = #{batchType,jdbcType=DECIMAL},
                </if>
                <if test="resultInfo != null">
                    RESULT_INFO = #{resultInfo,jdbcType=VARCHAR},
                </if>
                <if test="successNum != null">
                    SUCCESS_NUM = #{successNum,jdbcType=DECIMAL},
                </if>
                <if test="failNum != null">
                    FAIL_NUM = #{failNum,jdbcType=DECIMAL},
                </if>
                <if test="isDel != null">
                    IS_DEL = #{isDel,jdbcType=DECIMAL},
                </if>
                <if test="createdTime != null">
                    CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="note != null">
                    NOTE = #{note,jdbcType=VARCHAR},
                </if>
        LAST_MODIFIED_TIME = SYSDATE
        where   BO_BATCH_REC_ID = #{boBatchRecId,jdbcType=DECIMAL}
    </update>

</mapper>
