<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskRelatedOrderMapper">

    <resultMap id="BaseResultMap" type="com.wtyt.dao.bean.syf.BoTaskRelatedOrderBean">
        <!--@Table T_BO_TASK_RELATED_ORDER-->
        <result property="boTaskRelatedOrderId" column="BO_TASK_RELATED_ORDER_ID" jdbcType="VARCHAR"/>
        <result property="boTransOrderId" column="BO_TRANS_ORDER_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="query" resultMap="BaseResultMap">
        select
            BO_TASK_RELATED_ORDER_ID, BO_TRANS_ORDER_ID, BO_TRANS_TASK_ID
        from T_BO_TASK_RELATED_ORDER
        where IS_DEL=0
        <if test="boTransTaskId != null">
            and BO_TRANS_TASK_ID = #{boTransTaskId}
        </if>
        <if test="boTransOrderId != null">
            and BO_TRANS_ORDER_ID = #{boTransOrderId}
        </if>
    </select>

    <insert id="save">
        insert into T_BO_TASK_RELATED_ORDER(BO_TASK_RELATED_ORDER_ID, BO_TRANS_ORDER_ID, BO_TRANS_TASK_ID)
        values(#{boTaskRelatedOrderId}, #{boTransOrderId}, #{boTransTaskId})
    </insert>

    <update id="delete">
        update T_BO_TASK_RELATED_ORDER
        set IS_DEL = 1,LAST_MODIFIED_TIME = SYSDATE
        where BO_TASK_RELATED_ORDER_ID = #{boTaskRelatedOrderId}
        and IS_DEL = 0
    </update>

    <select id="list" resultType="com.wtyt.tt.bean.Resp5329699Bean">
        SELECT
        ro.BO_TASK_RELATED_ORDER_ID boTaskRelatedOrderId,
        ro.BO_TRANS_TASK_ID boTransTaskId,
        ro.BO_TRANS_ORDER_ID boTransOrderId,
        o.CUSTOMER_ORDER_NO customerOrderNo,
        o.THIRD_ORDER_NO thirdOrderNo,
        o.RECEIPT_NO receiptNo,
        o.SERVICE_REQUIRE serviceRequire,
        o.CUSTOMER_NAME customerName,
        to_char(o.TRANS_DATE, 'YYYY-MM-DD') transDate,
        o.GOODS_NAME goodsName,
        NVL2(o.GOODS_CASE_PACK, TO_CHAR(GOODS_CASE_PACK, 'FM999999990.0099'), '0') goodsCasePack,
        o.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        NVL2(o.GOODS_WEIGHT, TO_CHAR(GOODS_WEIGHT, 'FM999999990.0099'), '0') goodsWeight,
        o.GOODS_WEIGHT_UNIT goodsWeightUnit,
        NVL2(o.GOODS_VOLUME, TO_CHAR(GOODS_VOLUME, 'FM999999990.0099'), '0') goodsVolume,
        o.GOODS_VOLUME_UNIT goodsVolumeUnit,
        o.CONSIGNEE_NAME consigneeName,
        o.CONSIGNEE_CONTACT consigneeContact,
        o.CONSIGNEE_MOBILE_NO consigneeMobileNo,
        o.CONSIGNEE_UNIT consigneeUnit,
        o.STORAGE_PLACE storagePlace,
        o.GOODS_SPEC goodsSpec,
        o.END_ADDRESS endAddress,
        o.RAW_END_ADDRESS rawEndAddress,
        o.DT_ELECTRONIC_RECEIPT_MARK dtElectronicReceiptMark
        FROM
        T_BO_TASK_RELATED_ORDER ro
        JOIN T_BO_TRANS_ORDER o ON
        ro.BO_TRANS_ORDER_ID = o.BO_TRANS_ORDER_ID
        WHERE
        ro.IS_DEL = 0
        AND o.IS_DEL = 0
        AND ro.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND (o.ORG_ID =#{orgId}
        <if test="groupId != null and groupId != ''">
        OR EXISTS(SELECT 1 FROM T_BO_ORDER_GROUP_REL gr WHERE gr.BO_TRANS_ORDER_ID =o.BO_TRANS_ORDER_ID AND gr.IS_DEL =0 AND gr.GROUP_ID =#{groupId})
        </if>
        )
        ORDER BY ro.CREATED_TIME DESC
    </select>

</mapper>

