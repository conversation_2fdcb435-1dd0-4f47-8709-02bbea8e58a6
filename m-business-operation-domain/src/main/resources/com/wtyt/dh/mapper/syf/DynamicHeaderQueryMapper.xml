<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dh.mapper.syf.DynamicHeaderQueryMapper">

    <resultMap id="dhFieldInfo" type="com.wtyt.bean.dto.DhFieldDTO">
        <id property="boTpDhHeaderImportId" column="BO_TP_DH_HEADER_IMPORT_ID"/>
        <result property="headerName" column="HEADER_NAME"/>
        <result property="isRequired" column="IS_REQUIRED"/>
        <result property="showType" column="SHOW_TYPE"/>
        <result property="isInput" column="IS_INPUT"/>
        <result property="mergeShowId" column="MERGE_SHOW_ID"/>
        <result property="headerType" column="HEADER_TYPE"/>
        <result property="shippingShowType" column="SHIPPING_SHOW_TYPE"/>
        <result property="canModify" column="CAN_MODIFY"/>
        <result property="columnName" column="DATA_COLUMN_NAME"/>
        <result property="splitChar" column="SPLIT_CHAR"/>
        <result property="mergePrefix" column="MERGE_PREFIX"/>
        <result property="mergeSuffix" column="MERGE_SUFFIX"/>
        <result property="sortNum" column="SORT_NUM"/>
    </resultMap>

    <resultMap id="dhColumnDTO" type="com.wtyt.bean.dto.DhColumnDTO">
        <id property="boTpDhHeaderColumnId" column="BO_TP_DH_HEADER_COLUMN_ID"/>
        <result property="fieldName" column="FIELD_NAME"/>
        <result property="boTpDhHeaderImportId" column="BO_TP_DH_HEADER_IMPORT_ID"/>
        <result property="fieldNameDesc" column="FIELD_NAME_DESC"/>
        <result property="fieldType" column="FIELD_TYPE"/>
        <result property="inputType" column="INPUT_TYPE"/>
        <result property="maxLength" column="MAX_LENGTH"/>
        <result property="numberPrecision" column="NUMBER_PRECISION"/>
        <result property="boTpDhTaskFieldDictId" column="BO_TP_DH_TASK_FIELD_DICT_ID"/>
    </resultMap>

    <sql id="columnField">
        BO_TP_DH_HEADER_COLUMN_ID boTpDhHeaderColumnId,
        BO_TP_DH_TASK_FIELD_DICT_ID boTpDhTaskFieldDictId,
        BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
        DEFAULT_VALUE defaultValue,
        SEARCH_TYPE searchType,
        IS_PRECISE isPrecise,
        BO_TP_DH_TASK_FIELD_RULE_ID boTpDhTaskFieldRuleId,
        UNIT unit
    </sql>

    <select id="queryOrgDhTaskFieldList" resultType="com.wtyt.dh.bean.DhTaskFieldDictBean">
        SELECT
            d.BO_TP_DH_TASK_FIELD_DICT_ID boTpDhTaskFieldDictId,
            d.FIELD_NAME fieldName,
            d.FIELD_NAME_DESC fieldNameDesc,
            d.FIELD_TYPE fieldType,
            d.MAX_LENGTH maxLength,
            d.INPUT_TYPE inputType,
            d.NUMBER_PRECISION numberPrecision,
            d.UNIT_CONFIG unitConfig,
            d.ALLOW_NOT_REQUIRED allowNotRequired,
            d.ALLOW_DEFAULT allowDefault
        FROM
            T_BO_TP_DH_TASK_FIELD_DICT d
        WHERE
            d.is_del = 0 AND d.ORG_ID = #{orgId}
    </select>

    <select id="queryOrgDhTaskFieldByName" resultType="com.wtyt.dh.bean.DhTaskFieldDictBean" parameterType="com.wtyt.dh.bean.DhOrgTaskFieldBean">
        SELECT
            d.BO_TP_DH_TASK_FIELD_DICT_ID boTpDhTaskFieldDictId,
            d.FIELD_NAME fieldName,
            d.FIELD_NAME_DESC fieldNameDesc,
            d.FIELD_TYPE fieldType,
            d.MAX_LENGTH maxLength,
            d.INPUT_TYPE inputType,
            d.NUMBER_PRECISION numberPrecision,
            d.UNIT_CONFIG unitConfig,
            d.ALLOW_NOT_REQUIRED allowNotRequired,
            d.ALLOW_DEFAULT allowDefault,
            d.ORG_ID orgId
        FROM
            T_BO_TP_DH_TASK_FIELD_DICT d
        WHERE
            d.is_del = 0  AND d.ORG_ID = #{orgId}  and  d.FIELD_NAME  in
        <foreach collection="ruleResolverList" item="item" close=")" open="(" separator=",">
            #{item.fieldName}
        </foreach>

    </select>


  <select id="queryOrgDhTaskFieldRule" resultType="com.wtyt.dh.bean.DhTaskFieldRuleGroupBean">
      select
         BO_TP_DH_RULE_GROUP_ID boTpDhRuleGroupId,
         BO_TP_DH_TASK_FIELD_DICT_ID boTpDhTaskFieldDictId
      from ( SELECT
      r.ORG_ID ,
      r.BO_TP_DH_RULE_GROUP_ID ,
      r.BO_TP_DH_TASK_FIELD_DICT_ID ,
      ROW_NUMBER() OVER(PARTITION BY R.BO_TP_DH_TASK_FIELD_DICT_ID ORDER BY R.ORG_ID DESC) RN
      FROM
      T_BO_TP_DH_TASK_FIELD_RULE r
      WHERE r.IS_DEL = 0
      AND (r.ORG_ID = #{orgId} or r.ORG_ID =-1)
      and r.BO_TP_DH_TASK_FIELD_DICT_ID in
      <foreach collection="list" item="item" close=")" open="(" separator=",">
          #{item}
      </foreach>
      ) where RN =1
    </select>



    <resultMap id="ruleGroupMap" type="com.wtyt.dh.bean.DhRuleGroupBean">
        <id column="BO_TP_DH_RULE_GROUP_ID" property="boTpDhRuleGroupId" jdbcType="DECIMAL" />
        <result column="RULE_NAME" property="ruleGroupName" jdbcType="VARCHAR" />
        <result column="RULE_DESC" property="ruleGroupDesc" jdbcType="VARCHAR" />
        <result column="COMPOSITE_TYPE" property="compositeType" jdbcType="DECIMAL" />
        <collection property="dhRuleBeanList" ofType="com.wtyt.dh.bean.DhRuleBean">
            <result column="PRIORITY" property="priority" jdbcType="DECIMAL"/>
            <result column="RULE_NAME" property="ruleName" jdbcType="VARCHAR"/>
            <result column="RULE_DESC" property="ruleDesc" jdbcType="VARCHAR"/>
            <result column="RULE_CONDITION" property="ruleCondition" jdbcType="VARCHAR"/>
            <result column="RULE_ACTION" property="ruleAction" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="queryRuleGroupById" resultMap="ruleGroupMap">
        SELECT
            RG.BO_TP_DH_RULE_GROUP_ID ,
            RG.RULE_NAME ,
            RG.RULE_DESC ,
            RG.COMPOSITE_TYPE ,
            R.PRIORITY ,
            DR.RULE_NAME ,
            DR.RULE_DESC ,
            DR.RULE_CONDITION ,
            DR.RULE_ACTION
        FROM
            T_BO_TP_DH_RULE_GROUP RG LEFT JOIN T_BO_TP_DH_RULE_GROUP_REL R ON RG.BO_TP_DH_RULE_GROUP_ID = R.BO_TP_DH_RULE_GROUP_ID
            LEFT JOIN T_BO_TP_DH_RULE DR ON DR.BO_TP_DH_RULE_ID =R.BO_TP_DH_RULE_ID
        WHERE
            RG.is_del = 0  AND  R.is_del =0 and DR.is_del =0 and RG.BO_TP_DH_RULE_GROUP_ID = #{boTpDhRuleGroupId}
            ORDER BY R.PRIORITY ASC
    </select>

    <select id="querySearchItemListByConfigId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderColumnBean">
        SELECT
        <include refid="columnField"/>
        FROM T_BO_TP_DH_HEADER_COLUMN T
        WHERE T.IS_DEL = 0
        AND T.SEARCH_TYPE > 0
        AND EXISTS(SELECT 1 FROM T_BO_TP_DH_HEADER_IMPORT R WHERE IS_DEL = 0
            AND BO_TP_DH_CONFIG_ID = #{boTpDhConfigId} AND R.BO_TP_DH_HEADER_IMPORT_ID = T.BO_TP_DH_HEADER_IMPORT_ID
        )
    </select>
    <select id="queryDhInputFieldListByConfigId" resultType="com.wtyt.dh.bean.DhInputFieldBean">
        SELECT
            a.BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
            a.HEADER_NAME headerName,
            a.IS_REQUIRED isRequired,
            NVL(b.DEFAULT_VALUE, a.DEFAULT_VALUE) defaultValue,
            a.OPTION_INFO optionInfo,
            a.MERGE_SHOW_ID mergeShowId,
            b.UNIT ,
            c.BO_TP_DH_TASK_FIELD_DICT_ID boTpDhTaskFieldDictId,
            c.FIELD_NAME fieldName,
            c.FIELD_NAME_DESC fieldNameDesc,
            NVL(c.FIELD_TYPE, 0) fieldType,
            NVL(c.MAX_LENGTH, 256) maxLength,
            NVL(c.INPUT_TYPE, 0) inputType,
            c.NUMBER_PRECISION numberPrecision,
            c.UNIT_CONFIG unitConfig,
            a.IS_UNIQUE isUnique
        FROM
            T_BO_TP_DH_HEADER_IMPORT a
        LEFT JOIN T_BO_TP_DH_HEADER_COLUMN b ON
            a.BO_TP_DH_HEADER_IMPORT_ID = b.BO_TP_DH_HEADER_IMPORT_ID
            AND b.IS_DEL = 0
        LEFT JOIN T_BO_TP_DH_TASK_FIELD_DICT c ON
            b.BO_TP_DH_TASK_FIELD_DICT_ID = c.BO_TP_DH_TASK_FIELD_DICT_ID
            AND c.IS_DEL = 0
        WHERE
            a.IS_DEL =0
            AND a.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
            <if test="isInput != null"> AND ( a.IS_INPUT = #{isInput} OR c.FIELD_NAME='CART_TYPE' OR c.FIELD_NAME='CART_LENGTH' OR c.FIELD_NAME='SERVICE_REQUIRE')</if>
            AND HEADER_TYPE = 1 ORDER BY a.SORT_NUM ASC, b.BO_TP_DH_HEADER_COLUMN_ID ASC
    </select>

    <select id="queryDhInputFieldsByConfigId" resultType="com.wtyt.dh.bean.DhInputFieldBean">
        SELECT
            a.BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
            a.HEADER_NAME headerName,
            a.IS_REQUIRED isRequired,
            NVL(b.DEFAULT_VALUE, a.DEFAULT_VALUE) defaultValue,
            CASE WHEN a.IS_INPUT = 2 THEN b.OPTION_VALUE ELSE a.OPTION_INFO END optionInfo,
            a.MERGE_SHOW_ID mergeShowId,
            b.UNIT ,
            b.BO_TP_DH_HEADER_COLUMN_ID boTpDhHeaderColumnId,
            c.BO_TP_DH_TASK_FIELD_DICT_ID boTpDhTaskFieldDictId,
            c.FIELD_NAME fieldName,
            c.FIELD_NAME_DESC fieldNameDesc,
            NVL(c.FIELD_TYPE, 0) fieldType,
            NVL(c.MAX_LENGTH, 256) maxLength,
            NVL(c.INPUT_TYPE, 0) inputType,
            c.NUMBER_PRECISION numberPrecision,
            c.UNIT_CONFIG unitConfig,
            a.IS_UNIQUE isUnique,
            a.IS_INPUT isInput,
            CASE WHEN a.IS_INPUT = 2 THEN b.CREATE_SORT_NUM ELSE a.CREATE_SORT_NUM END createSortNum,
            CASE WHEN a.IS_INPUT = 2 THEN b.IS_CREATE_REQUIRE ELSE a.IS_CREATE_REQUIRE END isCreateRequire,
            CASE WHEN a.IS_INPUT = 2 THEN b.INPUT_TIPS ELSE a.INPUT_TIPS END inputTips,
            CASE WHEN a.IS_INPUT = 2 THEN b.CAN_MODIFY ELSE a.CAN_MODIFY END canModify,
            b.IS_LIST_SHOW_UNIT isListShowUnit,
            b.CREATE_UNIT createUnit,
            b.CREATE_UNIT_OPTION_VALUE createUnitOptionValue,
            b.OPTION_VALUE optionValue

        FROM
            T_BO_TP_DH_HEADER_IMPORT a
            LEFT JOIN T_BO_TP_DH_HEADER_COLUMN b ON a.BO_TP_DH_HEADER_IMPORT_ID = b.BO_TP_DH_HEADER_IMPORT_ID AND b.IS_DEL = 0
            LEFT JOIN T_BO_TP_DH_TASK_FIELD_DICT c ON b.BO_TP_DH_TASK_FIELD_DICT_ID = c.BO_TP_DH_TASK_FIELD_DICT_ID AND c.IS_DEL = 0
        WHERE
            a.IS_DEL =0
            AND a.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
            <if test="isInput">
             AND ( a.IS_INPUT in (1, 2) OR c.FIELD_NAME='CART_TYPE' OR c.FIELD_NAME='CART_LENGTH' OR c.FIELD_NAME='SERVICE_REQUIRE')
            </if>
            AND HEADER_TYPE = 1 ORDER BY a.SORT_NUM ASC, b.BO_TP_DH_HEADER_COLUMN_ID ASC
    </select>

    <select id="queryOtherShowHeaderByConfigId" resultType="com.wtyt.dao.bean.syf.DhHeaderDataCfgBean">
        SELECT T.BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
               T.HEADER_NAME      headerName,
               C.DATA_COLUMN_NAME dataColumnName,
               T.HEADER_TYPE  headerType,
               T.SORT_NUM sortNum
        FROM T_BO_TP_DH_HEADER_IMPORT T
                 LEFT join T_BO_TP_DH_HEADER_DATA_CFG c ON T.HEADER_NAME = C.HEADER_NAME  AND C.IS_DEL =0 AND C.ORG_ID = #{orgId}
        WHERE T.IS_DEL = 0
          AND T.SHOW_TYPE = 2
          AND T.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
          order BY T.SORT_NUM  ASC
    </select>
    <resultMap id="shippingListMap" type="com.wtyt.dao.bean.syf.DhHeaderDataCfgBean">
        <id column="BO_TP_DH_HEADER_IMPORT_ID" property="boTpDhHeaderImportId"></id>
        <result column="HEADER_NAME" property="headerName"></result>
        <result column="SORT_NUM" property="sortNum"></result>
        <result column="MERGE_SHOW_ID" property="mergeShowId"></result>
        <result column="MERGE_PREFIX" property="mergePrefix"></result>
        <result column="MERGE_SUFFIX" property="mergeSuffix"></result>
        <result column="DATA_COLUMN_NAME" property="dataColumnName"></result>
        <result column="HEADER_TYPE" property="headerType"></result>
        <collection property="orderColumnNameList" ofType="java.lang.String">
            <constructor>
                <arg column="FIELD_NAME"></arg>
            </constructor>
        </collection>
        <collection property="orderColumns" ofType="com.wtyt.dao.bean.syf.DhHeaderDataCfgBean$OrderColumn">
            <id property="boTpDhHeaderColumnId" column="BO_TP_DH_HEADER_COLUMN_ID"></id>
            <result property="fieldName" column="FIELD_NAME"></result>
            <result property="isListShowUnit" column="IS_LIST_SHOW_UNIT"></result>
        </collection>
    </resultMap>
    <select id="queryShippingListShowHeader"
            resultMap="shippingListMap">
        SELECT
            T.BO_TP_DH_HEADER_IMPORT_ID ,
            T.HEADER_NAME ,
            T.SORT_NUM,
            T.MERGE_SHOW_ID ,
            T.MERGE_PREFIX ,
            T.MERGE_SUFFIX ,
            dc.DATA_COLUMN_NAME ,
            T.HEADER_TYPE ,
            c.BO_TP_DH_HEADER_COLUMN_ID ,
            c.IS_LIST_SHOW_UNIT ,
            d.FIELD_NAME
        FROM
            T_BO_TP_DH_HEADER_IMPORT T
        LEFT JOIN T_BO_TP_DH_HEADER_COLUMN c ON
            T.BO_TP_DH_HEADER_IMPORT_ID = c.BO_TP_DH_HEADER_IMPORT_ID
            AND c.IS_DEL = 0
        LEFT JOIN T_BO_TP_DH_TASK_FIELD_DICT d ON
            c.BO_TP_DH_TASK_FIELD_DICT_ID = d.BO_TP_DH_TASK_FIELD_DICT_ID
            AND d.IS_DEL = 0
        LEFT JOIN T_BO_TP_DH_HEADER_DATA_CFG dc ON
            T.HEADER_NAME = dc.HEADER_NAME
            AND dc.IS_DEL = 0
            AND dc.ORG_ID = #{dhConfigBean.orgId}
        WHERE
            T.IS_DEL = 0
            <choose>
                <when test="list != null and list.size() > 0">
                    AND T.MERGE_SHOW_ID in
                    <foreach collection="list" item="item" close=")" open="(" separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND T.SHIPPING_SHOW_TYPE = #{shippingIsShow}
                </otherwise>
            </choose>
            AND T.BO_TP_DH_CONFIG_ID = #{dhConfigBean.boTpDhConfigId}
        ORDER BY
            T.SORT_NUM ASC
    </select>


    <select id="queryOtherShowHeaderByMergeShowId" resultType="com.wtyt.dao.bean.syf.DhHeaderDataCfgBean">
        SELECT
               T.MERGE_SHOW_ID  mergeShowId,
               T.HEADER_NAME      headerName,
               C.DATA_COLUMN_NAME dataColumnName,
               T.SORT_NUM  sortNum
        FROM T_BO_TP_DH_HEADER_IMPORT T
                 LEFT join T_BO_TP_DH_HEADER_DATA_CFG c ON T.HEADER_NAME = C.HEADER_NAME  AND C.IS_DEL =0  AND C.ORG_ID = #{dhConfigBean.orgId}
        WHERE T.IS_DEL = 0
          AND T.MERGE_SHOW_ID in
         <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
         </foreach>
          AND T.BO_TP_DH_CONFIG_ID = #{dhConfigBean.boTpDhConfigId}
        order BY T.SORT_NUM  ASC
    </select>

    <select id="queryDownloadHeaderByConfigId" resultType="com.wtyt.dao.bean.syf.DhHeaderDataCfgBean">
        SELECT T.BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
               T.HEADER_NAME      headerName,
               C.DATA_COLUMN_NAME dataColumnName
        FROM T_BO_TP_DH_HEADER_IMPORT T
                 LEFT join T_BO_TP_DH_HEADER_DATA_CFG c ON T.HEADER_NAME = C.HEADER_NAME
        WHERE T.IS_DEL = 0 AND C.IS_DEL =0
          AND T.IS_EXPORT = 1
          AND T.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId} AND C.ORG_ID = #{orgId}
        order BY T.SORT_NUM  ASC
    </select>

    <select id="queryHeaderDataMapper" resultType="com.wtyt.dh.bean.ImportDataBean$HeaderDataMapper">
        SELECT
            a.BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
            a.HEADER_NAME headerName,
            b.DATA_COLUMN_NAME dataColumnName,
            b.HEADER_EN_NAME headerEnName
        FROM
            T_BO_TP_DH_HEADER_IMPORT a
        LEFT JOIN T_BO_TP_DH_HEADER_DATA_CFG B ON
            a.HEADER_NAME = b.HEADER_NAME
            AND b.org_id = #{orgId}
        WHERE
            a.IS_DEL = 0
            AND a.BO_TP_DH_HEADER_IMPORT_ID IN
                <foreach collection="list" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            AND b.is_del = 0
    </select>


    <select id="querySearchHeaderDataListByConfigId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderDataCfgBean">
        SELECT
            DATA_COLUMN_NAME dataColumnName,
            HEADER_NAME headerName
        FROM T_BO_TP_DH_HEADER_DATA_CFG T
        WHERE T.IS_DEL = 0 AND ORG_ID = #{orgId}
        AND EXISTS(SELECT 1 FROM T_BO_TP_DH_HEADER_IMPORT R WHERE IS_DEL = 0
        AND BO_TP_DH_CONFIG_ID = #{boTpDhConfigId} AND R.HEADER_NAME = T.HEADER_NAME
        )
    </select>

    <select id="queryHasImportDataByOrgId" resultType="java.lang.Integer">
        SELECT DECODE(COUNT(1), 0, 0, 1)
        FROM T_BO_TP_IMPORT_DATA TBTID, T_BO_TRANS_ORDER TBTO
        WHERE  TBTID.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID
        AND TBTO.ORG_ID = #{orgId}
        <if test="boTpDhConfigId !=null and boTpDhConfigId !='' ">
            AND TBTID.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        </if>
        AND TBTID.IS_DEL = 0
        AND TBTO.IS_DEL = 0
    </select>

    <select id="queryHasImportDataIncludeDelByOrgId" resultType="java.lang.Integer">
        SELECT DECODE(COUNT(1), 0, 0, 1)
        FROM T_BO_TP_IMPORT_DATA TBTID, T_BO_TRANS_ORDER TBTO
        WHERE  TBTID.BO_TRANS_ORDER_ID = TBTO.BO_TRANS_ORDER_ID
        AND TBTO.ORG_ID = #{orgId}
        <if test="boTpDhConfigId !=null and boTpDhConfigId !='' ">
            AND TBTID.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        </if>
        AND TBTID.IS_DEL = 0
    </select>

    <update id="delDefaultImportDataByOrgId">
        update
            T_BO_TP_IMPORT_DATA TBTID
        set TBTID.IS_DEL=1,
             note='初始化订单数据，删除默认模版生成的订单数据,原订单id为'||TBTID.BO_TRANS_ORDER_ID,
             TBTID.BO_TRANS_ORDER_ID=TBTID.BO_TP_IMPORT_DATA_ID,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE TBTID.IS_DEL = 0
        <if test="boTpDhConfigId !=null and boTpDhConfigId !='' ">
            AND TBTID.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        </if>
          and exists (select 1
                      from T_BO_TRANS_ORDER TBTO
                      where  TBTO.ORG_ID = #{orgId}
                        and TBTO.BO_TRANS_ORDER_ID = TBTID.BO_TRANS_ORDER_ID)
    </update>

    <select id="getInitOrderIdList" resultType="String">
       SELECT o.BO_TRANS_ORDER_ID
        FROM  T_BO_TRANS_ORDER o LEFT JOIN  T_BO_TP_IMPORT_DATA d  ON  o.BO_TRANS_ORDER_ID =d.BO_TRANS_ORDER_ID  AND d.IS_DEL =0
        WHERE o.ORG_ID= #{orgId}
        AND d.BO_TRANS_ORDER_ID IS null
        ORDER BY o.CREATED_TIME ASC
    </select>

    <select id="queryInitOrderHeaderByConfigId" resultType="com.wtyt.dao.bean.syf.DhHeaderDataCfgBean">
        SELECT T.HEADER_NAME      headerName,
        T.IS_UNIQUE isUnique,
        C.DATA_COLUMN_NAME dataColumnName,
        C.HEADER_EN_NAME headerEnName
        FROM T_BO_TP_DH_HEADER_IMPORT T
        LEFT join T_BO_TP_DH_HEADER_DATA_CFG c ON T.HEADER_NAME = C.HEADER_NAME
        WHERE T.IS_DEL = 0 AND C.IS_DEL =0
        AND T.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId} AND C.ORG_ID = #{orgId}
        order BY T.SORT_NUM  ASC
    </select>

    <select id="queryOrderFieldListByConfigId" resultType="com.wtyt.dh.bean.DhInputFieldBean">
        SELECT
        I.BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
        I.HEADER_NAME headerName,
        D.FIELD_NAME fieldName
        FROM T_BO_TP_DH_HEADER_IMPORT I
        LEFT JOIN T_BO_TP_DH_HEADER_COLUMN C ON C.BO_TP_DH_HEADER_IMPORT_ID = I.BO_TP_DH_HEADER_IMPORT_ID
        LEFT JOIN T_BO_TP_DH_TASK_FIELD_DICT D ON D.BO_TP_DH_TASK_FIELD_DICT_ID = C.BO_TP_DH_TASK_FIELD_DICT_ID
        WHERE I.IS_DEL = 0 AND C.IS_DEL = 0  AND D.IS_DEL = 0
        AND I.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId} ORDER BY i.SORT_NUM ASC,c.BO_TP_DH_HEADER_COLUMN_ID asc
    </select>

    <select id="queryOrderFieldListByImportIdList" resultType="com.wtyt.dh.bean.DhInputFieldBean">
        SELECT
            I.BO_TP_DH_HEADER_IMPORT_ID  boTpDhHeaderImportId,
            I.HEADER_NAME headerName,
            c.IS_LIST_SHOW_UNIT isListShowUnit,
            D.FIELD_NAME fieldName,
            C.unit unit,
            D.UNIT_CONFIG unitConfig
        FROM T_BO_TP_DH_HEADER_IMPORT I
                 LEFT JOIN T_BO_TP_DH_HEADER_COLUMN C ON C.BO_TP_DH_HEADER_IMPORT_ID = I.BO_TP_DH_HEADER_IMPORT_ID
                 LEFT JOIN T_BO_TP_DH_TASK_FIELD_DICT D ON D.BO_TP_DH_TASK_FIELD_DICT_ID = C.BO_TP_DH_TASK_FIELD_DICT_ID
        WHERE I.IS_DEL = 0 AND C.IS_DEL = 0  AND D.IS_DEL = 0
          AND I.BO_TP_DH_HEADER_IMPORT_ID in
         <foreach collection="showList" item="item" close=")" open="(" separator=",">
            ${item.boTpDhHeaderImportId}
         </foreach>
    </select>
    <select id="queryOrderFieldListByFieldNameList" resultType="com.wtyt.dh.bean.DhInputFieldBean">
        SELECT
            I.BO_TP_DH_HEADER_IMPORT_ID  boTpDhHeaderImportId,
            I.HEADER_NAME headerName,
            c.IS_LIST_SHOW_UNIT isListShowUnit,
            D.FIELD_NAME fieldName,
            C.unit unit,
            D.UNIT_CONFIG unitConfig
        FROM T_BO_TP_DH_HEADER_IMPORT I
                 LEFT JOIN T_BO_TP_DH_HEADER_COLUMN C ON C.BO_TP_DH_HEADER_IMPORT_ID = I.BO_TP_DH_HEADER_IMPORT_ID
                 LEFT JOIN T_BO_TP_DH_TASK_FIELD_DICT D ON D.BO_TP_DH_TASK_FIELD_DICT_ID = C.BO_TP_DH_TASK_FIELD_DICT_ID
        WHERE I.IS_DEL = 0 AND C.IS_DEL = 0  AND D.IS_DEL = 0
            AND I.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
          AND D.FIELD_NAME in
         <foreach collection="fieldNameList" item="fieldName" close=")" open="(" separator=",">
            #{fieldName}
         </foreach>
    </select>

    <select id="queryOrderFieldListByImportIdList2" resultType="com.wtyt.dh.bean.DhInputFieldBean">
        SELECT
        I.BO_TP_DH_HEADER_IMPORT_ID  boTpDhHeaderImportId,
        I.HEADER_NAME headerName,
        c.IS_LIST_SHOW_UNIT isListShowUnit,
        D.FIELD_NAME fieldName
        FROM T_BO_TP_DH_HEADER_IMPORT I
        LEFT JOIN T_BO_TP_DH_HEADER_COLUMN C ON C.BO_TP_DH_HEADER_IMPORT_ID = I.BO_TP_DH_HEADER_IMPORT_ID
        LEFT JOIN T_BO_TP_DH_TASK_FIELD_DICT D ON D.BO_TP_DH_TASK_FIELD_DICT_ID = C.BO_TP_DH_TASK_FIELD_DICT_ID
        WHERE I.IS_DEL = 0 AND C.IS_DEL = 0  AND D.IS_DEL = 0
        AND I.BO_TP_DH_HEADER_IMPORT_ID in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            ${item}
        </foreach>
    </select>


    <select id="queryOrderDataListByOrderIdList" resultType="java.util.LinkedHashMap">
        SELECT to_char(BO_TRANS_ORDER_ID) BO_TRANS_ORDER_ID ,
        <foreach collection="list" item="item" close="" open="" separator=",">
            ${item.fieldName}
        </foreach>
        FROM T_BO_TRANS_ORDER
        WHERE  BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
            ${item}
        </foreach>
    </select>
    <select id="existsUniqueField" resultType="java.lang.String">
        SELECT
            o.BO_TRANS_ORDER_ID
        FROM
            T_BO_TRANS_ORDER o
        LEFT JOIN T_BO_TP_IMPORT_DATA id ON
            o.BO_TRANS_ORDER_ID = id.BO_TRANS_ORDER_ID
        WHERE
            o.IS_DEL = 0
            AND o.ORG_ID = #{orgId}
            AND id.IS_DEL = 0
            AND id.${dataColumnName} = #{inputValue}
    </select>


    <select id="queryExistsData" resultType="java.lang.String">
        SELECT DISTINCT ${dataColumnName} FROM T_BO_TP_IMPORT_DATA D
        WHERE IS_DEL = 0
        AND EXISTS(SELECT 1 FROM T_BO_TRANS_ORDER O WHERE O.BO_TRANS_ORDER_ID = D.BO_TRANS_ORDER_ID AND O.ORG_ID = #{orgId} AND O.IS_DEL = 0)
        AND ${dataColumnName} IN
        <foreach collection="pramaList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="queryShippingStaticShowHeader" resultType="com.wtyt.dh.bean.DhStaticColumnBean">
        SELECT
            cc.HEADER_NAME headerName,
            c.COLUMN_NAME columnName,
            r.SORT_NUM sortNum
        FROM
            T_BO_TP_DH_STATIC_COL_REL r
        LEFT JOIN T_BO_TP_DH_STATIC_COL c ON
            r.BO_TP_DH_STATIC_COL_ID = c.BO_TP_DH_STATIC_COL_ID
        LEFT JOIN T_BO_TP_DH_STATIC_COL_CFG cc ON cc.BO_TP_DH_STATIC_COL_ID = c.BO_TP_DH_STATIC_COL_ID
        WHERE
            r.IS_DEL = 0
            AND cc.IS_DEL = 0
            AND cc.ORG_ID = #{dhConfigBean.orgId}
            AND r.BO_TP_DH_CONFIG_ID = #{dhConfigBean.boTpDhConfigId} AND r.SHIPPING_IS_SHOW = #{shippingIsShow} ORDER BY r.SORT_NUM ,r.BO_TP_DH_STATIC_COL_REL_ID
    </select>

    <select id="queryOtherShowStaticByConfigId" resultType="com.wtyt.dh.bean.DhStaticColumnBean">
        SELECT
            cc.HEADER_NAME headerName,
            c.COLUMN_NAME columnName,
            r.SORT_NUM sortNum
        FROM
            T_BO_TP_DH_STATIC_COL_REL r
                LEFT JOIN T_BO_TP_DH_STATIC_COL c ON
                r.BO_TP_DH_STATIC_COL_ID = c.BO_TP_DH_STATIC_COL_ID
                LEFT JOIN T_BO_TP_DH_STATIC_COL_CFG cc ON cc.BO_TP_DH_STATIC_COL_ID = c.BO_TP_DH_STATIC_COL_ID
        WHERE
            r.IS_DEL = 0
          AND cc.IS_DEL = 0
          AND cc.ORG_ID = #{orgId}
          AND r.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId} AND r.IS_SHOW = 0 ORDER BY r.SORT_NUM ,r.BO_TP_DH_STATIC_COL_REL_ID
    </select>

    <select id="queryFiledHeadNameByFieldNames" resultType="com.wtyt.dao.bean.syf.DhHeaderDataCfgBean">
        SELECT
            i.HEADER_NAME headerName,
            dc.DATA_COLUMN_NAME dataColumnName
        FROM
            T_BO_TP_DH_HEADER_IMPORT i
        INNER JOIN T_BO_TP_DH_HEADER_COLUMN c ON
            i.BO_TP_DH_HEADER_IMPORT_ID = c.BO_TP_DH_HEADER_IMPORT_ID
        INNER JOIN T_BO_TP_DH_TASK_FIELD_DICT d ON
            c.BO_TP_DH_TASK_FIELD_DICT_ID = d.BO_TP_DH_TASK_FIELD_DICT_ID
        INNER JOIN T_BO_TP_DH_HEADER_DATA_CFG dc ON dc.HEADER_NAME = i.HEADER_NAME AND dc.IS_DEL =0 AND dc.ORG_ID = #{dhConfigBean.orgId}
        WHERE
            i.IS_DEL = 0
            AND c.IS_DEL = 0
            AND d.IS_DEL = 0
            AND i.SHIPPING_SHOW_TYPE = 1
            AND i.BO_TP_DH_CONFIG_ID = #{dhConfigBean.boTpDhConfigId} AND d.FIELD_NAME IN
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            ORDER BY i.SORT_NUM ASC
    </select>
    <select id="queryDynamicHeadOrderInfo" resultType="java.util.Map">
        SELECT
            <if test="fieldList !=null and fieldList.size()>0">
                <foreach collection="fieldList" item="item" close="" open="" separator=",">
                    o.${item}
                </foreach>
            </if>
            <if test="columnList !=null and columnList.size()>0">
                <foreach collection="columnList" item="item" close="" open=","  separator=",">
                    d.${item}
                </foreach>
            </if>
        FROM
            T_BO_TRANS_ORDER o ,
            T_BO_TP_IMPORT_DATA d
        WHERE
            o.IS_DEL = 0
            AND o.BO_TRANS_ORDER_ID = #{boTransOrderId}
            AND d.is_del = 0
            AND o.BO_TRANS_ORDER_ID = d.BO_TRANS_ORDER_ID
    </select>
    <select id="queryDynamicHeadOrderInfoById" resultType="java.util.Map">
        SELECT
            <if test="fieldList !=null and fieldList.size()>0">
                <foreach collection="fieldList" item="item" close="" open="" separator=",">
                    o.${item}
                </foreach>
            </if>
            <if test="columnList !=null and columnList.size()>0">
                <foreach collection="columnList" item="item" close="" open=","  separator=",">
                    d.${item}
                </foreach>
            </if>
        FROM
            T_BO_TRANS_ORDER o ,
            T_BO_TP_IMPORT_DATA d
        WHERE
            o.BO_TRANS_ORDER_ID = #{boTransOrderId}
            AND o.BO_TRANS_ORDER_ID = d.BO_TRANS_ORDER_ID
    </select>


    <select id="queryLatest10CreateDynamicHeadOrderInfo" resultType="java.util.Map">
        SELECT
            <if test="fieldList !=null and fieldList.size()>0">
                <foreach collection="fieldList" item="item" close="" open="" separator=",">
                    ${item}
                </foreach>
            </if>
            <if test="columnList !=null and columnList.size()>0">
                <foreach collection="columnList" item="item" close="" open="," separator=",">
                    ${item}
                </foreach>
            </if>
        FROM (
            SELECT
                <if test="fieldList !=null and fieldList.size()>0">
                    <foreach collection="fieldList" item="item" close="" open="" separator=",">
                        <choose>
                            <when test='item == "TRANS_DATE"'>
                                TO_CHAR(o.${item}, 'yyyy-MM-dd') AS ${item}
                            </when>
                            <otherwise>
                                o.${item}
                            </otherwise>
                        </choose>
                    </foreach>
                </if>
                <if test="columnList !=null and columnList.size()>0">
                    <foreach collection="columnList" item="item" close="" open="," separator=",">
                        d.${item}
                    </foreach>
                </if>
            FROM T_BO_TRANS_ORDER o INNER JOIN T_BO_TP_IMPORT_DATA d ON o.BO_TRANS_ORDER_ID = d.BO_TRANS_ORDER_ID
            WHERE o.IS_DEL = 0 AND d.is_del = 0 AND o.FROM_SOURCE IN (2,6) AND o.USER_ID = #{userId} AND o.ORG_ID = #{orgId}
            ORDER BY o.CREATED_TIME DESC
        ) temp
        WHERE ROWNUM <![CDATA[<=]]> 10
    </select>

    <select id="queryImportHeaderList" resultMap="dhFieldInfo">
        SELECT
            I.BO_TP_DH_HEADER_IMPORT_ID,
            I.HEADER_NAME,
            I.IS_REQUIRED,
            I.SHOW_TYPE,
            I.IS_INPUT,
            I.MERGE_SHOW_ID,
            I.HEADER_TYPE,
            I.SHIPPING_SHOW_TYPE,
            I.CAN_MODIFY,
            I.MERGE_PREFIX,
            I.MERGE_SUFFIX,
            H.DATA_COLUMN_NAME,
            S.SPLIT_CHAR,
            I.SORT_NUM
        FROM T_BO_TP_DH_HEADER_IMPORT I
            LEFT JOIN T_BO_TP_DH_HEADER_DATA_CFG H ON I.HEADER_NAME = H.HEADER_NAME AND H.IS_DEL = 0  AND H.ORG_ID = #{orgId}
            LEFT JOIN T_BO_TP_DH_HEADER_SHOW S ON I.BO_TP_DH_HEADER_IMPORT_ID = S.BO_TP_DH_HEADER_IMPORT_ID AND S.IS_DEL = 0
        WHERE I.IS_DEL = 0
          AND I.BO_TP_DH_CONFIG_ID = #{boTpDhConfigId}
        ORDER BY I.SORT_NUM ASC

    </select>

    <select id="queryImportHeaderColumnList" resultMap="dhColumnDTO">
        SELECT
            c.BO_TP_DH_HEADER_COLUMN_ID,
            c.BO_TP_DH_HEADER_IMPORT_ID,
            c.BO_TP_DH_TASK_FIELD_DICT_ID,
            d.FIELD_TYPE,
            d.FIELD_NAME_DESC,
            d.FIELD_NAME,
            d.INPUT_TYPE,
            d.MAX_LENGTH,
            d.NUMBER_PRECISION
        FROM T_BO_TP_DH_HEADER_COLUMN c INNER JOIN T_BO_TP_DH_TASK_FIELD_DICT d ON c.BO_TP_DH_TASK_FIELD_DICT_ID = d.BO_TP_DH_TASK_FIELD_DICT_ID
        WHERE c.IS_DEL= 0 AND d.IS_DEL = 0 AND c.BO_TP_DH_HEADER_IMPORT_ID IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

      <select id="queryDataCfgColumnEnName" resultType="com.wtyt.dao.bean.syf.DhHeaderDataCfgBean">
        SELECT
           DATA_COLUMN_NAME dataColumnName,
           HEADER_EN_NAME headerEnName
        FROM T_BO_TP_DH_HEADER_DATA_CFG c
        WHERE c.IS_DEL= 0 AND c.ORG_ID =  #{orgId} AND c.HEADER_EN_NAME IN
        <foreach collection="headerEnNameList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="queryDictFieldOptionInfo" resultType="com.wtyt.dh.bean.DhInputFieldBean">
        SELECT
            a.BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
            CASE WHEN a.IS_INPUT = 2 THEN b.OPTION_VALUE ELSE a.OPTION_INFO END optionInfo
        FROM
            T_BO_TP_DH_CONFIG c
            inner join T_BO_TP_DH_HEADER_IMPORT a on c.BO_TP_DH_CONFIG_ID= a.BO_TP_DH_CONFIG_ID
            LEFT JOIN T_BO_TP_DH_HEADER_COLUMN b ON a.BO_TP_DH_HEADER_IMPORT_ID = b.BO_TP_DH_HEADER_IMPORT_ID AND b.IS_DEL = 0
            LEFT JOIN T_BO_TP_DH_TASK_FIELD_DICT c ON b.BO_TP_DH_TASK_FIELD_DICT_ID = c.BO_TP_DH_TASK_FIELD_DICT_ID AND c.IS_DEL = 0
        WHERE
            a.IS_DEL =0 and c.IS_DEL =0 and  c.VALID_STATE=1
            AND c.ORG_ID  in
                <foreach collection="orgIdList" item="item" close=")" open="(" separator=",">
                      #{item}
                </foreach>
            AND  c.FIELD_NAME =  #{fieldName}
         order by a.BO_TP_DH_HEADER_IMPORT_ID
    </select>
    <select id="queryNoMapperHeaderByConfigId" resultType="com.wtyt.dao.bean.syf.DhHeaderDataCfgBean">
        SELECT
            i.BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
            i.HEADER_NAME headerName,
            i.HEADER_TYPE headerType,
            i.SORT_NUM sortNum,
            dc.DATA_COLUMN_NAME dataColumnName
        FROM
            T_BO_TP_DH_CONFIG c
        JOIN T_BO_TP_DH_HEADER_IMPORT i ON
            c.BO_TP_DH_CONFIG_ID = i.BO_TP_DH_CONFIG_ID
        JOIN T_BO_TP_DH_HEADER_DATA_CFG dc ON
            dc.HEADER_NAME = i.HEADER_NAME
            AND dc.ORG_ID = #{orgId}
        LEFT JOIN T_BO_TP_DH_HEADER_COLUMN hc ON
            hc.BO_TP_DH_HEADER_IMPORT_ID = i.BO_TP_DH_HEADER_IMPORT_ID
            AND hc.IS_DEL = 0
        WHERE
            c.IS_DEL = 0
            AND c.VALID_STATE = 1
            AND c.BO_TP_DH_CONFIG_ID  = #{boTpDhConfigId}
            AND hc.BO_TP_DH_TASK_FIELD_DICT_ID IS NULL
    </select>
    <resultMap id="headerCfgMap" type="com.wtyt.dao.bean.syf.DhHeaderDataCfgBean">
        <id column="BO_TP_DH_HEADER_IMPORT_ID" property="boTpDhHeaderImportId"></id>
        <result column="HEADER_NAME" property="headerName"></result>
        <result column="SORT_NUM" property="sortNum"></result>
        <result column="SHOW_TYPE" property="showType"></result>
        <result column="MERGE_SHOW_ID" property="mergeShowId"></result>
        <result column="MERGE_PREFIX" property="mergePrefix"></result>
        <result column="MERGE_SUFFIX" property="mergeSuffix"></result>
        <result column="DATA_COLUMN_NAME" property="dataColumnName"></result>
        <result column="HEADER_TYPE" property="headerType"></result>
        <collection property="orderColumnNameList" ofType="java.lang.String">
            <constructor>
                <arg column="FIELD_NAME"></arg>
            </constructor>
        </collection>
    </resultMap>
    <select id="queryMappedHeaderByConfigId" resultMap="headerCfgMap">
        SELECT
            i.BO_TP_DH_HEADER_IMPORT_ID ,
            i.HEADER_NAME ,
            i.HEADER_TYPE ,
            i.SHOW_TYPE,
            i.SORT_NUM ,
            fd.FIELD_NAME
        FROM
            T_BO_TP_DH_CONFIG c
        JOIN T_BO_TP_DH_HEADER_IMPORT i ON
            c.BO_TP_DH_CONFIG_ID = i.BO_TP_DH_CONFIG_ID
        JOIN T_BO_TP_DH_HEADER_COLUMN hc ON
            hc.BO_TP_DH_HEADER_IMPORT_ID = i.BO_TP_DH_HEADER_IMPORT_ID
            AND hc.IS_DEL = 0
        JOIN T_BO_TP_DH_TASK_FIELD_DICT fd ON hc.BO_TP_DH_TASK_FIELD_DICT_ID = fd.BO_TP_DH_TASK_FIELD_DICT_ID
        WHERE
            c.IS_DEL = 0
            AND c.VALID_STATE = 1
            AND c.BO_TP_DH_CONFIG_ID  = #{boTpDhConfigId}
            AND hc.BO_TP_DH_TASK_FIELD_DICT_ID IS NOT NULL
    </select>
    <select id="queryDynamicHeadOrderList" resultType="java.util.Map">
        SELECT
            <if test="fieldList !=null and fieldList.size()>0">
                <foreach collection="fieldList" item="item" close="" open="" separator=",">
                    o.${item}
                </foreach>
            </if>
            <if test="columnList !=null and columnList.size()>0">
                <foreach collection="columnList" item="item" close="" open=","  separator=",">
                    d.${item}
                </foreach>
            </if>
        FROM
            T_BO_TRANS_ORDER o ,
            T_BO_TP_IMPORT_DATA d
        WHERE
            o.IS_DEL = 0
            AND o.BO_TRANS_ORDER_ID IN
            <foreach collection="boTransOrderIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            AND d.IS_DEL = 0
            AND o.BO_TRANS_ORDER_ID = d.BO_TRANS_ORDER_ID
    </select>


</mapper>
