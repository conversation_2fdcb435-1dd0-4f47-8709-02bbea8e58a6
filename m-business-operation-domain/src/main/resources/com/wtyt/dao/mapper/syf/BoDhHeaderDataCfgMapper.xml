<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoDhHeaderDataCfgMapper">
    <insert id="insertBatch">
        INSERT INTO T_BO_DH_HEADER_DATA_CFG
        (BO_DH_HEADER_DATA_CFG_ID, BUSINESS_TYPE, GROUP_TYPE, GROUP_CODE, HEADER_NAME,DATA_COLUMN_NAME,HEADER_EN_NAME)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boDhHeaderDataCfgId} BO_DH_HEADER_DATA_CFG_ID,
            #{item.businessType} BUSINESS_TYPE,
            #{item.groupType} GROUP_TYPE,
            #{item.groupCode} GROUP_CODE,
            #{item.headerName} HEADER_NAME,
            #{item.dataColumnName} DATA_COLUMN_NAME,
            #{item.headerEnName,jdbcType=VARCHAR} HEADER_NAME
            FROM
            DUAL
        </foreach>
    </insert>


    <select id="queryHeaderDataCfgListByGroupCode" resultType="com.wtyt.dao.bean.syf.BoDhHeaderDataCfgBean">
        SELECT
            BO_DH_HEADER_DATA_CFG_ID boDhHeaderDataCfgId,
            BUSINESS_TYPE businessType,
            GROUP_TYPE groupType,
            GROUP_CODE groupCode,
            DATA_COLUMN_NAME dataColumnName,
            HEADER_NAME headerName,
            HEADER_EN_NAME headerEnName
        FROM T_BO_DH_HEADER_DATA_CFG
        WHERE IS_DEL = 0
        AND BUSINESS_TYPE = #{businessType} and GROUP_TYPE =#{groupType} and GROUP_CODE =#{groupCode}
    </select>

    <update id="updateHeaderName">
        UPDATE T_BO_DH_HEADER_DATA_CFG
        SET HEADER_NAME  = #{newHeaderName},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE HEADER_NAME = #{oldHeaderName}
          AND IS_DEL = 0
          AND BUSINESS_TYPE = #{boDhConfigBean.businessType}
          and GROUP_TYPE = #{boDhConfigBean.groupType}
          and GROUP_CODE = #{boDhConfigBean.groupCode}
    </update>

    <!-- 检查是否有更新后的表头名称的配置，如果存在，则删除掉他 -->
    <update id="removeHeaderName">
        UPDATE T_BO_DH_HEADER_DATA_CFG
        SET IS_DEL   =1,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE HEADER_NAME = #{newHeaderName}
          AND IS_DEL = 0
          AND BUSINESS_TYPE = #{boDhConfigBean.businessType}
          and GROUP_TYPE = #{boDhConfigBean.groupType}
          and GROUP_CODE = #{boDhConfigBean.groupCode}
    </update>

</mapper>
