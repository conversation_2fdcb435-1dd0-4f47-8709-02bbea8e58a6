spring:
  #设置编码格式
  http:
    encoding:
      force: true
      charset: UTF-8
      enabled: true
  messages:
    basename: config/messages
    encoding: UTF-8
  jackson:
    default-property-inclusion: non-null
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false
#设置内置服务器的URI编码格式
server:
  tomcat:
    uri-encoding: UTF-8
  servlet:
    context-path: /m-business-operation-domain

eureka:
  instance:
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}

apollo:
  bootstrap:
    enabled: true
    namespaces: application,yanfa.dynamic.pool
    eagerLoad:
      enabled: true