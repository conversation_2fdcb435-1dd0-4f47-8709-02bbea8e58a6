package com.wtyt.commons.toolkits;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wtyt.logoss.sdk.LogossClient;
import com.wtyt.logoss.sdk.annotation.Authorized;
import com.wtyt.logoss.sdk.bean.OssUrlsBean;

/**
 * @author: money-丁帅
 * @time: 2022/11/28 11:55
 */
public class LogossToolkit {
    private static LogossClient client = new LogossClient(5000, 10000);
    private static Logger log=LoggerFactory.getLogger(LogossToolkit.class);


    /**
     *
     * @Title: getAuthTokenUrl
     * <AUTHOR>
     * @Description: 获取授权结果信息
     * @param url oss上传原地址
     * @return
     * @throws Exception
     * @return OssUrlsBean 返回类型
     * @throws
     */
    public static OssUrlsBean getAuthTokenUrl(final String url) throws Exception{
        AssertUtil.notBlank(url, "url is blank");
        OssUrlsBean result=client.getAuthTokenUrl(url);
        return result;
    }
    
    
    public static String getAuthTokenUrlString(final String url,final long expirationSeconds) throws Exception{
    	AssertUtil.notBlank(url, "url is blank");
        OssAuthBean authBean=new OssAuthBean(url);
        client.getAuthTokenUrl(authBean,expirationSeconds);
        return authBean.getAuthUrl();
    }
    
    private static class OssAuthBean{
    	@Authorized
    	private String authUrl;

		public String getAuthUrl() {
			return authUrl;
		}

		public void setAuthUrl(String authUrl) {
			this.authUrl = authUrl;
		}

		public OssAuthBean(String authUrl) {
			super();
			this.authUrl = authUrl;
		}
    	
    }
    
    
    /**
     * 
     * @Title: getAuthTokenUrlIfAbsent
     * @author: 盲仔
     * @Description: 根据源地址获取授权地址
     * @param url
     * @param expirationSeconds 有效期（秒）
     * @return
     * @throws Exception  报错时返回原地址
     * @return String
     * @throws
     */
   public static String getAuthTokenUrlIfAbsent(final String url,long expirationSeconds){
	   if(StringUtils.isBlank(url)) {
		   return url;
	   }
	   try {
			return getAuthTokenUrlString(url,expirationSeconds);
		} catch (Exception e) {
			log.error("生成授权地址出错:"+e.getLocalizedMessage());
		}
		return url;
   }
}
