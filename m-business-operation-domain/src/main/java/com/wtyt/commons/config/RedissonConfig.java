package com.wtyt.commons.config;

import com.wtyt.security.wrapper.redis.RedisDBEnum;
import com.wtyt.security.wrapper.redis.RedissonWrapperConnFactory;
import org.redisson.Redisson;
import org.redisson.api.RBloomFilter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR>
 * @date 2023年12月12日 16时17分
 */
@Configuration
public class RedissonConfig  {

    @Primary
    @Bean("redisson")
    public Redisson getRedisson(){
        return RedissonWrapperConnFactory.createRedisson(
                "yanfa.datasource.redis.syf-session-redis",
                "spring.datasource.redis.syf-session-redis",
                RedisDBEnum.DB15);
    }

    @Bean("bloomFilter")
    public RBloomFilter<String> getBloomFilter(@Qualifier("redisson") Redisson redisson) {
        RBloomFilter<String> bloomFilter = redisson.getBloomFilter("boTaxWaybillIdList");
        // 初始化布隆过滤器：预计元素为*********,误差率为1%
        bloomFilter.tryInit(*********L,0.01);
        return bloomFilter;
    }
}
