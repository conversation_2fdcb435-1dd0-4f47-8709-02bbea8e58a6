<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransSendCarRecordMapper">

    <insert id="insertBoTransSendCarRecord" parameterType="BoTransSendCarRecordBean">
        INSERT INTO T_BO_TRANS_SEND_CAR_RECORD(
        BO_TRANS_SEND_CAR_RECORD_ID,
        <if test="type != null and type !=''">
            TYPE,
        </if>
        <if test="taxWaybillId != null and taxWaybillId !=''">
            TAX_WAYBILL_ID,
        </if>
        <if test="boTransTaskId != null and boTransTaskId !=''">
            BO_TRANS_TASK_ID,
        </if>
        <if test="driverName != null and driverName !=''">
            DRIVER_NAME,
        </if>

        <if test="mobileNo != null and mobileNo !=''">
            MOBILE_NO,
        </if>

        <if test="cartBadgeNo != null and cartBadgeNo !=''">
            CART_BADGE_NO,
        </if>

        <if test="cartLength != null and cartLength !=''">
            CART_LENGTH,
        </if>

        <if test="cartType != null and cartType !=''">
            CART_TYPE,
        </if>

        <if test="cartBadgeColor != null and cartBadgeColor !=''">
            CART_BADGE_COLOR,
        </if>

        USER_ID,
        XCY_USER_ID,
        <if test="reason != null and reason !=''">
            REASON,
        </if>
        <if test="note != null and note !=''">
            NOTE,
        </if>
        <if test="sysRoleType != null and sysRoleType !=''">
            SYS_ROLE_TYPE,
        </if>
        <if test="userFreight != null and userFreight !=''">
            USER_FREIGHT,
        </if>
        <if test="orgId != null and orgId !=''">
            ORG_ID,
        </if>
        CREATED_TIME

        )values(
        ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},
        <if test="type != null and type !=''">
            #{type},
        </if>
        <if test="taxWaybillId != null and taxWaybillId !=''">
            #{taxWaybillId},
        </if>
        <if test="boTransTaskId != null and boTransTaskId !=''">
            #{boTransTaskId},
        </if>
        <if test="driverName != null and driverName !=''">
            #{driverName},
        </if>
        <if test="mobileNo != null and mobileNo !=''">
            #{mobileNo},
        </if>
        <if test="cartBadgeNo != null and cartBadgeNo !=''">
            #{cartBadgeNo},
        </if>
        <if test="cartLength != null and cartLength !=''">
            #{cartLength},
        </if>
        <if test="cartType != null and cartType !=''">
            #{cartType},
        </if>
        <if test="cartBadgeColor != null and cartBadgeColor !=''">
            #{cartBadgeColor},
        </if>
        #{userId},
        #{xcyUserId},
        <if test="reason != null and reason !=''">
            #{reason},
        </if>
        <if test="note != null and note !=''">
            #{note},
        </if>
        <if test="sysRoleType != null and sysRoleType !=''">
            #{sysRoleType},
        </if>
        <if test="userFreight != null and userFreight !=''">
            #{userFreight},
        </if>
        <if test="orgId != null and orgId !=''">
            #{orgId},
        </if>
        SYSDATE
        )
    </insert>
    <update id="batchTransferOrg">
        UPDATE T_BO_TRANS_SEND_CAR_RECORD
        <set>
            LAST_MODIFIED_TIME = SYSDATE,
            NOTE = #{data.note},
            ORG_ID = #{data.toOrgId}
        </set>
        <where>
            ORG_ID = #{data.fromOrgId}
            AND IS_DEL = 0
            AND BO_TRANS_TASK_ID IN
            <foreach collection="taskIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </update>

    <select id="queryLastGroupAboutDispatch" resultType="BoTransSendCarRecordBean">
    SELECT  C.*
        FROM(SELECT A.BO_TRANS_TASK_ID boTransTaskId,
                    A.DRIVER_NAME driverName,
                    A.MOBILE_NO mobileNo,
                    A.CART_BADGE_NO cartBadgeNo,
                    A.CART_LENGTH cartLength,
                    A.CART_TYPE cartType,
                    A.CART_BADGE_COLOR cartBadgeColor,
                    A.USER_ID userId,
                    A.XCY_USER_ID xcyUserId,
                    A.REASON reason,
                    A.IS_DEL isDel,
                    A.NOTE note,
                    A.USER_FREIGHT userFreight
             FROM T_BO_TRANS_SEND_CAR_RECORD A
             WHERE
               A.BO_TRANS_TASK_ID = #{boTransTaskId}
               AND A.IS_DEL = 0
               AND A.CREATED_TIME &lt;= to_date(#{endCreatedTime},'YYYY-MM-DD HH24:MI:SS')
             ORDER BY A.CREATED_TIME DESC) C
        WHERE ROWNUM &lt;= 2

    </select>

    <select id="getRecentlyDispatchedCancelRecord" resultType="BoTransSendCarRecordBean">
        SELECT
            BO_TRANS_TASK_ID boTransTaskId,
            DRIVER_NAME driverName,
            MOBILE_NO mobileNo,
            CART_BADGE_NO cartBadgeNo,
            CART_LENGTH cartLength
        FROM (
            SELECT
                T.*
            FROM T_BO_TRANS_SEND_CAR_RECORD T
            WHERE T.IS_DEL = 0
            AND T.TYPE = 2
            AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
            ORDER BY T.CREATED_TIME DESC
        ) WHERE ROWNUM = 1
    </select>
</mapper>
