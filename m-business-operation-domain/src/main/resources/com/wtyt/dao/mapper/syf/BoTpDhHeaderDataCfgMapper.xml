<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhHeaderDataCfgMapper">
    <insert id="insertBatch">
        INSERT INTO T_BO_TP_DH_HEADER_DATA_CFG
        (BO_TP_DH_HEADER_DATA_CFG_ID, ORG_ID, DATA_COLUMN_NAME, HEADER_NAME, HEADER_EN_NAME,ACCOUNT_GROUP_ID)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTpDhHeaderDataCfgId} BO_TP_DH_HEADER_DATA_CFG_ID,
            #{item.orgId} ORG_ID,
            #{item.dataColumnName} DATA_COLUMN_NAME,
            #{item.headerName} HEADER_NAME,
            #{item.headerEnName,jdbcType=VARCHAR} HEADER_EN_NAME,
            #{item.accountGroupId} ACCOUNT_GROUP_ID
            FROM
            DUAL
        </foreach>
    </insert>

    <update id="updateHeaderName">
        UPDATE T_BO_TP_DH_HEADER_DATA_CFG
        SET HEADER_NAME = #{newHeaderName}, LAST_MODIFIED_TIME = SYSDATE
        WHERE HEADER_NAME = #{oldHeaderName} AND ORG_ID = #{orgId} AND IS_DEL = 0
    </update>

    <update id="updateOrgListHeaderName">
        UPDATE T_BO_TP_DH_HEADER_DATA_CFG
        SET HEADER_NAME = #{newHeaderName}, LAST_MODIFIED_TIME = SYSDATE
        WHERE HEADER_NAME = #{oldHeaderName}  AND IS_DEL = 0 AND ORG_ID IN
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </update>
    <!-- 检查是否有更新后的表头名称的配置，如果存在，则删除掉他 -->
    <update id="removeHeaderName">
        UPDATE T_BO_TP_DH_HEADER_DATA_CFG
        SET NOTE = HEADER_NAME, HEADER_NAME = BO_TP_DH_HEADER_DATA_CFG_ID, LAST_MODIFIED_TIME = SYSDATE
        WHERE HEADER_NAME = #{newHeaderName} AND ORG_ID = #{orgId} AND IS_DEL = 0
    </update>

    <!-- 检查是否有更新后的表头名称的配置，如果存在，则删除掉他 -->
    <update id="removeOrgListHeaderName">
        UPDATE T_BO_TP_DH_HEADER_DATA_CFG
        SET NOTE = HEADER_NAME, HEADER_NAME = BO_TP_DH_HEADER_DATA_CFG_ID, LAST_MODIFIED_TIME = SYSDATE
        WHERE HEADER_NAME = #{newHeaderName}  AND IS_DEL = 0 AND ORG_ID IN
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </update>


    <select id="queryHeaderDataCfgListByOrgId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderDataCfgBean">
        SELECT
            BO_TP_DH_HEADER_DATA_CFG_ID boTpDhHeaderDataCfgId,
            ORG_ID orgId,
            DATA_COLUMN_NAME dataColumnName,
            HEADER_NAME headerName
        FROM T_BO_TP_DH_HEADER_DATA_CFG
        WHERE IS_DEL = 0
        AND ORG_ID = #{orgId}
    </select>

    <delete id="deleteByOrgIds">
        DELETE FROM  T_BO_TP_DH_HEADER_DATA_CFG WHERE ORG_ID IN
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </delete>


    <insert id="backupOrdDataCfgToBackupTable">
        INSERT INTO T_BO_TP_DH_HEADER_DATA_BAC
         (BO_TP_DH_HEADER_DATA_BAC_ID, ORG_ID, DATA_COLUMN_NAME, HEADER_NAME, HEADER_EN_NAME,CREATED_TIME,LAST_MODIFIED_TIME,NOTE)
        SELECT BO_TP_DH_HEADER_DATA_CFG_ID, ORG_ID, DATA_COLUMN_NAME, HEADER_NAME, HEADER_EN_NAME,CREATED_TIME,LAST_MODIFIED_TIME,NOTE
        FROM T_BO_TP_DH_HEADER_DATA_CFG
        WHERE ORG_ID IN
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND ACCOUNT_GROUP_ID is null
        and is_del =0
    </insert>

    <select id="queryHeaderDataCfgListByOrgIdAndHeaderNames" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderDataCfgBean">
        SELECT
            BO_TP_DH_HEADER_DATA_CFG_ID boTpDhHeaderDataCfgId,
            ORG_ID orgId,
            DATA_COLUMN_NAME dataColumnName,
            HEADER_NAME headerName,
            HEADER_EN_NAME headerEnName
        FROM T_BO_TP_DH_HEADER_DATA_CFG
        WHERE IS_DEL = 0
          AND ORG_ID = #{orgId}
         AND HEADER_NAME IN
        <foreach collection="headerNames" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="queryHeaderDataCfgListByGroupIdAndHeaderNames" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderDataCfgBean">
        SELECT
            BO_TP_DH_HEADER_DATA_CFG_ID boTpDhHeaderDataCfgId,
            ORG_ID orgId,
            DATA_COLUMN_NAME dataColumnName,
            HEADER_NAME headerName,
            HEADER_EN_NAME headerEnName,
            ACCOUNT_GROUP_ID accountGroupId
        FROM T_BO_TP_DH_HEADER_DATA_CFG
        WHERE IS_DEL = 0
          AND ACCOUNT_GROUP_ID = #{accountGroupId}
         AND HEADER_NAME IN
        <foreach collection="headerNames" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>


</mapper>
