<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoDhHeaderShowMapper">

    <resultMap id="dynamicHeaderShowResultMap" type="com.wtyt.dao.bean.syf.BoDhHeaderShowBean">
        <id property="boDhHeaderShowId" column="BO_DH_HEADER_SHOW_ID"/>
        <result property="showHeaderName" column="SHOW_HEADER_NAME"/>
        <result property="splitChar" column="SPLIT_CHAR"/>
        <result property="boDhConfigId" column="BO_DH_CONFIG_ID"/>
        <collection property="bindings" ofType="com.wtyt.dao.bean.syf.BoDhHeaderShowBindBean"
                    resultMap="dynamicHeaderShowBindResultMap"/>
    </resultMap>

    <resultMap id="dynamicHeaderShowBindResultMap" type="com.wtyt.dao.bean.syf.BoDhHeaderShowBindBean">
        <id property="boDhHeaderShowBindId" column="BO_DH_HEADER_SHOW_BIND_ID"/>
        <result property="boDhHeaderShowId" column="BIND_SHOW_ID"/>
        <result property="boDhHeaderImportId" column="BO_DH_HEADER_IMPORT_ID"/>
    </resultMap>

    <select id="getHeaderShowByConfigId" resultMap="dynamicHeaderShowResultMap">
        SELECT
            s.BO_DH_HEADER_SHOW_ID,
            s.SHOW_HEADER_NAME,
            s.SPLIT_CHAR,
            s.BO_DH_CONFIG_ID,
            b.BO_DH_HEADER_SHOW_BIND_ID,
            b.BO_DH_HEADER_SHOW_ID as BIND_SHOW_ID,
            b.BO_DH_HEADER_IMPORT_ID
        FROM
            T_BO_DH_HEADER_SHOW s
                LEFT JOIN
            T_BO_DH_HEADER_SHOW_BIND b ON s.BO_DH_HEADER_SHOW_ID = b.BO_DH_HEADER_SHOW_ID  AND b.IS_DEL = 0
        WHERE
            s.BO_DH_CONFIG_ID = #{boDhConfigId}
          AND s.IS_DEL = 0
        ORDER BY
            s.BO_DH_HEADER_SHOW_ID, b.BO_DH_HEADER_SHOW_BIND_ID
    </select>

    <insert id="insertBatch">
        INSERT INTO T_BO_DH_HEADER_SHOW
        (BO_DH_HEADER_SHOW_ID, SHOW_HEADER_NAME, SPLIT_CHAR, BO_DH_CONFIG_ID)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boDhHeaderShowId} BO_DH_HEADER_SHOW_ID,
            #{item.showHeaderName} SHOW_HEADER_NAME,
            #{item.splitChar} SPLIT_CHAR,
            #{item.boDhConfigId} BO_DH_CONFIG_ID
            FROM
            DUAL
        </foreach>
    </insert>


     <insert id="insertBindBatch">
        INSERT INTO T_BO_DH_HEADER_SHOW_BIND
        (BO_DH_HEADER_SHOW_BIND_ID, BO_DH_HEADER_SHOW_ID, BO_DH_HEADER_IMPORT_ID)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boDhHeaderShowBindId} BO_DH_HEADER_SHOW_BIND_ID,
            #{item.boDhHeaderShowId} BO_DH_HEADER_SHOW_ID,
            #{item.boDhHeaderImportId} BO_DH_HEADER_IMPORT_ID
            FROM
            DUAL
        </foreach>
    </insert>

</mapper>
