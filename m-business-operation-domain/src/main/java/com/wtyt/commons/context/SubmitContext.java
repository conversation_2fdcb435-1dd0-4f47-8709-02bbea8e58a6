package com.wtyt.commons.context;

import com.wtyt.bo.bean.BoTaskDetailBean;
import com.wtyt.common.enums.EventProcessorEnum;
import com.wtyt.commons.bean.EventArguments;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023年06月15日 19时48分
 */
@Setter
@Getter
public class SubmitContext implements Serializable {

    private static final long serialVersionUID = -2680980939201212739L;
    private EventProcessorEnum eventProcessor;
    private EventArguments eventArguments;
    /**
     * 以下三个时间为基类使用的时间、请不要直接GET使用
     */
    private String generalProcessTime;
    private String alarmProcessTime;
    private String messageProcessTime;
    private BoTaskDetailBean boTaskDetailBean;
    private boolean loadTaskDetail;
}
