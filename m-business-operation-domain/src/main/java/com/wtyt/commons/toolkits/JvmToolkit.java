package com.wtyt.commons.toolkits;

import com.thoughtworks.xstream.InitializationException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025年07月29日 10时10分
 */
@Slf4j
public class JvmToolkit {

    private JvmToolkit() {
        throw new InitializationException("JvmToolkit Cannot initialize");
    }

    /**
     * 打印jvm当前内存信息
     * @param key
     */
    public static void printJvmMemory(String key) {
        if (log.isDebugEnabled()) {
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory() / 1024 / 1024;
            long totalMemory = runtime.totalMemory() / 1024 / 1024;
            long freeMemory = runtime.freeMemory() / 1024 / 1024;
            log.debug("[{}]当前jvm已配置内存: {}MB、已分配内存: {}MB、已空闲内存: {}MB、已使用内存: {}MB、可分配内存: {}MB",
                    key, maxMemory, totalMemory, freeMemory, totalMemory - freeMemory, maxMemory - totalMemory - freeMemory);
        }
    }
}
