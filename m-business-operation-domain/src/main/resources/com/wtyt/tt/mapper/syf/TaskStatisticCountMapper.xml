<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.tt.mapper.syf.TaskStatisticCountMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTaskStatisticBean" id="BoTaskStatisticMap">
        <result property="statisticEntityId" column="STATISTIC_ENTITY_ID" jdbcType="VARCHAR"/>
        <result property="statisticKey" column="STATISTIC_KEY" jdbcType="VARCHAR"/>
        <result property="statisticValue" column="STATISTIC_VALUE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="countTaskOtherInfoName" resultMap="BoTaskStatisticMap">
        SELECT
            DISTINCT t.ORG_ID STATISTIC_ENTITY_ID,TRIM(c.FIELD_NAME) STATISTIC_KEY
        FROM
            T_BO_TRANS_TASK t
        JOIN T_BO_TASK_CUS_FIELD c ON
            c.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        WHERE
        t.IS_DEL = 0
        AND c.IS_DEL =0
        AND c.CATEGORY_TYPE =1
        <if test="orgIds != null and orgIds.size() > 0">
            AND t.ORG_ID IN
            <foreach collection="orgIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        AND t.CREATED_TIME > SYSDATE - #{searchDays}
    </select>

    <select id="countTaskDispatchUserIdByTaskOrgId" resultMap="BoTaskStatisticMap">
        SELECT
            DISTINCT t.ORG_ID STATISTIC_ENTITY_ID,nr.USER_ID STATISTIC_KEY
        FROM
            T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA te ON
            te.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_NODE_RECORD nr ON
            nr.BO_TRANS_NODE_RECORD_ID = te.DISPATCH_CAR_RECORD_ID
        WHERE
            t.IS_DEL = 0
            AND (t.ORG_ID IN
            <foreach collection="orgIds" index="index" item="orgId" open="(" separator="," close=")">
                <if test="(index % 999) == 998"> NULL) OR t.ORG_ID IN(</if>#{orgId}
            </foreach>
            )
            <if test="searchDays != null">
                AND t.CREATED_TIME > SYSDATE - #{searchDays}
            </if>
            AND t.TRANS_MODE NOT IN (3, 4)
            AND nr.USER_ID !=-1
    </select>

    <select id="countTaskDispatchUserIdByAllocateOrgId" resultMap="BoTaskStatisticMap">
        SELECT
            DISTINCT ta.ORG_ID STATISTIC_ENTITY_ID,nr.USER_ID STATISTIC_KEY
        FROM
            T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA te ON
            te.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_NODE_RECORD nr ON
            nr.BO_TRANS_NODE_RECORD_ID = te.DISPATCH_CAR_RECORD_ID
        JOIN T_BO_TRANS_TASK_ALLOCATE ta ON ta.BO_TRANS_TASK_ID =t.BO_TRANS_TASK_ID AND ta.IS_DEL =0
        WHERE
            t.IS_DEL = 0
            AND (ta.ORG_ID IN
            <foreach collection="orgIds" index="index" item="orgId" open="(" separator="," close=")">
                <if test="(index % 999) == 998"> NULL) OR ta.ORG_ID IN(</if>#{orgId}
            </foreach>
            )
            <if test="searchDays != null">
                AND t.CREATED_TIME > SYSDATE - #{searchDays}
            </if>
            AND nr.USER_ID !=-1
    </select>

    <select id="countTaskDispatchGroupId" resultMap="BoTaskStatisticMap">
        SELECT
            DISTINCT t.ORG_ID STATISTIC_ENTITY_ID,TGR.GROUP_ID STATISTIC_KEY
        FROM
            T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA te ON
        te.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        JOIN T_BO_TRANS_TASK_GROUP_REL tgr ON
        tgr.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND TGR.IS_DEL=0 AND TGR.SUPPLIER_TYPE IN (1,2)
        WHERE
            t.IS_DEL = 0
            AND (t.ORG_ID IN
            <foreach collection="orgIds" index="index" item="orgId" open="(" separator="," close=")">
                <if test="(index % 999) == 998"> NULL) OR t.ORG_ID IN(</if>#{orgId}
            </foreach>
            )
            <if test="searchDays != null">
                AND t.CREATED_TIME > SYSDATE - #{searchDays}
            </if>
            AND t.TRANS_MODE IN (3, 4)
            AND TGR.GROUP_ID IS NOT NULL
    </select>

</mapper>
