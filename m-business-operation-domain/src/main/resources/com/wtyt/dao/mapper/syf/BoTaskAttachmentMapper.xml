<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskAttachmentMapper">

    <insert id="batchInsertTaskAttachment">
        INSERT INTO T_BO_TASK_ATTACHMENT (
            BO_TASK_ATTACHMENT_ID,
            BO_TRANS_TASK_ID,
            BUSINESS_TYPE,
            BUSINESS_ID,
            ATTACHMENT_TYPE,
            ATTACHMENT_NAME,
            ATTACHMENT_URL,
            OPERATOR_TYPE,
            OPERATOR_ID,
            OPERATOR_NAME,
            OPERATOR_MOBILE,
            SORT_NO
        )
        SELECT TEMP.* FROM (
        <foreach collection="attachmentList" item="attachment" separator="UNION ALL">
            SELECT
                #{attachment.boTaskAttachmentId},
                #{attachment.boTransTaskId},
                #{attachment.businessType},
                #{attachment.businessId},
                #{attachment.attachmentType},
                #{attachment.attachmentName},
                #{attachment.attachmentUrl},
                #{attachment.operatorType},
                #{attachment.operatorId},
                #{attachment.operatorName},
                #{attachment.operatorMobile},
                #{attachment.sortNo}
            FROM DUAL
        </foreach>
        ) TEMP
    </insert>

    <update id="batchDeleteTaskAttachment">
        <foreach collection="attachmentList" item="attachment" open="begin" close=";end;" separator=";">
            UPDATE T_BO_TASK_ATTACHMENT T
            SET T.IS_DEL = 1,
                T.OPERATOR_TYPE = #{attachment.operatorType},
                T.OPERATOR_ID = #{attachment.operatorId},
                T.OPERATOR_NAME = #{attachment.operatorName},
                T.OPERATOR_MOBILE = #{attachment.operatorMobile},
                T.LAST_MODIFIED_TIME = SYSDATE
            WHERE T.IS_DEL = 0
            AND T.BO_TASK_ATTACHMENT_ID = #{attachment.boTaskAttachmentId}
        </foreach>
    </update>

    <update id="batchUpdateTaskAttachment">
        <foreach collection="attachmentList" item="attachment" open="begin" close=";end;" separator=";">
            UPDATE T_BO_TASK_ATTACHMENT T
            SET T.ATTACHMENT_TYPE = #{attachment.attachmentType},
                T.ATTACHMENT_NAME = #{attachment.attachmentName},
                T.ATTACHMENT_URL = #{attachment.attachmentUrl},
                T.OPERATOR_TYPE = #{attachment.operatorType},
                T.OPERATOR_ID = #{attachment.operatorId},
                T.OPERATOR_NAME = #{attachment.operatorName},
                T.OPERATOR_MOBILE = #{attachment.operatorMobile},
                T.LAST_MODIFIED_TIME = SYSDATE
            WHERE T.IS_DEL = 0
            AND T.BO_TASK_ATTACHMENT_ID = #{attachment.boTaskAttachmentId}
        </foreach>
    </update>

    <select id="getTaskAttachmentMaxSortNo" resultType="int">
        SELECT NVL(MAX(T.SORT_NO), 0)
        FROM T_BO_TASK_ATTACHMENT T
        WHERE T.BUSINESS_ID = #{businessId}
        AND T.BUSINESS_TYPE = #{businessType}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="getTaskAttachmentList" resultType="BoTaskAttachmentBean">
        SELECT
            T.BO_TASK_ATTACHMENT_ID boTaskAttachmentId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.BUSINESS_TYPE businessType,
            T.BUSINESS_ID businessId,
            T.ATTACHMENT_TYPE attachmentType,
            T.ATTACHMENT_NAME attachmentName,
            T.ATTACHMENT_URL attachmentUrl,
            T.OPERATOR_TYPE operatorType,
            T.OPERATOR_ID operatorId,
            T.OPERATOR_NAME operatorName,
            T.OPERATOR_MOBILE operatorMobile,
            T.SORT_NO
        FROM T_BO_TASK_ATTACHMENT T
        WHERE T.IS_DEL = 0
        AND T.BUSINESS_TYPE = #{businessType}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY T.SORT_NO, T.CREATED_TIME
    </select>

    <select id="getTaskAttachmentListByBusiness" resultType="BoTaskAttachmentBean">
        SELECT
            T.BO_TASK_ATTACHMENT_ID boTaskAttachmentId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.BUSINESS_TYPE businessType,
            T.BUSINESS_ID businessId,
            T.ATTACHMENT_TYPE attachmentType,
            T.ATTACHMENT_NAME attachmentName,
            T.ATTACHMENT_URL attachmentUrl,
            T.OPERATOR_TYPE operatorType,
            T.OPERATOR_ID operatorId,
            T.OPERATOR_NAME operatorName,
            T.OPERATOR_MOBILE operatorMobile,
            T.SORT_NO
        FROM T_BO_TASK_ATTACHMENT T
        WHERE T.IS_DEL = 0
        AND T.BUSINESS_ID = #{businessId}
        AND T.BUSINESS_TYPE = #{businessType}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY T.SORT_NO, T.CREATED_TIME
    </select>

    <select id="getExistTaskAttachmentCount" resultType="Integer">
        SELECT
            NVL(COUNT(*), 0)
        FROM T_BO_TASK_ATTACHMENT T
        WHERE T.IS_DEL = 0
        AND T.BUSINESS_ID = #{businessId}
        AND T.BUSINESS_TYPE = #{businessType}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="batchGetTaskAttachmentList" resultType="BoTaskAttachmentBean">
        SELECT
            T.BO_TASK_ATTACHMENT_ID boTaskAttachmentId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.BUSINESS_TYPE businessType,
            T.BUSINESS_ID businessId,
            T.ATTACHMENT_TYPE attachmentType,
            T.ATTACHMENT_NAME attachmentName,
            T.ATTACHMENT_URL attachmentUrl,
            T.OPERATOR_TYPE operatorType,
            T.OPERATOR_ID operatorId,
            T.OPERATOR_NAME operatorName,
            T.OPERATOR_MOBILE operatorMobile,
            T.SORT_NO
        FROM T_BO_TASK_ATTACHMENT T
        WHERE T.IS_DEL = 0
        <if test="businessTypeList != null and businessTypeList.size() > 0">
            AND T.BUSINESS_TYPE IN
            <foreach collection="businessTypeList" item="businessType" open="(" separator="," close=")">
                #{businessType}
            </foreach>
        </if>
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
            #{boTransTaskId}
        </foreach>
        ORDER BY T.BUSINESS_TYPE, T.SORT_NO, T.CREATED_TIME
    </select>
    <select id="groupByBusinessType" resultType="com.wtyt.dao.bean.syf.BusinessTypeStatistics">
        SELECT
            T.BUSINESS_TYPE businessType,
            NVL(COUNT(*), 0) totalCount,
            MIN(T.SORT_NO) minSortNo,
            TO_CHAR(MAX(T.CREATED_TIME), 'yyyy-mm-dd hh24:mi:ss') lastCreatedTime
        FROM T_BO_TASK_ATTACHMENT T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.BUSINESS_TYPE IN
        <foreach collection="businessTypes" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
    </select>

</mapper>