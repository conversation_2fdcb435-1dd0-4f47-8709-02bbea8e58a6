<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoDhFieldDictMapper">


    <select id="queryBoDhFieldDictList" resultType="com.wtyt.dao.bean.syf.BoDhFiledDictBean">
      select BO_DH_FIELD_DICT_ID boDhFieldDictId,
             BUSINESS_TYPE       businessType,
             GROUP_TYPE          groupType,
             GROUP_CODE          groupCode,
             TABLE_NAME          tableName,
             FIELD_NAME          fieldName,
             FIELD_NAME_DESC     fieldNameDesc,
             FIELD_TYPE          fieldType,
             MAX_LENGTH          maxLength,
             NUMBER_PRECISION    numberPrecision,
             INPUT_CONFIG        inputConfigStr,
             SEARCH_CONFIG       searchConfigStr,
             HEADER_CONFIG       headerConfigStr
      from (SELECT BO_DH_FIELD_DICT_ID,
                   BUSINESS_TYPE,
                   GROUP_TYPE,
                   GROUP_CODE,
                   TABLE_NAME,
                   FIELD_NAME,
                   FIELD_NAME_DESC,
                   FIELD_TYPE,
                   MAX_LENGTH,
                   NUMBER_PRECISION,
                   INPUT_CONFIG,
                   SEARCH_CONFIG,
                   HEADER_CONFIG,
                   ROW_NUMBER() OVER (PARTITION BY TABLE_NAME,FIELD_NAME ORDER BY GROUP_CODE DESC) RN
            FROM T_BO_DH_FIELD_DICT d
            WHERE d.IS_DEL = 0
              and BUSINESS_TYPE = #{businessType}
              and GROUP_TYPE = #{groupType}
              AND (d.GROUP_CODE = #{groupCode} or d.GROUP_CODE = -1))
      where RN = 1 order by BO_DH_FIELD_DICT_ID asc
    </select>













</mapper>
