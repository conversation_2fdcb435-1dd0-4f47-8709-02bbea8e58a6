<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.UserColumnMapper">
    
    <delete id="delUserTemplateColumn">
      delete from  T_DL_USER_COLUMN c where
        c.UNIQUE_KEY = #{uniqueKey}
        and c.PAGE = #{page}
        and c.BUSINESS_TYPE = #{businessType}
    </delete>
    
    <insert id="insert">
        insert into T_DL_USER_COLUMN(DL_USER_COLUMN_ID,BUSINESS_TYPE,PAGE,ORG_ID,USER_ID,UNIQUE_KEY,DL_COLUMN_ID,COLUMN_NAME,SORT_NO)
        values (#{dlUserColumnId},#{businessType},#{page},#{orgId},#{userId},#{uniqueKey},#{dlColumnId},#{columnName},#{sortNo})
    </insert>
</mapper>