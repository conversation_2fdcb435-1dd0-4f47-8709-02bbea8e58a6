package com.wtyt.inquire.service;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wtyt.bo.bean.OrderOfferDelayBean;
import com.wtyt.bo.mapper.syf.BoTransOrderJoinMapper;
import com.wtyt.bo.service.BoTransOrderService;
import com.wtyt.bo.service.BusinessOperationService;
import com.wtyt.common.rpc.bean.*;
import com.wtyt.common.rpc.bean.base.BoTokenBean;
import com.wtyt.common.rpc.service.RpcTiService;
import com.wtyt.common.toolkits.GsonToolkit;
import com.wtyt.common.toolkits.ListToolkit;
import com.wtyt.commons.apollo.ApolloConfigCenter;
import com.wtyt.dao.mapper.syf.*;
import com.wtyt.inquire.bean.*;
import com.wtyt.common.rpc.service.RpcMMConfigService;
import com.wtyt.common.rpc.service.RpcMssuserService;
import com.wtyt.common.toolkits.NumberToolkit;
import com.wtyt.common.toolkits.StringToolkit;
import com.wtyt.dao.bean.syf.*;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.toolkits.CollectionToolkit;
import com.wtyt.money.commons.bean.ResDataBean;
import com.wtyt.money.commons.exception.BaseTipException;
import com.wtyt.pus.service.PusDataService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 业务运作订单询价相关
 * @createTime 2023/04/04 19:41:00
 */
@Service
public class BoInquireService {

    private static final Logger log = LoggerFactory.getLogger(BoInquireService.class);

    @Autowired
    private BoInquireMapper boInquireMapper;

    @Autowired
    private BoInquireAskMapper boInquireAskMapper;

    @Autowired
    private BoInquireUserRelMapper boInquireUserRelMapper;

    @Autowired
    private BoInquireOfferMapper boInquireOfferMapper;

    @Autowired
    private BoInquireOfferRecMapper boInquireOfferRecMapper;

    @Autowired
    private RpcMMConfigService rpcMMConfigService;

    @Autowired
    private RpcMssuserService rpcMssuserService;

    @Autowired
    private PusDataService pusDataService;

    @Autowired
    private ApolloConfigCenter apolloConfigCenter;

    @Autowired
    private BoTransOrderMapper boTransOrderMapper;

    @Autowired
    private BoOrderGroupRelMapper boOrderGroupRelMapper;

    @Autowired
    private BoTransOrderRelMapper boTransOrderRelMapper;

    @Autowired
    private BusinessOperationService businessOperationService;

    @Autowired
    private BoTransOrderJoinMapper boTransOrderJoinMapper;

    @Autowired
    private RpcTiService rpcTiService;

    public static final String AGREE_INFO = "%s通过审批";

    public static final String DATA_RESOURCE_CODE = "YWYZ-XJSP";

    public static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    @Transactional(rollbackFor = Exception.class,transactionManager = "syfTransactionManager")
    public void ask(Req5329124IBean data) throws Exception {
        if (StringUtils.isBlank(data.getMqFlag())){
            StringToolkit.notBlank("项目ID不能为空", data.getOrgId());
            StringToolkit.notBlank("用户ID不能为空", data.getUserId());
        }
        if (CollectionUtils.isEmpty(data.getOrderIdList())) {
            throw new BaseTipException("订单ID不能为空");
        }
        if (!"1".equals(data.getAskType()) && !"2".equals(data.getAskType())){
            throw new BaseTipException("askType不合法");
        }
        List<BoInquireBean> boInquireBeans = boInquireMapper.selectInquireList(data.getOrderIdList());
        if (CollectionUtils.isEmpty(boInquireBeans)) {
            throw new BaseTipException("订单记录不存在");
        }
        List<String> groupIdList = boInquireBeans.stream().filter(e -> StringUtils.isNotBlank(e.getGroupId())).map(BoInquireBean::getGroupId).collect(Collectors.toList());
        //查询项目部下所有调度
        Rpc19049IBean rpc19049IBean = new Rpc19049IBean();
        rpc19049IBean.setOrgId(data.getOrgId());
        rpc19049IBean.setRoleId("12000");
        rpc19049IBean.setGroupIds(String.join(",", groupIdList));
        Rpc19049OBean rpc19049OBean = rpcMMConfigService.rpc19049(rpc19049IBean);
        if (null == rpc19049OBean || CollectionUtils.isEmpty(rpc19049OBean.getUserList())) {
            throw new BaseTipException("询价失败，未查询到调度信息");
        }
        List<BoInquireAskBean> askList = new ArrayList<>();
        List<BoInquireUserRelBean> userRelList = new ArrayList<>();
        for (BoInquireBean boInquireBean : boInquireBeans) {
            if (StringUtils.isNotBlank(data.getMqFlag())) {
                if ("1".equals(boInquireBean.getInquireState()) || "3".equals(boInquireBean.getInquireState())) {
                    log.info("订单询价超时处理：订单ID：{}已无法询价，跳过", boInquireBean.getBoTransOrderId());
                    continue;
                }
            } else {
                if (!"0".equals(boInquireBean.getInquireState())) {
                    log.info("订单ID：{}已无法询价，跳过", boInquireBean.getBoTransOrderId());
                    continue;
                }
            }
            for (Rpc19049OBean oBean : rpc19049OBean.getUserList()) {
                if (StringUtils.isBlank(oBean.getGroupId())){
                    continue;
                }
                List<String> userGroupIdList = new ArrayList<>(Arrays.asList(oBean.getGroupId().split(",")));
                if (userGroupIdList.contains(boInquireBean.getGroupId())){
                    if ("1".equals(data.getAskType())){
                        BoInquireAskBean boInquireAskBean = new BoInquireAskBean();
                        boInquireAskBean.setBoInquireAskId(UidToolkit.generateUidString());
                        boInquireAskBean.setAskState("0");
                        boInquireAskBean.setBoTransOrderId(boInquireBean.getBoTransOrderId());
                        boInquireAskBean.setCreatedUserId(data.getUserId());
                        boInquireAskBean.setUserId(oBean.getUserId());
                        askList.add(boInquireAskBean);
                    }
                    if ("2".equals(data.getAskType())){
                        BoInquireUserRelBean boInquireUserRelBean = new BoInquireUserRelBean();
                        boInquireUserRelBean.setBoInquireUserRelId(UidToolkit.generateUidString());
                        boInquireUserRelBean.setCreatedUserId(data.getUserId());
                        boInquireUserRelBean.setUserId(oBean.getUserId());
                        boInquireUserRelBean.setBoTransOrderId(boInquireBean.getBoTransOrderId());
                        userRelList.add(boInquireUserRelBean);
                    }
                }
            }
            //更新询价状态
            BoInquireBean updateBean = new BoInquireBean();
            updateBean.setBoInquireId(boInquireBean.getBoInquireId());
            updateBean.setInquireState("2".equals(data.getAskType())? "1" : "2");
            if (StringUtils.isBlank(data.getMqFlag())){
                updateBean.setInquireRealState("2".equals(data.getAskType())? "1" : "2");
            }
            updateBean.setAskUserId(data.getUserId());
            updateBean.setAskTime(sdf.format(new Date()));
            if (StringUtils.isNotBlank(data.getMqFlag())){
                updateBean.setInquireState("1");
                updateBean.setAskTime(null);
                updateBean.setInquireOverTimeState(data.getOverTimeType());
            }
            boInquireMapper.updateByPk(updateBean);
            if (StringUtils.isBlank(data.getMqFlag())){
                OrderOfferDelayBean delayBean = new OrderOfferDelayBean();
                delayBean.setOrderId(boInquireBean.getBoTransOrderId());
                delayBean.setOverTimeType("2");
                pusDataService.sendDelayMessage(GsonToolkit.beanToJson(delayBean),
                        Long.parseLong(apolloConfigCenter.tcoCommon.getProperty("order.offer.overTime.notOffer")),
                        boInquireBean.getBoTransOrderId() + "-overTimeState-" + delayBean.getOverTimeType());
                delayBean.setOverTimeType("3");
                pusDataService.sendDelayMessage(GsonToolkit.beanToJson(delayBean),
                        Long.parseLong(apolloConfigCenter.tcoCommon.getProperty("order.offer.overTime.notApply")),
                        boInquireBean.getBoTransOrderId() +  "-overTimeState-" + delayBean.getOverTimeType());
            }
        }
        if ("1".equals(data.getAskType())
                && CollectionUtils.isNotEmpty(askList)){
            boInquireAskMapper.insert(askList);
        }
        if ("2".equals(data.getAskType())
                && CollectionUtils.isNotEmpty(userRelList)){
            boInquireUserRelMapper.insert(userRelList);
        }
    }

    @Transactional(rollbackFor = Exception.class,transactionManager = "syfTransactionManager")
    public void apply(Req5329127IBean data) throws Exception {
        StringToolkit.notBlank("项目ID不能为空", data.getOrgId());
        StringToolkit.notBlank("用户ID不能为空", data.getUserId());
        StringToolkit.notBlank("offerId不能为空", data.getBoInquireOfferId());
        BoInquireOfferBean boInquireOfferBean = boInquireOfferMapper.selectByPkId(data.getBoInquireOfferId());
        if (null == boInquireOfferBean){
            throw new BaseTipException("报价ID不存在");
        }
        if (!"1".equals(boInquireOfferBean.getApplyState())){
            throw new BaseTipException("此报价已完成审批");
        }
        if (!"2".equals(boInquireOfferBean.getInquireState())){
            throw new BaseTipException("此订单已完成询价");
        }
        //将此报价更新为通过，其他报价更新为不通过
        List<BoInquireOfferBean> boInquireOfferBeans = boInquireOfferMapper.selectByOrderId(boInquireOfferBean.getBoTransOrderId(), "2");
        for (BoInquireOfferBean inquireOfferBean : boInquireOfferBeans) {
            BoInquireOfferBean updateBean = new BoInquireOfferBean();
            updateBean.setBoInquireOfferId(inquireOfferBean.getBoInquireOfferId());
            updateBean.setApplyUserId(data.getUserId());
            //更新审批状态
            if (data.getBoInquireOfferId().equals(inquireOfferBean.getBoInquireOfferId())){
                //通过
                updateBean.setApplyState("2");
                updateBean.setAgreeTime(sdf.format(new Date()));
                //插入订单和调度关系表
                BoInquireUserRelBean boInquireUserRelBean = new BoInquireUserRelBean();
                boInquireUserRelBean.setBoInquireUserRelId(UidToolkit.generateUidString());
                boInquireUserRelBean.setCreatedUserId(data.getUserId());
                boInquireUserRelBean.setUserId(inquireOfferBean.getCreatedUserId());
                boInquireUserRelBean.setBoTransOrderId(inquireOfferBean.getBoTransOrderId());
                boInquireUserRelMapper.insert(Collections.singletonList(boInquireUserRelBean));
                //将offerId更新到询价主表
                BoInquireBean updateInquireBean = new BoInquireBean();
                updateInquireBean.setBoInquireId(inquireOfferBean.getBoInquireId());
                updateInquireBean.setBoInquireOfferId(data.getBoInquireOfferId());
                updateInquireBean.setBoInquireId(inquireOfferBean.getBoInquireId());
                updateInquireBean.setInquireState("3");
                updateInquireBean.setInquireRealState("3");
                boInquireMapper.updateByPk(updateInquireBean);
            }else{
                //未通过
                updateBean.setApplyState("3");
            }
            boInquireOfferMapper.update(updateBean);
        }
    }

    public Req5329126OBean applyList(Req5329126IBean data) throws Exception{
        StringToolkit.notBlank("项目ID不能为空", data.getOrgId());
        StringToolkit.notBlank("用户ID不能为空", data.getUserId());
        StringToolkit.notBlank("type不能为空", data.getType());
        if (null == data.getPageNo() || null == data.getPageSize()){
            throw new BaseTipException("分页参数不能为空");
        }
        if (!"1".equals(data.getType()) && !"2".equals(data.getType())){
            throw new BaseTipException("type参数不合法");
        }
        //兼容前端只传年月日
        if(StringUtils.isNotBlank(data.getStartTime())
                && StringUtils.isNotBlank(data.getEndTime())){
            data.setStartTime(data.getStartTime() + " 00:00:00");
            data.setEndTime(data.getEndTime() + " 23:59:59");
        }
        Req5329126OBean result = new Req5329126OBean();
        PageHelper.startPage(data.getPageNo(),data.getPageSize());
        List<Req5329126OBean> list = boInquireOfferMapper.selectApplyList(data);
        String defaultHeadImg = apolloConfigCenter.tcoCommon.getProperty("bo.manager.default.head.img");
        for (Req5329126OBean req5329126OBean : list) {
            req5329126OBean.setStartAddress(StringToolkit.getBlankStr(req5329126OBean.getStartProvinceName())
                    + StringToolkit.getBlankStr(req5329126OBean.getStartCityName())
                    + StringToolkit.getBlankStr(req5329126OBean.getStartCountyName())
                    + StringToolkit.getBlankStr(req5329126OBean.getStartAddress()));
            req5329126OBean.setEndAddress(StringToolkit.getBlankStr(req5329126OBean.getEndProvinceName())
                    + StringToolkit.getBlankStr(req5329126OBean.getEndCityName())
                    + StringToolkit.getBlankStr(req5329126OBean.getEndCountyName())
                    + StringToolkit.getBlankStr(req5329126OBean.getEndAddress()));
            String goodsInfo = "";
            if (StringUtils.isNotBlank(req5329126OBean.getGoodsName())){
                goodsInfo += req5329126OBean.getGoodsName();
            }
            if (StringUtils.isNotBlank(req5329126OBean.getGoodsWeight())){
                if (StringUtils.isNotBlank(goodsInfo)){
                    goodsInfo += ",";
                }
                goodsInfo += req5329126OBean.getGoodsWeight() + "吨";
            }
            if (StringUtils.isNotBlank(req5329126OBean.getGoodsVolume())){
                if (StringUtils.isNotBlank(goodsInfo)){
                    goodsInfo += ",";
                }
                goodsInfo += req5329126OBean.getGoodsWeight() + "方";
            }
            req5329126OBean.setGoodsInfo(goodsInfo);
            Map<String, List<Rpc19025OBean>> userMap = new HashMap<>();
            if ("1".equals(data.getType())){
                //待审批列表
                //查询所有待审批报价
                List<BoInquireOfferBean> boInquireOfferBeans = boInquireOfferMapper.selectByOrderId(req5329126OBean.getBoTransOrderId(), "2");
                if (CollectionUtils.isNotEmpty(boInquireOfferBeans)){
                    List<String> userIdList = boInquireOfferBeans.stream().map(BoInquireOfferBean::getCreatedUserId).collect(Collectors.toList());
                    Rpc19025IBean iBean = new Rpc19025IBean();
                    iBean.setUserIds(userIdList);
                    Rpc19025OBean oBean = rpcMssuserService.rpc19025(iBean);
                    if (oBean != null && oBean.getResult() != null && CollectionUtils.isNotEmpty(oBean.getResult().getUserInfoList())) {
                        userMap = oBean.getResult().getUserInfoList().stream().filter(e -> StringUtils.isNotBlank(e.getUserId())).collect(Collectors.groupingBy(Rpc19025OBean::getUserId));
                    }
                    for (BoInquireOfferBean boInquireOfferBean : boInquireOfferBeans) {
                        if (StringUtils.isNotBlank(boInquireOfferBean.getOilRatio())){
                            boInquireOfferBean.setOilRatio(NumberToolkit.getPercent(boInquireOfferBean.getOilRatio() ,2));
                        }
                        //调用19001查询用户信息
//                        Rpc19001IBean rpc19001IBean = new Rpc19001IBean();
//                        rpc19001IBean.setUserId(boInquireOfferBean.getCreatedUserId());
//                        Rpc19001OBean rpc19001OBean = rpcMssuserService.rpc19001(rpc19001IBean);
                        List<Rpc19025OBean> rpc19025OBeans = userMap.get(boInquireOfferBean.getCreatedUserId());
                        if (CollectionUtils.isNotEmpty(rpc19025OBeans)){
                            boInquireOfferBean.setRealName(rpc19025OBeans.get(0).getRealName());
                        }
                        boInquireOfferBean.setHeadImg(defaultHeadImg);

                    }
                }
                req5329126OBean.setOfferList(boInquireOfferBeans);
            }
            if ("2".equals(data.getType())){
                //查询审核通过的报价
                BoInquireOfferBean boInquireOfferBean = boInquireOfferMapper.selectByPkId(req5329126OBean.getBoInquireOfferId());
                if (null == boInquireOfferBean){
                    throw new BaseTipException("获取已报价记录失败");
                }
                Rpc19001IBean rpc19001IBean = new Rpc19001IBean();
                rpc19001IBean.setUserId(boInquireOfferBean.getCreatedUserId());
                Rpc19001OBean rpc19001OBean = rpcMssuserService.rpc19001(rpc19001IBean);
                if (null != rpc19001OBean){
                    req5329126OBean.setApplyInfo(String.format(AGREE_INFO,rpc19001OBean.getRealName()));
                }
                req5329126OBean.setApplyTime(boInquireOfferBean.getApplyTime());
                req5329126OBean.setAllFee(boInquireOfferBean.getAllFee());
                req5329126OBean.setOilFee(boInquireOfferBean.getOilFee());
                if (StringUtils.isNotBlank(boInquireOfferBean.getOilRatio())){
                    req5329126OBean.setOilRatio(NumberToolkit.getPercent(boInquireOfferBean.getOilRatio() ,2));
                }
            }
        }
        PageInfo<Req5329126OBean> pageInfo = new PageInfo<>(list);
        result.setResultList(list);
        result.setTotalPages(String.valueOf(pageInfo.getPages()));
        result.setTotalRecords(String.valueOf(pageInfo.getTotal()));
        return result;
    }

    public Req5329138OBean applyListCount(BoTokenBean data) throws Exception {
        StringToolkit.notBlank("项目ID不能为空", data.getOrgId());
        StringToolkit.notBlank("用户ID不能为空", data.getUserId());
        Req5329138OBean req5329138OBean = new Req5329138OBean();
        req5329138OBean.setWaitApplyCount(boInquireMapper.countApplyCount(data.getOrgId() ,"1"));
        req5329138OBean.setCheckedCount(boInquireMapper.countApplyCount(data.getOrgId() ,"2"));
        return req5329138OBean;
    }

    public ResDataBean offerList(BoAskIBean data) throws Exception{
        //参数校验
        StringToolkit.notBlank("报价状态不能为空",data.getState());
        StringToolkit.notBlank("用户ID不能为空",data.getUserId());
        StringToolkit.notBlank("项目ID不能为空", data.getOrgId());
        String pageNo = StringUtils.defaultIfBlank(data.getPageNo(),"1");
        String pageSize = StringUtils.defaultIfBlank(data.getPageSize(), "30");
        CollectionToolkit.limit("订单号数量最大50条", data.getCustomerOrderNoList(),50);
        StringToolkit.isOnlyNumber("页码不合法",pageNo);
        StringToolkit.isOnlyNumber("分页不合法",pageSize);
        String isDebangMode = debangMode(data.getOrgId());
        data.setIsDebangMode(isDebangMode);
        Req53291231OBean req53291231OBean = new Req53291231OBean();
        PageHelper.startPage(Integer.valueOf(pageNo), Integer.valueOf(pageSize));
        List<BoAskOBean> boAskOBeans = new ArrayList<>();
        if("0".equals(isDebangMode)){
            boAskOBeans = boInquireAskMapper.getOfferList(data);
        }
        if("1".equals(isDebangMode)){
            boAskOBeans = boInquireAskMapper.getOfferListForDebang(data);
        }
        PageInfo<BoAskOBean> pageInfo = new PageInfo<>(boAskOBeans);
        List<BoAskOBean> list = pageInfo.getList();
        //处理list数据
        for (BoAskOBean curBean:list) {
            curBean.setGoodsInfo(desc(curBean.getGoodsName(),",")
                    +desc(curBean.getGoodsWeight(),"吨,")+desc(curBean.getGoodsVolume(),"方"));
            curBean.setGoodsInfo(removeLastStr(curBean.getGoodsInfo(),","));
            if(curBean.getOfferInfo() != null && StringUtils.isNotBlank(curBean.getOfferInfo().getOilRatio())){
                curBean.getOfferInfo().setOilRatio(NumberToolkit.getPercent(curBean.getOfferInfo().getOilRatio(),2));
            }
        }
        req53291231OBean.setTotalRecords(String.valueOf(pageInfo.getTotal()));
        req53291231OBean.setTotalPages(String.valueOf(pageInfo.getPages()));
        req53291231OBean.setPageNum(pageInfo.getPageNum() + "");
        req53291231OBean.setResultList(list);
        req53291231OBean.setIsDebangMode(isDebangMode);
        return new ResDataBean().success(req53291231OBean);
    }

    private String desc(String s,String t){
        if(StringUtils.isNotBlank(s)){
            return s+t;
        }
        return "";
    }

    public String debangMode(String orgId){
        String isDebangMode = "0";
        String debangModeOrgStr = apolloConfigCenter.tcoCommon.getProperty("debang.mode.org.list");
        if(debangModeOrgStr.contains(orgId)){
            isDebangMode = "1";
        }
        return isDebangMode;
    }


    private String removeLastStr(String s,String t){
        if(StringUtils.isNotBlank(s)){
            String p = s.substring(s.length()-1,s.length());
            if(p.equals(t)){
                return s.substring(0,s.length()-1);
            }else {
                return s;
            }
        }
        return "";
    }

    public ResDataBean offerCount(BoAskIBean data) throws Exception{
        //参数校验
        String isDebangMode = debangMode(data.getOrgId());
        StringToolkit.notBlank("项目ID不能为空", data.getOrgId());
        StringToolkit.notBlank("用户ID不能为空",data.getUserId());
        CollectionToolkit.limit("订单号数量最大50条", data.getCustomerOrderNoList(),50);
        Req5329131OBean req5329131OBean = new Req5329131OBean();
        req5329131OBean.setAskedCount("0");
        req5329131OBean.setWaitAskCount("0");
        //先查询待报价
        data.setState("0");
        int offerListCount = 0;
        if("1".equals(isDebangMode)){
             offerListCount = boInquireAskMapper.getOfferListCountForDebang(data);
        }else {
             offerListCount = boInquireAskMapper.getOfferListCount(data);
        }
        req5329131OBean.setWaitAskCount(String.valueOf(offerListCount));
        //再查询已报价
        data.setState("1");
        if("1".equals(isDebangMode)){
            offerListCount = boInquireAskMapper.getOfferListCountForDebang(data);
        }else {
            offerListCount = boInquireAskMapper.getOfferListCount(data);
        }
        req5329131OBean.setAskedCount(String.valueOf(offerListCount));
        return new ResDataBean().success(req5329131OBean);
    }

    public ResDataBean offer(Req53291232IBean data) throws Exception {

        String isDebangMode = debangMode(data.getOrgId());
        //德邦报价
        if("1".equals(isDebangMode)){
            return doDebangOffer(data);
        }

        StringToolkit.notBlank("总运费不能为空",data.getAllFee());
        StringToolkit.notBlank("订单ID不能为空",data.getBoTransOrderId());
        StringToolkit.notBlank("询价ID不能为空",data.getBoInquireAskId());
        StringToolkit.notBlank("用户ID不能为空",data.getUserId());
        if(!NumberToolkit.isParsable(data.getAllFee())){
            throw new BaseTipException("运费金额必须为数字");
        }
        if(StringUtils.isNotBlank(data.getOilFee()) && !NumberToolkit.isParsable(data.getOilFee())){
            throw new BaseTipException("运费金额必须为数字");
        }
        BigDecimal bigAllFee = new BigDecimal(data.getAllFee());
        if(bigAllFee.compareTo(new BigDecimal(0)) <= 0 || bigAllFee.compareTo(new BigDecimal(1000000)) >= 0){
            throw new BaseTipException("运费金额必须是(0-100万)之间");
        }
        String oilAatio = "";
        if(StringUtils.isNotBlank(data.getOilFee())){
            //总金额>用油金额
            BigDecimal bigOilFee = new BigDecimal(data.getOilFee());
            if(bigOilFee.compareTo(new BigDecimal(0)) <= 0){
                throw new BaseTipException("用油金额必须大于0");
            }
            if(bigAllFee.compareTo(bigOilFee) <= 0){
                throw new BaseTipException("总金额必须大于用油金额");
            }
            oilAatio = bigOilFee.divide(bigAllFee,4, BigDecimal.ROUND_HALF_UP).toPlainString();
        }
        //判断报价是否超时
        String inquireState = boInquireAskMapper.getInquireState(data.getBoTransOrderId());
        if(StringUtils.isBlank(inquireState)){
            throw new BaseTipException("订单不存在，不可报价");
        }
        if("1".equals(inquireState)){
            throw new BaseTipException("当前订单已超时，不可报价");
        }

        if("3".equals(inquireState)){
            throw new BaseTipException("当前订单已被审批，不可报价");
        }

        //组装BoInquireOfferBean
        BoInquireOfferBean offerBean = new BoInquireOfferBean();
        offerBean.setAllFee(data.getAllFee());
        offerBean.setOilFee(data.getOilFee());
        offerBean.setOilRatio(oilAatio);

        //组装BoInquireOfferRecBean
        BoInquireOfferRecBean inquireOfferRecBean = new BoInquireOfferRecBean();
        inquireOfferRecBean.setBoInquireOfferId(offerBean.getBoInquireOfferId());
        inquireOfferRecBean.setBoInquireOfferRecId(UidToolkit.generateUidString());
        inquireOfferRecBean.setBoTransOrderId(data.getBoTransOrderId());
        inquireOfferRecBean.setCreatedUserId(data.getUserId());
        inquireOfferRecBean.setAllFee(data.getAllFee());
        inquireOfferRecBean.setOilFee(data.getOilFee());
        inquireOfferRecBean.setOilRatio(oilAatio);

        //判断当前调度是不是首次报价
        String boInquireOfferId = boInquireAskMapper.checkOfferState(data.getBoInquireAskId(),data.getUserId());

        if(StringUtils.isBlank(boInquireOfferId)){
            offerBean.setApplyState("1");
            offerBean.setCreatedUserId(data.getUserId());
            offerBean.setBoInquireAskId(data.getBoInquireAskId());
            offerBean.setBoTransOrderId(data.getBoTransOrderId());
            offerBean.setBoInquireOfferId(UidToolkit.generateUidString());
            //插入offer表，offer_record表
            boInquireOfferMapper.insertOffer(offerBean);
            inquireOfferRecBean.setBoInquireOfferId(offerBean.getBoInquireOfferId());
            boInquireOfferRecMapper.insertOfferRec(inquireOfferRecBean);
            //更新首次报价时间
            boInquireMapper.updateFirstOfferTime(data.getBoTransOrderId());
            //更新ask表的状态
            boInquireAskMapper.updateAskStatus(data.getBoInquireAskId());
//            //发送MQ
//            OrderOfferDelayBean delayBean = new OrderOfferDelayBean();
//            delayBean.setOrderId(offerBean.getBoTransOrderId());
//            delayBean.setOverTimeType("3");
//            String pushData = new Gson().toJson(delayBean);
//            pusDataService.sendDelayMessage(pushData,Long.parseLong(apolloConfigCenter.tcoCommon.getProperty("order.offer.overTime.notApply")),"");
        }else {
            //更新offer表，插入offer_record表
            offerBean.setBoInquireOfferId(boInquireOfferId);
            offerBean.setOfferTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
            boInquireOfferMapper.update(offerBean);
            inquireOfferRecBean.setBoInquireOfferId(boInquireOfferId);
            boInquireOfferRecMapper.insertOfferRec(inquireOfferRecBean);
        }
        return new ResDataBean().success();
    }

    private ResDataBean doDebangOffer(Req53291232IBean data) throws Exception{

        StringToolkit.notBlank("总运费不能为空",data.getAllFee());
        StringToolkit.notBlank("订单ID不能为空",data.getBoTransOrderId());
        StringToolkit.notBlank("用户ID不能为空",data.getUserId());
        StringToolkit.notBlank("询价ID不能为空",data.getBoInquireAskId());
        if(!NumberToolkit.isParsable(data.getAllFee())){
            throw new BaseTipException("运费金额必须为数字");
        }
        BigDecimal bigAllFee = new BigDecimal(data.getAllFee());
        if(bigAllFee.compareTo(new BigDecimal(0)) <= 0 || bigAllFee.compareTo(new BigDecimal(1000000)) >= 0){
            throw new BaseTipException("运费金额必须是(0-100万)之间");
        }
        String bigAllFeeStr = bigAllFee.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString();
        //判断这个订单是不是已经报过价，已报价的不允许再报价
        String boInquireOfferId = boInquireAskMapper.checkOfferStateByOrderId(data.getBoTransOrderId());
        if(StringUtils.isNotBlank(boInquireOfferId)){
            throw new BaseTipException("该笔订单已报价，不可重复报价");
        }
        //组装BoInquireOfferBean
        BoInquireOfferBean offerBean = new BoInquireOfferBean();
        offerBean.setAllFee(bigAllFeeStr);
        offerBean.setApplyState("1");
        offerBean.setCreatedUserId(data.getUserId());
        offerBean.setBoInquireAskId(data.getBoInquireAskId());
        offerBean.setBoTransOrderId(data.getBoTransOrderId());
        offerBean.setBoInquireOfferId(UidToolkit.generateUidString());
        //插入offer表，offer_record表
        boInquireOfferMapper.insertOffer(offerBean);
        //更新ask表的状态
        boInquireAskMapper.updateAskStatus(data.getBoInquireAskId());
        //调用TI组接口
        Rpc70065IBean iBean = new Rpc70065IBean();
        //根据订单ID查询订单号
        iBean.setCustomerOrderNo(boTransOrderMapper.getCustomerNoByOrderId(data.getBoTransOrderId()));
        iBean.setPrice(bigAllFeeStr);
        rpcTiService.rpc70065(iBean);
        return new ResDataBean().success();
    }

    public Req5329128OBean applyDetail(Req5329128IBean data) throws Exception {
        StringToolkit.notBlank("项目ID不能为空", data.getOrgId());
        StringToolkit.notBlank("用户ID不能为空", data.getUserId());
        StringToolkit.notBlank("订单ID不能为空", data.getBoTransOrderId());
        Req5329128OBean result = new Req5329128OBean();
        BoInquireBean boInquireBean = boInquireMapper.selectInquire(data.getBoTransOrderId());
        if (null == boInquireBean){
            throw new BaseTipException("订单不存在");
        }
        List<InquireNodeBean> resultNodeList = new ArrayList<>();
        //查询询价信息
        //下发询价节点
        InquireNodeBean askNodeBean = new InquireNodeBean();
        askNodeBean.setListFlag("0");
        askNodeBean.setNodeName("下发询价");
        askNodeBean.setNodeTime(boInquireBean.getAskTime());
        //调用19001查询用户信息
        Rpc19001IBean rpc19001IBean = new Rpc19001IBean();
        rpc19001IBean.setUserId(boInquireBean.getAskUserId());
        Rpc19001OBean rpc19001OBean = rpcMssuserService.rpc19001(rpc19001IBean);
        askNodeBean.setNodeContent("内勤" +
                (null != rpc19001OBean && StringUtils.isNotBlank(rpc19001OBean.getRealName()) ? ("-" + rpc19001OBean.getRealName()) :"")
                + "发起了询价");
        resultNodeList.add(askNodeBean);
        //报价节点
        List<OfferInfoBean> allAskList = boInquireOfferMapper.selectAskDetailInfoList(data.getBoTransOrderId());
        List<InquireNodeBean> offerNodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allAskList)){
            Map<String, List<Rpc19025OBean>> userMap = new HashMap<>();
            List<String> userIdList = allAskList.stream().map(OfferInfoBean::getUserId).collect(Collectors.toList());
            Rpc19025IBean iBean = new Rpc19025IBean();
            iBean.setUserIds(userIdList);
            Rpc19025OBean oBean = rpcMssuserService.rpc19025(iBean);
            if (oBean != null && oBean.getResult() != null && CollectionUtils.isNotEmpty(oBean.getResult().getUserInfoList())) {
                userMap = oBean.getResult().getUserInfoList().stream().filter(e -> StringUtils.isNotBlank(e.getUserId())).collect(Collectors.groupingBy(Rpc19025OBean::getUserId));
            }
            for (OfferInfoBean offerInfoBean : allAskList) {
                if (StringUtils.isNotBlank(offerInfoBean.getOilRatio())){
                    offerInfoBean.setOilRatio(NumberToolkit.getPercent(offerInfoBean.getOilRatio(), 2));
                }
                String realName = "";
                if (StringUtils.isNotBlank(offerInfoBean.getUserId())){
                    List<Rpc19025OBean> rpc19025OBeans = userMap.get(offerInfoBean.getUserId());
                    if (CollectionUtils.isNotEmpty(rpc19025OBeans)){
                        offerInfoBean.setRealName(rpc19025OBeans.get(0).getRealName());
                    }
                    //调用19001查询用户信息
//                    Rpc19001IBean rpc19001param = new Rpc19001IBean();
//                    rpc19001param.setUserId(offerInfoBean.getUserId());
//                    Rpc19001OBean rpc19001Result = rpcMssuserService.rpc19001(rpc19001param);
//                    offerInfoBean.setRealName(null != rpc19001Result && StringUtils.isNotBlank(rpc19001Result.getRealName()) ? rpc19001Result.getRealName() : null);
                    realName = offerInfoBean.getRealName();
                }

                InquireNodeBean offerNodeBean = new InquireNodeBean();
                offerNodeBean.setNodeTime(offerInfoBean.getOfferTime());
                offerNodeBean.setListFlag("0");
                offerNodeBean.setNodeContent("调度-" + realName + "发起了报价");
                if (StringUtils.isBlank(offerInfoBean.getBoInquireOfferId())){
                    continue;
                }
                offerNodeList.add(offerNodeBean);
            }
        }
        InquireNodeBean offerInfoNode = new InquireNodeBean();
        offerInfoNode.setNodeName("报价");
        offerInfoNode.setListFlag("1");
        //按报价时间倒序排序
        offerNodeList = offerNodeList.stream().sorted(Comparator.comparing(InquireNodeBean::getNodeTime).reversed()).collect(Collectors.toList());
        offerInfoNode.setChildList(offerNodeList);
        resultNodeList.add(offerInfoNode);
        //审批节点
        if ("3".equals(boInquireBean.getInquireState())
                && StringUtils.isNotBlank(boInquireBean.getBoInquireOfferId())){
            BoInquireOfferBean boInquireOfferBean = boInquireOfferMapper.selectByPkId(boInquireBean.getBoInquireOfferId());
            InquireNodeBean applyNode = new InquireNodeBean();
            applyNode.setListFlag("0");
            applyNode.setNodeName("审批");
            applyNode.setNodeTime(boInquireOfferBean.getApplyTime());
            //调用19001查询用户信息
            Rpc19001IBean rpc19001param = new Rpc19001IBean();
            rpc19001param.setUserId(boInquireOfferBean.getApplyUserId());
            Rpc19001OBean rpc19001Result = rpcMssuserService.rpc19001(rpc19001param);
            applyNode.setNodeContent("项目经理" +
                    (null != rpc19001Result && StringUtils.isNotBlank(rpc19001Result.getRealName()) ? ("-" + rpc19001Result.getRealName()) :"")
                    + "确认了报价");
            resultNodeList.add(applyNode);
        }
        result.setAskInfo(resultNodeList);

        //查询订单信息
        List<BoTransOrderBean> orderByPkIds = boTransOrderMapper.getOrderByPkIds(Collections.singletonList(boInquireBean.getBoTransOrderId()));
        if (CollectionUtils.isEmpty(orderByPkIds)){
            throw new BaseTipException("订单信息不存在");
        }
        BoTransOrderBean boTransOrderBean = orderByPkIds.get(0);
        OrderInfoBean orderInfoBean = new OrderInfoBean();
        orderInfoBean.setCustomerOrderNo(boTransOrderBean.getCustomerOrderNo());
        String goodsInfo = "";
        if (StringUtils.isNotBlank(boTransOrderBean.getGoodsName())){
            goodsInfo += boTransOrderBean.getGoodsName();
        }
        if (StringUtils.isNotBlank(boTransOrderBean.getGoodsWeight())){
            if (StringUtils.isNotBlank(goodsInfo)){
                goodsInfo += ",";
            }
            goodsInfo += boTransOrderBean.getGoodsWeight() + "吨";
        }
        if (StringUtils.isNotBlank(boTransOrderBean.getGoodsVolume())){
            if (StringUtils.isNotBlank(goodsInfo)){
                goodsInfo += ",";
            }
            goodsInfo += boTransOrderBean.getGoodsWeight() + "方";
        }
        orderInfoBean.setGoodsInfo(goodsInfo);
        orderInfoBean.setStartAddress(StringToolkit.getBlankStr(boTransOrderBean.getStartProvinceName())
                + StringToolkit.getBlankStr(boTransOrderBean.getStartCityName())
                + StringToolkit.getBlankStr(boTransOrderBean.getStartCountyName())
                + StringToolkit.getBlankStr(boTransOrderBean.getStartAddress()));
        orderInfoBean.setEndAddress(StringToolkit.getBlankStr(boTransOrderBean.getEndProvinceName())
                + StringToolkit.getBlankStr(boTransOrderBean.getEndCityName())
                + StringToolkit.getBlankStr(boTransOrderBean.getEndCountyName())
                + StringToolkit.getBlankStr(boTransOrderBean.getEndAddress()));
        orderInfoBean.setMileage(boTransOrderBean.getMileage());
        result.setOrderInfo(orderInfoBean);
        result.setOfferList(allAskList);
        return result;
    }

    public Req5329129OBean offerRecord(Req5329127IBean data) throws Exception {
        StringToolkit.notBlank("项目ID不能为空", data.getOrgId());
        StringToolkit.notBlank("用户ID不能为空", data.getUserId());
        StringToolkit.notBlank("报价ID不能为空", data.getBoInquireOfferId());
        List<BoInquireOfferRecBean> list = boInquireOfferRecMapper.selectByOfferId(data.getBoInquireOfferId());
        for (BoInquireOfferRecBean boInquireOfferRecBean : list) {
            if (StringUtils.isNotBlank(boInquireOfferRecBean.getOilRatio())){
                boInquireOfferRecBean.setOilRatio(NumberToolkit.getPercent(boInquireOfferRecBean.getOilRatio(),2));
            }
        }
        Req5329129OBean req5329129OBean = new Req5329129OBean();
        req5329129OBean.setOfferRecordList(list);
        return req5329129OBean;
    }

    public Req5329142OBean getInquireInfo(Req5329124IBean data) throws Exception {
        if (CollectionUtils.isEmpty(data.getOrderIdList()) && StringUtils.isAllBlank(data.getBoTransTaskId(),data.getTaxWaybillId())){
            throw new BaseTipException("订单ID和运输任务ID不能同时为空");
        }
        Req5329142OBean result = new Req5329142OBean();
        if (StringUtils.isNotBlank(data.getTaxWaybillId())
                || StringUtils.isNotBlank(data.getBoTransTaskId())){
            if (StringUtils.isBlank(data.getBoTransTaskId())){
                data.setBoTransTaskId(businessOperationService.getBoTransTaskId(data.getTaxWaybillId()));
            }
//            List<BoTransOrderRelBean> orderRelBeanList = boTransOrderRelMapper.selectByTaxWaybillId(Collections.singletonList(data.getTaxWaybillId()));
            List<BoTransOrderRelBean> orderRelBeanList = boTransOrderJoinMapper.selectByBoTransTaskIdList(Collections.singletonList(data.getBoTransTaskId()));
            if (CollectionUtils.isNotEmpty(orderRelBeanList)){
                data.setOrderIdList(orderRelBeanList.stream().map(BoTransOrderRelBean::getBoTransOrderId).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isNotEmpty(data.getOrderIdList())){
            List<BoOrderGroupRelBean> orderGroupList = boOrderGroupRelMapper.selectByOrderIdList(data.getOrderIdList());
            result.setGroupIdList(orderGroupList);
            result.setInquireFlag("0");
            if (data.getOrderIdList().size() > 1){
                return result;
            }
            BoInquireBean boInquireBean = boInquireMapper.selectInquire(data.getOrderIdList().get(0));
            if (null == boInquireBean
                    || StringUtils.isBlank(boInquireBean.getBoInquireOfferId())){
                return result;
            }
            BoInquireOfferBean boInquireOfferBean = boInquireOfferMapper.selectByPkId(boInquireBean.getBoInquireOfferId());
            if (null == boInquireOfferBean){
                return result;
            }
            if (!"3".equals(boInquireOfferBean.getInquireState())){
                return result;
            }
            result.setOilFee(boInquireOfferBean.getOilFee());
            result.setAllFee(boInquireOfferBean.getAllFee());
            if (StringUtils.isNotBlank(boInquireOfferBean.getAllFee()) && StringUtils.isNotBlank(boInquireOfferBean.getOilFee())){
                result.setAllFee(new BigDecimal(boInquireOfferBean.getAllFee()).subtract(new BigDecimal(boInquireOfferBean.getOilFee())).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }
            result.setInquireFlag("1");
        }
        return result;
    }

    public ResDataBean taskHasMerge(BoTaskHasMergeIBean data) throws Exception{
        //参数校验
        if(CollectionUtils.isEmpty(data.getTaxWaybillIds()) && CollectionUtils.isEmpty(data.getBoTransTaskIdList())) {
            throw new BaseTipException("运输任务ID集合，运单ID集合不能同时为空");
        }
        if(CollectionUtils.isNotEmpty(data.getTaxWaybillIds()) && data.getTaxWaybillIds().size() >= 1000) {
            throw new BaseTipException("运单ID集合最大不超过1000笔");
        }
        if(CollectionUtils.isNotEmpty(data.getBoTransTaskIdList()) && data.getBoTransTaskIdList().size() >= 1000) {
            throw new BaseTipException("运输任务ID集合最大不超过1000笔");
        }
        if (CollectionUtils.isEmpty(data.getBoTransTaskIdList())){
            data.setBoTransTaskIdList(businessOperationService.getBoTransTaskIdList(data.getTaxWaybillIds()));
        }
        BoTaskHasMergeOBean boTaskHasMergeOBean = new BoTaskHasMergeOBean();
        List<BoTaskHasMergeOBean> boTaskHasMergeOBeans = boTransOrderRelMapper.selectTaskHasMerge(data.getBoTransTaskIdList());
        boTaskHasMergeOBean.setResultList(CollectionUtils.isEmpty(boTaskHasMergeOBeans)?new ArrayList<>():boTaskHasMergeOBeans);
        return new ResDataBean().success(boTaskHasMergeOBean);
    }
}
