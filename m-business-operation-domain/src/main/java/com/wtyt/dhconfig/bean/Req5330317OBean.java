package com.wtyt.dhconfig.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class Req5330317OBean {

    private String configId;

    private String isDefault;
    //表头所在行
    private String headerRowNum;
    //数据起始行
    private String dataStartRowNum;

    //导入表头列表
    private List<ImportHeader> headerList;
    //合并展示配置
    private List<HeaderShowInfo> headerShowList;


    @Getter
    @Setter
    public static class ImportHeader {
        //表头名称
        private String headerName;
        //是否必填
        private String isRequired;
        private String isCreateRequired;
        private String isUnique;
        //是否展示
        private String isShow;

        //是否展示
        private String appIsShow;
        //查询项类型
        private String searchType;
        //是否精确搜索
        private String isPrecise;
        //是否导出字段
        private String isExport;
        //是否新建计划录入字段
        private String isInput;
        //是否唯一
        private String isImport;
        private String canModify;
        //默认值
        private String defaultValue;

        private String sortNum;
        //导出排序字段
        private String exportSortNum;

        private String  appSortNum;

        //关联的预设字段信息
        private List<ColumnInfo> columnList;
    }
    @Getter
    @Setter
    public static class ColumnInfo {
        private String boDhFieldDictId;

        private String tableName;
        private String fieldName;
        private String fieldNameDesc;
        private String fieldType;
        private String maxLength;
        private String numberPrecision;
        private String defaultValue;
        private String headerEnName;

        private  Map<String,Object> searchConfig;
        private  Map<String,Object> headerConfig;
        private  Map<String,Object> inputConfig;
    }
    @Getter
    @Setter
    public static class HeaderShowInfo {
        //合并后展示表头名称
        private String showHeaderName;
        //合并展示分隔符
        private String splitChar;
        //需要合并的导入表头id列表
        private List<String> headerNameList;
    }

}
