package com.wtyt.commons.toolkits;

import com.github.pagehelper.PageInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年08月07日 18时17分
 */
public class PageToolkit {

    public static Map<String, Object> getResult(PageInfo<?> pageInfo) {
        Map<String, Object> resultMap = new HashMap<>(4);
        // 总数据数
        resultMap.put("totalRows", pageInfo.getTotal());
        // 总页数
        resultMap.put("totalPages", pageInfo.getPages());
        // 是否有下一页
        resultMap.put("hasNextPage", pageInfo.isHasNextPage());
        // 下一页的页码
        resultMap.put("nextPage", pageInfo.getNextPage());
        return resultMap;
    }

    /**
     * 计算当前线程开始查询的分页码
     * @param index        当前线程的索引，从0开始
     * @param totalThreads 总线程数
     * @param totalCount   数据总条数
     * @param pageSize     每页数据条数
     * @return 开始页码
     */
    public int calcuteStartPage(int index, int totalThreads, long totalCount, Integer pageSize) {
        long totalPageCount = (totalCount + pageSize - 1) / pageSize;
        long pagesPerThread = totalPageCount / totalThreads;
        return (int) (index * pagesPerThread + 1);
    }

    /**
     * 计算当前线程最后一页查询的分页码
     * @param index        当前线程的索引
     * @param totalThreads 总线程数
     * @param totalCount   数据总条数
     * @param pageSize     每页数据条数
     * @return 结束页码
     */
    public int calcuteEndPage(int index, int totalThreads, long totalCount, Integer pageSize) {
        long totalPageCount = (totalCount + pageSize - 1) / pageSize;
        long pagesPerThread = totalPageCount / totalThreads;
        int endPage = (int) ((index + 1) * pagesPerThread);

        // 如果是最后一个线程，确保查询到最后一页
        if (index == totalThreads - 1) {
            endPage = (int) totalPageCount;
        }

        return endPage;
    }
}
