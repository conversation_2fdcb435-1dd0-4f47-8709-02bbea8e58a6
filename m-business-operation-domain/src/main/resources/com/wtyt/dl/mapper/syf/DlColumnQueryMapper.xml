<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.DlColumnQueryMapper">
    <resultMap type="com.wtyt.dl.bean.DlColumnDetailBean" id="DlColumnQueryMap">
        <result property="dlColumnId" column="DL_COLUMN_ID" jdbcType="VARCHAR"/>
        <result property="columnKey" column="COLUMN_KEY" jdbcType="VARCHAR"/>
        <result property="columnName" column="COLUMN_NAME" jdbcType="VARCHAR"/>
        <result property="forceSelect" column="FORCE_SELECT" jdbcType="VARCHAR"/>
        <result property="columnScopes" column="COLUMN_SCOPES" jdbcType="VARCHAR"/>
        <result property="dlColumnFieldId" column="DL_COLUMN_FIELD_ID" jdbcType="VARCHAR"/>
        <result property="fieldKey" column="FIELD_KEY" jdbcType="VARCHAR"/>
        <result property="fieldName" column="FIELD_NAME" jdbcType="VARCHAR"/>
        <result property="renderConfig" column="RENDER_CONFIG" jdbcType="VARCHAR"/>
        <result property="scopes" column="SCOPES" jdbcType="VARCHAR"/>
        <result property="selectedMode" column="SELECTED_MODE" jdbcType="VARCHAR"/>
        <result property="categoryName" column="CATEGORY_NAME" jdbcType="VARCHAR"/>
        <result property="dlCategoryId" column="DL_CATEGORY_ID" jdbcType="VARCHAR"/>
        <result property="columnRenderConfig" column="COLUMN_RENDER_CONFIG" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryUserColumn" resultMap="DlColumnQueryMap">
        SELECT
            a.DL_COLUMN_ID,
            c.COLUMN_KEY COLUMN_KEY,
            NVL(a.COLUMN_NAME,c.COLUMN_NAME) COLUMN_NAME,
            c.FORCE_SELECT,
            b.DL_COLUMN_FIELD_ID,
            d.FIELD_KEY,
            d.FIELD_NAME,
            c.RENDER_CONFIG COLUMN_RENDER_CONFIG,
            d.RENDER_CONFIG
        FROM
        T_DL_USER_COLUMN a
        JOIN T_DL_USER_COLUMN_FIELD b ON
        a.DL_USER_COLUMN_ID = b.DL_USER_COLUMN_ID
        JOIN T_DL_COLUMN c ON a.DL_COLUMN_ID =c.DL_COLUMN_ID
        JOIN T_DL_COLUMN_FIELD d ON d.DL_COLUMN_FIELD_ID = b.DL_COLUMN_FIELD_ID
        WHERE
        a.IS_DEL = 0
        AND b.IS_DEL = 0
        AND c.IS_DEL =0
        AND d.IS_DEL =0
        AND a.BUSINESS_TYPE = #{businessType}
        AND a.UNIQUE_KEY = #{uniqueKey}
        AND a.page= #{page}
        ORDER BY a.SORT_NO ASC,b.SORT_NO ASC
    </select>

    <select id="queryUserColumnSimple" resultMap="DlColumnQueryMap">
        SELECT
            a.DL_COLUMN_ID,
            b.DL_COLUMN_FIELD_ID
        FROM
        T_DL_USER_COLUMN a
        JOIN T_DL_USER_COLUMN_FIELD b ON
        a.DL_USER_COLUMN_ID = b.DL_USER_COLUMN_ID
        WHERE
            a.IS_DEL = 0
            AND b.IS_DEL = 0
            AND a.BUSINESS_TYPE = #{businessType}
            AND a.UNIQUE_KEY = #{uniqueKey}
            AND a.page= #{page}
    </select>

    <select id="queryColumnDefaultSelected" resultMap="DlColumnQueryMap">
        SELECT
            c.DL_COLUMN_ID,
            c.COLUMN_KEY,
            c.COLUMN_NAME ,
            c.FORCE_SELECT,
            c.SCOPES COLUMN_SCOPES,
            d.DL_COLUMN_FIELD_ID,
            d.FIELD_KEY,
            d.FIELD_NAME,
            d.RENDER_CONFIG,
            c.RENDER_CONFIG COLUMN_RENDER_CONFIG,
            d.SCOPES
        FROM
        T_DL_CATEGORY b
        JOIN T_DL_COLUMN c ON
        c.DL_CATEGORY_ID = b.DL_CATEGORY_ID
        JOIN T_DL_COLUMN_FIELD d ON
        d.DL_COLUMN_ID = c.DL_COLUMN_ID
        WHERE
            b.IS_DEL = 0
            AND c.IS_DEL = 0
            AND d.IS_DEL = 0
            AND b.BUSINESS_TYPE = #{businessType}
            AND (d.PAGES IS NULL OR ',' || d.pages || ',' LIKE '%,' || #{page} || ',%')
            AND d.SELECTED_MODE = 1
        ORDER BY
        c.SORT_NO ASC,
        d.SORT_NO ASC
    </select>

    <select id="queryColumnList" resultMap="DlColumnQueryMap">
        SELECT
            b.DL_CATEGORY_ID,
            b.CATEGORY_NAME,
            c.DL_COLUMN_ID,
            c.COLUMN_NAME ,
            c.FORCE_SELECT,
            c.SCOPES COLUMN_SCOPES,
            c.RENDER_CONFIG COLUMN_RENDER_CONFIG,
            d.DL_COLUMN_FIELD_ID,
            d.FIELD_KEY,
            d.FIELD_NAME,
            d.RENDER_CONFIG,
            d.SELECTED_MODE,
            d.SCOPES
        FROM
        T_DL_CATEGORY b
        JOIN T_DL_COLUMN c ON
        c.DL_CATEGORY_ID = b.DL_CATEGORY_ID
        JOIN T_DL_COLUMN_FIELD d ON
        d.DL_COLUMN_ID = c.DL_COLUMN_ID
        WHERE
        b.IS_DEL = 0
        AND c.IS_DEL = 0
        AND d.IS_DEL = 0
        AND b.BUSINESS_TYPE = #{businessType}
        AND (d.PAGES IS NULL OR ',' || d.pages || ',' LIKE '%,' || #{page} || ',%')
        ORDER BY
        b.SORT_NO ASC,
        c.SORT_NO ASC,
        d.SORT_NO ASC
    </select>

    <select id="getColumnFieldListByBusiness" resultType="com.wtyt.dl.bean.DlColumnDetailBean">
        SELECT
            T1.FIELD_KEY fieldKey,
            T1.FIELD_NAME fieldName,
            T2.FIELD_SQL fieldSql,
            T2.FIELD_MAPPING fieldMapping
        FROM T_DL_COLUMN_FIELD T1
        LEFT JOIN T_DL_COLUMN_FIELD_MAPPING T2
            ON T2.DL_COLUMN_FIELD_ID = T1.DL_COLUMN_FIELD_ID
            AND T2.IS_DEL = 0
        LEFT JOIN T_DL_COLUMN T3
            ON T3.DL_COLUMN_ID = T1.DL_COLUMN_ID
            AND T3.IS_DEL = 0
        LEFT JOIN T_DL_CATEGORY T4
            ON T4.DL_CATEGORY_ID = T3.DL_CATEGORY_ID
            AND T4.IS_DEL = 0
        WHERE T1.IS_DEL = 0
        AND (T1.PAGES IS NULL OR INSTR(',' || T1.PAGES || ',', ',' || #{page} || ',') > 0)
        AND T4.BUSINESS_TYPE = #{businessType}
    </select>

</mapper>