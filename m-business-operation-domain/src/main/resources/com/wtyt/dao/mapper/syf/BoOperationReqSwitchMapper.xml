<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoOperationReqSwitchMapper">

    <resultMap id="BaseResultMap" type="com.wtyt.dao.bean.syf.BoOperationReqSwitchBean">
        <!--@Table T_BO_OPERATION_REQ_SWITCH-->
        <result property="boOperationReqSwitchId" column="BO_OPERATION_REQ_SWITCH_ID" jdbcType="VARCHAR"/>
        <result property="configEntityId" column="CONFIG_ENTITY_ID" jdbcType="VARCHAR"/>
        <result property="configType" column="CONFIG_TYPE" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="state" column="STATE" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="saveOrUpdate">
        MERGE INTO T_BO_OPERATION_REQ_SWITCH A
        USING (
            SELECT #{configEntityId} CONFIG_ENTITY_ID, #{configType} CONFIG_TYPE, #{orgId} ORG_ID, #{state} STATE FROM DUAL
        ) B
        ON (A.CONFIG_ENTITY_ID = B.CONFIG_ENTITY_ID AND A.CONFIG_TYPE = B.CONFIG_TYPE AND A.ORG_ID = B.ORG_ID AND IS_DEL=0)
        WHEN MATCHED THEN
        UPDATE SET
        A.STATE = B.STATE,
        A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
        INSERT
        (BO_OPERATION_REQ_SWITCH_ID,CONFIG_ENTITY_ID,CONFIG_TYPE,ORG_ID,STATE)
        VALUES(${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},B.CONFIG_ENTITY_ID, B.CONFIG_TYPE, B.ORG_ID, B.STATE)
    </insert>

    <select id="queryAllByOrgId" resultMap="BaseResultMap">
        SELECT CONFIG_ENTITY_ID,CONFIG_TYPE,STATE FROM T_BO_OPERATION_REQ_SWITCH WHERE ORG_ID=#{orgId} AND IS_DEL=0
    </select>

    <select id="query" resultMap="BaseResultMap">
        SELECT CONFIG_ENTITY_ID,CONFIG_TYPE,STATE FROM T_BO_OPERATION_REQ_SWITCH
        WHERE ORG_ID= #{orgId} AND IS_DEL=0 AND
        <foreach collection="configList" item="item" index="index" separator="OR" open="(" close=")">
            (CONFIG_TYPE=#{item.configType} AND CONFIG_ENTITY_ID= #{item.configEntityId})
        </foreach>
    </select>

</mapper>

