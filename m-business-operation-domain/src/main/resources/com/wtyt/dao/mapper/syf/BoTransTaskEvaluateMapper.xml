<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskEvaluateMapper">


    <insert id="saveIfNotExist" parameterType="com.wtyt.dao.bean.syf.BoTransTaskEvaluateBean">
        INSERT INTO T_BO_TRANS_TASK_EVALUATE
        (BO_TRANS_TASK_EVALUATE_ID, BO_TRANS_TASK_ID, EVALUATE_TYPE, EVALUATE_CONTENT)
        SELECT #{boTransTaskEvaluateId} boTransTaskEvaluateId, #{boTransTaskId} boTransTaskId, #{evaluateType} evaluateType, #{evaluateContent} evaluateContent FROM DUAL
        WHERE NOT EXISTS (SELECT 1 FROM T_BO_TRANS_TASK_EVALUATE WHERE BO_TRANS_TASK_ID=#{boTransTaskId})
    </insert>
</mapper>