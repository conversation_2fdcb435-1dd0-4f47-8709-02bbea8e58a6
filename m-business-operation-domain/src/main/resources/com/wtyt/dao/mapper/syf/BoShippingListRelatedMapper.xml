<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoShippingListRelatedMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoShippingListRelatedBean" id="BoShippingListRelatedMap">
        <result property="boShippingListRelatedId" column="BO_SHIPPING_LIST_RELATED_ID" jdbcType="VARCHAR"/>
        <result property="boShippingListId" column="BO_SHIPPING_LIST_ID" jdbcType="VARCHAR"/>
        <result property="actualTransportQuantity" column="ACTUAL_TRANSPORT_QUANTITY" jdbcType="VARCHAR"/>
        <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="queryByShippingListIds" resultMap="BoShippingListRelatedMap">
        SELECT
        BO_SHIPPING_LIST_ID,
        ACTUAL_TRANSPORT_QUANTITY,
        REMARK
        FROM
        T_BO_SHIPPING_LIST_RELATED
        WHERE
        IS_DEL = 0
        AND BO_SHIPPING_LIST_ID IN
        <foreach collection="shippingListIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryByShippingListId" resultMap="BoShippingListRelatedMap">
        SELECT
        BO_SHIPPING_LIST_ID,
        ACTUAL_TRANSPORT_QUANTITY,
        REMARK
        FROM
        T_BO_SHIPPING_LIST_RELATED
        WHERE
        IS_DEL = 0
        AND BO_SHIPPING_LIST_ID = #{shippingListId}
    </select>

    <insert id="saveOrUpdateReport">
        MERGE INTO T_BO_SHIPPING_LIST_RELATED A
        USING (
        SELECT #{boShippingListId} BO_SHIPPING_LIST_ID,#{actualTransportQuantity} ACTUAL_TRANSPORT_QUANTITY, #{remark} REMARK FROM DUAL
        ) B
        ON (A.BO_SHIPPING_LIST_ID = B.BO_SHIPPING_LIST_ID AND IS_DEL=0)
        WHEN MATCHED THEN
        UPDATE SET
        A.ACTUAL_TRANSPORT_QUANTITY = B.ACTUAL_TRANSPORT_QUANTITY,
        A.REMARK = B.REMARK,
        A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
        INSERT
        (BO_SHIPPING_LIST_RELATED_ID,BO_SHIPPING_LIST_ID,ACTUAL_TRANSPORT_QUANTITY,REMARK)
        VALUES(${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},B.BO_SHIPPING_LIST_ID, B.ACTUAL_TRANSPORT_QUANTITY, B.REMARK)
    </insert>


</mapper>

