<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoSVExportConfigMapper">

    <resultMap id="boSVExportConfigBeanMapper" type="com.wtyt.dao.bean.syf.BoSVExportConfigBean">
        <id property="boSVExportConfigId" column="BO_SV_EXPORT_CONFIG_ID"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupType" column="GROUP_TYPE"/>
        <result property="itemName" column="ITEM_NAME"/>
        <result property="itemValueExpression" column="ITEM_VALUE_EXPRESSION"/>
        <result property="headerSort" column="HEADER_SORT"/>
    </resultMap>

    <select id="selectExportConfigListByGroup" resultMap="boSVExportConfigBeanMapper">
        SELECT BO_SV_EXPORT_CONFIG_ID, GROUP_CODE, GROUP_TYPE, ITEM_NAME, ITEM_VALUE_EXPRESSION, HEADER_SORT
        FROM T_BO_SV_EXPORT_CONFIG
        WHERE IS_DEL = 0 AND GROUP_CODE = #{groupCode} AND GROUP_TYPE = #{groupType}
        ORDER BY HEADER_SORT
    </select>
</mapper>