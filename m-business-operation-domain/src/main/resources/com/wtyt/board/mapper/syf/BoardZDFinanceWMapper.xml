<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardZDFinanceWMapper">


    <sql id="dateAndOrgListCondition">
        <if test="beginTime !=null and beginTime!=''">
            <![CDATA[ AND T.CREATED_TIME >= to_date(#{beginTime},'yyyy-MM-dd') ]]>
        </if>
        <if test="endTime !=null and endTime !=''">
            <![CDATA[ AND T.CREATED_TIME <= to_date(#{endTime} || '23:59:59','yyyy-MM-dd Hh24:mi:ss') ]]>
        </if>
        <if test="orgIdList!= null and orgIdList.size() > 0">
            AND T.ORG_ID IN
            <foreach collection="orgIdList" item="item" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="customerNameList != null and customerNameList.size() > 0">
            AND T.WB_ITEM IN
            <foreach collection="customerNameList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>


    <select id="orgTotalAmount" resultType="com.wtyt.dao.bean.syf.BoTransTaskFeeBean">
        SELECT f.EXPENSIVE_TYPE expensiveType , TO_CHAR(NVL(SUM(f.CONFIG_VALUE), 0), 'FM99999999990.00')  configValue
        FROM  T_BO_TRANS_TASK_FEE f INNER JOIN T_BO_TRANS_TASK t ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        WHERE  f.IS_DEL=0 and t.IS_DEL=0 and  f.EXPENSIVE_TYPE IN (0,3)
        <include refid="dateAndOrgListCondition"/>
        GROUP BY f.EXPENSIVE_TYPE
    </select>

    <select id="orgTotalUserFreight" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        SELECT count(t.BO_TRANS_TASK_ID) as taskCount , TO_CHAR(NVL(SUM(t.USER_FREIGHT), 0), 'FM99999999990.00')  userFreight
        FROM  T_BO_TRANS_TASK t
        WHERE  t.IS_DEL=0
        <include refid="dateAndOrgListCondition"/>
    </select>

    <select id="orgTaskCountTopTen" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        select * from (
        SELECT ORG_ID orgId,
        count(1) as taskCount
        FROM T_BO_TRANS_TASK t
        WHERE t.IS_DEL=0
        <include refid="dateAndOrgListCondition"/>
        group by t.ORG_ID ORDER BY count(1) desc
        ) WHERE ROWNUM &lt;= 10
    </select>

    <select id="orgReceivableAmountTopTen" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        select ORG_ID  orgId  ,TO_CHAR(CONFIG_VALUE, 'FM99999999990.00') receivableAmount from (
        SELECT t.ORG_ID , NVL(SUM(f.CONFIG_VALUE), 0) CONFIG_VALUE
        FROM  T_BO_TRANS_TASK t left JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID and f.IS_DEL=0  and  f.EXPENSIVE_TYPE =3
        WHERE  t.IS_DEL=0
        <include refid="dateAndOrgListCondition"/>
        group by t.ORG_ID ORDER BY CONFIG_VALUE desc
        ) WHERE ROWNUM &lt;= 10
    </select>

    <select id="orgPayableAmountTopTen" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        select ORG_ID orgId ,TO_CHAR(CONFIG_VALUE, 'FM99999999990.00') payableAmount from (
        SELECT A.ORG_ID , SUM(A.CONFIG_VALUE) CONFIG_VALUE FROM (
        SELECT
        t.ORG_ID , t.BO_TRANS_TASK_ID ,NVL(SUM(f.CONFIG_VALUE), 0) + nvl(max(t.USER_FREIGHT),0) CONFIG_VALUE
        FROM T_BO_TRANS_TASK t left JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID and f.IS_DEL =0 and f.EXPENSIVE_TYPE =0
        WHERE t.IS_DEL=0
        <include refid="dateAndOrgListCondition"/>
        GROUP BY t.ORG_ID , t.BO_TRANS_TASK_ID
        ) A GROUP BY A.ORG_ID ORDER BY CONFIG_VALUE desc
        ) WHERE ROWNUM &lt;= 10
    </select>

    <select id="orgProfitTopTen" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        select ORG_ID orgId ,TO_CHAR(CONFIG_VALUE, 'FM99999999990.00') profit from (
        SELECT A.ORG_ID , SUM(A.CONFIG_VALUE) CONFIG_VALUE FROM (
        SELECT
        t.ORG_ID ,
        t.BO_TRANS_TASK_ID ,
        NVL(SUM(DECODE(F.EXPENSIVE_TYPE,3,f.CONFIG_VALUE,0)), 0)  - NVL(SUM(DECODE(F.EXPENSIVE_TYPE,0,f.CONFIG_VALUE,0)), 0) - nvl(max(t.USER_FREIGHT),0)    CONFIG_VALUE
        FROM T_BO_TRANS_TASK t left JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID and f.IS_DEL =0  and f.EXPENSIVE_TYPE IN (0,3)
        WHERE t.IS_DEL=0
        <include refid="dateAndOrgListCondition"/>
        GROUP BY t.ORG_ID , t.BO_TRANS_TASK_ID
        ) A GROUP BY A.ORG_ID ORDER BY CONFIG_VALUE desc
        ) WHERE ROWNUM &lt;= 10
    </select>

    <select id="orgProfitRate" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        SELECT ORG_ID orgId ,TO_CHAR(CONFIG_VALUE, 'FM99999999990.00') profitRate from (
        SELECT A.ORG_ID ,TO_NUMBER(decode(SUM(A.receivableAmount),0,null,SUM(A.profit) / SUM(A.receivableAmount) *100 ))CONFIG_VALUE FROM (
        SELECT
        t.ORG_ID ,
        t.BO_TRANS_TASK_ID ,
        NVL(SUM(DECODE(F.EXPENSIVE_TYPE,3,f.CONFIG_VALUE,0)), 0) receivableAmount,
        NVL(SUM(DECODE(F.EXPENSIVE_TYPE,3,f.CONFIG_VALUE,0)), 0) - NVL(SUM(DECODE(F.EXPENSIVE_TYPE,0,f.CONFIG_VALUE,0)),0) - nvl(max(t.USER_FREIGHT),0) profit
        FROM T_BO_TRANS_TASK t left JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID and f.IS_DEL =0  and f.EXPENSIVE_TYPE IN (0,3)
        WHERE t.IS_DEL=0
        <include refid="dateAndOrgListCondition"/>
        GROUP BY t.ORG_ID , t.BO_TRANS_TASK_ID
        ) A GROUP BY A.ORG_ID ORDER BY CONFIG_VALUE desc
        ) WHERE CONFIG_VALUE is not null and ROWNUM &lt;= 10
    </select>

    <select id="wbItemTaskCountTopTen" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        select * from (
        SELECT WB_ITEM wbItem,
        count(1) as taskCount
        FROM T_BO_TRANS_TASK t
        WHERE t.IS_DEL=0 and  t.WB_ITEM is not null
        <include refid="dateAndOrgListCondition"/>
        group by t.WB_ITEM ORDER BY count(1) desc
        ) WHERE ROWNUM &lt;= 10
    </select>

    <select id="wbItemReceivableAmountTopTen" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        select WB_ITEM  wbItem  ,TO_CHAR(CONFIG_VALUE, 'FM99999999990.00') receivableAmount from (
        SELECT t.WB_ITEM , NVL(SUM(f.CONFIG_VALUE), 0) CONFIG_VALUE
        FROM  T_BO_TRANS_TASK t left JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID and f.IS_DEL=0 and  f.EXPENSIVE_TYPE =3
        WHERE    t.IS_DEL=0  and  t.WB_ITEM is not null
        <include refid="dateAndOrgListCondition"/>
        group by t.WB_ITEM ORDER BY CONFIG_VALUE desc
        ) WHERE ROWNUM &lt;= 10
    </select>

    <select id="wbItemPayableAmountTopTen" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        select WB_ITEM  wbItem ,TO_CHAR(CONFIG_VALUE, 'FM99999999990.00') payableAmount from (
        SELECT A.WB_ITEM , SUM(A.CONFIG_VALUE) CONFIG_VALUE FROM (
        SELECT
        t.WB_ITEM , t.BO_TRANS_TASK_ID ,NVL(SUM(f.CONFIG_VALUE), 0) + nvl(max(t.USER_FREIGHT),0) CONFIG_VALUE
        FROM T_BO_TRANS_TASK t left JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID and  f.IS_DEL=0 and f.EXPENSIVE_TYPE =0
        WHERE  t.IS_DEL=0  and  t.WB_ITEM is not null
        <include refid="dateAndOrgListCondition"/>
        GROUP BY t.WB_ITEM , t.BO_TRANS_TASK_ID
        ) A GROUP BY A.WB_ITEM ORDER BY CONFIG_VALUE desc
        ) WHERE ROWNUM &lt;= 10
    </select>
    <select id="orgWbItemAmountCount" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        SELECT
            ORG_ID orgId,
            WB_ITEM wbItem,
            SUM(USER_FREIGHT) userFreight,
            COUNT(BO_TRANS_TASK_ID) taskCount,
            SUM(payable) payableAmount,
            to_char(SUM(receivable), 'FM99999999990.00') receivableAmount
        FROM
        (
            SELECT
                ORG_ID,
                WB_ITEM ,
                BO_TRANS_TASK_ID,
                MIN(USER_FREIGHT) USER_FREIGHT,
                SUM(payable) payable,
                SUM(receivable) receivable
            FROM
            (
                SELECT
                    t.ORG_ID,
                    t.WB_ITEM,
                    NVL(t.USER_FREIGHT, 0) USER_FREIGHT,
                    t.BO_TRANS_TASK_ID,
                    CASE WHEN f.EXPENSIVE_TYPE = 0 THEN nvl(f.CONFIG_VALUE, 0) ELSE 0 END payable,
                    CASE WHEN f.EXPENSIVE_TYPE = 3 THEN nvl(f.CONFIG_VALUE, 0) ELSE 0 END receivable
                FROM
                    T_BO_TRANS_TASK t
                    LEFT JOIN T_BO_TRANS_TASK_FEE f ON f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND f.IS_DEL = 0 AND f.EXPENSIVE_TYPE IN (0, 3)
                WHERE
                    t.IS_DEL = 0
                    <include refid="dateAndOrgListCondition"/>
                ) t1
            GROUP BY
                ORG_ID,
                WB_ITEM ,
                BO_TRANS_TASK_ID
        )TEMP
        GROUP BY
            ORG_ID,
            WB_ITEM
    </select>

    <select id="wbItemProfitTopTen" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        select WB_ITEM wbItem ,TO_CHAR(CONFIG_VALUE, 'FM99999999990.00') profit from (
        SELECT A.WB_ITEM , SUM(A.CONFIG_VALUE) CONFIG_VALUE FROM (
        SELECT
        t.WB_ITEM ,
        t.BO_TRANS_TASK_ID ,
        NVL(SUM(DECODE(F.EXPENSIVE_TYPE,3,f.CONFIG_VALUE,0)), 0)  - NVL(SUM(DECODE(F.EXPENSIVE_TYPE,0,f.CONFIG_VALUE,0)), 0) - nvl(max(t.USER_FREIGHT),0)    CONFIG_VALUE
        FROM T_BO_TRANS_TASK t left JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID and f.IS_DEL=0 and f.EXPENSIVE_TYPE IN (0,3)
        WHERE  t.IS_DEL=0 and  t.WB_ITEM is not null
        <include refid="dateAndOrgListCondition"/>
        GROUP BY WB_ITEM , t.BO_TRANS_TASK_ID
        ) A GROUP BY A.WB_ITEM ORDER BY CONFIG_VALUE desc
        ) WHERE ROWNUM &lt;= 10
    </select>

    <select id="wbItemProfitRate" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        SELECT WB_ITEM wbItem ,TO_CHAR(CONFIG_VALUE, 'FM99999999990.00') profitRate from (
        SELECT A.WB_ITEM ,  TO_NUMBER(decode(SUM(A.receivableAmount),0,null,  SUM(A.profit) / SUM(A.receivableAmount) *100 ))  CONFIG_VALUE  FROM (
        SELECT
        t.WB_ITEM ,
        t.BO_TRANS_TASK_ID ,
        NVL(SUM(DECODE(F.EXPENSIVE_TYPE,3,f.CONFIG_VALUE,0)), 0) receivableAmount,
        NVL(SUM(DECODE(F.EXPENSIVE_TYPE,3,f.CONFIG_VALUE,0)), 0)  - NVL(SUM(DECODE(F.EXPENSIVE_TYPE,0,f.CONFIG_VALUE,0)), 0) - nvl(max(t.USER_FREIGHT),0)    profit
        FROM T_BO_TRANS_TASK t left JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID and f.IS_DEL=0 and f.EXPENSIVE_TYPE IN (0,3)
        WHERE  t.IS_DEL=0 and  t.WB_ITEM is not null
        <include refid="dateAndOrgListCondition"/>
        GROUP BY t.WB_ITEM , t.BO_TRANS_TASK_ID
        ) A GROUP BY A.WB_ITEM ORDER BY CONFIG_VALUE desc
        )WHERE CONFIG_VALUE is not null and ROWNUM &lt;= 10
    </select>


    <select id="wbItemLineProfitProfitTopFive" resultType="com.wtyt.board.bean.BoardZdTaskAmountBean">
        select START_PROVINCE_NAME startProvinceName, START_CITY_NAME startCityName,START_COUNTY_NAME startCountyName,
        END_PROVINCE_NAME endProvinceName,END_CITY_NAME endCityName,END_COUNTY_NAME endCountyName,
        TO_CHAR(CONFIG_VALUE, 'FM99999999990.00') profit,
        TO_CHAR(AVERAGE_USER_FREIGHT, 'FM99999999990.00') averageUserFreight
        from (
        SELECT START_PROVINCE_NAME , START_CITY_NAME,START_COUNTY_NAME ,
        END_PROVINCE_NAME,END_CITY_NAME,END_COUNTY_NAME ,
        SUM(A.CONFIG_VALUE) CONFIG_VALUE ,
        SUM(A.USER_FREIGHT) /COUNT(1) AS AVERAGE_USER_FREIGHT
        FROM (
        SELECT
        t.START_PROVINCE_NAME , t.START_CITY_NAME,t.START_COUNTY_NAME,
        t.END_PROVINCE_NAME,t.END_CITY_NAME,t.END_COUNTY_NAME,
        t.BO_TRANS_TASK_ID ,
        nvl(max(t.USER_FREIGHT),0) USER_FREIGHT,
        NVL(SUM(DECODE(F.EXPENSIVE_TYPE,3,f.CONFIG_VALUE,0)), 0) - NVL(SUM(DECODE(F.EXPENSIVE_TYPE,0,f.CONFIG_VALUE,0)), 0) - nvl(max(t.USER_FREIGHT),0) CONFIG_VALUE
        FROM T_BO_TRANS_TASK t left JOIN T_BO_TRANS_TASK_FEE f ON t.BO_TRANS_TASK_ID = f.BO_TRANS_TASK_ID and  f.IS_DEL=0  and f.EXPENSIVE_TYPE IN (0,3)
        WHERE  t.IS_DEL=0 and t.WB_ITEM =#{wbItem}
        AND t.START_PROVINCE_NAME IS NOT NULL AND (t.START_CITY_NAME IS NOT NULL or t.START_COUNTY_NAME is not null)
        AND t.END_PROVINCE_NAME IS NOT NULL AND (t.END_CITY_NAME IS NOT NULL or t.END_COUNTY_NAME is not null)
        <include refid="dateAndOrgListCondition"/>
        GROUP BY t.START_PROVINCE_NAME , t.START_CITY_NAME,t.START_COUNTY_NAME,
        t.END_PROVINCE_NAME,t.END_CITY_NAME,t.END_COUNTY_NAME, t.BO_TRANS_TASK_ID
        ) A GROUP BY START_PROVINCE_NAME , START_CITY_NAME,START_COUNTY_NAME
        ,END_PROVINCE_NAME,END_CITY_NAME,END_COUNTY_NAME
        ORDER BY CONFIG_VALUE desc
        ) WHERE ROWNUM &lt;= 5
    </select>

    <select id="lineAverageUserFreight" resultType="com.wtyt.board.bean.BoardZdLineFreightBean">
        SELECT
        <if test="timeType =='0'.toString()">
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD') AS yearMonth,
        </if>
        <if test="timeType =='1'.toString()">
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM') AS yearMonth,
        </if>
            TO_CHAR(NVL(SUM(t.USER_FREIGHT),0) /COUNT(1), 'FM99999999990.00') averageUserFreight
        FROM
            T_BO_TRANS_TASK t
        WHERE
            t.IS_DEL = 0
          AND t.WB_ITEM =#{wbItem}
        <if test="startProvinceName != null and startProvinceName != ''">
            and T.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName != null and startCityName != ''">
            and T.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName != null and startCountyName != ''">
            and T.START_COUNTY_NAME = #{startCountyName}
        </if>
        <if test="endProvinceName != null and endProvinceName != ''">
            and T.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName != null and endCityName != ''">
            and T.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName != null and endCountyName != ''">
            and T.END_COUNTY_NAME = #{endCountyName}
        </if>
        <include refid="dateAndOrgListCondition"/>
        GROUP BY
        <if test="timeType =='0'.toString()">
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD')
        </if>
        <if test="timeType =='1'.toString()">
            TO_CHAR(T.CREATED_TIME, 'YYYY-MM')
        </if>
        ORDER BY  yearMonth
    </select>


</mapper>
