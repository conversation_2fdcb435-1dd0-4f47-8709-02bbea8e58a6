<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.ColumnFieldMappingMapper">
    
    <select id="getColumnFieldMappingListByBusiness" resultType="com.wtyt.dl.bean.ColumnFieldMappingBean">
        SELECT
            T1.DL_COLUMN_FIELD_MAPPING_ID dlColumnFieldMappingId,
            T1.DL_COLUMN_FIELD_ID dlColumnFieldId,
            T1.FIELD_SQL fieldSql,
            T1.FIELD_MAPPING fieldMapping
        FROM T_DL_COLUMN_FIELD_MAPPING T1
        LEFT JOIN T_DL_COLUMN_FIELD T2
            ON T2.DL_COLUMN_FIELD_ID = T1.DL_COLUMN_FIELD_ID
            AND T2.IS_DEL = 0
        LEFT JOIN T_DL_COLUMN T3
            ON T3.DL_COLUMN_ID = T2.DL_COLUMN_ID
            AND T3.IS_DEL = 0
        LEFT JOIN T_DL_CATEGORY T4
            ON T4.DL_CATEGORY_ID = T3.DL_CATEGORY_ID
            AND T4.IS_DEL = 0
        WHERE T1.IS_DEL = 0
        AND (T2.PAGES IS NULL OR INSTR(',' || T2.PAGES || ',', ',' || #{page} || ',') > 0)
        AND T4.BUSINESS_TYPE = #{businessType}
    </select>

</mapper>
