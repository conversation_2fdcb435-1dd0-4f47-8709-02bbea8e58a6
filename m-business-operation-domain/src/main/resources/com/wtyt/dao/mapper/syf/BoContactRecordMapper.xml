<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoContactRecordMapper">

    <insert id="insertContactRecord">
        INSERT INTO T_BO_CONTACT_RECORD (
            BO_CONTACT_RECORD_ID,
            BUSINESS_TYPE,
            BUSINESS_ID,
            CALLER_ID,
            CALLER_ROLE,
            CALLER_NAME,
            CALLEE_ID,
            CALLEE_ROLE,
            CALLEE_NAME,
            CALLEE_MOBILE
        ) VALUES (
            #{boContactRecordId},
            #{businessType},
            #{businessId},
            #{callerId},
            #{callerRole},
            #{callerName},
            #{calleeId},
            #{calleeRole},
            #{calleeName},
            #{calleeMob<PERSON>}
        )
    </insert>

</mapper>
