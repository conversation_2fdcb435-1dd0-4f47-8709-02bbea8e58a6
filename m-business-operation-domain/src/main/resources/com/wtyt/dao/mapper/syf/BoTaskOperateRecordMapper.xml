<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskOperateRecordMapper">


    <insert id="insertTaskOperateRecord">
        INSERT INTO T_BO_TASK_OPERATE_RECORD (
            BO_TASK_OPERATE_RECORD_ID,
            ORG_ID,
            BO_TRANS_TASK_ID,
            OPERATOR_TYPE,
            OPERATOR_ID,
            OPERATOR_NAME,
            OPERATOR_MOBILE,
            OPERATE_TYPE,
            OPERATE_STATUS,
            OPERATE_REASON,
            BUSINESS_TYPE,
            ENTITY_TYPE,
            ENTITY_NAME,
            BUSINESS_ID,
            <if test="operateStartTime != null">
                OPERATE_START_TIME,
            </if>
            <if test="createdTime != null">
                CREATED_TIME,
            </if>
            EXTEND_JSON
        ) VALUES (
            #{boTaskOperateRecordId},
            #{orgId},
            #{boTransTaskId},
            #{operatorType},
            #{operatorId},
            #{operatorName},
            #{operatorMobile},
            #{operateType},
            #{operateStatus},
            #{operateReason},
            #{businessType},
            #{entityType},
            #{entityName},
            #{businessId},
            <if test="operateStartTime != null">
                TO_DATE(#{operateStartTime},'YYYY-MM-DD HH24:MI:SS'),
            </if>
            <if test="createdTime != null">
                SYSDATE + INTERVAL '1' SECOND,
            </if>
            #{extendJson}
        )
    </insert>
    <update id="delRecord">
        UPDATE T_BO_TASK_OPERATE_RECORD
        SET IS_DEL = 1 ,LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TASK_OPERATE_RECORD_ID = #{boTaskOperateRecordId}
    </update>

    <update id="returnYdApply">
        UPDATE T_BO_TASK_OPERATE_RECORD
        SET
            OPERATE_STATUS = 5 ,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND OPERATE_STATUS = 3
            AND BO_TASK_OPERATE_RECORD_ID = #{boTaskOperateRecordId}
    </update>

    <update id="updateYdJson">
        UPDATE T_BO_TASK_OPERATE_RECORD
        SET
            OPERATE_STATUS = #{operateStatus},
            EXTEND_JSON = #{extendJson},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND BO_TASK_OPERATE_RECORD_ID = #{boTaskOperateRecordId}
    </update>
    <insert id="saveOperationRecord">
        INSERT INTO T_BO_TASK_OPERATE_RECORD
        (
        BO_TASK_OPERATE_RECORD_ID,
        ORG_ID,
        BO_TRANS_TASK_ID,
        <if test="entityType != null and entityType != ''">
            ENTITY_TYPE,
        </if>
        <if test="entityName != null and entityName != ''">
            ENTITY_NAME,
        </if>
        OPERATE_TYPE,
        OPERATOR_TYPE,
        OPERATOR_ID,
        OPERATOR_NAME,
        <if test="operatorMobile != null and operatorMobile != ''">
            OPERATOR_MOBILE,
        </if>
        OPERATE_STATUS,
        <if test="operateReason != null and operateReason != ''">
            OPERATE_REASON,
        </if>
        BUSINESS_TYPE
        <if test="businessId != null and businessId != ''">
            ,BUSINESS_ID
        </if>
        <if test="extendJson != null and extendJson != ''">
            ,EXTEND_JSON
        </if>
        <if test="operateStartTime != null and operateStartTime != ''">
            , OPERATE_START_TIME
        </if>
        )
        VALUES
        (
        #{boTaskOperateRecordId},
        #{orgId},
        #{boTransTaskId},
        <if test="entityType != null and entityType != ''">
            #{entityType},
        </if>
        <if test="entityName != null and entityName != ''">
            #{entityName},
        </if>
        #{operateType},
        #{operatorType},
        #{operatorId},
        #{operatorName},
        <if test="operatorMobile != null and operatorMobile != ''">
            #{operatorMobile},
        </if>
        #{operateStatus},
        <if test="operateReason != null and operateReason != ''">
            #{operateReason},
        </if>
        #{businessType}
        <if test="businessId != null and businessId != ''">
            , #{businessId}
        </if>
        <if test="extendJson != null and extendJson != ''">
           , #{extendJson}
        </if>
        <if test="operateStartTime != null and operateStartTime != ''">
            ,TO_DATE(#{operateStartTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        )
    </insert>
    <update id="updateOperationRecord">
        UPDATE T_BO_TASK_OPERATE_RECORD
        SET
        LAST_MODIFIED_TIME = SYSDATE,
        OPERATE_STATUS = #{operateStatus}
        <if test="operatorType != null and operatorType != ''">
              , OPERATOR_TYPE = #{operatorType}
        </if>
        <if test="operatorId != null and operatorId != ''">
             , OPERATOR_ID = #{operatorId}
        </if>
        <if test="operatorName != null and operatorName != ''">
            , OPERATOR_NAME = #{operatorName}
        </if>
        <if test="operatorMobile != null and operatorMobile != ''">
            ,OPERATOR_MOBILE = #{operatorMobile}
        </if>
        <if test="operateResult != null and operateResult != ''">
            , OPERATE_RESULT = #{operateResult}
        </if>
        <if test="operateStartTime != null and operateStartTime != ''">
          ,  OPERATE_START_TIME =  TO_DATE(#{operateStartTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="operateEndTime != null and operateEndTime != ''">
          , OPERATE_END_TIME = TO_DATE(#{operateEndTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        <if test="extraField != null and extraField != ''">
            ,EXTRA_FIELD = #{extraField}
        </if>
        WHERE BO_TASK_OPERATE_RECORD_ID = #{boTaskOperateRecordId}
    </update>

    <update id="updateJobFlagBatch">
        UPDATE T_BO_TASK_OPERATE_RECORD
        SET JOB_FLAG = #{jobFlag}
        WHERE
            BO_TASK_OPERATE_RECORD_ID IN
            <foreach collection="boTaskOperateRecordIds" close=")" open="(" item="boTaskOperateRecordId" separator=",">
                #{boTaskOperateRecordId}
            </foreach>
    </update>

    <select id="getLatelyTaskOperateRecord" resultType="BoTaskOperateRecordBean">
        SELECT *
        FROM  (
            SELECT
                T.BO_TASK_OPERATE_RECORD_ID boTaskOperateRecordId,
                T.ORG_ID orgId,
                T.BO_TRANS_TASK_ID boTransTaskId,
                T.OPERATOR_TYPE operatorType,
                T.OPERATOR_ID operatorId,
                T.OPERATOR_NAME operatorName,
                T.OPERATE_TYPE operateType,
                T.OPERATE_STATUS operateStatus,
                T.OPERATE_REASON operateReason,
                TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') operateTime
            FROM T_BO_TASK_OPERATE_RECORD T
            WHERE T.IS_DEL = 0
            AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
            AND T.OPERATE_TYPE = #{operateType}
            ORDER BY T.CREATED_TIME DESC
        ) WHERE ROWNUM = 1
    </select>

    <select id="getLatelyTaskOperateRecordList" resultType="BoTaskOperateRecordBean">
        SELECT *
        FROM  (
            SELECT
                T.BO_TASK_OPERATE_RECORD_ID boTaskOperateRecordId,
                T.ORG_ID orgId,
                T.BO_TRANS_TASK_ID boTransTaskId,
                T.OPERATOR_TYPE operatorType,
                T.OPERATOR_ID operatorId,
                T.OPERATOR_NAME operatorName,
                T.OPERATE_TYPE operateType,
                T.OPERATE_STATUS operateStatus,
                T.OPERATE_REASON operateReason,
                T.BUSINESS_TYPE businessType,
                T.ENTITY_NAME entityName,
                T.ENTITY_TYPE entityType,
                T.EXTEND_JSON extendJson,
                T.EXTRA_FIELD extraField,
                T.JOB_FLAG jobFlag,
                TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') operateTime,
                ROW_NUMBER() OVER(PARTITION BY T.BO_TRANS_TASK_ID ORDER BY T.CREATED_TIME DESC) RN
            FROM T_BO_TASK_OPERATE_RECORD T
            WHERE T.IS_DEL = 0
            AND T.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
                #{boTransTaskId}
            </foreach>
            AND T.OPERATE_TYPE = #{operateType}
            ORDER BY T.CREATED_TIME DESC
        ) WHERE RN = 1
    </select>

    <select id="getOperationRecordList" resultType="com.wtyt.dao.bean.syf.BoTaskOperateRecordBean">
        SELECT
            T.BUSINESS_TYPE              As businessType,
            T.BUSINESS_ID                As businessId,
            T.ENTITY_TYPE                AS entityType,
            T.ENTITY_NAME                AS entityName,
            T.OPERATOR_TYPE              AS operatorType,
            T.OPERATOR_NAME              AS operatorName,
            T.OPERATE_STATUS             AS operateStatus,
            T.OPERATE_REASON             AS operateReason,
            T.OPERATE_RESULT             AS operateResult,
            TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') AS operateTime,
            TO_CHAR(T.OPERATE_START_TIME, 'yyyy-mm-dd hh24:mi:ss') AS operateStartTime,
            TO_CHAR(T.OPERATE_END_TIME, 'yyyy-mm-dd hh24:mi:ss')   AS operateEndTime
        FROM T_BO_TASK_OPERATE_RECORD T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.OPERATE_TYPE = #{operateType}
        <!-- operatorType 非空时才允许继续拼接 operatorId / operatorMobile -->
        <if test="operatorType != null and operatorType != ''">
            AND T.OPERATOR_TYPE = #{operatorType}
            <if test="operatorId != null and operatorId != ''">
                AND T.OPERATOR_ID = #{operatorId}
            </if>
            <if test="operatorMobile != null and operatorMobile != ''">
                AND T.OPERATOR_MOBILE = #{operatorMobile}
            </if>
        </if>
        ORDER BY CASE WHEN T.OPERATE_START_TIME IS NULL  THEN 1 ELSE 0 END, OPERATE_START_TIME DESC NULLS LAST, CREATED_TIME DESC
    </select>



    <select id="getYdProcessingRecords" resultType="com.wtyt.dao.bean.syf.BoTaskOperateRecordBean">
        SELECT
            T.BO_TASK_OPERATE_RECORD_ID boTaskOperateRecordId,
            T.ORG_ID orgId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.OPERATOR_TYPE operatorType,
            T.OPERATOR_ID operatorId,
            T.OPERATOR_NAME operatorName,
            T.OPERATE_TYPE operateType,
            T.OPERATE_STATUS operateStatus,
            T.OPERATE_REASON operateReason,
            T.ENTITY_NAME entityName,
            T.EXTEND_JSON extendJson,
            T.JOB_FLAG jobFlag,
            TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') operateTime
        FROM T_BO_TASK_OPERATE_RECORD T
        WHERE
            T.IS_DEL = 0
            AND OPERATE_STATUS IN (1,5)
            AND OPERATE_TYPE = 4
    </select>

    <select id="getOperationRecord" resultType="com.wtyt.dao.bean.syf.BoTaskOperateRecordBean">
        SELECT
        T.BO_TASK_OPERATE_RECORD_ID  AS boTaskOperateRecordId,
        T.ORG_ID                     AS orgId,
        T.BO_TRANS_TASK_ID           AS boTransTaskId,
        T.OPERATOR_TYPE              AS operatorType,
        T.OPERATOR_ID                AS operatorId,
        T.OPERATOR_NAME              AS operatorName,
        T.OPERATOR_MOBILE            AS operatorMobile,
        T.OPERATE_TYPE               AS operateType,
        T.OPERATE_STATUS             AS operateStatus,
        T.OPERATE_REASON             AS operateReason,
        TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') AS operateTime,
        T.ENTITY_TYPE                AS entityType,
        T.ENTITY_NAME                AS entityName,
        T.OPERATE_RESULT             AS operateResult,
        T.BUSINESS_TYPE              AS businessType,
        T.BUSINESS_ID                AS businessId,
        T.EXTEND_JSON                AS extendJson,
        T.JOB_FLAG                   AS jobFlag,
        TO_CHAR(T.OPERATE_START_TIME, 'yyyy-mm-dd hh24:mi:ss') AS operateStartTime,
        TO_CHAR(T.OPERATE_END_TIME, 'yyyy-mm-dd hh24:mi:ss')   AS operateEndTime
        FROM T_BO_TASK_OPERATE_RECORD T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.ENTITY_TYPE = #{entityType}
        AND T.ENTITY_NAME = #{entityName}
        AND T.OPERATE_TYPE = #{operateType}
        ORDER BY NVL(T.OPERATE_START_TIME, T.CREATED_TIME) DESC
    </select>

    <select id="getLatelyTaskOperateRecordByEntityGroup" resultType="BoTaskOperateRecordBean">
        SELECT *
        FROM  (
            SELECT
                T.BO_TASK_OPERATE_RECORD_ID boTaskOperateRecordId,
                T.ORG_ID orgId,
                T.BO_TRANS_TASK_ID boTransTaskId,
                T.OPERATOR_TYPE operatorType,
                T.OPERATOR_ID operatorId,
                T.OPERATOR_NAME operatorName,
                T.OPERATE_TYPE operateType,
                T.OPERATE_STATUS operateStatus,
                T.OPERATE_REASON operateReason,
                T.ENTITY_TYPE entityType,
                T.ENTITY_NAME entityName,
                T.JOB_FLAG jobFlag,
                T.EXTRA_FIELD extraField,
                TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') operateTime,
                ROW_NUMBER() OVER(PARTITION BY T.ENTITY_TYPE, T.ENTITY_NAME ORDER BY T.CREATED_TIME DESC, T.BO_TASK_OPERATE_RECORD_ID DESC) RN
            FROM T_BO_TASK_OPERATE_RECORD T
            WHERE T.IS_DEL = 0
            AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
            AND T.OPERATE_TYPE = #{operateType}
            ORDER BY T.CREATED_TIME DESC
        ) WHERE RN = 1
    </select>
    <select id="getLatestOperationRecord" resultType="com.wtyt.dao.bean.syf.BoTaskOperateRecordBean">
        SELECT
        T.BO_TASK_OPERATE_RECORD_ID AS boTaskOperateRecordId,
        T.ORG_ID AS orgId,
        T.BO_TRANS_TASK_ID AS boTransTaskId,
        T.OPERATOR_TYPE AS operatorType,
        T.OPERATOR_ID AS operatorId,
        T.OPERATOR_NAME AS operatorName,
        T.OPERATOR_MOBILE AS operatorMobile,
        T.OPERATE_TYPE AS operateType,
        T.OPERATE_STATUS AS operateStatus,
        T.OPERATE_REASON AS operateReason,
        TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') AS operateTime,
        T.ENTITY_TYPE AS entityType,
        T.ENTITY_NAME AS entityName,
        T.OPERATE_RESULT AS operateResult,
        T.BUSINESS_TYPE AS businessType,
        T.BUSINESS_ID AS businessId,
        T.EXTEND_JSON AS extendJson,
        T.JOB_FLAG AS jobFlag,
        TO_CHAR(T.OPERATE_START_TIME, 'yyyy-mm-dd hh24:mi:ss') AS operateStartTime,
        TO_CHAR(T.OPERATE_END_TIME, 'yyyy-mm-dd hh24:mi:ss') AS operateEndTime
        FROM (
        SELECT
        ROW_NUMBER() OVER (ORDER BY NVL(OPERATE_START_TIME, CREATED_TIME) DESC) rn,
        T_BO_TASK_OPERATE_RECORD.*
        FROM T_BO_TASK_OPERATE_RECORD
        WHERE
        IS_DEL = 0
        AND BO_TRANS_TASK_ID = #{boTransTaskId}
        AND ENTITY_TYPE = #{entityType}
        AND ENTITY_NAME = #{entityName}
        AND OPERATE_TYPE = #{operateType}
        ) T
        WHERE rn = 1

    </select>

    <select id="getLatelyTaskOperateRecordByEntity" resultType="BoTaskOperateRecordBean">
        SELECT
            T.BO_TASK_OPERATE_RECORD_ID boTaskOperateRecordId,
            T.ORG_ID orgId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.OPERATOR_TYPE operatorType,
            T.OPERATOR_ID operatorId,
            T.OPERATOR_NAME operatorName,
            T.OPERATE_TYPE operateType,
            T.OPERATE_STATUS operateStatus,
            T.OPERATE_REASON operateReason,
            T.ENTITY_TYPE entityType,
            T.ENTITY_NAME entityName,
            T.JOB_FLAG jobFlag,
            T.EXTRA_FIELD extraField,
            TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') operateTime
        FROM T_BO_TASK_OPERATE_RECORD T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.OPERATE_TYPE = #{operateType}
        AND T.ENTITY_TYPE = #{entityType}
        AND T.ENTITY_NAME = #{entityName}
        ORDER BY T.CREATED_TIME DESC
    </select>
    <select id="getLatelyTaskOperateRecordByBusinessIdGroup"
            resultType="com.wtyt.dao.bean.syf.BoTaskOperateRecordBean">
        SELECT *
            FROM  (
            SELECT
                T.BO_TASK_OPERATE_RECORD_ID boTaskOperateRecordId,
                T.ORG_ID orgId,
                T.BO_TRANS_TASK_ID boTransTaskId,
                T.BUSINESS_ID businessId,
                T.BUSINESS_TYPE businessType,
                T.OPERATOR_TYPE operatorType,
                T.OPERATOR_ID operatorId,
                T.OPERATOR_NAME operatorName,
                T.OPERATE_TYPE operateType,
                T.OPERATE_STATUS operateStatus,
                T.OPERATE_REASON operateReason,
                T.ENTITY_TYPE entityType,
                T.ENTITY_NAME entityName,
                T.JOB_FLAG jobFlag,
                T.EXTRA_FIELD extraField,
                TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') operateTime,
                ROW_NUMBER() OVER(PARTITION BY T.BUSINESS_ID ORDER BY T.CREATED_TIME DESC, T.BO_TASK_OPERATE_RECORD_ID DESC) RN
            FROM T_BO_TASK_OPERATE_RECORD T
            WHERE
                T.IS_DEL = 0
                AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
                AND T.OPERATE_TYPE = #{operateType}
            ORDER BY T.CREATED_TIME DESC
        ) WHERE RN = 1
    </select>

</mapper>