<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskRelatedMapper">

    <select id="getTransTaskRelatedByTask" resultType="com.wtyt.dao.bean.syf.BoTransTaskRelatedBean">
        SELECT
            T.BO_TRANS_TASK_RELATED_ID boTransTaskRelatedId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.IS_ALLOCATE isAllocate,
            T.LOSS_FEE_NOTE lossFeeNote,
            T.FREIGHT_INCR_NOTE freightIncrNote
        FROM T_BO_TRANS_TASK_RELATED T
        WHERE T.IS_DEL = 0
        AND T.IS_ALLOCATE = #{isAllocate}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <insert id="insertTransTaskRelated">
        INSERT INTO T_BO_TRANS_TASK_RELATED (
            BO_TRANS_TASK_RELATED_ID,
            BO_TRANS_TASK_ID,
            IS_ALLOCATE,
            LOSS_FEE_NOTE,
            FREIGHT_INCR_NOTE
        ) VALUES (
            #{boTransTaskRelatedId},
            #{boTransTaskId},
            #{isAllocate},
            #{lossFeeNote},
            #{freightIncrNote}
        )
    </insert>

    <update id="updateTransTaskRelated">
        UPDATE T_BO_TRANS_TASK_RELATED T
        SET <if test="lossFeeNote != null">
                T.LOSS_FEE_NOTE = #{lossFeeNote},
            </if>
            <if test="freightIncrNote != null">
                T.FREIGHT_INCR_NOTE = #{freightIncrNote},
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_RELATED_ID = #{boTransTaskRelatedId}
    </update>

</mapper>