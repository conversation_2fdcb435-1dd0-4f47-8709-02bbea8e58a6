<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoCertificateSnapshotMapper">

    <sql id="tableName">
        T_BO_CERTIFICATE_SNAPSHOT
    </sql>

    <insert id="insert" parameterType="com.wtyt.dao.bean.syf.BoCertificateSnapshotBean">
        INSERT
            INTO
            <include refid="tableName"/> (BO_CERTIFICATE_SNAPSHOT_ID,
            ORG_ID,
            CUSTOMER_NAME,
            BO_TRANS_TASK_ID,
            OUT_CERTIFICATE_NO,
            IS_DEL,
            GOODS_CASE_PACK,
            GOODS_WEIGHT,
            <if test="goodsName != null and goodsName != ''">
                GOODS_NAME,
            </if>

            <if test="cartType != null and cartType != ''">
                CART_TYPE,
            </if>
            <if test="cartLength != null and cartLength != ''">
                CART_LENGTH,
            </if>
            <if test="cartBadgeNo != null and cartBadgeNo != ''">
                CART_BADGE_NO,
            </if>
            <if test="consigneeUnit != null and consigneeUnit != ''">
                CONSIGNEE_UNIT,
            </if>
            <if test="department != null and department != ''">
                DEPARTMENT,
            </if>
            <if test="directorSign != null and directorSign != ''">
                DIRECTOR_SIGN,
            </if>
            <if test="titleLogoUrl != null and titleLogoUrl != ''">
                TITLE_LOGO_URL,
            </if>
            <if test="batchNo != null and batchNo != ''">
                BATCH_NO,
            </if>
            <if test="outReason != null and outReason != ''">
                OUT_REASON,
            </if>
            <if test="carrierIdCard != null and carrierIdCard != ''">
                CARRIER_ID_CARD,
            </if>
            <if test="handleBy != null and handleBy != ''">
                HANDLE_BY,
            </if>
            CREATED_TIME,
            LAST_MODIFIED_TIME,
            NOTE,
            MODIFY_REAL_NAME)
        VALUES (${boCertificateSnapshotId},
        #{orgId},
        #{customerName},
        #{boTransTaskId},
        #{outCertificateNo},
        0,
        #{goodsCasePack},
        #{goodsWeight},
        <if test="goodsName != null and goodsName != ''">
            #{goodsName},
        </if>
        <if test="cartType != null and cartType != ''">
            #{cartType},
        </if>
        <if test="cartLength != null and cartLength != ''">
            #{cartLength},
        </if>
        <if test="cartBadgeNo != null and cartBadgeNo != ''">
            #{cartBadgeNo},
        </if>
        <if test="consigneeUnit != null and consigneeUnit != ''">
            #{consigneeUnit},
        </if>
        <if test="department != null and department != ''">
            #{department},
        </if>
        <if test="directorSign != null and directorSign != ''">
            #{directorSign},
        </if>
        <if test="titleLogoUrl != null and titleLogoUrl != ''">
            #{titleLogoUrl},
        </if>
        <if test="batchNo != null and batchNo != ''">
            #{batchNo},
        </if>
        <if test="outReason != null and outReason != ''">
            #{outReason},
        </if>
        <if test="carrierIdCard != null and carrierIdCard != ''">
            #{carrierIdCard},
        </if>
        <if test="handleBy != null and handleBy != ''">
            #{handleBy},
        </if>
        SYSDATE,
        SYSDATE,
        #{note},
        #{modifyRealName})
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/>
        (BO_CERTIFICATE_SNAPSHOT_ID,
        ORG_ID,
        CUSTOMER_NAME,
        BO_TRANS_TASK_ID,
        OUT_CERTIFICATE_NO,
        IS_DEL,
        GOODS_CASE_PACK,
        GOODS_WEIGHT,
        GOODS_NAME,
        CART_TYPE,
        CART_LENGTH,
        CART_BADGE_NO,
        CONSIGNEE_UNIT,
        DEPARTMENT,
        DIRECTOR_SIGN,
        TITLE_LOGO_URL,
        BATCH_NO,
        OUT_REASON,
        CARRIER_ID_CARD,
        HANDLE_BY,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE,
        MODIFY_REAL_NAME,
        OPERATE_USER_ID)
        <foreach collection="list" index="index" item="item" separator="UNION ALL">
            SELECT
            ${item.boCertificateSnapshotId} BO_CERTIFICATE_SNAPSHOT_ID,
            #{item.orgId} ORG_ID,
            #{item.customerName} CUSTOMER_NAME,
            #{item.boTransTaskId} BO_TRANS_TASK_ID,
            #{item.outCertificateNo} OUT_CERTIFICATE_NO,
            0 IS_DEL,
            #{item.goodsCasePack} GOODS_CASE_PACK,
            #{item.goodsWeight} GOODS_WEIGHT,
            #{item.goodsName} GOODS_NAME,
            #{item.cartType} CART_TYPE,
            #{item.cartLength} CART_LENGTH,
            #{item.cartBadgeNo} CART_BADGE_NO,
            #{item.consigneeUnit} CONSIGNEE_UNIT,
            #{item.department} DEPARTMENT,
            #{item.directorSign} DIRECTOR_SIGN,
            #{item.titleLogoUrl} TITLE_LOGO_URL,
            #{item.batchNo} BATCH_NO,
            #{item.outReason} OUT_REASON,
            #{item.carrierIdCard} CARRIER_ID_CARD,
            #{item.handleBy} HANDLE_BY,
            SYSDATE CREATED_TIME,
            SYSDATE LAST_MODIFIED_TIME,
            #{item.note} NOTE,
            #{item.modifyRealName} MODIFY_REAL_NAME,
            #{item.operateUserId} OPERATE_USER_ID
            FROM DUAL
        </foreach>
    </insert>

    <select id="queryByCondition" resultType="com.wtyt.dao.bean.syf.BoCertificateSnapshotBean"
            parameterType="com.wtyt.bo.bean.request.BoCertificateSnapshotSubBean">
        SELECT
            BO_CERTIFICATE_SNAPSHOT_ID boCertificateSnapshotId,
            ORG_ID orgId,
            CUSTOMER_NAME customerName,
            BO_TRANS_TASK_ID boTransTaskId,
            OUT_CERTIFICATE_NO outCertificateNo,
            GOODS_NAME goodsName,
            GOODS_CASE_PACK goodsCasePack,
            GOODS_WEIGHT goodsWeight,
            CART_TYPE cartType,
            CART_LENGTH cartLength,
            CART_BADGE_NO cartBadgeNo,
            CONSIGNEE_UNIT consigneeUnit,
            DEPARTMENT department,
            DIRECTOR_SIGN directorSign,
            TITLE_LOGO_URL titleLogoUrl,
            IS_DEL isDel,
            TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            TO_CHAR(LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI:SS') lastModifiedTime,
            NOTE note,
            MODIFY_REAL_NAME modifyRealName
        FROM <include refid="tableName"/>
        <where>
            IS_DEL = 0
            <if test="boTransTaskId != null and boTransTaskId != ''">
                AND BO_TRANS_TASK_ID = #{boTransTaskId}
            </if>
            <choose>
                <when test="customerNameList != null and customerNameList.size() > 0">
                    AND CUSTOMER_NAME IN
                    <foreach collection="customerNameList" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND CUSTOMER_NAME = #{customerName}
                </otherwise>
            </choose>
        </where>
        ORDER BY CREATED_TIME DESC
    </select>
    <select id="getLatestCertificateSnapshotBean" resultType="com.wtyt.dao.bean.syf.BoCertificateSnapshotBean"
            parameterType="com.wtyt.bo.bean.request.BoCertificateSnapshotSubBean">
        SELECT * FROM
        (SELECT
            BO_CERTIFICATE_SNAPSHOT_ID boCertificateSnapshotId,
            ORG_ID orgId,
            CUSTOMER_NAME customerName,
            BO_TRANS_TASK_ID boTransTaskId,
            OUT_CERTIFICATE_NO outCertificateNo,
            GOODS_NAME goodsName,
            GOODS_CASE_PACK goodsCasePack,
            GOODS_WEIGHT goodsWeight,
            CART_TYPE cartType,
            CART_BADGE_NO cartBadgeNo,
            CONSIGNEE_UNIT consigneeUnit,
            DEPARTMENT department,
            DIRECTOR_SIGN directorSign,
            TITLE_LOGO_URL titleLogoUrl,
            IS_DEL isDel,
            TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            TO_CHAR(LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI:SS') lastModifiedTime,
            NOTE note,
            MODIFY_REAL_NAME modifyRealName
        FROM <include refid="tableName"/>
        <where>
            IS_DEL = 0
            <if test="boTransTaskId != null and boTransTaskId != ''">
                AND BO_TRANS_TASK_ID = #{boTransTaskId}
            </if>
            <choose>
                <when test="customerNameList != null and customerNameList.size() > 0">
                    AND CUSTOMER_NAME IN
                    <foreach collection="customerNameList" index="index" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND CUSTOMER_NAME = #{customerName}
                </otherwise>
            </choose>
        </where>
        ORDER BY CREATED_TIME DESC) WHERE ROWNUM = 1
    </select>

    <select id="getRecentlyCertificateSnapshotByOperateUser" resultType="com.wtyt.dao.bean.syf.BoCertificateSnapshotBean">
        SELECT * FROM (
            SELECT
                T.BO_CERTIFICATE_SNAPSHOT_ID boCertificateSnapshotId,
                T.DIRECTOR_SIGN directorSign
            FROM <include refid="tableName"/> T
            WHERE T.IS_DEL = 0
            AND T.DIRECTOR_SIGN IS NOT NULL
            AND T.OPERATE_USER_ID = #{operateUserId}
            ORDER BY T.CREATED_TIME DESC
        ) WHERE ROWNUM = 1
    </select>
    <select id="queryByBoTransTaskIds" resultType="com.wtyt.dao.bean.syf.BoCertificateSnapshotBean"
            parameterType="java.util.List">
        SELECT
            BO_CERTIFICATE_SNAPSHOT_ID boCertificateSnapshotId,
            ORG_ID orgId,
            BO_TRANS_TASK_ID boTransTaskId
        FROM
            T_BO_CERTIFICATE_SNAPSHOT
        WHERE
            IS_DEL = 0
          AND BO_TRANS_TASK_ID IN
            <foreach collection="taskIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
</mapper>