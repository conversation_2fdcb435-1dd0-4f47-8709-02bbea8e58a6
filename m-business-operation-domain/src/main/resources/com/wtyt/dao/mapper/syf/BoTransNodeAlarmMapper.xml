<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransNodeAlarmMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTransNodeAlarmBean" id="baseResultMap">
        <result property="boTransNodeAlarmId" column="BO_TRANS_NODE_ALARM_ID" jdbcType="VARCHAR"/>
        <result property="taxWaybillId" column="TAX_WAYBILL_ID" jdbcType="VARCHAR"/>
        <result property="nodeId" column="NODE_ID" jdbcType="VARCHAR"/>
        <result property="alarmType" column="ALARM_TYPE" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="nodeDataType" column="NODE_DATA_TYPE" jdbcType="VARCHAR"/>
        <result property="alarmProcessResult" column="ALARM_PROCESS_RESULT" jdbcType="VARCHAR"/>
        <result property="bossNodeCode" column="BOSS_NODE_CODE" jdbcType="VARCHAR"/>
        <result property="alarmStartTime" column="ALARM_START_TIME" jdbcType="VARCHAR"/>
        <result property="alarmEndTime" column="ALARM_END_TIME" jdbcType="VARCHAR"/>
        <result property="address" column="ADDRESS" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insert" parameterType="BoTransNodeAlarmBean">
        INSERT INTO T_BO_TRANS_NODE_ALARM (
            BO_TRANS_NODE_ALARM_ID,
            BO_TRANS_TASK_ID,
            TAX_WAYBILL_ID,
            NODE_ID,
            NODE_DATA_TYPE,
            ALARM_TYPE,
            ALARM_PROCESS_RESULT,
            BOSS_NODE_CODE,
            <if test="alarmStartTime != null and alarmStartTime != ''">
                ALARM_START_TIME,
            </if>
            <if test="alarmEndTime != null and alarmEndTime != ''">
                ALARM_END_TIME,
            </if>
            <if test="address != null and address != ''">
                ADDRESS,
            </if>
            ORG_ID,
            NOTE,
            EXTEND
        ) VALUES (
            #{boTransNodeAlarmId},
            #{boTransTaskId},
            #{taxWaybillId},
            #{nodeId},
            #{nodeDataType},
            #{alarmType},
            #{alarmProcessResult},
            #{bossNodeCode},
            <if test="alarmStartTime != null and alarmStartTime != ''">
                TO_DATE(#{alarmStartTime}, 'YYYY-MM-DD HH24:MI:SS'),
            </if>
            <if test="alarmEndTime != null and alarmEndTime != ''">
                TO_DATE(#{alarmEndTime}, 'YYYY-MM-DD HH24:MI:SS'),
            </if>
            <if test="address != null and address != ''">
                #{address},
            </if>
            #{orgId},
            #{note},
            #{extend}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO T_BO_TRANS_NODE_ALARM (
            BO_TRANS_NODE_ALARM_ID,
            TAX_WAYBILL_ID,
            BO_TRANS_TASK_ID,
            NODE_ID,
            ALARM_TYPE,
            NODE_DATA_TYPE,
            BOSS_NODE_CODE,
            ALARM_PROCESS_RESULT,
            ALARM_START_TIME,
            IS_DEL,
            CREATED_TIME,
            LAST_MODIFIED_TIME,
            NOTE
        )
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
                #{item.boTransNodeAlarmId} BO_TRANS_NODE_ALARM_ID,
                #{item.taxWaybillId} TAX_WAYBILL_ID,
                #{item.boTransTaskId} BO_TRANS_TASK_ID,
                #{item.nodeId} NODE_ID,
                #{item.alarmType} ALARM_TYPE,
                #{item.nodeDataType} NODE_DATA_TYPE,
                #{item.bossNodeCode} BOSS_NODE_CODE,
                #{item.alarmProcessResult} ALARM_PROCESS_RESULT,
                TO_DATE(#{item.alarmStartTime}, 'yyyy-mm-dd hh24:mi:ss') ALARM_START_TIME,
                0 IS_DEL,
                SYSDATE CREATED_TIME,
                SYSDATE LAST_MODIFIED_TIME,
                #{item.note} NOTE
            FROM DUAL
        </foreach>
    </insert>

    <select id="getNodeWarnList" parameterType="java.util.List"  resultType="BoTransNodeAlarmBean">
        SELECT TAX_WAYBILL_ID taxWaybillId,
               BO_TRANS_TASK_ID boTransTaskId,
               NODE_ID nodeId,
               TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
        FROM T_BO_TRANS_NODE_ALARM T
        WHERE T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND T.ALARM_TYPE = 1
        AND T.IS_DEL = 0
        AND T.NODE_DATA_TYPE IN (
            SELECT NODE_DATA_TYPE
            FROM T_BO_ROLE_ALARM_TYPE_REL
            WHERE ROLE_ID = 12000
            AND IS_DEL = 0
        )
        ORDER BY T.CREATED_TIME
    </select>
    <select id="getNodeWarnListApp" parameterType="java.util.List"  resultType="BoTransNodeAlarmBean">
        SELECT TAX_WAYBILL_ID taxWaybillId,
        BO_TRANS_TASK_ID boTransTaskId,
        NODE_ID nodeId,
        ALARM_TYPE alarmType,
        TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        TO_CHAR(T.ALARM_END_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmEndTime,
        TO_CHAR(T.ALARM_START_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmStartTime,
        T.NODE_DATA_TYPE nodeDataType
        FROM T_BO_TRANS_NODE_ALARM T
        WHERE T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND T.ALARM_TYPE =1
        AND T.ALARM_PROCESS_RESULT = 0
        AND T.IS_DEL = 0
        AND T.NODE_DATA_TYPE IN
        <foreach collection="nodeDataTypeList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="getShowNodeWarnListById" resultType="BoTransNodeAlarmBean">
        SELECT TAX_WAYBILL_ID taxWaybillId,
            BO_TRANS_TASK_ID boTransTaskId,
            NODE_ID nodeId,
            TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            BOSS_NODE_CODE bossNodeCode,
            ALARM_TYPE alarmType,
            ALARM_PROCESS_RESULT alarmProcessResult,
            NODE_DATA_TYPE nodeDataType
        FROM T_BO_TRANS_NODE_ALARM T
        WHERE T.BO_TRANS_TASK_ID  = #{boTransTaskId}
        AND T.IS_DEL = 0
        AND BOSS_NODE_CODE is not null
        AND T.ALARM_TYPE in (1,2)
        AND T.NODE_DATA_TYPE != '4'
        AND T.NODE_DATA_TYPE IN (
            SELECT NODE_DATA_TYPE
            FROM T_BO_ROLE_ALARM_TYPE_REL
            WHERE ROLE_ID = #{roleId}
              AND IS_DEL = 0
        )
        ORDER BY CREATED_TIME desc
    </select>

    <update id="delNodeAlarm" parameterType="BoTransNodeAlarmBean">
        UPDATE T_BO_TRANS_NODE_ALARM SET
        IS_DEL = 1,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_TASK_ID = #{boTransTaskId}
        and NODE_ID = #{nodeId}
        and ALARM_TYPE = #{alarmType}
    </update>

    <update id="batchUpdateProcessResult" parameterType="BoTransNodeAlarmBean">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_TRANS_NODE_ALARM T
            SET T.ALARM_PROCESS_RESULT = #{item.alarmProcessResult},
                <if test="item.alarmProcessResult == '1'.toString()">
                    T.ALARM_END_TIME =
                        CASE
                            WHEN ALARM_START_TIME IS NOT NULL AND ALARM_END_TIME IS NULL THEN SYSDATE
                            ELSE ALARM_END_TIME
                        END,
                </if>
                T.LAST_MODIFIED_TIME = SYSDATE
            WHERE T.IS_DEL = 0
            AND T.NODE_ID = #{item.nodeId}
            AND T.ALARM_TYPE = #{item.alarmType}
            AND T.NODE_DATA_TYPE = #{item.nodeDataType}
            AND T.ALARM_PROCESS_RESULT != #{item.alarmProcessResult}
            AND T.BO_TRANS_TASK_ID = #{item.boTransTaskId}
        </foreach>
    </update>

    <select id="queryIdListNotProcess" resultType="string">
        SELECT BO_TRANS_NODE_ALARM_ID
        FROM T_BO_TRANS_NODE_ALARM
        WHERE BO_TRANS_TASK_ID = #{boTransTaskId}
        AND ALARM_PROCESS_RESULT = 0
        AND IS_DEL = 0
        AND NODE_DATA_TYPE IN
        <foreach collection="nodeDataTypeList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryIdList" resultType="string">
        SELECT BO_TRANS_NODE_ALARM_ID
        FROM T_BO_TRANS_NODE_ALARM
        WHERE BO_TRANS_TASK_ID = #{boTransTaskId}
        AND IS_DEL = 0
        AND NODE_DATA_TYPE IN
        <foreach collection="nodeDataTypeList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryBeanList" resultMap="baseResultMap">
        SELECT * FROM (SELECT A.BO_TRANS_NODE_ALARM_ID,A.TAX_WAYBILL_ID,A.NODE_ID,A.ALARM_TYPE,A.IS_DEL,
        A.NOTE,A.NODE_DATA_TYPE,A.ALARM_PROCESS_RESULT,A.BOSS_NODE_CODE, TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') CREATED_TIME,
        TO_CHAR(LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI:SS') LAST_MODIFIED_TIME,
        TO_CHAR(A.ALARM_START_TIME, 'YYYY-MM-DD HH24:MI:SS') ALARM_START_TIME, TO_CHAR(A.ALARM_END_TIME, 'YYYY-MM-DD HH24:MI:SS') ALARM_END_TIME, A.ADDRESS ADDRESS
        ,ROW_NUMBER() OVER(PARTITION BY A.BO_TRANS_TASK_ID,A.NODE_ID ORDER BY A.CREATED_TIME DESC) RN
        FROM T_BO_TRANS_NODE_ALARM A
        WHERE A.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND A.IS_DEL = 0
        AND A.NODE_DATA_TYPE IN
        <foreach collection="nodeDataTypeList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        )
    </select>

    <select id="queryTransNodeAlarmList" resultType="com.wtyt.dao.bean.syf.BoTransNodeAlarmBean">
        SELECT
            T.BO_TRANS_NODE_ALARM_ID boTransNodeAlarmId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.NODE_ID nodeId,
            T.ALARM_TYPE alarmType,
            T.NODE_DATA_TYPE nodeDataType,
            T.ALARM_PROCESS_RESULT alarmProcessResult,
            T.BOSS_NODE_CODE bossNodeCode,
            TO_CHAR(T.ALARM_END_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmEndTime,
            TO_CHAR(T.ALARM_START_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmStartTime,
            T.ADDRESS address
        FROM T_BO_TRANS_NODE_ALARM T
        WHERE T.IS_DEL = 0
        <if test="alarmType!=null and alarmType!=''">
            AND T.ALARM_TYPE = #{alarmType}
        </if>
        AND T.NODE_DATA_TYPE = #{nodeDataType}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY CREATED_TIME DESC
    </select>

    <update id="batchUpdateClearAlarm">
        UPDATE T_BO_TRANS_NODE_ALARM T
        SET T.ALARM_PROCESS_RESULT = 1,
            T.ALARM_END_TIME =
            CASE
                WHEN ALARM_START_TIME IS NOT NULL AND ALARM_END_TIME IS NULL THEN SYSDATE
                ELSE ALARM_END_TIME
            END,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_NODE_ALARM_ID IN
        <foreach item="item" index="index" collection="alarmIdList" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchDelByAlarmIds">
        UPDATE T_BO_TRANS_NODE_ALARM SET
        IS_DEL = 1,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_NODE_ALARM_ID IN
        <foreach item="item" index="index" collection="alarmIdList" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND IS_DEL = 0
    </update>
    <update id="updateProcessResult">
        UPDATE
            T_BO_TRANS_NODE_ALARM
        SET
            ALARM_PROCESS_RESULT = #{alarmProcessResult},
            <if test="alarmStartTime != null and alarmStartTime != ''">
                ALARM_START_TIME = TO_DATE(#{alarmStartTime}, 'YYYY-MM-DD HH24:MI:SS'),
            </if>
            <if test="alarmEndTime != null and alarmEndTime != ''">
                ALARM_END_TIME = TO_DATE(#{alarmEndTime}, 'YYYY-MM-DD HH24:MI:SS'),
            </if>
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TRANS_NODE_ALARM_ID = #{boTransNodeAlarmId}
            AND IS_DEL = 0
            AND ALARM_PROCESS_RESULT != #{alarmProcessResult}
    </update>
    <select id="queryNodeAlarmData" resultType="com.wtyt.dao.bean.syf.BoTransNodeAlarmBean">
        SELECT
            a.BO_TRANS_NODE_ALARM_ID boTransNodeAlarmId,
            a.TAX_WAYBILL_ID taxWaybillId,
            a.BO_TRANS_TASK_ID boTransTaskId,
            a.NODE_ID nodeId,
            a.ALARM_TYPE alarmType,
            a.NODE_DATA_TYPE nodeDataType,
            a.ALARM_PROCESS_RESULT alarmProcessResult,
            a.BOSS_NODE_CODE bossNodeCode
        FROM T_BO_TRANS_NODE_ALARM a
        WHERE
        a.IS_DEL = 0
        AND a.NODE_DATA_TYPE IN
        <foreach collection="nodeDataTypeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND a.ALARM_TYPE = 1
        AND a.ALARM_PROCESS_RESULT = 0
        AND a.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getUnprocessedNodeAlarmList" resultType="com.wtyt.dao.bean.syf.BoTransNodeAlarmBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.NODE_ID nodeId,
            T.NODE_DATA_TYPE nodeDataType,
            T.ALARM_TYPE alarmType,
            T.EXTEND extend,
            TO_CHAR(T.ALARM_END_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmEndTime,
            TO_CHAR(T.ALARM_START_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmStartTime,
            TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime
        FROM T_BO_TRANS_NODE_ALARM T
        WHERE T.IS_DEL = 0
        AND T.NODE_DATA_TYPE IN
        <foreach collection="nodeDataTypeList" item="nodeDataType" open="(" separator="," close=")">
            #{nodeDataType}
        </foreach>
        AND T.ALARM_PROCESS_RESULT IN (0,3)
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
            #{boTransTaskId}
        </foreach>
        ORDER BY T.CREATED_TIME DESC
    </select>

    <select id="queryById" resultMap="baseResultMap">
        SELECT
            NODE_DATA_TYPE,
            ALARM_PROCESS_RESULT,
            TO_CHAR(ALARM_START_TIME, 'yyyy-mm-dd hh24:mi:ss') ALARM_START_TIME,
            TO_CHAR(ALARM_END_TIME, 'yyyy-mm-dd hh24:mi:ss') ALARM_END_TIME,
            ADDRESS
        FROM
            T_BO_TRANS_NODE_ALARM
        WHERE
            IS_DEL = 0
            AND BO_TRANS_NODE_ALARM_ID = #{id}
    </select>

    <select id="getNodeAlarmCount" resultType="java.lang.Integer">
        SELECT
            NVL(COUNT(*), 0)
        FROM T_BO_TRANS_NODE_ALARM T
        WHERE T.IS_DEL = 0
        <if test="alarmType != null and alarmType.length > 0">
            AND T.ALARM_TYPE = #{alarmType}
        </if>
        <if test="nodeDataType != null and nodeDataType.length > 0">
            AND T.NODE_DATA_TYPE = #{nodeDataType}
        </if>
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <select id="countUnProcessAlarm" resultType="java.lang.Integer">
        SELECT
        NVL(COUNT(*), 0)
        FROM T_BO_TRANS_NODE_ALARM T
        WHERE T.IS_DEL = 0
        <if test="alarmType != null and alarmType.length > 0">
            AND T.ALARM_TYPE = #{alarmType}
        </if>
        <if test="nodeDataType != null and nodeDataType.length > 0">
            AND T.NODE_DATA_TYPE = #{nodeDataType}
        </if>
        AND T.ALARM_PROCESS_RESULT IN (0,3)
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <update id="updateWarningProcessed">
        UPDATE T_BO_TRANS_NODE_ALARM T
        SET T.ALARM_PROCESS_RESULT = 1,
            T.ALARM_END_TIME =
                CASE
                    WHEN ALARM_START_TIME IS NOT NULL AND ALARM_END_TIME IS NULL
                    THEN SYSDATE
                    ELSE ALARM_END_TIME
                END,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND ALARM_PROCESS_RESULT = 0
        AND ALARM_TYPE = 0
        AND NODE_DATA_TYPE = #{nodeDataType}
        AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <select id="getTransNodeAlarmList" resultType="com.wtyt.dao.bean.syf.BoTransNodeAlarmBean">
        SELECT
            T.NODE_DATA_TYPE nodeDataType,
            T.ALARM_TYPE alarmType
        FROM (
            SELECT
                T1.NODE_DATA_TYPE,
                T1.ALARM_TYPE,
                T1.ALARM_PROCESS_RESULT
            FROM T_BO_TRANS_NODE_ALARM T1
            JOIN T_BO_TRANS_TASK T2
                ON T2.BO_TRANS_TASK_ID = T1.BO_TRANS_TASK_ID
            WHERE T1.IS_DEL = 0
            AND T2.IS_DEL = 0
            <if test="alarmList != null and alarmList.size() > 0">
                <foreach collection="alarmList" item="alarm" open="AND (" separator=" OR " close=")">
                    (T1.NODE_DATA_TYPE = #{alarm.nodeDataType} AND T1.ALARM_TYPE = #{alarm.alarmType}
                    <if test="alarm.alarmProcessResultList != null and alarm.alarmProcessResultList.size() > 0">
                        AND T1.ALARM_PROCESS_RESULT IN
                        <foreach collection="alarm.alarmProcessResultList" item="alarmProcessResult" open="(" separator="," close=")">
                            #{alarmProcessResult}
                        </foreach>
                    </if>
                    )
                </foreach>
            </if>
            AND T2.ORG_ID = #{orgId}

            UNION

            SELECT
                T3.NODE_DATA_TYPE,
                T3.ALARM_TYPE,
                T3.ALARM_PROCESS_RESULT
            FROM T_BO_TRANS_NODE_ALARM T3
            JOIN T_BO_TRANS_TASK T4
                ON T4.BO_TRANS_TASK_ID = T3.BO_TRANS_TASK_ID
            JOIN T_BO_TRANS_TASK_ALLOCATE T5
                ON T3.BO_TRANS_TASK_ID = T5.BO_TRANS_TASK_ID
            WHERE T3.IS_DEL = 0
            AND T4.IS_DEL = 0
            AND T5.IS_DEL = 0
            <if test="alarmList != null and alarmList.size() > 0">
                <foreach collection="alarmList" item="alarm" open="AND (" separator=" OR " close=")">
                    (T3.NODE_DATA_TYPE = #{alarm.nodeDataType} AND T3.ALARM_TYPE = #{alarm.alarmType}
                    <if test="alarm.alarmProcessResultList != null and alarm.alarmProcessResultList.size() > 0">
                        AND T3.ALARM_PROCESS_RESULT IN
                        <foreach collection="alarm.alarmProcessResultList" item="alarmProcessResult" open="(" separator="," close=")">
                            #{alarmProcessResult}
                        </foreach>
                    </if>
                    )
                </foreach>
            </if>
            AND T5.ORG_ID = #{orgId}
        )T
        GROUP BY T.NODE_DATA_TYPE, T.ALARM_TYPE
        ORDER BY T.NODE_DATA_TYPE, T.ALARM_TYPE
    </select>

    <select id="getTransTaskAlarmList" resultType="com.wtyt.dao.bean.syf.BoTransNodeAlarmBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.NODE_DATA_TYPE nodeDataType,
            T.ALARM_TYPE alarmType,
            T.EXTEND extend,
            TO_CHAR(T.ALARM_START_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmStartTime,
            TO_CHAR(T.ALARM_END_TIME, 'yyyy-mm-dd hh24:mi:ss') alarmEndTime,
            TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
            TO_CHAR(T.LAST_MODIFIED_TIME, 'yyyy-mm-dd hh24:mi:ss') lastModifiedTime,
            T.ALARM_PROCESS_RESULT alarmProcessResult
        FROM T_BO_TRANS_NODE_ALARM T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="boTransTaskId" open="(" separator="," close=")">
            #{boTransTaskId}
        </foreach>
        ORDER BY T.LAST_MODIFIED_TIME DESC
    </select>

    <update id="deleteAlarmByCancelDispatched">
        UPDATE T_BO_TRANS_NODE_ALARM T
        SET T.IS_DEL = 1,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.NODE_ID >= 200
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

</mapper>
