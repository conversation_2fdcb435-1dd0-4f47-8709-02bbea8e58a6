<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoDispatchAlarmMapper">

    <select id="getNeedAlarmWaybillList" parameterType="BoXxlJobAlarmBean" resultType="BoXxlJobAlarmBean">
        SELECT T.TAX_WAYBILL_ID         taxWaybillId,
            T.BO_TRANS_TASK_ID    boTransTaskId,
            L.NODE_ID                nodeId,
            TO_CHAR(L.DEAD_LINE_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
            A.BO_TRANS_NODE_ALARM_ID boTransNodeAlarmId,
            nvl(A.NODE_DATA_TYPE,'1') nodeDataType,
            N.BOSS_NODE_CODE bossNodeCode
        FROM T_BO_TRANS_NODE_DEAD_LINE L
        LEFT JOIN T_BO_TRANS_NODE_ALARM A
        ON L.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
        AND L.NODE_ID = A.NODE_ID
        AND A.NODE_DATA_TYPE = 1
        AND A.ALARM_TYPE = #{alarmType}
        AND A.IS_DEL = 0
        LEFT JOIN T_BO_WAYBILL_BOSS_NODE N on L.BO_TRANS_TASK_ID = N.BO_TRANS_TASK_ID and N.is_del = 0
        ,T_BO_TRANS_TASK T, T_BO_TRANS_TASK_EXTRA TE
        WHERE L.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        AND L.BO_TRANS_TASK_ID = TE.BO_TRANS_TASK_ID
        AND (T.TRANSPORT_TYPE =0 OR T.TRANSPORT_TYPE IS NULL)
        AND L.NODE_ID = #{nodeId}
        <if test="alarmType == '0'.toString()">
            AND L.DEAD_LINE_TIME >= SYSDATE
            AND L.DEAD_LINE_TIME &lt;= SYSDATE + #{lastDispatchTimeBefore} / 24
        </if>
        <if test="alarmType == '1'.toString()">
            AND L.DEAD_LINE_TIME &lt; SYSDATE
        </if>
        <if test="dispatchedTime != null and dispatchedTime.length > 0">
            AND TE.CREATED_TIME <![CDATA[<]]> TO_DATE(#{dispatchedTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>
        AND L.IS_DEL = 0
        AND T.CART_BADGE_NO IS NULL
        AND T.IS_DEL = 0
    </select>
</mapper>