<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.fh.mapper.syf.FirstHandTemplateMapper">
    

    <select id="queryToFhConfig" resultType="com.wtyt.fh.bean.ToFhConfigBean">
        SELECT
            BO_TO_FH_CONFIG_ID boToFhConfigId,
            STICK_CONTENT_TIPS stickContentTips,
            GROUP_CODE groupCode,
            GROUP_TYPE groupType,
            VALID_STATE validState
        FROM
            T_BO_TO_FH_CONFIG
        WHERE
            IS_DEL = 0
            AND GROUP_CODE = #{groupCode}
            AND GROUP_TYPE = #{groupType}
            AND VALID_STATE = 1
    </select>
    <resultMap id="toFhHeaderMap" type="com.wtyt.fh.bean.ToFhHeaderBean">
        <id column="BO_TO_FH_HEADER_ID" property="boToFhHeaderId"/>
        <result column="BO_TO_FH_CONFIG_ID" property="boToFhConfigId"/>
        <result column="HEADER_NAME" property="headerName"/>
        <result column="IS_REQUIRED" property="isRequired"/>
        <result column="DEFAULT_VALUE" property="defaultValue"/>
        <result column="OPTION_VALUES" property="optionValues"/>
        <result column="IS_MODIFY" property="isModify"/>
        <collection property="headerMapList" ofType="com.wtyt.fh.bean.ToFhHeaderBean$HeaderMap">
            <id column="BO_TO_FH_HEADER_MAP_ID" property="boToFhHeaderMapId"  />
            <result column="DEFAULT_UNIT" property="defaultUnit" />
            <result column="BO_TP_DH_TASK_FIELD_DICT_ID" property="boTpTaskFieldDictId" />
            <result column="FIELD_NAME" property="fieldName" />
            <result column="BO_TO_FH_RECOGNIZE_DICT_ID" property="boToFhRecognizeDictId" />
            <result column="RECOGNIZE_FIELD_NAME" property="recognizeFieldName" />
            <result column="INPUT_TYPE" property="inputType" />
        </collection>
    </resultMap>
    <select id="queryToFhHeaderListByConfigId" resultMap="toFhHeaderMap">
        SELECT
            h.BO_TO_FH_HEADER_ID ,
            h.BO_TO_FH_CONFIG_ID ,
            h.HEADER_NAME ,
            h.IS_REQUIRED ,
            h.DEFAULT_VALUE ,
            h.OPTION_VALUES ,
            h.IS_MODIFY,
            m.BO_TO_FH_HEADER_MAP_ID,
            m.DEFAULT_UNIT ,
            d.BO_TP_DH_TASK_FIELD_DICT_ID,
            d.FIELD_NAME,
            rd.BO_TO_FH_RECOGNIZE_DICT_ID,
            rd.RECOGNIZE_FIELD_NAME,
            rd.INPUT_TYPE
        FROM
            T_BO_TO_FH_HEADER h
        INNER JOIN T_BO_TO_FH_HEADER_MAP m ON
            h.BO_TO_FH_HEADER_ID = m.BO_TO_FH_HEADER_ID
        INNER JOIN T_BO_TO_FH_RECOGNIZE_DICT rd ON
            m.BO_TO_FH_RECOGNIZE_DICT_ID = rd.BO_TO_FH_RECOGNIZE_DICT_ID
        LEFT JOIN T_BO_TP_DH_TASK_FIELD_DICT d ON
            m.BO_TP_DH_TASK_FIELD_DICT_ID = d.BO_TP_DH_TASK_FIELD_DICT_ID AND d.IS_DEL = 0
        WHERE
            h.IS_DEL = 0 AND m.IS_DEL = 0 AND rd.IS_DEL = 0
            AND h.BO_TO_FH_CONFIG_ID = #{boToFhConfigId} ORDER BY h.SORT_NUM ASC , m.BO_TO_FH_HEADER_MAP_ID ASC
    </select>
    <select id="queryToFhConfigById" resultType="com.wtyt.fh.bean.ToFhConfigBean">
        SELECT
            BO_TO_FH_CONFIG_ID boToFhConfigId,
            STICK_CONTENT_TIPS stickContentTips,
            GROUP_CODE groupCode,
            GROUP_TYPE groupType,
            VALID_STATE validState
        FROM
            T_BO_TO_FH_CONFIG
        WHERE
            IS_DEL = 0
            AND BO_TO_FH_CONFIG_ID = #{boToFhConfigId}
    </select>


</mapper>
