<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.account.mapper.syf.AccountDetailQueryMapper">


    <select id="queryChargeDetailList" resultType="com.wtyt.dao.bean.syf.BoTaskChargeDetailBean">
        SELECT
            TO_CHAR(d.CHARGE_TIME, 'YYYY-MM-DD HH24:MI:SS') chargeTime,
            d.ORG_ID orgId,
            d.USER_NAME userName,
            d.SYS_ROLE_TYPE sysRoleType,
            d.MOBILE_NO mobileNo,
            d.CHARGE_TYPE chargeType,
            d.CHARGE_AMOUNT chargeAmount,
            t.TAX_WAYBILL_NO taxWaybillNo
        FROM
            T_BO_TASK_CHARGE_DETAIL d
        LEFT JOIN T_BO_TRANS_TASK t ON
            t.BO_TRANS_TASK_ID = d.BO_TRANS_TASK_ID
        WHERE
            d.IS_DEL = 0
            <if test="companyId != null and companyId !=''">
                AND d.COMPANY_ID = #{companyId}
            </if>
            <if test="queryOrgId != null and queryOrgId !=''">
                AND d.ORG_ID = #{queryOrgId}
            </if>
            <if test="keyWords != null and keyWords !=''">
                AND (d.USER_NAME = #{keyWords}
                OR d.MOBILE_NO = #{keyWords})
            </if>
            <if test="startDate != null and startDate !=''">
                AND d.CHARGE_TIME >= TO_DATE(#{startDate} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endDate != null and endDate !=''">
                AND d.CHARGE_TIME &lt;= TO_DATE(#{endDate} || '23:59:59','YYYY-MM-DD HH24:MI:SS')
            </if>
        ORDER BY
            d.CHARGE_TIME DESC,d.CHARGE_TYPE desc
    </select>
</mapper>
