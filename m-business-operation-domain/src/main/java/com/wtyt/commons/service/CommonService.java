package com.wtyt.commons.service;

import cn.hutool.core.util.ReflectUtil;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.wtyt.bean.TransTaxFeeCfgBean;
import com.wtyt.bo.mapper.syf.BoTaskDetailMapper;
import com.wtyt.common.beans.OrgInfoBean;
import com.wtyt.common.constants.Constants;
import com.wtyt.common.constants.SidConstants;
import com.wtyt.common.enums.DataResourceCodeEnum;
import com.wtyt.common.enums.GoodsAmountTypeEnum;
import com.wtyt.common.enums.YesOrNotEnum;
import com.wtyt.common.rpc.bean.*;
import com.wtyt.common.rpc.service.RpcGwayService;
import com.wtyt.common.toolkits.AlarmToolkit;
import com.wtyt.common.toolkits.GsonToolkit;
import com.wtyt.commons.apollo.ApolloConfigCenter;
import com.wtyt.commons.featureprobe.FeatureProbeUtil;
import com.wtyt.commons.toolkits.GoodsUnitToolkit;
import com.wtyt.dao.bean.syf.BoTransTaskBean;
import com.wtyt.dao.mapper.syf.BoTransTaskMapper;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.util.OrgCacheDataUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/9 16:38
 * @description
 */
@Service("domainCommonService")
public class CommonService {
    private final static Logger log = LoggerFactory.getLogger(CommonService.class);

    @Autowired
    private RpcGwayService rpcGwayService;
    @Autowired
    private GoodsUnitToolkit goodsUnitToolkit;
    @Autowired
    private ApolloConfigCenter apolloConfigCenter;
    @Autowired
    private BoTransTaskMapper boTransTaskMapper;
    @Autowired
    private BoTaskDetailMapper boTaskDetailMapper;

    /**
     * 获取用户姓名
     * @param userId
     * @return
     */
    public String getUserName(String userId){
        if(StringUtils.equals(userId,Constants.MINUS_ONE)){
            return null;
        }
        Map<String, String> userNameMap = this.getUserNameMap(Collections.singletonList(userId));
        return userNameMap.containsKey(userId) ? userNameMap.get(userId) : null;
    }

    /**
     * 批量获取用户姓名
     * @param userIds
     * @return
     */
    public Map<String,String> getUserNameMap(List<String> userIds){
        Map<String,String> userNameMap = new HashMap<>();
        if(CollectionUtils.isEmpty(userIds)){
            return userNameMap;
        }
        List<String> newUserIds = userIds.stream().filter(userId -> StringUtils.isNotBlank(userId) && !Constants.MINUS_ONE.equals(userId)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(newUserIds)){
            return userNameMap;
        }
        Rpc19025IBean iBean = new Rpc19025IBean();
        iBean.setUserIds(newUserIds);
        try {
            Rpc19025OBean oBean = rpcGwayService.rpcResult(iBean, SidConstants.SID_19025, Rpc19025OBean.class);
            if(oBean!=null && CollectionUtils.isNotEmpty(oBean.getUserInfoList())){
                userNameMap = oBean.getUserInfoList().stream().filter(o->StringUtils.isNotBlank(o.getRealName()))
                        .collect(Collectors.toMap(Rpc19025OBean::getUserId,Rpc19025OBean::getRealName));
            }
        } catch (Exception e) {
            AlarmToolkit.logAlarm(e.getLocalizedMessage(),e,null);
            log.error(e.getLocalizedMessage(),e);
        }

        return userNameMap;
    }

    public Rpc19025OBean getUserInfo(String userId){
        if(StringUtils.isBlank(userId) || Constants.MINUS_ONE.equals(userId)){
            return null;
        }
        Rpc19025IBean iBean = new Rpc19025IBean();
        iBean.setUserIds(Collections.singletonList(userId));
        Rpc19025OBean oBean = rpcGwayService.rpcResultNoException(iBean, SidConstants.SID_19025, Rpc19025OBean.class);
        if(oBean ==null || CollectionUtils.isEmpty(oBean.getUserInfoList())){
            return null;
        }

        return oBean.getUserInfoList().get(0);
    }



    /**
     * 获取运输任务相关保障信息
     * @param taskIds 任务ID
     * @param waybillIdTaskIdMap <运单ID,任务ID> 可以传null
     * @return
     */
    public Map<String, Rpc5351013OBean> getInsureInfoMap(List<String> taskIds, Map<String, String> waybillIdTaskIdMap){
        Map<String, Rpc5351013OBean> map = new HashMap<>();
        if(CollectionUtils.isEmpty(taskIds)){
            return map;
        }
        if(waybillIdTaskIdMap==null){
            waybillIdTaskIdMap = new HashMap<>(0);
        }
        //运单ID
        Set<String> taxWaybillIds = waybillIdTaskIdMap.keySet();
        List<Rpc5351013IBean.BusinessData> businessDataList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(taxWaybillIds)){
            for (String taxWaybillId : taxWaybillIds) {
                Rpc5351013IBean.BusinessData businessData = new Rpc5351013IBean.BusinessData();
                //运单
                businessData.setType(Constants.ONE);
                businessData.setBusinessId(taxWaybillId);
                businessDataList.add(businessData);
            }
        }
        Collection<String> hasWaybillTaskIds = waybillIdTaskIdMap.values();
        for (String taskId : taskIds) {
            if(CollectionUtils.isEmpty(hasWaybillTaskIds) || !hasWaybillTaskIds.contains(taskId)){
                //非结算的运输任务
                Rpc5351013IBean.BusinessData businessData = new Rpc5351013IBean.BusinessData();
                //运输任务
                businessData.setType(Constants.TWO);
                businessData.setBusinessId(taskId);
                businessDataList.add(businessData);
            }
        }

        try {
            //接口最多支持50比，这里循环查询
            for (List<Rpc5351013IBean.BusinessData> tempList : Lists.partition(businessDataList, 50)) {
                Rpc5351013IBean iBean = new Rpc5351013IBean();
                iBean.setGroupName(Constants.TF);
                iBean.setBusinessDataList(tempList);
                Rpc5351013OBean rpc5351013OBean = rpcGwayService.rpcResult(iBean, SidConstants.SID_5351013, Rpc5351013OBean.class);
                if(rpc5351013OBean!=null && CollectionUtils.isNotEmpty(rpc5351013OBean.getInsureList())){
                    for (Rpc5351013OBean tempBean : rpc5351013OBean.getInsureList()) {
                        if(StringUtils.isAnyBlank(tempBean.getBusinessId(),tempBean.getType())){
                            continue;
                        }
                        switch (tempBean.getType()){
                            case Constants.ONE:
                                //运单
                                String tempTaskId = waybillIdTaskIdMap.get(tempBean.getBusinessId());
                                map.put(tempTaskId,tempBean);
                                break;
                            case Constants.TWO:
                                //运输任务
                                map.put(tempBean.getBusinessId(),tempBean);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        } catch (UnifiedBusinessException e) {
            log.error(e.getLocalizedMessage(),e);
            AlarmToolkit.logAlarm(e.getLocalizedMessage(),e,null);
        }

        return map;
    }

    /**
     * 根据货物单位名称获取货物单位类型
     * @param desc
     * @return
     */
    public String getGoodsAmountTypeByDesc(String desc){
        String goodsAmountType = null;
        if(StringUtils.isBlank(desc)){
            return null;
        }
        goodsAmountType = GoodsAmountTypeEnum.getGoodsAmountTypeByDesc(desc);
        if(StringUtils.isBlank(goodsAmountType)){
            try {
                Map<String, String> goodsDictMap = goodsUnitToolkit.getReverseGoodsDictMap();
                goodsAmountType = goodsDictMap.get(desc);
            } catch (Exception e) {
                log.error(e.getLocalizedMessage(),e);
                AlarmToolkit.logAlarm(e.getLocalizedMessage(),e,null);
            }
        }

        return goodsAmountType;
    }

    /**
     * 根据orgId和（单个）手机号获取用户信息
     * @param orgId
     * @param mobileNo
     * @return
     */
    public Rpc19092OBean getUserInfoByOrgIdAndMobileNo(String orgId,String mobileNo){
        if(StringUtils.isAnyBlank(orgId,mobileNo)){
            return null;
        }
        Rpc19092IBean iBean = new Rpc19092IBean();
        iBean.setOrgId(orgId);
        iBean.setMobileNos(Collections.singletonList(mobileNo));
        Rpc19092OBean rpc19092OBean = rpcGwayService.rpcResultNoException(iBean, SidConstants.SID_19092, Rpc19092OBean.class);
        if(rpc19092OBean== null || CollectionUtils.isEmpty(rpc19092OBean.getUserList())){
            return null;
        }

        return rpc19092OBean.getUserList().get(0);
    }

    /**
     * 根据orgId和手机号批量获取用户信息
     * @param orgId
     * @param mobileNo
     * @return
     */
    public Map<String,Rpc19092OBean> getUserInfoNotForbidden(String orgId,List<String> mobileNos){
        if(StringUtils.isBlank(orgId) || CollectionUtils.isEmpty(mobileNos)){
            return MapUtils.EMPTY_MAP;
        }
        Rpc19092IBean iBean = new Rpc19092IBean();
        iBean.setOrgId(orgId);
        iBean.setMobileNos(mobileNos);
        Rpc19092OBean rpc19092OBean = rpcGwayService.rpcResultNoException(iBean, SidConstants.SID_19092, Rpc19092OBean.class);
        if(rpc19092OBean== null || CollectionUtils.isEmpty(rpc19092OBean.getUserList())){
            return MapUtils.EMPTY_MAP;
        }

        return rpc19092OBean.getUserList().stream().filter(o->!Constants.ONE.equals(o.getUserState()))
                .collect(Collectors.toMap(Rpc19092OBean::getMobileNo,o->o,(v1,v2)->v1));
    }

    /**
     * 获取用户项目下的资源权限
     * @param userId
     * @param orgId
     * @param dataResourceCodeList
     * @return
     */
    public List<Rpc19098OBean.DataResourcePermission> getDataPermission(String userId, String orgId, List<String> dataResourceCodeList) {
        Rpc19098IBean rpc19098IBean = new Rpc19098IBean();
        rpc19098IBean.setQueryUserId(userId);
        rpc19098IBean.setOrgId(orgId);
        rpc19098IBean.setDataResourceCodeList(dataResourceCodeList);
        Rpc19098OBean rpc19098OBean = rpcGwayService.rpcResult(rpc19098IBean, SidConstants.SID_19098, Rpc19098OBean.class);
        return rpc19098OBean==null? new ArrayList<>() : rpc19098OBean.getDataResourcePermissionList();
    }

    public List<Rpc19098OBean.DataResourcePermission> getDataPermission(String userId, String orgId, List<String> groupIdList, List<String> dataResourceCodeList) {
        if(CollectionUtils.isEmpty(groupIdList)){
            return this.getDataPermission(userId,orgId,dataResourceCodeList);
        }
        /**
         * 根据groupId结合查询,最大支持100个，先不多线程，应该也不会太多
         */
        List<Rpc19098OBean.DataResourcePermission> list = new ArrayList<>();
        for (List<String> tempList : Lists.partition(groupIdList, 100)) {
            Rpc19098IBean rpc19098IBean = new Rpc19098IBean();
            rpc19098IBean.setQueryUserId(userId);
            rpc19098IBean.setGroupIdList(tempList);
            rpc19098IBean.setDataResourceCodeList(dataResourceCodeList);
            Rpc19098OBean rpc19098OBean = rpcGwayService.rpcResult(rpc19098IBean, SidConstants.SID_19098, Rpc19098OBean.class);
            if(rpc19098OBean!=null && CollectionUtils.isNotEmpty(rpc19098OBean.getDataResourcePermissionList())){
                list.addAll(rpc19098OBean.getDataResourcePermissionList());
            }
        }
        return list;
    }

    /**
     * 获取运输任务列表数据权限
     * @param userId
     * @param orgId
     * @param groupIdList
     * @return
     */
    public List<Rpc19098OBean.DataResourcePermission> getDataPermissionTaskList(String userId, String orgId, List<String> groupIdList) {
        List<String> dataResourceCodeList = null;
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            //管理端不要随车清单的条件
            dataResourceCodeList = Arrays.asList("YWYZ-YSRWGL");
        } else {
            dataResourceCodeList = Arrays.asList("YWYZ-YSRWGL", "YWYZ-SCQD");
        }
        return this.getDataPermissionList(userId, orgId, groupIdList, dataResourceCodeList);
    }
    /**
     * 获取运输计划列表数据权限
     * @param userId
     * @param orgId
     * @param groupIdList
     * @return
     */
    public List<Rpc19098OBean.DataResourcePermission> getOrderDataPermissionList(String userId, String orgId, List<String> groupIdList){
        String resourceCode = apolloConfigCenter.boCommons.getProperty("dataResourceCode.ywyz.ysjhgl");
        List<String> dataResourceCodeList = Collections.singletonList(resourceCode);
        return this.getDataPermissionList(userId, orgId, groupIdList, dataResourceCodeList);
    }

    private List<Rpc19098OBean.DataResourcePermission> getDataPermissionList(String userId, String orgId, List<String> groupIdList,List<String> dataResourceCodeList){
        List<Rpc19098OBean.DataResourcePermission> dataPermission = this.getDataPermission(userId, orgId, groupIdList, dataResourceCodeList);
        if(CollectionUtils.isNotEmpty(groupIdList) && CollectionUtils.isNotEmpty(dataPermission)){
            //管理端需要过滤一下返回的重复权限
            for (Rpc19098OBean.DataResourcePermission dataResourcePermission : dataPermission) {
                if(CollectionUtils.isNotEmpty(dataResourcePermission.getPermissionInfoList())){
                    Set<String> groupIdSet = new HashSet<>();
                    for (Rpc19098OBean.Permission permission : dataResourcePermission.getPermissionInfoList()) {
                        if(CollectionUtils.isNotEmpty(permission.getScopeList())){
                            Iterator<Rpc19098OBean.Scope> it = permission.getScopeList().iterator();
                            while (it.hasNext()){
                                Rpc19098OBean.Scope scope = it.next();
                                String groupId = scope.getGroupId();
                                if(StringUtils.isNotBlank(scope.getGroupType()) && !StringUtils.equalsAny(scope.getGroupType(),Constants.TWO,Constants.THREE)){
                                    //非项目和项目子集的组织直接移除，因为我们存的是项目和项目子集
                                    it.remove();
                                    continue;
                                }
                                if(groupIdSet.contains(groupId)){
                                    //不带condition的条件已经包含改groupId，直接去除该条件
                                    it.remove();
                                    continue;
                                }
                                if(StringUtils.isAllBlank(permission.getCondition(),scope.getUserId())){
                                    groupIdSet.add(groupId);
                                }
                            }
                            if(CollectionUtils.isEmpty(permission.getScopeList())){
                                permission.setCondition(null);
                            }
                        }
                    }
                }
            }
        }
        return dataPermission;
    }


    public YesOrNotEnum isOilSettleWhiteList(String reqOrgId) {
        String oilSettleWhitelist = apolloConfigCenter.boCommons.getProperty("oil.settle.whitelist");
        if (StringUtils.isBlank(oilSettleWhitelist)) {
            return YesOrNotEnum.NO;
        }
        oilSettleWhitelist = oilSettleWhitelist.trim();
        if (StringUtils.isBlank(oilSettleWhitelist)) {
            return YesOrNotEnum.NO;
        }
        List<String> list = Arrays.asList(oilSettleWhitelist.split(","));
        if (!list.contains(reqOrgId)) {
            return YesOrNotEnum.NO;
        }
        return YesOrNotEnum.YES;
    }

    public boolean isOilSettleOrg(String orgId) {
        return StringUtils.equals(this.isOilSettleWhiteList(orgId).getCode(), YesOrNotEnum.YES.getCode());
    }

    /**
     * 任务经纬度解析为地址
     * @param latitude
     * @param longitude
     * @param coordType 坐标系，可以为空，默认国测局
     * @return
     */
    public Rpc1133001OBean taskXyAnalysis(String latitude, String longitude,String coordType){
        if(StringUtils.isAnyBlank(latitude,longitude)){
            return null;
        }
        Rpc1133001IBean rpc1133001IBean = new Rpc1133001IBean();
        rpc1133001IBean.setBusSource("TF-taskAddressAnalysis");
        rpc1133001IBean.setLongitude(longitude);
        rpc1133001IBean.setLatitude(latitude);
        rpc1133001IBean.setCoordType(coordType);
        Rpc1133001OBean rpc1133001OBean = rpcGwayService.rpcResult(rpc1133001IBean, SidConstants.SID_1133001, Rpc1133001OBean.class);
        if(rpc1133001OBean==null){
            throw new UnifiedBusinessException("根据经纬度解析地址异常:");
        }
        if(!StringUtils.equals(rpc1133001OBean.getStatus(),Constants.ZERO)){
            throw new UnifiedBusinessException("根据经纬度解析地址异常:"+rpc1133001OBean.getStatusDesc());
        }

        return rpc1133001OBean;
    }

    /**
     * 获取机构map
     * @param groupIdList
     * @return
     */
    public Map<String, String> getInstNameMap(List<String> groupIdList) {
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            Rpc19147IBean rpc19147IBean = new Rpc19147IBean();
            rpc19147IBean.setGroupIds(groupIdList);
            Rpc19147OBean rpc19147OBean = this.rpcGwayService.rpcResult(rpc19147IBean, SidConstants.SID_19147, Rpc19147OBean.class);
            List<Rpc19147OBean.Vehicle> vehicleList = rpc19147OBean.getVehicleList();
            if (vehicleList != null) {
                Map<String, String> instNameMap = new HashMap<>(vehicleList.size());
                for (Rpc19147OBean.Vehicle vehicle : vehicleList) {
                    String groupId = vehicle.getGroupId();
                    String groupName = vehicle.getGroupName();
                    String mobileNo = vehicle.getMobileNo();
                    instNameMap.put(groupId, groupName + " - " + mobileNo);
                }
                return instNameMap;
            }
        }
        return Collections.emptyMap();
    }

    /**
     * 任务是否存在
     * @param boTransTaskId
     * @return
     */
    public boolean isExistTransTask(String boTransTaskId) {
        if (StringUtils.isBlank(boTransTaskId))
            return false;

        int count = boTransTaskMapper.queryCountByBoTransTaskId(boTransTaskId);
        return count > 0 ? true : false;
    }

    public List<TransTaxFeeCfgBean> getTaskFeeConfig(Object config){
        List<TransTaxFeeCfgBean> transTaxFeeCfgBeanList = new ArrayList<>(1);
        if(config==null){
            return transTaxFeeCfgBeanList;
        }
        String configStr = (String) config;
        if(StringUtils.isBlank(configStr)){
            return transTaxFeeCfgBeanList;
        }
        try {
            transTaxFeeCfgBeanList = GsonToolkit.jsonToList(configStr, new TypeToken<List<TransTaxFeeCfgBean>>() {}.getType());
        } catch (Exception e) {
           log.error(e.getLocalizedMessage(),e);
        }

        return transTaxFeeCfgBeanList;
    }

    public boolean inTaskFeeVerifyNotAllowModifyOrgIds(String orgId){
        if(StringUtils.isBlank(orgId)){
            return false;
        }
        String scopeOrgIds = apolloConfigCenter.boCommons.getProperty("task.fee.verify.not.allow.modify.orgIds");
        if(StringUtils.isBlank(scopeOrgIds)){
            return false;
        }
        String[] arr = scopeOrgIds.split(Constants.SEPARATOR_COMMA);
        return StringUtils.equalsAny(orgId,arr);
    }


    /**
     * 查找有没有审核中的任务
     * @param transTaskIds
     * @return
     */
    public Map<String, String> queryTaskCheckingMap(List<String> transTaskIds) {
        Map<String, String> canApplyMap = new HashMap<>();
        try {
            //获取状态
            Rpc5345508IBean iBean = new Rpc5345508IBean();
            iBean.setDomainIds(transTaskIds);
            iBean.setDomainTypeCode("1");
            iBean.setModelsTypeCode("YFZJ");
            iBean.setProcessStatusList(Arrays.asList("0"));
            Rpc5345508OBean rpc5345508OBean = rpcGwayService.rpcResult(iBean, SidConstants.SID_5345508, Rpc5345508OBean.class);
            if (rpc5345508OBean != null && CollectionUtils.isNotEmpty(rpc5345508OBean.getList())) {
                for (Rpc5345508OBean tmp : rpc5345508OBean.getList()) {
                    String status = StringUtils.EMPTY;
                    if (CollectionUtils.isNotEmpty(tmp.getInstances())) {
                        for (Rpc5345508OBean.Rpc5345508InstanceBean l : tmp.getInstances()) {
                            if ("0".equals(l.getProcessStatus())) {
                                status = l.getProcessStatus();
                                break;
                            }
                        }
                    }
                    canApplyMap.putIfAbsent(tmp.getDomainId(), status);
                }
            }
        } catch (Exception e) {
            //不处理。
            log.info(e.getMessage(), e);
        }
        return canApplyMap;
    }

    public String getGroupId(String orgId){
        Rpc19089IBean rpc19089IBean = new Rpc19089IBean();
        rpc19089IBean.setGroupCode(orgId);
        rpc19089IBean.setGroupType("2");
        Rpc19089OBean rpc19089OBean = rpcGwayService.rpcResult(rpc19089IBean, SidConstants.SID_19089, Rpc19089OBean.class);
        if(rpc19089OBean ==null || CollectionUtils.isEmpty(rpc19089OBean.getGroupList())){
            return null;
        }

        return rpc19089OBean.getGroupList().get(0).getGroupId();
    }

    /**
     * 根据运单ID获取运输任务ID-支持上下游
     * @param taxWaybillId
     * @return
     */
    public String getTaskIdByTaxWaybillId(String taxWaybillId){

        return this.getTaskIdByTaxWaybillId(taxWaybillId,false);
    }

    public String getTaskIdByTaxWaybillId(String taxWaybillId,boolean throwException){
        String taskId = boTransTaskMapper.getTaskIdByTaxWaybillId(taxWaybillId);
        if(StringUtils.isBlank(taskId)){
            taskId = boTaskDetailMapper.getTaskIdByAllocateTaxWaybillId(taxWaybillId,"0");
        }
        if(throwException && StringUtils.isBlank(taskId)){
            throw new UnifiedBusinessException(String.format("未找到运单ID：%s对应的运输任务",taxWaybillId));
        }
        return taskId;
    }

    public String getTaskIdByOrgIdAndTaxWaybillNo(String orgId,String taxWaybillNo){
        if(StringUtils.isAnyBlank(orgId,taxWaybillNo)){
            return null;
        }
        BoTransTaskBean boTransTaskBean = boTransTaskMapper.getBoTransTaskByOrgTaxWayBillNo(orgId, taxWaybillNo);
        if(boTransTaskBean!=null){
            return boTransTaskBean.getBoTransTaskId();
        }
        return boTaskDetailMapper.getTaskIdByAllocateOrgIdAndTaxWaybillNo(orgId, taxWaybillNo);
    }

    /**
     * 通过运单ID查询运输任务ID（支持isDel查询条件）
     * @param taxWaybillId
     * @param isDel 可以为空
     * @return
     */
    public String getTaskIdByTaxWaybillId(String taxWaybillId,String isDel){
        String taskId = boTransTaskMapper.queryTaskIdByTaxWaybillId(taxWaybillId,isDel);
        if(StringUtils.isBlank(taskId)){
            taskId = boTaskDetailMapper.getTaskIdByAllocateTaxWaybillId(taxWaybillId,isDel);
        }

        return taskId;
    }

    /**
     * 查询供应商信息，不抛出异常
     * @param orgId
     * @param groupId
     * @return
     */
    public Rpc22117OBean.Supplier getSupplier(String orgId, String groupId) {
        if(StringUtils.isAnyBlank(orgId,groupId)){
            return null;
        }
        Rpc22117IBean rpc22117IBean = new Rpc22117IBean();
        rpc22117IBean.setOrgId(orgId);
        rpc22117IBean.setSupplierId(groupId);
        Rpc22117OBean rpc22117OBean = rpcGwayService.rpcResultNoException(rpc22117IBean, SidConstants.SID_22117, Rpc22117OBean.class);
        if (rpc22117OBean==null || CollectionUtils.isEmpty(rpc22117OBean.getSupplierList())) {
            return null;
        }
        return rpc22117OBean.getSupplierList().get(0);
    }

    public List<String> getOrgIdsFromPermission(List<String> groupIds){
        List<OrgInfoBean> list = this.getOrgInfoListFromPermission(groupIds);
        if(CollectionUtils.isNotEmpty(list)){
            return list.stream().map(OrgInfoBean::getOrgId).collect(Collectors.toList());
        }

        return null;
    }

    public List<OrgInfoBean> getOrgInfoListFromPermission(List<String> groupIds){

        return this.getOrgInfoListFromPermission(groupIds,false);
    }

    public List<OrgInfoBean> getOrgInfoListFromPermission(List<String> groupIds,boolean qryParentNodes){
        if(CollectionUtils.isEmpty(groupIds)){
            return null;
        }
        Rpc19157IBean rpc19157IBean = new Rpc19157IBean();
        rpc19157IBean.setGroupIdList(groupIds);
        //是否查询链路上级
        if(qryParentNodes){
            rpc19157IBean.setQryParentNodes(Constants.ONE);
        }
        //是否查询链路下级
        rpc19157IBean.setQryChildNodes(Constants.ONE);
        Rpc19157OBean rpc19157OBean = rpcGwayService.rpcResult(rpc19157IBean, SidConstants.SID_19157, Rpc19157OBean.class);
        if(rpc19157OBean==null || CollectionUtils.isEmpty(rpc19157OBean.getGroupInfoList())){
            return Collections.emptyList();
        }
        return this.getOrgInfoListFromPermissionTree(rpc19157OBean.getGroupInfoList());
    }

    /**
     * 获取用户权限组织树种的所有项目信息
     * @param groupInfoList
     * @return
     */
    private List<OrgInfoBean> getOrgInfoListFromPermissionTree(List<Rpc19157OBean> groupInfoList){
        List<OrgInfoBean> orgInfoList = new ArrayList<>();
        List<String> groupNames = new LinkedList<>();
        List<String> groupIds = new LinkedList<>();

        this.getOrgInfoListFromPermissionTree(groupInfoList,orgInfoList,groupNames,groupIds);

        return orgInfoList;
    }

    private List<OrgInfoBean> getOrgInfoListFromPermissionTree(List<Rpc19157OBean> groupInfoList,List<OrgInfoBean> orgInfoList,List<String> groupNames,List<String> groupIds){
        for (Rpc19157OBean oBean : groupInfoList) {
            if(StringUtils.equals(oBean.getGroupType(),Constants.TWO)){
                //组织类型为项目
                OrgInfoBean tempBean = new OrgInfoBean();
                tempBean.setOrgId(oBean.getGroupCode());
                tempBean.setOrgName(oBean.getGroupName());
                tempBean.setGroupId(oBean.getGroupId());
                tempBean.setGroupNames(groupNames);
                groupIds.add(oBean.getGroupId());
                tempBean.setGroupIds(groupIds);
                orgInfoList.add(tempBean);

                //当前层级是项目级了，不再往下级找了
                continue;
            }
            List<String> newGroupNames = new LinkedList<>(groupNames);
            newGroupNames.add(oBean.getGroupName());

            List<String> newGroupIds = new LinkedList<>(groupIds);
            newGroupIds.add(oBean.getGroupId());

            if(CollectionUtils.isEmpty(oBean.getElementGroup())){
                continue;
            }
            this.getOrgInfoListFromPermissionTree(oBean.getElementGroup(),orgInfoList,newGroupNames,newGroupIds);
        }

        return orgInfoList;
    }

    public Rpc1705149OBean getOrgCpdPoolGroup(String cpdPoolGroupId){
        Rpc1705149IBean rpc1705149IBean = new Rpc1705149IBean();
        rpc1705149IBean.setCpdPoolGroupId(cpdPoolGroupId);
        return Optional.ofNullable(rpcGwayService.rpcResultNoException(rpc1705149IBean, SidConstants.SID_1705149, Rpc1705149OBean.class)).orElse(new Rpc1705149OBean());
    }

    /**
     * 获取数据权限
     * @param request
     * @param orgId
     * @param userId
     * @param dataResourceCodes
     * @return
     */
    public List<Rpc19098OBean.DataResourcePermission> getDataPermission(Object request, String orgId, String userId, DataResourceCodeEnum... dataResourceCodes) {
        if (ArrayUtils.isNotEmpty(dataResourceCodes)) {
            List<String> dataResourceCodeList = Arrays.stream(dataResourceCodes)
                    .map(DataResourceCodeEnum::getCode)
                    .collect(Collectors.toList());
            Rpc19098IBean rpc19098IBean = new Rpc19098IBean();
            rpc19098IBean.setQueryUserId(userId);
            rpc19098IBean.setOrgId(orgId);
            rpc19098IBean.setDataResourceCodeList(dataResourceCodeList);
            Rpc19098OBean rpc19098OBean = rpcGwayService.rpcResult(rpc19098IBean, SidConstants.SID_19098, Rpc19098OBean.class);
            List<Rpc19098OBean.DataResourcePermission> dataResourcePermissionList = rpc19098OBean.getDataResourcePermissionList();
            for (Rpc19098OBean.DataResourcePermission dataResourcePermission : dataResourcePermissionList) {
                if (StringUtils.equals(dataResourcePermission.getDataResourceCode(), DataResourceCodeEnum.YWYZ_SRWGL.getCode())) {
                    List<Rpc19098OBean.Permission> permissionList = dataResourcePermission.getPermissionInfoList();
                    // 运输任务
                    this.processTransTaskPermission(request, permissionList);
                } else if (StringUtils.equals(dataResourcePermission.getDataResourceCode(), DataResourceCodeEnum.YWYZ_SCQD.getCode())) {
                    // 随车清单
                    List<Rpc19098OBean.Permission> shippingListPermissionInfoList = dataResourcePermission.getPermissionInfoList();
                    if (CollectionUtils.isNotEmpty(shippingListPermissionInfoList)) {
                        // 随车清单不查询组织相关知查询condition、所以这里需要判断下condition不为空
                        shippingListPermissionInfoList = shippingListPermissionInfoList.stream()
                                .filter(permission -> StringUtils.isNotBlank(permission.getCondition()))
                                .collect(Collectors.toList());
                        // 处理随车清单权限
                        this.processShippingListPermission(request, shippingListPermissionInfoList);
                    }
                }
            }
            return dataResourcePermissionList;
        }
        return Collections.emptyList();
    }



    /**
     * 处理运输任务权限
     * @param request
     * @param permissionList Req1735209Bean Req1735210Bean
     */
    private void processTransTaskPermission(Object request, List<Rpc19098OBean.Permission> permissionList) {
        if (CollectionUtils.isNotEmpty(permissionList)) {
            for (Rpc19098OBean.Permission permission : permissionList) {
                String condition = permission.getCondition();
                // 去除前后空格
                condition = StringUtils.trim(condition);
                // 检查是否存在T_BO_TRANS_TASK表
                if (StringUtils.contains(condition, "T_BO_TRANS_TASK.")) {
                    condition = StringUtils.replace(condition, "T_BO_TRANS_TASK.", "BT.");
                    this.setFieldValue(request, "hasTransTaskCondition", true);
                }
                // 检查是否存在T_BO_TRANS_TASK_EXTRA表
                if (StringUtils.contains(condition, "T_BO_TRANS_TASK_EXTRA.")) {
                    condition = StringUtils.replace(condition, "T_BO_TRANS_TASK_EXTRA.", "BTE.");
                    this.setFieldValue(request, "hasTransTaskExtraCondition", true);
                }
                permission.setCondition(condition);
            }
            this.setFieldValue(request, "transTaskPermissionList", permissionList);
        }
    }
    public Rpc1616006OBean getCompanyInfoByOrgId(String orgId){
        Rpc1616006IBean rpc1616006IBean = new Rpc1616006IBean();
        rpc1616006IBean.setOrgId(orgId);
        Rpc1616006OBean rpc1616006OBean = rpcGwayService.rpcResultNoException(rpc1616006IBean, SidConstants.SID_1616006, Rpc1616006OBean.class);
        if (null == rpc1616006OBean || CollectionUtils.isEmpty(rpc1616006OBean.getCompanyList())) {
            return null;
        }

        return rpc1616006OBean.getCompanyList().get(0);
    }

    public String getCompanyNameByOrgId(String orgId){
        Rpc1616006OBean companyInfo = this.getCompanyInfoByOrgId(orgId);
        if(companyInfo==null){
            return null;
        }
        return companyInfo.getCompanyName();
    }


    /**
     * 处理随车清单权限
     * @param request
     * @param permissionList
     */
    private void processShippingListPermission(Object request, List<Rpc19098OBean.Permission> permissionList) {
        if (CollectionUtils.isNotEmpty(permissionList)) {
            for (Rpc19098OBean.Permission permission : permissionList) {
                String condition = permission.getCondition();
                // 去除前后空格
                condition = StringUtils.trim(condition);
                // 检查是否存在T_BO_SHIPPING_LIST表
                if (StringUtils.contains(condition, "T_BO_SHIPPING_LIST.")) {
                    condition = StringUtils.replace(condition, "T_BO_SHIPPING_LIST.", "BSL.");
                }
                permission.setCondition(condition);
                this.setFieldValue(request, "hasShippingListCondition", true);
            }
            this.setFieldValue(request, "shippingListPermissionList", permissionList);
        }
    }

    /**
     * 字段赋值
     * @param request
     * @param fieldName
     * @param fieldValue
     */
    private void setFieldValue(Object request, String fieldName, Object fieldValue) {
        if (request != null) {
            try {
                ReflectUtil.setFieldValue(request, fieldName, fieldValue);
            } catch (Exception e) {
                log.error(e.getLocalizedMessage());
            }
        }
    }

    /**
     * 获取运费结构项配置
     * @param orgId
     * @return
     */
    public List<TransTaxFeeCfgBean> queryTransTaskFeeCfg(String orgId, String customerName, String orgCpdPoolGroupId) {
        List<TransTaxFeeCfgBean> transTaskFeeList = null;
        try {
            if(FeatureProbeUtil.isNewTaskFee(orgId)){
                Rpc5415002IBean rpc5415002IBean = new Rpc5415002IBean();
                rpc5415002IBean.setOrgId(orgId);
                rpc5415002IBean.setCustomerName(customerName);
                rpc5415002IBean.setCapacityId(orgCpdPoolGroupId);
                Rpc5415002OBean rpc5415002OBean = rpcGwayService.rpcResult(rpc5415002IBean, SidConstants.SID_5415002, Rpc5415002OBean.class);
                if(rpc5415002OBean!=null && CollectionUtils.isNotEmpty(rpc5415002OBean.getCfgList())){
                    transTaskFeeList = new ArrayList<>();
                    for (Rpc5415002OBean oBean : rpc5415002OBean.getCfgList()) {
                        if(CollectionUtils.isEmpty(oBean.getChildNodes())){
                            continue;
                        }
                        TransTaxFeeCfgBean cfgBean = new TransTaxFeeCfgBean();
                        cfgBean.setConfigKey(oBean.getCfgItemKey());
                        cfgBean.setConfigName(oBean.getCfgItem());
                        cfgBean.setChildNodes(new ArrayList<>());
                        for (Rpc5415002OBean.ChildNode childNode : oBean.getChildNodes()) {
                            TransTaxFeeCfgBean newChildNode = new TransTaxFeeCfgBean();
                            BeanUtils.copyProperties(childNode,newChildNode);
                            newChildNode.setConfigKey(childNode.getCfgItemKey());
                            newChildNode.setConfigName(childNode.getCfgItem());
                            newChildNode.setAutoCalculateFieldList(childNode.getAutoCalculateFieldList());
                            cfgBean.getChildNodes().add(newChildNode);
                        }
                        transTaskFeeList.add(cfgBean);
                    }
                }
            }else{
                String config = (String)OrgCacheDataUtil.getSingleSysCfg(orgId, "920");
                if (StringUtils.isNotBlank(config)) {
                    transTaskFeeList = GsonToolkit.jsonToList(config, new TypeToken<List<TransTaxFeeCfgBean>>(){}.getType());
                }
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(),e);
            AlarmToolkit.logAlarm("获取运费结构项异常",e,null);
        }
        if (transTaskFeeList == null) {
            transTaskFeeList = new ArrayList<>(1);
        }

        return transTaskFeeList;
    }

    public List<TransTaxFeeCfgBean> queryTransTaskFeeCfg(String orgId) {
        return this.queryTransTaskFeeCfg(orgId, null, null);
    }
}
