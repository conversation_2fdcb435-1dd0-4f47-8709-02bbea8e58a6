<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransOrderProgressMapper">


    <insert id="add">
        INSERT
            INTO
            T_BO_TRANS_ORDER_PROGRESS (BO_TRANS_ORDER_PROGRESS_ID,
            BO_TRANS_ORDER_ID,
            BO_BUSINESS_LINE_ID,
            "STATE",
            TOTAL_USE_CART_NUM,
            IS_DEL,
            CREATED_TIME,
            LAST_MODIFIED_TIME,
            NOTE)
        VALUES (#{boTransOrderProgressId},
        #{boTransOrderId},
        #{boBusinessLineId},
        #{state},
        #{totalUseCartNum},
        0,
        SYSDATE,
        SYSDATE,
        NULL)
    </insert>
    <update id="updateByPk">
        UPDATE T_BO_TRANS_ORDER_PROGRESS SET
        <if test="state != null and state != ''">
            "STATE"= #{state},
        </if>
        <if test="totalUseCartNum != null ">
            TOTAL_USE_CART_NUM= #{totalUseCartNum},
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_ORDER_PROGRESS_ID = #{boTransOrderProgressId}
    </update>
    <delete id="delByPk">
        UPDATE T_BO_TRANS_ORDER_PROGRESS
        SET
        IS_DEL = 1,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        IS_DEL = 0
        AND BO_TRANS_ORDER_PROGRESS_ID IN
        <foreach collection="progressIdList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
    <select id="selectByOrderId" resultType="com.wtyt.dao.bean.syf.BoTransOrderProgressBean">
        SELECT
            <include refid="baseColumn"/>
        FROM
            T_BO_TRANS_ORDER_PROGRESS
        WHERE
            IS_DEL = 0
            AND BO_TRANS_ORDER_ID = #{boTransOrderId}
    </select>
    <select id="selectByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoTransOrderProgressBean">
        SELECT
        <include refid="baseColumn"/>
        FROM
        T_BO_TRANS_ORDER_PROGRESS
        WHERE
        IS_DEL = 0
        AND BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <sql id="baseColumn" >
            BO_TRANS_ORDER_PROGRESS_ID boTransOrderProgressId,
            BO_TRANS_ORDER_ID boTransOrderId,
            BO_BUSINESS_LINE_ID boBusinessLineId,
            "STATE" state,
            TOTAL_USE_CART_NUM totalUseCartNum
    </sql>

    <update id="batchUpdateState">
        UPDATE T_BO_TRANS_ORDER_PROGRESS
        SET
        "STATE" = 2,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        IS_DEL = 0
        AND BO_TRANS_ORDER_PROGRESS_ID IN
        <foreach collection="progressIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

</mapper>