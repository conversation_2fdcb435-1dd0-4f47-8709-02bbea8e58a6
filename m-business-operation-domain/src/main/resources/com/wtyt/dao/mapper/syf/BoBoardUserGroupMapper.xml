<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoBoardUserGroupMapper">

    <select id="queryGroupNameList" resultType="com.wtyt.dao.bean.syf.BoBoardUserGroupBean">
        SELECT
            BO_BOARD_USER_GROUP_ID boBoardUserGroupId, USER_ID userId,REAL_NAME realName,MOBILE_NO mobileNo,ORG_ID orgId,GROUP_NAME groupName
        FROM
            T_BO_BOARD_USER_GROUP
        WHERE
            ORG_ID = #{orgId}
            AND IS_DEL = 0
    </select>

</mapper>