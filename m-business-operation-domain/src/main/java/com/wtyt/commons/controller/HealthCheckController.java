package com.wtyt.commons.controller;

import com.alibaba.druid.pool.DruidDataSource;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.wtyt.common.annotation.ExcludeAppComponent;
import com.wtyt.common.annotation.ExcludeJobComponent;
import com.wtyt.dao.mapper.syf.SyfCommonsMapper;
import com.wtyt.money.commons.consts.FormatConsts;
import com.wtyt.money.commons.exception.BaseTipException;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 项目监控
 * 
 * <AUTHOR>
 *
 */
@RestController
@ExcludeAppComponent
@ExcludeJobComponent
public class HealthCheckController {

    private static final SimpleDateFormat YMD = new SimpleDateFormat(FormatConsts.YYYYMMDD);
    private static final String TOKEN_ERROR = "E1";// token验证错误
    private static final String DB_ERROR = "E2";// 数据库错误
    private static final String OTHER_ERROR = "E3";// 其他未知错误
    private static final String SUCCESS = "0";// 成功

    @Resource
    private DruidDataSource syfDataSource;
    @Autowired
    private SyfCommonsMapper syfCommonsMapper;

    @RequestMapping(value = "/lg/health/check", method = RequestMethod.GET)
    public String healthCheck(String lhctoken) throws BaseTipException {
        JsonObject json = new JsonObject();
        String resCode = SUCCESS;
        String resInfo = "处理成功";
        JsonArray dbList = new JsonArray();
        try {
            if (StringUtils.isBlank(lhctoken)) {
                resCode = TOKEN_ERROR;
                resInfo = "token不能为空";
            } else {
                String sign = DigestUtils.md5Hex(YMD.format(new Date()));
                if (!lhctoken.equalsIgnoreCase(sign)) {
                    resInfo = "token验证失败";
                    resCode = TOKEN_ERROR;
                } else {
                    this.dbHealthCheck();
                    JsonObject syf = new JsonObject();
                    syf.addProperty("db_active", String.valueOf(syfDataSource.getActiveCount()));
                    syf.addProperty("db_total", String.valueOf(syfDataSource.getMaxActive()));
                    syf.addProperty("db_name", syfDataSource.getUsername());
                    dbList.add(syf);
                }
            }
        } catch (BaseTipException e) {
            resCode = DB_ERROR;
            resInfo = e.getMessage();
        } catch (Exception e) {
            resCode = OTHER_ERROR;
            resInfo = e.getMessage();
        }
        json.addProperty("res_code", resCode);
        json.addProperty("res_info", resInfo);
        if (dbList.size() > 0) {
            json.add("db_list", dbList);
        }
        return json.toString();
    }
    
    private void dbHealthCheck() throws BaseTipException {
        try {
            syfCommonsMapper.healthCheck();
        } catch (Exception e) {
            throw new BaseTipException(DB_ERROR, e);
        }
    }

}
