<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransAlarmReadMapper">

    <insert id="insertBoTransAlarmRead" parameterType="BoTransAlarmReadBean">
	INSERT INTO T_BO_TRANS_ALARM_READ(
	BO_TRANS_ALARM_READ_ID,
	USER_ID,
	TAX_WAYBILL_ID,
	BO_TRANS_NODE_ALARM_ID,
	IS_DEL,
	CREATED_TIME,
	LAST_MODIFIED_TIME,
	NOTE
	)values(
	#{boTransAlarmReadId},
	#{userId},
	#{taxWaybillId},
	#{boTransNodeAlarmId},
	0,
	sysdate,
	sysdate,
	null
	)
</insert>
    
    
    <insert id="batchInsertBoTransAlarmRead" parameterType="java.util.List">
		INSERT INTO T_BO_TRANS_ALARM_READ(
		BO_TRANS_ALARM_READ_ID,
		USER_ID,
		TAX_WAYBILL_ID,
		BO_TRANS_TASK_ID,
		BO_TRANS_NODE_ALARM_ID,
		IS_DEL,
		CREATED_TIME,
		LAST_MODIFIED_TIME,
		NOTE
		)
	 	<foreach item="item" index="index" collection="list" separator="UNION ALL">
	 		SELECT 
	 		#{item.boTransAlarmReadId} boTransAlarmReadId,
			#{item.userId} userId,
			#{item.taxWaybillId} taxWaybillId,
			#{item.boTransTaskId} boTransTaskId,
			#{item.boTransNodeAlarmId} boTransNodeAlarmId,
			0 isDel,
			sysdate createTime,
			sysdate updateTime,
			null note
			FROM DUAL
	   </foreach>
		
	</insert>
</mapper>