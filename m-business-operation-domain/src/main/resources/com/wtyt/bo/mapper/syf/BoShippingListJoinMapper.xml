<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoShippingListJoinMapper">


    <select id="queryNcOrderShippingListBeanByPermissionAndId" resultType="com.wtyt.dao.bean.syf.BoShippingListBean">
        SELECT
        BSL.BO_TRANS_TASK_ID boTransTaskId,
        BSL.CUSTOMER_ORDER_NO customerOrderNo,
        BSL.BO_TRANS_ORDER_ID boTransOrderId,
        BSL.BO_SHIPPING_LIST_ID boShippingListId,
        BSL.CUSTOMER_NAME customerName,
        BSL.RECEIPT_NO receiptNo,
        BSL.GOODS_CASE_PACK goodsCasePack,
        BSL.GOODS_WEIGHT goodsWeight,
        BSL.CONSIGNEE_UNIT consigneeUnit
        FROM
        T_BO_SHIPPING_LIST BSL INNER JOIN T_BO_TRANS_ORDER O ON O.BO_TRANS_ORDER_ID = BSL.BO_TRANS_ORDER_ID AND O.IS_DEL = 0
        WHERE
        BSL.IS_DEL = 0
        AND BSL.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND O.FROM_SOURCE = 3
        <if test="shippingListPermissionList != null and shippingListPermissionList.size() > 0">
            AND (
            <foreach collection="shippingListPermissionList" item="shippingListPermission" open="(" separator="OR" close=")">
                ${shippingListPermission.condition}
            </foreach>
            )
        </if>
    </select>
</mapper>
