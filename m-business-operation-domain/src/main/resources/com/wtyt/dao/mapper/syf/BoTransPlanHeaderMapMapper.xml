<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransPlanHeaderMapMapper">

    <insert id="insert">
        INSERT
        INTO
        T_BO_TRANS_PLAN_HEADER_MAP (
        BO_TRANS_PLAN_HEADER_MAP_ID,
        ORG_ID,
        STANDARD_FIELD,
        HEADER_FIELD,
        IS_REQUIRED,
        FIELD_TYPE,
        MAX_LENGTH,
        HEADER_TYPE,
        SORT,
        DEFAULT_VALUE,
        IS_SEARCH,
        IS_PRECISE,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE)
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTransPlanHeaderMapId} boTransPlanHeaderMapId,
            #{item.orgId} orgId,
            #{item.standardField} standardField,
            #{item.headerField} headerField,
            #{item.isRequired} isRequired,
            #{item.fieldType} fieldType,
            #{item.maxLength} maxLength,
            #{item.headerType} headerType,
            #{item.sort} sort,
            #{item.defaultValue} defaultValue,
            #{item.isSearch} isSearch,
            #{item.isPrecise} isPrecise,
            0 isDel,
            SYSDATE createdTime,
            SYSDATE lastModifiedTime,
            NULL note
            FROM DUAL
        </foreach>
        ) A
    </insert>

    <select id="selectByOrgId" resultType="com.wtyt.dao.bean.syf.BoTransPlanHeaderMapBean">
        SELECT
        ORG_ID orgId,
        STANDARD_FIELD standardField,
        HEADER_FIELD headerField,
        IS_REQUIRED isRequired,
        FIELD_TYPE fieldType,
        MAX_LENGTH maxLength,
        HEADER_TYPE headerType,
        DEFAULT_VALUE defaultValue,
        SORT sort,
        IS_SEARCH isSearch,
        IS_PRECISE isPrecise
        FROM
        T_BO_TRANS_PLAN_HEADER_MAP
        WHERE
        IS_DEL = 0
        <choose>
            <when test="orgId != null and orgId != ''">
                AND ORG_ID = #{orgId}
            </when>
            <otherwise>
                AND ORG_ID IS NULL
            </otherwise>
        </choose>
        ORDER BY CREATED_TIME DESC
    </select>
</mapper>