package com.wtyt.reviews.bean.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年03月15日 17时56分
 */
@Setter
@Getter
public class Resp5329576Bean implements Serializable {

    private static final long serialVersionUID = -8623691467349598510L;
    private long totalRecords;
    private List<Reviews> reviewsList;

    @Setter
    @Getter
    public static class Reviews implements Serializable {

        private static final long serialVersionUID = -8904503704146702778L;
        private String pmReviewsId;
        private String subjectType;
        private String subjectId;
        private String score;
        private String content;
        private String isAnonymity;
        private String reviewsTime;
        private Map<String, ?> subjectExtend;
    }
}
