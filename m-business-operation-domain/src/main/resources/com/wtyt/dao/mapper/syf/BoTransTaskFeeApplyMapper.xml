<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskFeeApplyMapper">
    <resultMap type="com.wtyt.dao.bean.syf.BoTransTaskFeeApplyBean" id="TBoTransTaskFeeApplyMap">
        <result property="boTransTaskFeeApplyId" column="BO_TRANS_TASK_FEE_APPLY_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="submitUserId" column="SUBMIT_USER_ID" jdbcType="VARCHAR"/>
        <result property="submitUserName" column="SUBMIT_USER_NAME" jdbcType="VARCHAR"/>
        <result property="useOilFreight" column="USE_OIL_FREIGHT" jdbcType="VARCHAR"/>
        <result property="prepayments" column="PREPAYMENTS" jdbcType="VARCHAR"/>
        <result property="userFreight" column="USER_FREIGHT" jdbcType="VARCHAR"/>
        <result property="allFreight" column="ALL_FREIGHT" jdbcType="VARCHAR"/>
        <result property="freightGuarantee" column="FREIGHT_GUARANTEE" jdbcType="VARCHAR"/>
        <result property="backFee" column="BACK_FEE" jdbcType="VARCHAR"/>
        <result property="freightIncr" column="FREIGHT_INCR" jdbcType="VARCHAR"/>
        <result property="lossFee" column="LOSS_FEE" jdbcType="VARCHAR"/>
        <result property="prepaymentsOilcard" column="PREPAYMENTS_OILCARD" jdbcType="VARCHAR"/>
        <result property="goodsCost" column="GOODS_COST" jdbcType="VARCHAR"/>
        <result property="insFee" column="INS_FEE" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="VARCHAR"/>
        <result property="lastAuditUserId" column="LAST_AUDIT_USER_ID" jdbcType="VARCHAR"/>
        <result property="lastAuditUserName" column="LAST_AUDIT_USER_NAME" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="auditTime" column="AUDIT_TIME" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.wtyt.bo.bean.response.RespTransTaskFeeApplyBean" id="baseListMap">
        <result property="boTransApplyRecordId" column="BO_TRANS_APPLY_RECORD_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskFeeApplyId" column="BO_TRANS_TASK_FEE_APPLY_ID" jdbcType="VARCHAR"/>
        <result property="userFreight" column="USER_FREIGHT" jdbcType="VARCHAR"/>
        <result property="useOilFreight" column="USE_OIL_FREIGHT" jdbcType="VARCHAR"/>
        <result property="prepayments" column="PREPAYMENTS" jdbcType="VARCHAR"/>
        <result property="backFee" column="BACK_FEE" jdbcType="VARCHAR"/>
        <result property="taxWaybillNo" column="TAX_WAYBILL_NO" jdbcType="VARCHAR"/>
        <result property="startCityName" column="START_CITY_NAME" jdbcType="VARCHAR"/>
        <result property="startProvinceName" column="START_PROVINCE_NAME" jdbcType="VARCHAR"/>
        <result property="startCountyName" column="START_COUNTY_NAME" jdbcType="VARCHAR"/>
        <result property="endCityName" column="END_CITY_NAME" jdbcType="VARCHAR"/>
        <result property="endProvinceName" column="END_PROVINCE_NAME" jdbcType="VARCHAR"/>
        <result property="endCountyName" column="END_COUNTY_NAME" jdbcType="VARCHAR"/>
        <result property="goodsName" column="GOODS_NAME" jdbcType="VARCHAR"/>
        <result property="driverName" column="DRIVER_NAME" jdbcType="VARCHAR"/>
        <result property="mobileNo" column="MOBILE_NO" jdbcType="VARCHAR"/>
        <result property="cartBadgeNo" column="CART_BADGE_NO" jdbcType="VARCHAR"/>
        <result property="applyTime" column="applyTime" jdbcType="VARCHAR"/>
        <result property="submitUserName" column="SUBMIT_USER_NAME" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="VARCHAR"/>
        <result property="auditTime" column="auditTime" jdbcType="VARCHAR"/>
        <result property="allFreight" column="ALL_FREIGHT" jdbcType="VARCHAR"/>
        <result property="prepaymentsOilcard" column="PREPAYMENTS_OILCARD" jdbcType="VARCHAR"/>
        <result property="goodsCost" column="GOODS_COST" jdbcType="VARCHAR"/>
        <result property="insFee" column="INS_FEE" jdbcType="VARCHAR"/>
        <result property="offerType" column="OFFER_TYPE" jdbcType="VARCHAR"/>
        <result property="goodsAmount" column="GOODS_AMOUNT" jdbcType="VARCHAR"/>
        <result property="goodsAmountType" column="GOODS_AMOUNT_TYPE" jdbcType="VARCHAR"/>
        <result property="cartLength" column="CART_LENGTH" jdbcType="VARCHAR"/>
        <result property="cartType" column="CART_TYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">
        T_BO_TRANS_TASK_FEE_APPLY
    </sql>

    <sql id="wherePageSql">
        <where>

            <if test="bean.orgId != null and bean.orgId != ''">
                and transTaskApply.ORG_ID = #{bean.orgId,jdbcType=VARCHAR}
            </if>

            <if test="bean.taxWaybillNo != null and bean.taxWaybillNo != ''">
                and transTask.TAX_WAYBILL_NO = #{bean.taxWaybillNo,jdbcType=VARCHAR}
            </if>
            <if test="bean.driverName != null and bean.driverName != ''">
                and transTask.DRIVER_NAME = #{bean.driverName,jdbcType=VARCHAR}
            </if>
            <if test="bean.applyStartTime != null and bean.applyStartTime != ''">
                and <![CDATA[ transTaskApply.CREATED_TIME >= to_date(#{bean.applyStartTime,jdbcType=VARCHAR},'yyyy-MM-dd')]]>
            </if>
            <if test="bean.applyEndTime != null and bean.applyEndTime != ''">
                and <![CDATA[ transTaskApply.CREATED_TIME <= to_date(#{bean.applyEndTime,jdbcType=VARCHAR} || '23:59:59','yyyy-MM-dd Hh24:mi:ss')]]>
            </if>
            <if test="bean.mobileNo != null and bean.mobileNo != ''">
                and transTask.MOBILE_NO = #{bean.mobileNo,jdbcType=VARCHAR}
            </if>
            <if test="bean.cartBadgeNo != null and bean.cartBadgeNo != ''">
                and transTask.CART_BADGE_NO = #{bean.cartBadgeNo,jdbcType=VARCHAR}
            </if>
            <if test="bean.auditStartTime != null and bean.auditStartTime != ''">
                and <![CDATA[ transTaskApply.AUDIT_TIME >= to_date(#{bean.auditStartTime,jdbcType=VARCHAR},'yyyy-MM-dd')]]>
            </if>
            <if test="bean.auditEndTime != null and bean.auditEndTime != ''">
                and <![CDATA[ transTaskApply.AUDIT_TIME <= to_date(#{bean.auditEndTime,jdbcType=VARCHAR} || '23:59:59','yyyy-mm-dd Hh24:mi:ss')]]>
            </if>
            <if test="bean.status != null and bean.status != ''">
                <choose>
                    <when test="bean.status == 0">
                        and transTaskApply.STATUS = 0
                    </when>
                    <otherwise>
                        and transTaskApply.STATUS in (1,2)
                    </otherwise>
                </choose>
            </if>
        </where>
    </sql>

    <sql id="columnField">
        BO_TRANS_TASK_FEE_APPLY_ID,
               BO_TRANS_TASK_ID,
               SUBMIT_USER_ID,
               SUBMIT_USER_NAME,
               TO_CHAR(NVL(USE_OIL_FREIGHT,0),'FM999999990.00') USE_OIL_FREIGHT,
               TO_CHAR(NVL(PREPAYMENTS,0),'FM999999990.00') PREPAYMENTS,
               TO_CHAR(NVL(USER_FREIGHT,0),'FM999999990.00') USER_FREIGHT,
               TO_CHAR(NVL(ALL_FREIGHT,0),'FM999999990.00') ALL_FREIGHT,
               TO_CHAR(NVL(BACK_FEE,0),'FM999999990.00') BACK_FEE,
               TO_CHAR(NVL(FREIGHT_INCR,0),'FM999999990.00') FREIGHT_INCR,
               TO_CHAR(NVL(LOSS_FEE,0),'FM999999990.00') LOSS_FEE,
               TO_CHAR(NVL(PREPAYMENTS_OILCARD,0),'FM999999990.00') PREPAYMENTS_OILCARD,
               TO_CHAR(NVL(GOODS_COST,0),'FM999999990.00') GOODS_COST,
               TO_CHAR(NVL(INS_FEE,0),'FM999999990.00') INS_FEE,
               TO_CHAR(NVL(FREIGHT_GUARANTEE,0),'FM999999990.00') FREIGHT_GUARANTEE,
               STATUS,
               LAST_AUDIT_USER_ID,
               LAST_AUDIT_USER_NAME,
               ORG_ID,
               IS_DEL,
               CREATED_TIME,
               LAST_MODIFIED_TIME,
               NOTE,
               AUDIT_TIME
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="TBoTransTaskFeeApplyMap">
        select BO_TRANS_TASK_FEE_APPLY_ID,
               BO_TRANS_TASK_ID,
               SUBMIT_USER_ID,
               SUBMIT_USER_NAME,
               USE_OIL_FREIGHT,
               PREPAYMENTS,
               USER_FREIGHT,
               ALL_FREIGHT,
               BACK_FEE,
               FREIGHT_INCR,
               LOSS_FEE,
               PREPAYMENTS_OILCARD,
               GOODS_COST,
               INS_FEE,
               STATUS,
               LAST_AUDIT_USER_ID,
               LAST_AUDIT_USER_NAME,
               ORG_ID,
               IS_DEL,
               CREATED_TIME,
               LAST_MODIFIED_TIME,
               NOTE
        from T_BO_TRANS_TASK_FEE_APPLY
        where BO_TRANS_TASK_FEE_APPLY_ID = #{boTransTaskFeeApplyId}
        and IS_DEL = 0
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="baseListMap">

        SELECT
        transTaskApply.BO_TRANS_TASK_FEE_APPLY_ID,
        transTaskApply.USER_FREIGHT,
        transTaskApply.USE_OIL_FREIGHT,
        transTaskApply.PREPAYMENTS,
        transTaskApply.BACK_FEE,
        transTask.TAX_WAYBILL_NO,
        transTask.START_PROVINCE_NAME,
        transTask.START_CITY_NAME,
        transTask.START_COUNTY_NAME,
        transTask.END_PROVINCE_NAME,
        transTask.END_CITY_NAME,
        transTask.END_COUNTY_NAME,
        transTask.GOODS_NAME,
        transTask.DRIVER_NAME,
        transTask.MOBILE_NO,
        transTask.CART_BADGE_NO,
        TO_CHAR(transTask.GOODS_AMOUNT, 'FM999999990.0000') GOODS_AMOUNT,
        transTask.GOODS_AMOUNT_TYPE,
        TO_CHAR(transTaskApply.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') applyTime,
        transTaskApply.SUBMIT_USER_NAME,
        transTaskApply.STATUS,
        TO_CHAR(transTaskApply.AUDIT_TIME, 'YYYY-MM-DD HH24:MI:SS') auditTime,
        transTaskApply.ALL_FREIGHT,
        transTaskApply.PREPAYMENTS_OILCARD,
        transTaskApply.GOODS_COST,
        transTaskApply.INS_FEE,
        transTask.OFFER_TYPE,
        nvl2(extra.CART_TYPE, extra.CART_TYPE, '') AS CART_TYPE,
        nvl2(extra.CART_LENGTH, extra.CART_LENGTH, '') AS CART_LENGTH
        FROM
        T_BO_TRANS_TASK_FEE_APPLY transTaskApply
        INNER JOIN T_BO_TRANS_TASK transTask ON
        transTaskApply.BO_TRANS_TASK_ID = transTask.BO_TRANS_TASK_ID and transTask.IS_DEL = 0 and transTaskApply.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA extra ON transTask.BO_TRANS_TASK_ID = extra.BO_TRANS_TASK_ID and extra.IS_DEL = 0 and transTask.IS_DEL = 0
        <include refid="wherePageSql"/>

    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        SELECT
        count(transRecord.BO_TRANS_APPLY_RECORD_ID)
        FROM
        T_BO_TRANS_APPLY_RECORD transRecord
        INNER JOIN
        T_BO_TRANS_TASK_FEE_APPLY transTaskApply
        ON
        transRecord.APPLY_ID = transTaskApply.BO_TRANS_TASK_FEE_APPLY_ID
        LEFT JOIN T_BO_TRANS_TASK transTask ON
        transTaskApply.BO_TRANS_TASK_ID = transTask.BO_TRANS_TASK_ID
        <include refid="wherePageSql"/>
    </select>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.wtyt.dao.bean.syf.BoTransTaskFeeApplyBean">
        insert into T_BO_TRANS_TASK_FEE_APPLY(BO_TRANS_TASK_FEE_APPLY_ID,BO_TRANS_TASK_ID, SUBMIT_USER_ID, SUBMIT_USER_NAME, USE_OIL_FREIGHT,
                                              PREPAYMENTS, USER_FREIGHT, ALL_FREIGHT, BACK_FEE, FREIGHT_INCR, LOSS_FEE,
                                              PREPAYMENTS_OILCARD, GOODS_COST, INS_FEE, STATUS,  ORG_ID,FREIGHT_GUARANTEE)
        values (#{boTransTaskFeeApplyId},#{boTransTaskId}
        , #{submitUserId}, #{submitUserName}, #{useOilFreight}, #{prepayments}, #{userFreight},
                #{allFreight}, #{backFee}, #{freightIncr}, #{lossFee}, #{prepaymentsOilcard}, #{goodsCost}, #{insFee},
                '0', #{orgId},#{freightGuarantee})
    </insert>

    <update id="updateBatchAudit">
        update T_BO_TRANS_TASK_FEE_APPLY t set t.STATUS = #{status},t.LAST_MODIFIED_TIME = SYSDATE,t.AUDIT_TIME = SYSDATE, t.LAST_AUDIT_USER_ID = #{lastAuditUserId},
        t.LAST_AUDIT_USER_NAME = #{lastAuditUserName}
        where
        t.BO_TRANS_TASK_FEE_APPLY_ID in
        <foreach collection="idList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        and t.is_del = 0
        and t.status = 0
    </update>

    <!--通过主键修改数据-->
    <update id="update">
        update T_BO_TRANS_TASK_FEE_APPLY
        <set>
            <if test="boTransTaskId != null and boTransTaskId != ''">
                BO_TRANS_TASK_ID = #{boTransTaskId},
            </if>
            <if test="submitUserId != null and submitUserId != ''">
                SUBMIT_USER_ID = #{submitUserId},
            </if>
            <if test="submitUserName != null and submitUserName != ''">
                SUBMIT_USER_NAME = #{submitUserName},
            </if>
            <if test="useOilFreight != null and useOilFreight != ''">
                USE_OIL_FREIGHT = #{useOilFreight},
            </if>
            <if test="prepayments != null and prepayments != ''">
                PREPAYMENTS = #{prepayments},
            </if>
            <if test="userFreight != null and userFreight != ''">
                USER_FREIGHT = #{userFreight},
            </if>
            <if test="allFreight != null and allFreight != ''">
                ALL_FREIGHT = #{allFreight},
            </if>
            <if test="backFee != null and backFee != ''">
                BACK_FEE = #{backFee},
            </if>
            <if test="freightIncr != null and freightIncr != ''">
                FREIGHT_INCR = #{freightIncr},
            </if>
            <if test="lossFee != null and lossFee != ''">
                LOSS_FEE = #{lossFee},
            </if>
            <if test="prepaymentsOilcard != null and prepaymentsOilcard != ''">
                PREPAYMENTS_OILCARD = #{prepaymentsOilcard},
            </if>
            <if test="goodsCost != null and goodsCost != ''">
                GOODS_COST = #{goodsCost},
            </if>
            <if test="insFee != null and insFee != ''">
                INS_FEE = #{insFee},
            </if>
            <if test="status != null and status != ''">
                STATUS = #{status},
            </if>
            <if test="lastAuditUserId != null and lastAuditUserId != ''">
                LAST_AUDIT_USER_ID = #{lastAuditUserId},
            </if>
            <if test="lastAuditUserName != null and lastAuditUserName != ''">
                LAST_AUDIT_USER_NAME = #{lastAuditUserName},
            </if>
            <if test="orgId != null and orgId != ''">
                ORG_ID = #{orgId},
            </if>
            <if test="isDel != null and isDel != ''">
                IS_DEL = #{isDel},
            </if>
            <if test="createdTime != null">
                CREATED_TIME = #{createdTime},
            </if>
            <if test="lastModifiedTime != null">
                LAST_MODIFIED_TIME = #{lastModifiedTime},
            </if>
            <if test="note != null and note != ''">
                NOTE = #{note},
            </if>
        </set>
        where BO_TRANS_TASK_FEE_APPLY_ID = #{boTransTaskFeeApplyId}
    </update>

    <select id="queryLastedApplyStatusByTaskIds" resultMap="TBoTransTaskFeeApplyMap">
        SELECT
            BO_TRANS_TASK_ID,
            STATUS
        FROM
            (SELECT BO_TRANS_TASK_ID,STATUS,row_number() over ( PARTITION BY BO_TRANS_TASK_ID ORDER BY CREATED_TIME DESC ) AS row_number FROM T_BO_TRANS_TASK_FEE_APPLY WHERE IS_DEL=0 AND BO_TRANS_TASK_ID IN
            <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        ) temp
        WHERE
        row_number = 1
    </select>

    <select id="queryCountByIdList" resultType="int">
        select count(*) from <include refid="tableName"/>
        <where>
            IS_DEL = 0
            AND BO_TRANS_TASK_FEE_APPLY_ID in
            <foreach collection="idList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="queryByIdList" resultMap="TBoTransTaskFeeApplyMap">
        select <include refid="columnField"/> from <include refid="tableName"/>
        <where>
            IS_DEL = 0
            AND BO_TRANS_TASK_FEE_APPLY_ID in
            <foreach collection="idList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="queryByReqDto" resultMap="TBoTransTaskFeeApplyMap">
        select
        <include refid="columnField"/>
        from
        <include refid="tableName"/>
        <where>
            IS_DEL = 0
            AND BO_TRANS_TASK_FEE_APPLY_ID in
            <foreach collection="boTransTaskFeeApplyIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="queryCountByQueryBean" resultType="java.lang.Long">
        select count(*) from
        T_BO_TRANS_TASK_FEE_APPLY a INNER JOIN T_BO_TRANS_TASK t on a.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID and a.IS_DEL = 0 and t.is_DEL = 0
        <where>
            a.STATUS = #{status}
            and a.ORG_ID = #{orgId}
        </where>
    </select>

    <select id="queryByTransTaskId" resultMap="TBoTransTaskFeeApplyMap">
        select <include refid="columnField"/> from <include refid="tableName"/>
        <where>
            BO_TRANS_TASK_ID = #{boTransTaskId}
            and IS_DEL = 0
            and STATUS = #{status}
        </where>
    </select>

    <update id="deleteById">
        UPDATE T_BO_TRANS_TASK_FEE_APPLY SET IS_DEL =1,LAST_MODIFIED_TIME =SYSDATE WHERE BO_TRANS_TASK_FEE_APPLY_ID =#{id} AND IS_DEL =0
    </update>
    <update id="deleteByTaskId">
        UPDATE T_BO_TRANS_TASK_FEE_APPLY SET IS_DEL =1,LAST_MODIFIED_TIME =SYSDATE WHERE BO_TRANS_TASK_ID =#{taskId} AND IS_DEL =0
    </update>

    <select id="queryLastTaskApply" resultMap="TBoTransTaskFeeApplyMap">
        SELECT
            BO_TRANS_TASK_ID,
            STATUS
            FROM
                (
                    SELECT
                    t.BO_TRANS_TASK_ID,
                    t.STATUS,
                    ROW_NUMBER() OVER(PARTITION BY t.BO_TRANS_TASK_ID ORDER BY t.CREATED_TIME DESC) AS ROW_NUMBER
                FROM
                    T_BO_TRANS_TASK_FEE_APPLY t
                WHERE
                    t.is_del = 0
                    AND t.BO_TRANS_TASK_ID = #{boTransTaskId}
                ) temp
        WHERE
            ROW_NUMBER = 1
    </select>

    <update id="batchTransferTaskFeeApply">
        UPDATE T_BO_TRANS_TASK_FEE_APPLY SET ORG_ID = #{data.toOrgId}, LAST_MODIFIED_TIME = SYSDATE WHERE ORG_ID = #{data.fromOrgId} AND IS_DEL = 0 AND BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIds" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>

