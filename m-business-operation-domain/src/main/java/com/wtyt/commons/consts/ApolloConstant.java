package com.wtyt.commons.consts;

/**
 * <AUTHOR>
 * @ClassName: ApolloConstant
 * @Description: 阿波罗常量
 * @date 2022年3月15日
 */
public class ApolloConstant {
    public static class NameSpace {
        public final static String TAX_WHITE_LIST = "yanfa.money.tax.whitelist";

        /**
         * 车长车型数据
         */
        public final static String BASIC_DATA_TRUCK = "yanfa.public.basic.data.truck";
    }

    public static class PropertyKey {

        /**
         * 结算费用规则，用以处理司机费用计算
         */
        public static final String FREIGHT_SETTLE_RULE = "freight.settle.rule";

        /**
         * oss.typeCode
         */
        public static final String OSS_TYPE_CODE = "oss.typeCode";

        /**
         * oss.accessKey
         */
        public static final String OSS_ACCESS_KEY = "oss.accessKey";

        /**
         * 车型数据
         */
        public static final String TRUCK_TYPE = "basic.data.truck.type";

        /**
         * 运输计划发布找车记录车型车长默认值。
         */
        public static final String PLAN_PUBLISH_DEFAULT_CAR_LENGTH_TYPE = "fh.trans.order.publish.car.value";

        /**
         * 删除计划时删除任务的配置
         */
        public static final String DEL_TASK_WHEN_DEL_ORDER_ORG_LIST = "delTaskWhenDelOrder.org.list";

        /**
         * 金颜直发货可见范围枚举
         */
        public static final String JY_FH_VISIBILITY_FLAG_ENUM = "fh.visibility.flag.enum";
    }
}
