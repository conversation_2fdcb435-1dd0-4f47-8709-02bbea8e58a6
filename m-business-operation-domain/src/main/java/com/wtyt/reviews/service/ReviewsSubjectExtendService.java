package com.wtyt.reviews.service;

import com.wtyt.util.OrgCacheDataUtil;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024年03月15日 15时20分
 */
public interface ReviewsSubjectExtendService {

    Logger LOGGER = LoggerFactory.getLogger(ReviewsSubjectExtendService.class);

    /**
     * 获取评价主体扩展
     * @param subjectId
     * @return
     */
    Map<String, ?> getSubjectExtend(String subjectId);

    /**
     * 获取评价主体扩展
     * @param subjectIdList
     * @return
     */
    Map<String, Map<String, ?>> getSubjectExtend(List<String> subjectIdList);

    default String getOrgName(String orgId) {
        Map<String, String> orgNameMap = this.getOrgName(Collections.singletonList(orgId));
        if (MapUtils.isNotEmpty(orgNameMap)) {
            return orgNameMap.get(orgId);
        }
        return null;
    }

    default Map<String, String> getOrgName(List<String> orgIdList) {
        try {
            Map<String, String> orgNameMap = new HashMap<>(orgIdList.size());
            List<String> fieldList = new ArrayList<>(1);
            String orgNameField = "orgName";
            fieldList.add(orgNameField);
            Map<String, Map<String, Object>> orgInfoMap = OrgCacheDataUtil.getOrgInfos(orgIdList, fieldList);
            for (Map.Entry<String, Map<String, Object>> entry : orgInfoMap.entrySet()) {
                String orgId = entry.getKey();
                Map<String, Object> orgConfigMap = entry.getValue();
                if (MapUtils.isNotEmpty(orgConfigMap)) {
                    String orgName = (String)orgConfigMap.get(orgNameField);
                    orgNameMap.put(orgId, orgName);
                }
            }
            return orgNameMap;
        } catch (Exception e) {
            String errorMsg = e.getLocalizedMessage();
            LOGGER.error("调用远程接口[4000]异常:{}", errorMsg, e);
        }
        return Collections.emptyMap();
    }
}