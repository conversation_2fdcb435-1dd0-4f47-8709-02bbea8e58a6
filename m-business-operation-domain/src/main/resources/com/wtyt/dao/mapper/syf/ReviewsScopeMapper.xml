<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.ReviewsScopeMapper">

    <insert id="batchInsertReviewsScope">
        INSERT INTO T_PM_REVIEWS_SCOPE (
            PM_REVIEWS_SCOPE_ID,
            PM_REVIEWS_SUBJECT_ID,
            SCOPE,
            SCOPE_ID
        )
        SELECT
            T.*
        FROM (
        <foreach collection="list" item="reviewsScope" separator="UNION ALL">
            SELECT
                #{reviewsScope.pmReviewsScopeId} PM_REVIEWS_SCOPE_ID,
                #{reviewsScope.pmReviewsSubjectId} PM_REVIEWS_SUBJECT_ID,
                #{reviewsScope.scope} SCOPE,
                #{reviewsScope.scopeId} SCOPE_ID
            FROM DUAL
        </foreach>
        )T
    </insert>

</mapper>
