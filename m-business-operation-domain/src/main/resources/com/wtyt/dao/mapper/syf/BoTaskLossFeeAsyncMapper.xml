<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskLossFeeAsyncMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTaskLossFeeAsyncBean" id="BoTaskLossFeeAsyncMap">
        <result property="boTaskLossFeeAsyncId" column="BO_TASK_LOSS_FEE_ASYNC_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="lossFeeItem" column="LOSS_FEE_ITEM" jdbcType="VARCHAR"/>
        <result property="lossFeeNote" column="LOSS_FEE_NOTE" jdbcType="VARCHAR"/>
        <result property="freightIncrItem" column="FREIGHT_INCR_ITEM" jdbcType="VARCHAR"/>
        <result property="operationUserId" column="OPERATION_USER_ID" jdbcType="VARCHAR"/>
        <result property="operationSysRole" column="OPERATION_SYS_ROLE" jdbcType="VARCHAR"/>
        <result property="optSource" column="OPT_SOURCE" jdbcType="VARCHAR"/>
        <result property="dealStatus" column="DEAL_STATUS" jdbcType="VARCHAR"/>
        <result property="dealRemark" column="DEAL_REMARK" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>


    <insert id="save">
        INSERT
        INTO
        T_BO_TASK_LOSS_FEE_ASYNC
        (BO_TASK_LOSS_FEE_ASYNC_ID,
        BO_TRANS_TASK_ID,
        ORG_ID,
        LOSS_FEE_ITEM,
        LOSS_FEE_NOTE,
        FREIGHT_INCR_ITEM,
        OPERATION_USER_ID,
        OPERATION_SYS_ROLE,
        OPT_SOURCE,
        DEAL_STATUS,
        DEAL_REMARK)
        VALUES(#{boTaskLossFeeAsyncId},
        #{boTransTaskId},
        #{orgId},
        #{lossFeeItem},
        #{lossFeeNote},
        #{freightIncrItem},
        #{operationUserId},
        #{operationSysRole},
        #{optSource},
        #{dealStatus},
        #{dealRemark})
    </insert>

    <update id="updateDealResult">
        UPDATE T_BO_TASK_LOSS_FEE_ASYNC SET DEAL_STATUS =#{dealStatus},DEAL_REMARK =#{dealRemark},LAST_MODIFIED_TIME =SYSDATE WHERE BO_TASK_LOSS_FEE_ASYNC_ID =#{boTaskLossFeeAsyncId}
    </update>

    <select id="queryNeedDeal" resultMap="BoTaskLossFeeAsyncMap">
        SELECT
            BO_TASK_LOSS_FEE_ASYNC_ID,
            BO_TRANS_TASK_ID,
            ORG_ID,
            TO_CHAR(LOSS_FEE_ITEM, 'FM999999990.00') LOSS_FEE_ITEM,
            TO_CHAR(FREIGHT_INCR_ITEM, 'FM999999990.00') FREIGHT_INCR_ITEM,
            LOSS_FEE_NOTE,
            OPERATION_USER_ID,
            OPERATION_SYS_ROLE,
            OPT_SOURCE,
            DEAL_STATUS,
            DEAL_REMARK
        FROM
            T_BO_TASK_LOSS_FEE_ASYNC
        WHERE
            DEAL_STATUS =0
            AND IS_DEL =0
            AND CREATED_TIME >SYSDATE - 180
            ORDER BY CREATED_TIME ASC
    </select>
</mapper>