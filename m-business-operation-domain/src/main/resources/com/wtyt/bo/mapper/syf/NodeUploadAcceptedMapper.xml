<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.NodeUploadAcceptedMapper">

    <select id="checkExistsArriveRecordAfterDispatch" resultType="Integer">
        SELECT COUNT(1)
        FROM T_BO_TRANS_NODE_RECORD A,
             (SELECT BO_TRANS_TASK_ID, CREATED_TIME
              FROM (SELECT R.BO_TRANS_TASK_ID,
                           R.CREATED_TIME,
                           ROW_NUMBER() OVER(PARTITION BY R.BO_TRANS_TASK_ID ORDER BY R.CREATED_TIME DESC) RN
                    FROM T_BO_TRANS_NODE_RECORD R
                    WHERE R.BO_TRANS_TASK_ID = #{boTransTaskId}
                      AND R.NODE_ID = 200
                      AND R.IS_DEL = 0) T
              WHERE T.RN = 1) B
        WHERE A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID
          AND A.CREATED_TIME > B.CREATED_TIME
          AND A.NODE_ID = 500
          AND A.IS_DEL = 0
    </select>
</mapper>