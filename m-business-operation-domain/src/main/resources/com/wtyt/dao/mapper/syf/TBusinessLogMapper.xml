<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.TBusinessLogMapper">

    <insert id="insert">
        insert into T_BUSINESS_LOG (
        BUSINESS_LOG_ID,
        BUSINESS_ID,
        BUSINESS_TYPE,
        OPERATOR_ID,
        OPERATOR_NAME,
        KLB_USER_ID,
        KLB_USER_NAME,
        OPERATE_TYPE,
        SOURCE,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE
        )
        values (
        #{businessLogId},
        #{businessId},
        #{businessType},
        #{operatorId},
        #{operatorName},
        #{klbUserId},
        #{klbUserName},
        #{operateType},
        #{source},
        0,
        SYSDATE,
        SYSDATE,
        #{note}
        )
    </insert>


    <insert id="batchInsert">
        insert into T_BUSINESS_LOG
        (
        BUSINESS_LOG_ID,
        BUSINESS_ID,
        BUSINESS_TYPE,
        OPERATOR_ID,
        OPERATOR_NAME,
        KLB_USER_ID,
        KLB_USER_NAME,
        OPERATE_TYPE,
        SOURCE,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE
        )
        SELECT
        A.*
        FROM(
        <foreach collection="list" item="item" index="index" separator="union all">
            SELECT
            #{item.businessLogId} BUSINESS_LOG_ID,
            #{item.businessId} BUSINESS_ID,
            #{item.businessType} BUSINESS_TYPE,
            #{item.operatorId} OPERATOR_ID,
            #{item.operatorName} OPERATOR_NAME,
            #{item.klbUserId} KLB_USER_ID,
            #{item.klbUserName} KLB_USER_NAME,
            #{item.operateType} OPERATE_TYPE,
            #{item.source} SOURCE,
            0 IS_DEL,
            SYSDATE CREATED_TIME,
            SYSDATE LAST_MODIFIED_TIME,
            #{item.note} NOTE
            FROM
            DUAL
        </foreach>) A
    </insert>

</mapper>
