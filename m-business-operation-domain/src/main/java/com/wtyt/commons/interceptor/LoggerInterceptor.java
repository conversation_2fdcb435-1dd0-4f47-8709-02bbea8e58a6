package com.wtyt.commons.interceptor;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.wtyt.common.toolkits.GsonToolkit;
import com.wtyt.common.toolkits.RequestToolkit;
import com.wtyt.common.util.base.BaseException;
import com.wtyt.commons.MessageSourceAccessor;
import com.wtyt.commons.annotation.Logger;
import com.wtyt.commons.apollo.ApolloConfigCenter;
import com.wtyt.commons.bean.AccessLogger;
import com.wtyt.commons.consts.MessageConsts;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lg.commons.toolkits.DateToolkit;
import com.wtyt.money.commons.bean.ResDataBean;
import com.wtyt.money.commons.exception.BaseCustomException;
import com.wtyt.money.commons.exception.BaseTipException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 日志记录拦截器
 * @author: <EMAIL>
 * @date: 2019年12月03日 10时13分
 */
@Slf4j
@Aspect
public class LoggerInterceptor {

    private static final ThreadLocal<AccessLogger> THREAD_LOCAL = new InheritableThreadLocal<>();
    private static final ObjectMapper REQUEST_OBJECT_MAPPER = new ObjectMapper();
    private static final ObjectMapper RESPONSE_OBJECT_MAPPER = new ObjectMapper();
    private MessageSourceAccessor messageSourceAccessor;
    private ApolloConfigCenter apolloConfigCenter;
    private HttpServletRequest request;
    private HttpServletResponse response;

    static {
        REQUEST_OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        RESPONSE_OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        RESPONSE_OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Around(value = "@annotation(logger)")
    public Object around(ProceedingJoinPoint proceedingJoinPoint, Logger logger) throws Throwable {
        try {
            // 初始化记录器
            this.initializeLogger();
            // 设置请求体
            this.setRequestBody();
            // 打印请求日志
            this.printRequestLog(logger);
            // 执行业务逻辑
            Object response = proceedingJoinPoint.proceed();
            // 设置响应体
            this.setResponseBody(response);
            return response;
        } catch (Throwable t) {
            this.setErrorLog(t);
            throw t;
        } finally {
            this.printResponseLog(logger);
        }
    }


    /**
     * 打印响应日志
     * @param logger
     */
    private void printResponseLog(Logger logger) {
        AccessLogger accessLogger = THREAD_LOCAL.get();
        THREAD_LOCAL.remove();
        StopWatch stopWatch = accessLogger.getStopWatch();
        stopWatch.stop();
        long depleteTime = stopWatch.getTime(TimeUnit.MILLISECONDS);
        accessLogger.setDepleteTime(depleteTime);
        accessLogger.setResponseTime(DateToolkit.getNowDateStr());
        log.info("离开{}、响应状态:{}、响应耗时:{}、响应内容:{}",
                logger.value(),
                accessLogger.getResponseStatus(),
                accessLogger.getDepleteTime(),
                accessLogger.getResponseBody());
    }

    /**
     * 打印请求日志
     * @param logger
     */
    private void printRequestLog(Logger logger) {
        AccessLogger accessLogger = THREAD_LOCAL.get();
        String requestHeader = accessLogger.getRequestHeader();
        String requestParameters = accessLogger.getRequestParameters();
        String requestHeaderName = "{}";
        if (StringUtils.isNotBlank(requestHeader)) {
            requestHeaderName = "请求头:{}、";
        }
        String requestParametersName = "{}";
        if (StringUtils.isNotBlank(requestParameters)) {
            requestParametersName = "请求参数:{}、";
        }
        String format = String.format("进入{}、请求地址:{}、请求类型:{}、%s%s请求内容:{}", requestHeaderName, requestParametersName);
        log.info(format, logger.value(),
                accessLogger.getRequestApi(),
                accessLogger.getRequestType(),
                accessLogger.getRequestHeader(),
                accessLogger.getRequestParameters(),
                accessLogger.getRequestBody());
    }

    /**
     * 设置响应体
     * @param response
     */
    private void setResponseBody(Object response) {
        String responseBody = StringUtils.EMPTY;
        if (response != null) {
            try {
                responseBody = RESPONSE_OBJECT_MAPPER.writeValueAsString(response);
            } catch (Exception e) {
                String errorMsg = e.getLocalizedMessage();
                ResDataBean<Object> result = new ResDataBean<>();
                result.tipFail("响应体解析异常:" + errorMsg, null);
                try {
                    responseBody = RESPONSE_OBJECT_MAPPER.writeValueAsString(result);
                } catch (Exception ex) {
                    // 不处理
                }
            }
        }
        int responseStatus = this.response.getStatus();
        AccessLogger accessLogger = THREAD_LOCAL.get();
        accessLogger.setResponseBody(responseBody);
        accessLogger.setResponseStatus(responseStatus);
    }

    /**
     * 设置请求体
     */
    private void setRequestBody() {
        String requestBody = RequestToolkit.getRequestBody(this.request);
        try {
            Object requestJson = REQUEST_OBJECT_MAPPER.readValue(requestBody, Object.class);
            requestBody = REQUEST_OBJECT_MAPPER.writeValueAsString(requestJson);
        } catch (Exception e) {
            String errorMsg = e.getLocalizedMessage();
            ResDataBean<Object> result = new ResDataBean<>();
            result.tipFail("请求体解析异常:" + errorMsg, null);
            try {
                requestBody = RESPONSE_OBJECT_MAPPER.writeValueAsString(result);
            } catch (Exception ex) {
                // 不处理
            }
        }
        THREAD_LOCAL.get().setRequestBody(requestBody);
    }

    /**
     * 设置错误日志
     * @param t
     */
    private void setErrorLog(Throwable t) {
        ResDataBean<Object> result = new ResDataBean<>();
        if (t instanceof UnifiedBusinessException) {
            UnifiedBusinessException ex = (UnifiedBusinessException)t;
            result.tipFail(ex.getLocalizedMessage(), null);
        } else if (t instanceof BaseTipException) {
            BaseTipException ex = (BaseTipException)t;
            result.tipFail(ex.getLocalizedMessage(), null);
        } else if (t instanceof BaseException) {
            BaseException ex = (BaseException)t;
            result.tipFail(ex.getLocalizedMessage(), null);
        } else if (t instanceof BaseCustomException) {
            BaseCustomException ex = (BaseCustomException)t;
            result.setReCode(ex.getReCode());
            result.setReInfo(ex.getReInfo());
            result.setResult(ex.getData());
        } else if (t instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException)t;
            StringBuilder message = new StringBuilder();
            List<ObjectError> allErrors = ex.getBindingResult().getAllErrors();
            if (CollectionUtils.isEmpty(allErrors)) {
                result.tipFail(ex.getMessage(), null);
            } else {
                for (ObjectError error : allErrors) {
                    message.append(error.getDefaultMessage() == null ? StringUtils.EMPTY : error.getDefaultMessage());
                    message.append(",");
                }
                if (message.length() > 0) {
                    message.deleteCharAt(message.length() - 1);
                }
                result.tipFail(message.toString(), null);
            }
        } else if (t instanceof HttpMessageNotReadableException) {
            result.tipFail("请求报文数据格式解析错误，请参考接口文档检查您的请求数据！", null);
        } else {
            result.tipFail(this.messageSourceAccessor.getMessage(MessageConsts.MERROR), null);
        }
        String responseBody = StringUtils.EMPTY;
        try {
            responseBody = RESPONSE_OBJECT_MAPPER.writeValueAsString(result);
        } catch (Exception e) {
            // 不处理
        }
        THREAD_LOCAL.get().setResponseBody(responseBody);
    }

    /**
     * 初始化
     */
    private void initializeLogger() {
        StopWatch stopWatch = StopWatch.createStarted();
        String[] headers = null;
        String requestHeaders = this.apolloConfigCenter.boCommons.getProperty("logger.request.headers");
        if (StringUtils.isNotBlank(requestHeaders)) {
            List<String> requestHeaderList = GsonToolkit.jsonToList(requestHeaders, String.class);
            headers = requestHeaderList.toArray(new String[]{});
        }
        String requestApi = this.request.getRequestURI();
        String requestType = this.request.getMethod();
        String requestParameters = this.request.getQueryString();
        String requestHeader = null;
        if (headers != null) {
            requestHeader = RequestToolkit.getRequestHeader(this.request, headers);
        }
        requestParameters = StringUtils.defaultString(requestParameters);
        requestHeader = StringUtils.defaultString(requestHeader);

        AccessLogger accessLogger = new AccessLogger();
        accessLogger.setRequestApi(requestApi);
        accessLogger.setRequestTime(DateToolkit.getNowDateStr());
        accessLogger.setRequestType(requestType);
        accessLogger.setRequestParameters(requestParameters);
        accessLogger.setRequestHeader(requestHeader);
        accessLogger.setStopWatch(stopWatch);
        THREAD_LOCAL.set(accessLogger);
    }

    @Autowired(required = false)
    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    @Autowired(required = false)
    public void setResponse(HttpServletResponse response) {
        this.response = response;
    }

    @Autowired
    public void setMessageSourceAccessor(MessageSourceAccessor messageSourceAccessor) {
        this.messageSourceAccessor = messageSourceAccessor;
    }

    @Autowired
    public void setApolloConfigCenter(ApolloConfigCenter apolloConfigCenter) {
        this.apolloConfigCenter = apolloConfigCenter;
    }
}
