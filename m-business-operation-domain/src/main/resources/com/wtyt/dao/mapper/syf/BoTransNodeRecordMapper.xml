<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransNodeRecordMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTransNodeRecordBean" id="baseResultMap">
        <result property="boTransNodeRecordId" column="BO_TRANS_NODE_RECORD_ID" jdbcType="VARCHAR"/>
        <result property="taxWaybillId" column="TAX_WAYBILL_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="nodeId" column="NODE_ID" jdbcType="VARCHAR"/>
        <result property="optSource" column="OPT_SOURCE" jdbcType="VARCHAR"/>
        <result property="userId" column="USER_ID" jdbcType="VARCHAR"/>
        <result property="userName" column="USER_NAME" jdbcType="VARCHAR"/>
        <result property="driverId" column="DRIVER_ID" jdbcType="VARCHAR"/>
        <result property="reason" column="REASON" jdbcType="VARCHAR"/>
        <result property="overTime" column="OVER_TIME" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="taxPosInfoId" column="TAX_POS_INFO_ID" jdbcType="VARCHAR"/>
        <result property="sysRoleType" column="SYS_ROLE_TYPE" jdbcType="VARCHAR"/>
        <result property="idCard" column="ID_CARD" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">
        T_BO_TRANS_NODE_RECORD
    </sql>

    <insert id="insertBoTransNodeRecord" parameterType="BoTransNodeRecordBean">
        INSERT INTO T_BO_TRANS_NODE_RECORD(
            BO_TRANS_NODE_RECORD_ID,
            TAX_WAYBILL_ID,
            BO_TRANS_TASK_ID,
            NODE_ID,
            OPT_SOURCE,
            <if test="requestSource != null and requestSource !=''">
                REQUEST_SOURCE,
            </if>
            <if test="userId != null and userId !=''">
                USER_ID,
            </if>
            <if test="driverId != null and driverId !=''">
                DRIVER_ID,
            </if>
            <if test="sysRoleType != null and sysRoleType !=''">
                SYS_ROLE_TYPE,
            </if>
            <if test="reason != null and reason !=''">
                REASON,
            </if>
            <if test="overTime != null and overTime !=''">
                OVER_TIME,
            </if>
            <if test="note != null and note !=''">
                NOTE,
            </if>
            <if test="taxPosInfoId != null and taxPosInfoId !=''">
                TAX_POS_INFO_ID,
            </if>
            <if test="bossNodeCode != null and bossNodeCode !=''">
                BOSS_NODE_CODE,
            </if>
            <if test="jobName != null and jobName !=''">
                JOB_NAME,
            </if>
            <if test="orgId != null and orgId !=''">
                ORG_ID,
            </if>
            <if test="groupId != null and groupId !=''">
                GROUP_ID,
            </if>
            <if test="latitude != null and latitude !=''">
                LATITUDE,
            </if>
            <if test="longitude != null and longitude !=''">
                LONGITUDE,
            </if>
            <if test="address != null and address !=''">
                ADDRESS,
            </if>
            <if test="userName != null and userName !=''">
                USER_NAME,
            </if>
            <if test="extend != null and extend !=''">
                EXTEND,
            </if>
            <if test="nodeSubObjectType != null and nodeSubObjectType !=''">
                NODE_SUB_OBJECT_TYPE,
            </if>
            <if test="nodeSubObjectValue != null and nodeSubObjectValue !=''">
                NODE_SUB_OBJECT_VALUE,
            </if>
            <if test="mobileNo != null and mobileNo !=''">
                MOBILE_NO,
            </if>
            <if test="idCard != null and idCard !=''">
                ID_CARD,
            </if>
            <if test="cartBadgeNo != null and cartBadgeNo !=''">
                CART_BADGE_NO,
            </if>
            <if test="deviceId != null and deviceId !=''">
                DEVICE_ID,
            </if>
            <if test="cartLongitude != null and cartLongitude !=''">
                CART_LONGITUDE,
            </if>
            <if test="cartLatitude != null and cartLatitude !=''">
                CART_LATITUDE,
            </if>
            <if test="cartAddress != null and cartAddress !=''">
                CART_ADDRESS,
            </if>
            CREATED_TIME,
            LAST_MODIFIED_TIME
        ) values (
            #{boTransNodeRecordId},
            #{taxWaybillId},
            #{boTransTaskId},
            #{nodeId},
            #{optSource},
            <if test="requestSource != null and requestSource !=''">
                #{requestSource},
            </if>
            <if test="userId != null and userId !=''">
                #{userId},
            </if>
            <if test="driverId != null and driverId !=''">
                #{driverId},
            </if>
            <if test="sysRoleType != null and sysRoleType !=''">
                #{sysRoleType},
            </if>
            <if test="reason != null and reason !=''">
                #{reason},
            </if>
            <if test="overTime != null and overTime !=''">
                #{overTime},
            </if>
            <if test="note != null and note !=''">
                #{note},
            </if>
            <if test="taxPosInfoId != null and taxPosInfoId !=''">
                #{taxPosInfoId},
            </if>
            <if test="bossNodeCode != null and bossNodeCode !=''">
                #{bossNodeCode},
            </if>
            <if test="jobName != null and jobName !=''">
                #{jobName},
            </if>
            <if test="orgId != null and orgId !=''">
                #{orgId},
            </if>
            <if test="groupId != null and groupId !=''">
                #{groupId},
            </if>
            <if test="latitude != null and latitude !=''">
                #{latitude},
            </if>
            <if test="longitude != null and longitude !=''">
                #{longitude},
            </if>
            <if test="address != null and address !=''">
                #{address},
            </if>
            <if test="userName != null and userName !=''">
                #{userName},
            </if>
            <if test="extend != null and extend !=''">
                #{extend},
            </if>
            <if test="nodeSubObjectType != null and nodeSubObjectType !=''">
                #{nodeSubObjectType},
            </if>
            <if test="nodeSubObjectValue != null and nodeSubObjectValue !=''">
                #{nodeSubObjectValue},
            </if>
            <if test="mobileNo != null and mobileNo !=''">
                #{mobileNo},
            </if>
            <if test="idCard != null and idCard !=''">
                #{idCard},
            </if>
            <if test="cartBadgeNo != null and cartBadgeNo !=''">
                #{cartBadgeNo},
            </if>
            <if test="deviceId != null and deviceId !=''">
                #{deviceId},
            </if>
            <if test="cartLongitude != null and cartLongitude !=''">
                #{cartLongitude},
            </if>
            <if test="cartLatitude != null and cartLatitude !=''">
                #{cartLatitude},
            </if>
            <if test="cartAddress != null and cartAddress !=''">
                #{cartAddress},
            </if>
            <choose>
                <when test="createdTime != null and createdTime !=''">TO_DATE(#{createdTime},'YYYY-MM-DD HH24:MI:SS'),</when>
                <otherwise>SYSDATE,</otherwise>
            </choose>
            SYSDATE
        )
    </insert>

    <resultMap id="listNodeMap" type="Resp1735218Bean">
        <id property="boTransTaskId" column="BO_TRANS_TASK_ID" />
        <result property="taxWaybillId" column="TAX_WAYBILL_ID" />
        <collection property="nodeList" ofType="com.wtyt.bo.bean.response.Resp1735218Bean$NodeInfo">
            <id property="nodeId" column="NODE_ID" />
            <result property="nodeTime" column="CREATED_TIME"/>
        </collection>
    </resultMap>

    <select id="getCompleteNodeList" parameterType="Req1735218Bean" resultMap="listNodeMap">
        SELECT * FROM (
            SELECT  R.TAX_WAYBILL_ID,
                R.BO_TRANS_TASK_ID,
                R.NODE_ID,
                TO_CHAR(R.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') CREATED_TIME
            FROM T_BO_TRANS_NODE_RECORD R
            WHERE R.BO_TRANS_TASK_ID IN
                <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                    #{item.boTransTaskId}
                </foreach>
                AND R.IS_DEL = 0 AND R.NODE_ID NOT IN (900 ,910 ,1200)
            UNION
            SELECT  R.TAX_WAYBILL_ID,
                R.BO_TRANS_TASK_ID,
                R.NODE_ID,
                TO_CHAR(R.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') CREATED_TIME
            FROM T_BO_TRANS_NODE_RECORD R
            WHERE (R.BO_TRANS_TASK_ID, R.ORG_ID) IN
                <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                    (#{item.boTransTaskId},#{item.orgId})
                </foreach>
                AND R.IS_DEL = 0 AND R.NODE_ID IN (900 ,910 ,1200)
        ) temp
        ORDER BY
        temp.CREATED_TIME DESC
    </select>

	 <select id="getNodeRecordList" parameterType="java.util.List"  resultType="BoTransNodeRecordBean">
        SELECT TAX_WAYBILL_ID taxWaybillId,
               BO_TRANS_TASK_ID boTransTaskId,
               NODE_ID nodeId,
               TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI') createdTime,
               NVL(OVER_TIME,0) overTime
        FROM T_BO_TRANS_NODE_RECORD T
        WHERE T.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND T.IS_DEL = 0
        ORDER BY T.CREATED_TIME
    </select>

    <select id="getNodeRecordListById" resultType="BoTransNodeRecordBean">
        SELECT TAX_WAYBILL_ID taxWaybillId,
               BO_TRANS_TASK_ID boTransTaskId,
               NODE_ID nodeId,
               OPT_SOURCE optSource,
               USER_ID userId,
               DRIVER_ID driverId,
               SYS_ROLE_TYPE sysRoleType,
               TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
               TAX_POS_INFO_ID taxPosInfoId,
               reason,
               job_name jobName,
               BOSS_NODE_CODE bossNodeCode,
               NVL(OVER_TIME,0) overTime
        FROM T_BO_TRANS_NODE_RECORD T
        WHERE T.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.IS_DEL = 0
        ORDER BY T.CREATED_TIME desc
    </select>

    <select id="getLastRecordByNodeIdAndTaxWaybillId" resultType="BoTransNodeRecordBean">
        SELECT *
        FROM
            (SELECT TAX_WAYBILL_ID taxWaybillId,
                    NODE_ID nodeId,
                    TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI') createdTime,
                    NVL(OVER_TIME,0) overTime
             FROM T_BO_TRANS_NODE_RECORD T
             WHERE T.BO_TRANS_TASK_ID = #{boTransTaskId}
               AND T.NODE_ID = #{nodeId}
               AND T.IS_DEL = 0
             ORDER BY T.CREATED_TIME DESC
            )T1
        WHERE ROWNUM = 1
    </select>

    <select id="getLastRecordByNodeIdAndTransTaskId" resultType="BoTransNodeRecordBean">
        SELECT *
        FROM
            (SELECT
                    ST.BO_TRANS_TASK_ID boTransTaskId,
                    ST.NODE_ID nodeId,
                    ST.USER_ID userId,
                    ST.DRIVER_ID driverId,
                    ST.ID_CARD idCard,
                    ST.SYS_ROLE_TYPE sysRoleType,
                    ST.LONGITUDE longitude,
                    ST.LATITUDE latitude,
                    ST.ADDRESS address,
                    TO_CHAR(ST.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
                    ST.NODE_SUB_OBJECT_TYPE nodeSubObjectType,
                    ST.NODE_SUB_OBJECT_VALUE nodeSubObjectValue,
                    NVL(ST.OVER_TIME,0) overTime
             FROM T_BO_TRANS_NODE_RECORD ST
             WHERE ST.IS_DEL = 0
               AND ST.NODE_ID = #{nodeId}
               AND ST.BO_TRANS_TASK_ID = #{boTransTaskId}
             ORDER BY ST.CREATED_TIME DESC
            )T1
        WHERE ROWNUM = 1
    </select>

    <select id="getLatelyNodeRecord" resultType="BoTransNodeRecordBean">
        SELECT *
        FROM
            (SELECT
                    ST.BO_TRANS_TASK_ID boTransTaskId,
                    ST.NODE_ID nodeId,
                    ST.USER_ID userId,
                    ST.DRIVER_ID driverId,
                    ST.SYS_ROLE_TYPE sysRoleType,
                    ST.LONGITUDE longitude,
                    ST.LATITUDE latitude,
                    ST.ADDRESS address,
                    TO_CHAR(ST.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
                    NVL(ST.OVER_TIME,0) overTime
             FROM T_BO_TRANS_NODE_RECORD ST
             WHERE ST.IS_DEL = 0
                <choose>
                    <when test="nodeSubObjectType != null and nodeSubObjectType.length > 0">
                        AND ST.NODE_SUB_OBJECT_TYPE = #{nodeSubObjectType}
                        AND ST.NODE_SUB_OBJECT_VALUE = #{nodeSubObjectValue}
                    </when>
                    <otherwise>
                        AND ST.NODE_SUB_OBJECT_TYPE IS NULL
                        AND ST.NODE_SUB_OBJECT_VALUE IS NULL
                    </otherwise>
                </choose>
               AND ST.NODE_ID = #{nodeId}
               AND ST.BO_TRANS_TASK_ID = #{boTransTaskId}
             ORDER BY ST.CREATED_TIME DESC
            )T1
        WHERE ROWNUM = 1
    </select>

    <select id="getFirstRecordByNodeIdAndTransTaskId" resultType="BoTransNodeRecordBean">
        SELECT *
        FROM
            (SELECT
                    ST.BO_TRANS_TASK_ID boTransTaskId,
                    ST.NODE_ID nodeId,
                    ST.USER_ID userId,
                    ST.DRIVER_ID driverId,
                    ST.SYS_ROLE_TYPE sysRoleType,
                    ST.LONGITUDE longitude,
                    ST.LATITUDE latitude,
                    ST.ADDRESS address,
                    TO_CHAR(ST.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
                    NVL(ST.OVER_TIME,0) overTime
             FROM T_BO_TRANS_NODE_RECORD ST
             WHERE ST.IS_DEL = 0
               AND ST.NODE_ID = #{nodeId}
               AND ST.BO_TRANS_TASK_ID = #{boTransTaskId}
             ORDER BY ST.CREATED_TIME
            )T1
        WHERE ROWNUM = 1
    </select>

    <update id="updateDeleteNode">
        UPDATE T_BO_TRANS_NODE_RECORD
        SET IS_DEL = 1
        WHERE BO_TRANS_TASK_ID = #{boTransTaskId}
        AND NODE_ID IN
        <foreach collection="nodeIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND IS_DEL = 0
    </update>
    <update id="updateSelective" parameterType="com.wtyt.dao.bean.syf.BoTransNodeRecordBean">
        update <include refid="tableName"/>
        <set>
            <if test="userId != null and userId != ''">
                USER_ID = #{userId},
            </if>
            <if test="driverId != null and driverId != ''">
                DRIVER_ID = #{driverId},
            </if>
            <if test="reason != null and reason != ''">
                REASON = #{reason},
            </if>
            <if test="isDel != null and isDel != ''">
                IS_DEL = #{isDel},
            </if>
            <if test="note != null and note != ''">
                NOTE = #{note},
            </if>
            <if test="optSource != null and optSource != ''">
                OPT_SOURCE = #{optSource},
            </if>
            <if test="createdTime != null and createdTime != ''">
                CREATED_TIME = to_date(#{createdTime},'yyyy-mm-dd hh24:mi:ss'),
            </if>
            <if test="taxPosInfoId != null and taxPosInfoId != ''">
                TAX_POS_INFO_ID = #{taxPosInfoId},
            </if>
            <if test="sysRoleType != null and sysRoleType != ''">
                SYS_ROLE_TYPE = #{sysRoleType},
            </if>
            <if test="overTime != null and overTime != ''">
                OVER_TIME = to_date(#{overTime},'YYYY-MM-DD HH24:MI:SS'),
            </if>
            <if test="bossNodeCode != null and bossNodeCode != ''">
                BOSS_NODE_CODE = #{bossNodeCode},
            </if>
            LAST_MODIFIED_TIME = SYSDATE,
        </set>
        <where>
            BO_TRANS_NODE_RECORD_ID = #{boTransNodeRecordId}
            AND IS_DEL = 0
        </where>
    </update>

    <select id="countByNodeId" resultType="int">
        SELECT COUNT(1)
        FROM T_BO_TRANS_NODE_RECORD
        WHERE
        BO_TRANS_TASK_ID = #{boTransTaskId}
        AND NODE_ID = #{nodeId}
        AND IS_DEL = 0
    </select>

    <select id="queryRecordByNodeId" resultMap="baseResultMap"
            parameterType="java.lang.String">
        select * from T_BO_TRANS_NODE_RECORD where NODE_ID = #{nodeId} and BO_TRANS_TASK_ID = #{boTransTaskId} and IS_DEL = 0
    </select>

    <select id="queryUserIdByNodeRecordId" resultType="java.lang.String" parameterType="java.lang.String">
        select USER_ID from T_BO_TRANS_NODE_RECORD where BO_TRANS_NODE_RECORD_ID = #{dispatchCarRecordId} and is_del = 0
    </select>

    <update id="updateBossNodeCode">
        update T_BO_TRANS_NODE_RECORD r set r.BOSS_NODE_CODE = #{bossNodeCode},r.LAST_MODIFIED_TIME = sysdate
        where r.NODE_ID = #{nodeId} and
        r.BO_TRANS_TASK_ID = #{boTransTaskId}
         and r.BOSS_NODE_CODE is NULL
    </update>

    <select id="queryUserIdByNodeRecordIdList" resultType="BoTransNodeRecordBean" parameterType="java.lang.String">
        SELECT T.BO_TRANS_NODE_RECORD_ID boTransNodeRecordId,
               T.USER_ID userId
          FROM T_BO_TRANS_NODE_RECORD T
         WHERE T.BO_TRANS_NODE_RECORD_ID IN
         <foreach collection="boTransNodeRecordIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
           AND T.IS_DEL = 0
    </select>
    <select id="queryOrgIdByNodeRecordId" resultType="java.lang.String" parameterType="java.lang.String">
        select ORG_ID from T_BO_TRANS_NODE_RECORD where BO_TRANS_NODE_RECORD_ID = #{recordId} and IS_DEL = 0
    </select>

    <update id="deleteOldSupplyNodeRecord">
        UPDATE T_BO_TRANS_NODE_RECORD SET IS_DEL =1,LAST_MODIFIED_TIME =SYSDATE WHERE IS_DEL =0 AND BO_TRANS_TASK_ID = #{boTransTaskId} AND NODE_ID>=200
    </update>
    <update id="deleteNodeRecord">
        UPDATE T_BO_TRANS_NODE_RECORD SET IS_DEL =1,LAST_MODIFIED_TIME =SYSDATE WHERE IS_DEL =0 AND BO_TRANS_TASK_ID = #{boTransTaskId} AND NODE_ID IN
        <foreach collection="nodeIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdateNodeRecord">
        <foreach collection="recordBeanList" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_TRANS_NODE_RECORD T
            <set>
                <if test="item.longitude != null and item.longitude != ''">
                    T.LONGITUDE = #{item.longitude},
                </if>
                <if test="item.latitude != null and item.latitude != ''">
                    T.LATITUDE = #{item.latitude},
                </if>
                <if test="item.address != null and item.address != ''">
                    T.ADDRESS = #{item.address},
                </if>
            </set>
            <where>
                T.IS_DEL = 0
                AND T.BO_TRANS_TASK_ID = #{item.boTransTaskId}
                AND T.NODE_ID = #{item.nodeId}
            </where>
        </foreach>
    </update>
    <update id="batchTransferOrg">
        UPDATE T_BO_TRANS_NODE_RECORD SET ORG_ID = #{data.toOrgId}, GROUP_ID = #{data.groupId}, LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0 AND ORG_ID = #{data.fromOrgId} AND BO_TRANS_TASK_ID IN
        <foreach collection="taskIds" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
    <update id="batchTransferUserInfo" parameterType="java.util.List">
        <foreach collection="boTaskDetailBeans" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_TRANS_NODE_RECORD
            <set>
                LAST_MODIFIED_TIME = SYSDATE,
                USER_ID = #{item.toUserId},
                <if test="item.userName != null and item.userName != ''">
                    USER_NAME = #{item.userName},
                </if>
                <if test="item.createdUserSysRoleType != null and item.createdUserSysRoleType != ''">
                    SYS_ROLE_TYPE = #{item.createdUserSysRoleType},
                </if>
                <if test="item.createdUserJobName != null and item.createdUserJobName != ''">
                    JOB_NAME = #{item.createdUserJobName}
                </if>
            </set>
            WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID = #{item.boTransTaskId} AND USER_ID = #{item.createdUserId} AND USER_ID IS NOT NULL
        </foreach>
    </update>

    <select id="queryLatestGuaranteeNodeInfo" resultType="com.wtyt.dao.bean.syf.BoTransNodeRecordBean">
        SELECT
        NODE_ID nodeId,
        TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI') createdTime
        FROM T_BO_TRANS_NODE_RECORD WHERE IS_DEL =0 AND BO_TRANS_TASK_ID = #{boTransTaskId} AND NODE_ID IN
        <foreach collection="nodeIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND CREATED_TIME >= (SELECT CREATED_TIME  FROM (SELECT CREATED_TIME FROM T_BO_TRANS_NODE_RECORD WHERE IS_DEL =0 AND BO_TRANS_TASK_ID = #{boTransTaskId} AND NODE_ID = 200 ORDER BY CREATED_TIME DESC) WHERE rownum=1)
        <if test="guaranteeDealTime!=null and guaranteeDealTime!=''">
            AND CREATED_TIME &lt;= TO_DATE(#{guaranteeDealTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        GROUP BY NODE_ID,CREATED_TIME
        ORDER BY CREATED_TIME ASC
    </select>
    <select id="queryByTransTaskIdAndNodeIdList" resultType="com.wtyt.dao.bean.syf.BoTransNodeRecordBean">
        SELECT
            BO_TRANS_NODE_RECORD_ID boTransNodeRecordId,
            NODE_ID nodeId,
            TO_CHAR(CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime,
            NODE_SUB_OBJECT_VALUE nodeSubObjectValue
        FROM T_BO_TRANS_NODE_RECORD
        WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID = #{boTransTaskId}
        AND NODE_ID IN
        <foreach collection="nodeIdList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        ORDER BY CREATED_TIME
    </select>
    <select id="queryLatestNodeIdByBoTransTaskId" resultType="java.lang.String"
            parameterType="java.lang.String">
        SELECT
            NODE_ID
        FROM
            (
                SELECT
                    NODE_ID
                FROM
                    T_BO_TRANS_NODE_RECORD tbtnr
                WHERE
                    BO_TRANS_TASK_ID = #{boTransTaskId}
                  AND IS_DEL = 0
                ORDER BY
                    NODE_ID DESC )
        WHERE
            rownum = 1
    </select>

    <select id="queryByBoTransTaskIds" resultMap="baseResultMap">
        select * from T_BO_TRANS_NODE_RECORD
        where
        NODE_ID = #{nodeId}
        AND ORG_ID = #{orgId}
        AND IS_DEL = 0
        AND BO_TRANS_TASK_ID IN
       <foreach collection="boTransTaskIdList" index="index" item="item" open="(" close=")" separator=",">
           #{item}
       </foreach>
        ORDER BY CREATED_TIME DESC
    </select>

    <select id="queryById" resultMap="baseResultMap">
        SELECT
        USER_ID,
        USER_NAME,
        SYS_ROLE_TYPE
        FROM
        T_BO_TRANS_NODE_RECORD
        WHERE
        BO_TRANS_NODE_RECORD_ID = #{id}
    </select>

    <select id="batchQueryLatestNodeRecord" resultMap="baseResultMap">
        SELECT
            BO_TRANS_TASK_ID,
            ID_CARD
        FROM (
            SELECT
                BO_TRANS_TASK_ID,
                ID_CARD,
                ROW_NUMBER() OVER(PARTITION BY BO_TRANS_TASK_ID ORDER BY CREATED_TIME DESC) RN
            FROM
                T_BO_TRANS_NODE_RECORD
            WHERE
                IS_DEL = 0
                AND BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                AND NODE_ID = #{nodeId}
        )
        WHERE
            RN = 1
    </select>

    <select id="getTransNodeRecord" resultType="BoTransNodeRecordBean">
        SELECT
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.NODE_ID nodeId,
            T.USER_ID userId,
            T.DRIVER_ID driverId,
            T.SYS_ROLE_TYPE sysRoleType,
            T.LONGITUDE longitude,
            T.LATITUDE latitude,
            T.ADDRESS address,
            T.NODE_SUB_OBJECT_TYPE nodeSubObjectType,
            T.NODE_SUB_OBJECT_VALUE nodeSubObjectValue,
            TO_CHAR(T.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime
        FROM T_BO_TRANS_NODE_RECORD T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_NODE_RECORD_ID = #{boTransNodeRecordId}
    </select>
</mapper>
