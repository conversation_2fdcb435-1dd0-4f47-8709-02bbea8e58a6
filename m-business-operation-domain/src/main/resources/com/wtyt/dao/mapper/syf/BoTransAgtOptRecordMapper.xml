<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransAgtOptRecordMapper">


    <insert id="insertBoTransAgtOptRecord">
        INSERT INTO T_BO_TRANS_AGT_OPT_RECORD(BO_TRANS_AGT_OPT_RECORD_ID, BO_TRANS_AGREEMENT_ID, USER_ID, SYS_ROLE_TYPE, OPT_TYPE, OPT_WAY)
        VALUES(#{boTransAgtOptRecordId}, #{boTransAgreementId}, #{userId}, #{sysRoleType}, #{optType}, #{optWay})
    </insert>

    <select id="getAllAgtOptInfo" resultType="com.wtyt.dao.bean.syf.BoTransAgtOptRecordBean">
        SELECT
            BO_TRANS_AGT_OPT_RECORD_ID boTransAgtOptRecordId,
            AGREEMENT_INFO agreementInfo
        FROM T_BO_TRANS_AGT_OPT_RECORD
        WHERE IS_DEL = 0
    </select>
</mapper>
