<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpDhHeaderColumnMapper">

    <sql id="columnField">
        BO_TP_DH_HEADER_COLUMN_ID boTpDhHeaderColumnId,
        BO_TP_DH_TASK_FIELD_DICT_ID boTpDhTaskFieldDictId,
        BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
        DEFAULT_VALUE defaultValue,
        SEARCH_TYPE searchType,
        IS_PRECISE isPrecise,
        BO_TP_DH_TASK_FIELD_RULE_ID boTpDhTaskFieldRuleId,
        UNIT unit
    </sql>

    <!-- 含新颜动态表头开关功能开关新增字段 -->
    <sql id="columnList">
        BO_TP_DH_HEADER_COLUMN_ID boTpDhHeaderColumnId,
        BO_TP_DH_TASK_FIELD_DICT_ID boTpDhTaskFieldDictId,
        BO_TP_DH_HEADER_IMPORT_ID boTpDhHeaderImportId,
        DEFAULT_VALUE defaultValue,
        SEARCH_TYPE searchType,
        IS_PRECISE isPrecise,
        BO_TP_DH_TASK_FIELD_RULE_ID boTpDhTaskFieldRuleId,
        UNIT unit,
        OPTION_VALUE optionValue,
        CREATE_SORT_NUM createSortNum,
        IS_CREATE_REQUIRE createRequire,
        INPUT_TIPS inputTips,
        CAN_MODIFY canModify,
        IS_LIST_SHOW_UNIT listShowUnit,
        CREATE_UNIT createUnit,
        CREATE_UNIT_OPTION_VALUE createUnitOptionValue
    </sql>

    <insert id="insertBatch">
        INSERT INTO T_BO_TP_DH_HEADER_COLUMN(BO_TP_DH_HEADER_COLUMN_ID, BO_TP_DH_TASK_FIELD_DICT_ID,
        BO_TP_DH_HEADER_IMPORT_ID, DEFAULT_VALUE, SEARCH_TYPE, IS_PRECISE, BO_TP_DH_TASK_FIELD_RULE_ID, UNIT)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTpDhHeaderColumnId} BO_TP_DH_HEADER_COLUMN_ID,
            #{item.boTpDhTaskFieldDictId} BO_TP_DH_TASK_FIELD_DICT_ID,
            #{item.boTpDhHeaderImportId} BO_TP_DH_HEADER_IMPORT_ID,
            #{item.defaultValue} DEFAULT_VALUE,
            #{item.searchType} SEARCH_TYPE,
            #{item.isPrecise} IS_PRECISE,
            #{item.boTpDhTaskFieldRuleId} BO_TP_DH_TASK_FIELD_RULE_ID,
            #{item.unit} UNIT
            FROM
            DUAL
        </foreach>
    </insert>

    <insert id="batchInsert">
        INSERT INTO T_BO_TP_DH_HEADER_COLUMN(BO_TP_DH_HEADER_COLUMN_ID, BO_TP_DH_TASK_FIELD_DICT_ID,
        BO_TP_DH_HEADER_IMPORT_ID, DEFAULT_VALUE, SEARCH_TYPE, IS_PRECISE, BO_TP_DH_TASK_FIELD_RULE_ID, UNIT, CREATE_SORT_NUM, IS_CREATE_REQUIRE, OPTION_VALUE, INPUT_TIPS, CAN_MODIFY, IS_LIST_SHOW_UNIT, CREATE_UNIT, CREATE_UNIT_OPTION_VALUE)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTpDhHeaderColumnId} BO_TP_DH_HEADER_COLUMN_ID,
            #{item.boTpDhTaskFieldDictId} BO_TP_DH_TASK_FIELD_DICT_ID,
            #{item.boTpDhHeaderImportId} BO_TP_DH_HEADER_IMPORT_ID,
            #{item.defaultValue} DEFAULT_VALUE,
            #{item.searchType} SEARCH_TYPE,
            #{item.isPrecise} IS_PRECISE,
            #{item.boTpDhTaskFieldRuleId} BO_TP_DH_TASK_FIELD_RULE_ID,
            #{item.unit} UNIT,
            #{item.createSortNum,jdbcType=NUMERIC} CREATE_SORT_NUM,
            #{item.createRequire} IS_CREATE_REQUIRE,
            #{item.optionValue,jdbcType=VARCHAR} OPTION_VALUE,
            #{item.inputTips,jdbcType=VARCHAR} INPUT_TIPS,
            #{item.canModify} CAN_MODIFY,
            #{item.listShowUnit} IS_LIST_SHOW_UNIT,
            #{item.createUnit,jdbcType=VARCHAR} CREATE_UNIT,
            #{item.createUnitOptionValue,jdbcType=VARCHAR} CREATE_UNIT_OPTION_VALUE
            FROM
            DUAL
        </foreach>
    </insert>

    <select id="queryDhHeaderColumnListByImportId" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderColumnBean">
        SELECT
        <include refid="columnField"/>
        FROM T_BO_TP_DH_HEADER_COLUMN
        WHERE IS_DEL = 0
        AND BO_TP_DH_HEADER_IMPORT_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryDhHeaderColumnListByImportIds" resultType="com.wtyt.dao.bean.syf.BoTpDhHeaderColumnBean">
        SELECT
        <include refid="columnList"/>
        FROM T_BO_TP_DH_HEADER_COLUMN
        WHERE IS_DEL = 0
        AND BO_TP_DH_HEADER_IMPORT_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateCreateAttributes">
            UPDATE T_BO_TP_DH_HEADER_COLUMN SET CREATE_SORT_NUM = #{createSortNum}, IS_CREATE_REQUIRE =
            #{createRequire}, INPUT_TIPS = #{inputTips}, CAN_MODIFY = #{canModify}, IS_LIST_SHOW_UNIT =
            #{listShowUnit}, CREATE_UNIT = #{createUnit}, CREATE_UNIT_OPTION_VALUE =
            #{createUnitOptionValue} WHERE
                BO_TP_DH_HEADER_COLUMN_ID = #{boTpDhHeaderColumnId}
    </update>
</mapper>
