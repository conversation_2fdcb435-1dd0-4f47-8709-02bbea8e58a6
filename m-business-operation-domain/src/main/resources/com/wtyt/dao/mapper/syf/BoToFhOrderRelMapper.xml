<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoToFhOrderRelMapper">


	<select id="selectByTransOrderIdList" resultType="com.wtyt.dao.bean.syf.BoToFhOrderRelBean">
		SELECT
			BO_TO_FH_ORDER_REL_ID boToFhOrderRelId,
			BO_TRANS_ORDER_ID boTransOrderId,
			BO_TP_FH_ORDER_ID boTpFhOrderId,
			FH_STATE fhState
		FROM
			T_BO_TO_FH_ORDER_REL
		WHERE
			IS_DEL = 0
			AND BO_TRANS_ORDER_ID IN
			<foreach collection="list" item="item" close=")" open="(" separator=",">
				#{item}
			</foreach>
		ORDER BY CREATED_TIME
	</select>

	<select id="selectByFhOrderIdList" resultType="com.wtyt.dao.bean.syf.BoToFhOrderRelBean">
		SELECT
			BO_TO_FH_ORDER_REL_ID boToFhOrderRelId,
			BO_TRANS_ORDER_ID boTransOrderId,
			BO_TP_FH_ORDER_ID boTpFhOrderId,
			FH_STATE fhState
		FROM
			T_BO_TO_FH_ORDER_REL
		WHERE
			IS_DEL = 0
		  AND BO_TP_FH_ORDER_ID in
			<foreach collection="list" item="item" close=")" open="(" separator=",">
				#{item}
			</foreach>
		ORDER BY CREATED_TIME
	</select>



	<select id="countToFhOrderRelTO" resultType="java.lang.Integer">
		SELECT count(*)
		FROM
		T_BO_TO_FH_ORDER_REL
		WHERE
		IS_DEL = 0
		AND BO_TP_FH_ORDER_ID = #{boTpFhOrderId}
	</select>

	<insert id="batchSave">
		INSERT INTO
		T_BO_TO_FH_ORDER_REL (BO_TO_FH_ORDER_REL_ID,
		BO_TRANS_ORDER_ID,
		BO_TP_FH_ORDER_ID,
		FH_STATE)
		SELECT
		A.*
		FROM (
		<foreach item="item" index="index" collection="list" separator="UNION ALL">
			SELECT #{item.boToFhOrderRelId} boToFhOrderRelId,
			#{item.boTransOrderId} boTransOrderId,
			#{item.boTpFhOrderId} boTpFhOrderId,
			#{item.fhState} fhState
			FROM DUAL
		</foreach>
		) A
	</insert>

	<update id="deleteByFhOrderId">
		UPDATE T_BO_TO_FH_ORDER_REL SET IS_DEL = 1, LAST_MODIFIED_TIME = sysdate WHERE BO_TP_FH_ORDER_ID = #{boTpFhOrderId}
	</update>

	<update id="deleteByFhOrderRelId">
		UPDATE T_BO_TO_FH_ORDER_REL SET IS_DEL = 1, LAST_MODIFIED_TIME = sysdate WHERE BO_TO_FH_ORDER_REL_ID = #{boToFhOrderRelId}
	</update>

	<update id="updateFhStateByFhOrderId">
		UPDATE T_BO_TO_FH_ORDER_REL SET FH_STATE = #{fhSate}, LAST_MODIFIED_TIME = sysdate WHERE BO_TP_FH_ORDER_ID = #{boTpFhOrderId}
	</update>

	<select id="selectByFhOrderId" resultType="com.wtyt.dao.bean.syf.BoToFhOrderRelBean">
		SELECT
		BO_TO_FH_ORDER_REL_ID boToFhOrderRelId,
		BO_TRANS_ORDER_ID boTransOrderId,
		BO_TP_FH_ORDER_ID boTpFhOrderId,
		FH_STATE fhState
		FROM
		T_BO_TO_FH_ORDER_REL
		WHERE
			IS_DEL = 0
		  AND BO_TP_FH_ORDER_ID = #{boTpFhOrderId}
	</select>

	<select id="getOriginOrder" resultType="com.wtyt.dao.bean.syf.BoToFhOrderRelBean">
		SELECT
			b.BO_TRANS_ORDER_ID boTransOrderId,
			b.MERGE_ID mergeId,
			d.BO_TRANS_TASK_ID boTransTaskId,
			DECODE(d.NODE_ID, 0, 1, 100, 1, 300, 1, NULL,1 ,2) transportState
		FROM
			T_BO_TRANS_ORDER b
				LEFT JOIN T_BO_TRANS_ORDER_REL c ON
						b.BO_TRANS_ORDER_ID = c.BO_TRANS_ORDER_ID
					AND c.IS_DEL = 0
				LEFT JOIN T_BO_TRANS_TASK d ON
						d.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID
					AND d.IS_DEL = 0
		WHERE
			b.IS_DEL = 0
		  AND b.BO_TRANS_ORDER_ID IN (
			SELECT
				BO_TRANS_ORDER_ID
			FROM
				T_BO_TO_FH_ORDER_REL a
			WHERE
			 a.BO_TP_FH_ORDER_ID = #{boTpFhOrderId} )
	</select>
</mapper>