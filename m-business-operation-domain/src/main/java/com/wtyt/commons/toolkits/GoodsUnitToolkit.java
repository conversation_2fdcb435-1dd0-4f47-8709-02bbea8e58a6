package com.wtyt.commons.toolkits;

import com.alibaba.fastjson.JSONArray;
import com.wtyt.commons.bean.GoodsUnitBean;
import com.wtyt.dao.bean.syf.TaxDictBean;
import com.wtyt.util.OrgCacheDataUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 
 * @ClassName: GoodsUnitToolkit
 * @Description: 货物单位转换工具类
 * <AUTHOR>
 * @date 2021年7月16日
 *
 */
@Component
public class GoodsUnitToolkit {
    private static Logger log = LoggerFactory.getLogger(GoodsUnitToolkit.class);
    @Autowired
    private TaxDictToolkit taxDictToolkit;

    public Map<String,String> getGoodsDictMap(){
        return taxDictToolkit.getDictByDictType(TaxDictToolkit.GOODS_UNIT);
    }

    /**
     * 获取字典map（反向的），key为货物单位中文名称，value为货物单位类型
     * @return
     */
    public Map<String,String> getReverseGoodsDictMap(){
        return taxDictToolkit.getReverseDictByDictType(TaxDictToolkit.GOODS_UNIT);
    }

    /**
     * 获取货物数量单位翻译值
     * 
     * @param code
     * @return
     */
    public String getGoodsUnitName(String code) {
        return getGoodsUnitName(code, null);
    }

    public String getGoodsUnitName(String code, String defaulltName) {
        TaxDictBean taxDictBean = taxDictToolkit.findDictByTypeAndKey(TaxDictToolkit.GOODS_UNIT, code);
        if (taxDictBean != null)
            return taxDictBean.getDictValue();
        return defaulltName;
    }

    /**
     * 判断货物单位是否合法
     *
     * @param code 货物单位
     * @param orgId 项目id
     * @param isUsed 是否正在使用中的配置
     * @return
     */
    public boolean checkGoodsUnitCode(String code, String orgId, boolean isUsed) {
        if (isUsed) {
            return checkGoodsUnitCode(code, orgId);
        }
        Map<String, String> map = OrgCacheDataUtil.getTaxSysValueByOrgId(orgId);
        String value = map.get("785");
        if (StringUtils.isNotBlank(value)) {
            List<GoodsUnitBean> list = JSONArray.parseArray(map.get("785"), GoodsUnitBean.class);
            for (GoodsUnitBean goodsUnitBean : list) {
                if (goodsUnitBean.getKey().equals(code)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断货物单位是否合法
     * 
     * @param code
     * @param orgId
     * @return
     */
    public boolean checkGoodsUnitCode(String code, String orgId) {
        Map<String, String> map = OrgCacheDataUtil.getTaxSysValueByOrgId(orgId);
        String value = map.get("785");
        if (StringUtils.isNotBlank(value)) {
            List<GoodsUnitBean> list = JSONArray.parseArray(map.get("785"), GoodsUnitBean.class);
            for (GoodsUnitBean goodsUnitBean : list) {
                if (goodsUnitBean.getKey().equals(code) && "1".equals(goodsUnitBean.getCheck())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     *
     * 判断货物单位名称是否合法
     * 
     * @param
     * @return
     * @throw
     */
    public static boolean checkGoodsUnitName(String name, String orgId) {
        Map<String, String> map = OrgCacheDataUtil.getTaxSysValueByOrgId(orgId);
        String value = map.get("785");
        if (StringUtils.isNotBlank(value)) {
            List<GoodsUnitBean> list = JSONArray.parseArray(map.get("785"), GoodsUnitBean.class);
            for (GoodsUnitBean goodsUnitBean : list) {
                if (goodsUnitBean.getValue().equals(name) && "1".equals(goodsUnitBean.getCheck())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     *
     * 判断货物单位名称是否合法
     * 
     * @param
     * @return
     * @throw
     */
    public GoodsUnitBean getGoodsUnitBeanByName(String name, String orgId) {
        Map<String, String> map = OrgCacheDataUtil.getTaxSysValueByOrgId(orgId);
        String value = map.get("785");
        if (StringUtils.isNotBlank(value)) {
            List<GoodsUnitBean> list = JSONArray.parseArray(map.get("785"), GoodsUnitBean.class);
            for (GoodsUnitBean goodsUnitBean : list) {
                if (goodsUnitBean.getValue().equals(name) && "1".equals(goodsUnitBean.getCheck())) {
                    return goodsUnitBean;
                }
            }
        }
        return null;
    }
}
