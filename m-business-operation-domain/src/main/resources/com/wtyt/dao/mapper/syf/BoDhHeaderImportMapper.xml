<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoDhHeaderImportMapper">

    <sql id="columnField">
        BO_DH_HEADER_IMPORT_ID boDhHeaderImportId,
        BO_DH_CONFIG_ID boDhConfigId,
        HEADER_NAME headerName,
        SAMPLE_DATA sampleData,
        IS_REQUIRED isRequired,
        IS_CREATE_REQUIRED isCreateRequired,
        IS_UNIQUE isUnique,
        IS_SHOW isShow,
        IS_APP_SHOW appIsShow,
        SEARCH_TYPE searchType,
        IS_PRECISE isPrecise,
        IS_EXPORT isExport,
        IS_INPUT isInput,
        IS_IMPORT isImport,
        CAN_MODIFY canModify,
        DEFAULT_VALUE defaultValue,
        SORT_NUM sortNum,
        EXPORT_SORT_NUM exportSortNum,
        APP_SORT_NUM appSortNum,
        FROM_SOURCE fromSource
    </sql>



    <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO T_BO_DH_HEADER_IMPORT (
        BO_DH_HEADER_IMPORT_ID,
        BO_DH_CONFIG_ID,
        HEADER_NAME,
        SAMPLE_DATA,
        IS_REQUIRED,
        IS_CREATE_REQUIRED,
        IS_UNIQUE,
        IS_SHOW,
        IS_APP_SHOW,
        SEARCH_TYPE,
        IS_PRECISE,
        IS_EXPORT,
        IS_INPUT,
        IS_IMPORT,
        CAN_MODIFY,
        DEFAULT_VALUE,
        SORT_NUM,
        EXPORT_SORT_NUM,
        APP_SORT_NUM,
        FROM_SOURCE
    )
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
        SELECT
            #{item.boDhHeaderImportId,jdbcType=VARCHAR},
            #{item.boDhConfigId,jdbcType=VARCHAR},
            #{item.headerName,jdbcType=VARCHAR},
            #{item.sampleData,jdbcType=VARCHAR},
            #{item.isRequired,jdbcType=VARCHAR},
            #{item.isCreateRequired,jdbcType=VARCHAR},
            #{item.isUnique,jdbcType=VARCHAR},
            #{item.isShow,jdbcType=VARCHAR},
            #{item.appIsShow,jdbcType=VARCHAR},
            #{item.searchType,jdbcType=VARCHAR},
            #{item.isPrecise,jdbcType=VARCHAR},
            #{item.isExport,jdbcType=VARCHAR},
            #{item.isInput,jdbcType=VARCHAR},
            #{item.isImport,jdbcType=VARCHAR},
            #{item.canModify,jdbcType=VARCHAR},
            #{item.defaultValue,jdbcType=VARCHAR},
            #{item.sortNum,jdbcType=VARCHAR},
            #{item.exportSortNum,jdbcType=VARCHAR},
            #{item.appSortNum,jdbcType=VARCHAR},
            #{item.fromSource,jdbcType=VARCHAR}
        FROM DUAL
    </foreach>
</insert>


    <update id="updateHeaderName">
        UPDATE T_BO_DH_HEADER_IMPORT
        SET HEADER_NAME = #{headerName}, LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_DH_HEADER_IMPORT_ID = #{boDhHeaderImportId}
    </update>

    <select id="queryHeaderImportById" resultType="com.wtyt.dao.bean.syf.BoDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM T_BO_DH_HEADER_IMPORT WHERE
        IS_DEL = 0
        AND BO_DH_HEADER_IMPORT_ID = #{boDhHeaderImportId}
    </select>


    <select id="queryDhHeaderImportListByConfigId" resultType="com.wtyt.dao.bean.syf.BoDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM
        T_BO_DH_HEADER_IMPORT
        WHERE
        is_del = 0
        AND BO_DH_CONFIG_ID = #{boTpDhConfigId}
        ORDER BY SORT_NUM ASC
    </select>

    <select id="queryImportDhHeaderListByConfigId" resultType="com.wtyt.dao.bean.syf.BoDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM
        T_BO_DH_HEADER_IMPORT
        WHERE
        is_del = 0
        AND BO_DH_CONFIG_ID = #{boTpDhConfigId} and IS_IMPORT=1
        ORDER BY SORT_NUM ASC
    </select>


    <select id="queryHeaderImportByConfigAndName" resultType="com.wtyt.dao.bean.syf.BoDhHeaderImportBean">
        SELECT
        <include refid="columnField"/>
        FROM T_BO_DH_HEADER_IMPORT WHERE
        IS_DEL = 0
        AND BO_DH_CONFIG_ID = #{boDhConfigId}
        AND HEADER_NAME = #{headerName}
        AND rownum = 1
    </select>





</mapper>
