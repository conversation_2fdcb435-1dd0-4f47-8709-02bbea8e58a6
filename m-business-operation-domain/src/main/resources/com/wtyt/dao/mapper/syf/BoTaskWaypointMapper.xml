<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskWaypointMapper">
    <resultMap type="com.wtyt.dao.bean.syf.BoTaskWaypointBean" id="BoTaskWaypointMap">
        <result property="boTaskWaypointId" column="BO_TASK_WAYPOINT_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="waypointType" column="WAYPOINT_TYPE" jdbcType="VARCHAR"/>
        <result property="regionId" column="REGION_ID" jdbcType="VARCHAR"/>
        <result property="regionName" column="REGION_NAME" jdbcType="VARCHAR"/>
        <result property="waypointId" column="WAYPOINT_ID" jdbcType="VARCHAR"/>
        <result property="waypointName" column="WAYPOINT_NAME" jdbcType="VARCHAR"/>
        <result property="provinceName" column="PROVINCE_NAME" jdbcType="VARCHAR"/>
        <result property="cityName" column="CITY_NAME" jdbcType="VARCHAR"/>
        <result property="countyName" column="COUNTY_NAME" jdbcType="VARCHAR"/>
        <result property="longitude" column="LONGITUDE" jdbcType="VARCHAR"/>
        <result property="latitude" column="LATITUDE" jdbcType="VARCHAR"/>
        <result property="goodsName" column="GOODS_NAME" jdbcType="VARCHAR"/>
        <result property="arriveTime" column="ARRIVE_TIME" jdbcType="VARCHAR"/>
        <result property="voucherUploadTime" column="VOUCHER_UPLOAD_TIME" jdbcType="VARCHAR"/>
        <result property="sortNo" column="SORT_NO" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="address" column="ADDRESS" jdbcType="VARCHAR"/>
    </resultMap>


    <insert id="saveBatch" parameterType="java.util.List">
        INSERT INTO T_BO_TASK_WAYPOINT(BO_TASK_WAYPOINT_ID,BO_TRANS_TASK_ID, WAYPOINT_TYPE, REGION_ID, REGION_NAME, WAYPOINT_ID, WAYPOINT_NAME, PROVINCE_NAME, CITY_NAME, COUNTY_NAME, LONGITUDE, LATITUDE, GOODS_NAME, SORT_NO, ADDRESS)
        <foreach item="entity" index="index" collection="list" separator="UNION ALL">
            SELECT
                #{entity.boTaskWaypointId} boTaskWaypointId,
                #{entity.boTransTaskId} boTransTaskId,
                #{entity.waypointType} waypointType,
                #{entity.regionId} regionId,
                #{entity.regionName} regionName,
                #{entity.waypointId} waypointId,
                #{entity.waypointName} waypointName,
                #{entity.provinceName} provinceName,
                #{entity.cityName} cityName,
                #{entity.countyName} countyName,
                #{entity.longitude} longitude,
                #{entity.latitude} latitude,
                #{entity.goodsName} goodsName,
                #{entity.sortNo} sortNo,
                #{entity.address} address
            FROM
                DUAL
        </foreach>
    </insert>

    <select id="queryByTaskId" resultMap="BoTaskWaypointMap">
        SELECT
            BO_TASK_WAYPOINT_ID,
            WAYPOINT_TYPE,
            REGION_ID,
            REGION_NAME,
            WAYPOINT_ID,
            WAYPOINT_NAME,
            PROVINCE_NAME,
            CITY_NAME,
            COUNTY_NAME,
            ADDRESS,
            LONGITUDE,
            LATITUDE,
            GOODS_NAME,
            TO_CHAR(ARRIVE_TIME, 'YYYY-MM-DD HH24:MI:SS') ARRIVE_TIME,
            TO_CHAR(VOUCHER_UPLOAD_TIME, 'YYYY-MM-DD HH24:MI:SS') VOUCHER_UPLOAD_TIME,
            SORT_NO
        FROM
            T_BO_TASK_WAYPOINT
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID = #{taskId}
        ORDER BY
            WAYPOINT_TYPE,SORT_NO ASC
    </select>

    <delete id="deleteByIds">
        UPDATE T_BO_TASK_WAYPOINT SET IS_DEL = 1,LAST_MODIFIED_TIME =SYSDATE WHERE BO_TASK_WAYPOINT_ID IN
        <foreach collection="ids" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </delete>

    <update id="updateBatchFull">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE
            T_BO_TASK_WAYPOINT
            SET
            WAYPOINT_TYPE = #{item.waypointType},
            REGION_ID = #{item.regionId},
            REGION_NAME = #{item.regionName},
            WAYPOINT_ID = #{item.waypointId},
            WAYPOINT_NAME = #{item.waypointName},
            PROVINCE_NAME = #{item.provinceName},
            CITY_NAME = #{item.cityName},
            COUNTY_NAME = #{item.countyName},
            ADDRESS =#{item.address},
            LONGITUDE = #{item.longitude},
            LATITUDE = #{item.latitude},
            GOODS_NAME = #{item.goodsName},
            SORT_NO = #{item.sortNo},
            LAST_MODIFIED_TIME = SYSDATE
            WHERE
            BO_TASK_WAYPOINT_ID = #{item.boTaskWaypointId}

        </foreach>
    </update>

    <select id="getTaskWaypointListByTaskIdList" resultMap="BoTaskWaypointMap">
        SELECT
            BO_TASK_WAYPOINT_ID,
            BO_TRANS_TASK_ID,
            WAYPOINT_TYPE,
            REGION_ID,
            REGION_NAME,
            WAYPOINT_ID,
            WAYPOINT_NAME,
            PROVINCE_NAME,
            CITY_NAME,
            COUNTY_NAME,
            ADDRESS,
            LONGITUDE,
            LATITUDE,
            GOODS_NAME,
            SORT_NO,
            TO_CHAR(ARRIVE_TIME, 'YYYY-MM-DD HH24:MI:SS') ARRIVE_TIME,
            TO_CHAR(VOUCHER_UPLOAD_TIME, 'YYYY-MM-DD HH24:MI:SS') VOUCHER_UPLOAD_TIME
        FROM T_BO_TASK_WAYPOINT T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY T.BO_TRANS_TASK_ID,T.WAYPOINT_TYPE,T.SORT_NO
    </select>

    <select id="getTaskLoadingWaypointListByTaskIdAndAddress" resultMap="BoTaskWaypointMap">
        SELECT T.*
        FROM T_BO_TASK_WAYPOINT T
        WHERE T.IS_DEL = 0
        AND T.WAYPOINT_TYPE IN (1, 3)
        AND T.PROVINCE_NAME || T.CITY_NAME || T.COUNTY_NAME || T.ADDRESS = #{address}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY T.SORT_NO
    </select>

    <select id="getTaskLoadingWaypointListByTaskIdAndWaypointId" resultMap="BoTaskWaypointMap">
        SELECT T.*
        FROM T_BO_TASK_WAYPOINT T
        WHERE T.IS_DEL = 0
        AND T.WAYPOINT_TYPE IN (1, 3)
        AND T.WAYPOINT_ID = #{waypointId}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY T.SORT_NO
    </select>

    <select id="getTaskLoadingWaypointListByTaskId" resultMap="BoTaskWaypointMap">
        SELECT T.*
        FROM T_BO_TASK_WAYPOINT T
        WHERE T.IS_DEL = 0
        AND T.WAYPOINT_TYPE IN (1, 3)
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY T.SORT_NO
    </select>

    <select id="getTaskUnloadingWaypointListByTaskIdAndAddress" resultMap="BoTaskWaypointMap">
        SELECT T.*
        FROM T_BO_TASK_WAYPOINT T
        WHERE T.IS_DEL = 0
        AND T.WAYPOINT_TYPE IN (2, 3)
        AND T.PROVINCE_NAME || T.CITY_NAME || T.COUNTY_NAME || T.ADDRESS = #{address}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY T.SORT_NO
    </select>

    <select id="getTaskUnloadingWaypointListByTaskIdAndWaypointId" resultMap="BoTaskWaypointMap">
        SELECT T.*
        FROM T_BO_TASK_WAYPOINT T
        WHERE T.IS_DEL = 0
        AND T.WAYPOINT_TYPE IN (2, 3)
        AND T.WAYPOINT_ID = #{waypointId}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY T.SORT_NO
    </select>

    <select id="getTaskUnloadingWaypointListByTaskId" resultMap="BoTaskWaypointMap">
        SELECT T.*
        FROM T_BO_TASK_WAYPOINT T
        WHERE T.IS_DEL = 0
        AND T.WAYPOINT_TYPE IN (2, 3)
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY T.SORT_NO
    </select>

    <update id="updateArriveTime">
        UPDATE T_BO_TASK_WAYPOINT T
        SET T.ARRIVE_TIME = TO_DATE(#{arriveTime}, 'yyyy-mm-dd hh24:mi:ss'),
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.ARRIVE_TIME IS NULL
        AND T.BO_TASK_WAYPOINT_ID = #{boTaskWaypointId}
    </update>

    <update id="updateVoucherUploadTime">
        UPDATE T_BO_TASK_WAYPOINT T
        SET T.VOUCHER_UPLOAD_TIME = TO_DATE(#{voucherUploadTime}, 'yyyy-mm-dd hh24:mi:ss'),
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.VOUCHER_UPLOAD_TIME IS NULL
        AND T.BO_TASK_WAYPOINT_ID = #{boTaskWaypointId}
    </update>

    <update id="clearWaypointTimes">
        UPDATE T_BO_TASK_WAYPOINT T
        SET T.ARRIVE_TIME = NULL,
            T.VOUCHER_UPLOAD_TIME = NULL,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND (T.ARRIVE_TIME IS NOT NULL OR T.VOUCHER_UPLOAD_TIME IS NOT NULL)
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

</mapper>