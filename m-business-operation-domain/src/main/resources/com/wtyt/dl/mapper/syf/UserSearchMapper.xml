<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.UserSearchMapper">

    <delete id="deleteSelectedSearch">
        DELETE FROM T_DL_USER_SEARCH T
        WHERE T.BUSINESS_TYPE = #{businessType}
            AND T.PAGE = #{page}
            AND T.UNIQUE_KEY = #{uniqueKey}
    </delete>
    
    <insert id="batchInsertSelectedSearch">
        INSERT INTO T_DL_USER_SEARCH (
            DL_USER_SEARCH_ID,
            BUSINESS_TYPE,
            PAGE,
            ORG_ID,
            USER_ID,
            UNIQUE_KEY,
            DL_SEARCH_ID,
            SORT_NO
        )
        <foreach collection="list" index="index" item="item" separator="UNION ALL">
            SELECT
                #{item.dlUserSearchId} DL_USER_SEARCH_ID,
                #{item.businessType} BUSINESS_TYPE,
                #{item.page} PAGE,
                #{item.orgId} ORG_ID,
                #{item.userId} USER_ID,
                #{item.uniqueKey} UNIQUE_KEY,
                #{item.dlSearchId} DL_SEARCH_ID,
                #{item.sortNo} SORT_NO
            FROM DUAL
        </foreach>
    </insert>

</mapper>