<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskCommonRelatedMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTaskCommonRelatedBean" id="BoTaskCommonRelatedMap">
        <result property="boTaskCommonRelatedId" column="BO_TASK_COMMON_RELATED_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="paymentTermType" column="PAYMENT_TERM_TYPE" jdbcType="VARCHAR"/>
        <result property="paymentTermCycle" column="PAYMENT_TERM_CYCLE" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="saveOrUpdatePaymentTerm">
        MERGE INTO T_BO_TASK_COMMON_RELATED A
        USING (
            SELECT #{boTransTaskId} BO_TRANS_TASK_ID,#{paymentTermType} PAYMENT_TERM_TYPE,#{paymentTermCycle} PAYMENT_TERM_CYCLE FROM DUAL
        ) B
        ON (A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID AND A.IS_DEL = 0)
        WHEN MATCHED THEN
        UPDATE SET
            A.PAYMENT_TERM_TYPE = B.PAYMENT_TERM_TYPE,
            A.PAYMENT_TERM_CYCLE = B.PAYMENT_TERM_CYCLE,
            A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
        INSERT
            (BO_TASK_COMMON_RELATED_ID,
            BO_TRANS_TASK_ID,
            PAYMENT_TERM_TYPE,
            PAYMENT_TERM_CYCLE)
        VALUES
            (
                ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},
                B.BO_TRANS_TASK_ID,
                B.PAYMENT_TERM_TYPE,
                B.PAYMENT_TERM_CYCLE
            )
    </insert>

    <update id="clearPaymentTerm">
        UPDATE T_BO_TASK_COMMON_RELATED SET PAYMENT_TERM_TYPE=NULL,PAYMENT_TERM_CYCLE =NULL ,LAST_MODIFIED_TIME =SYSDATE WHERE BO_TRANS_TASK_ID =#{boTransTaskId} AND IS_DEL =0
    </update>

    <select id="queryByTaskIds" resultMap="BoTaskCommonRelatedMap">
        SELECT
        BO_TRANS_TASK_ID,
        PAYMENT_TERM_TYPE,
        PAYMENT_TERM_CYCLE
        FROM
        T_BO_TASK_COMMON_RELATED
        WHERE
        BO_TRANS_TASK_ID IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND IS_DEL = 0
    </select>

    <select id="queryNeedInitialPayment" resultType="com.wtyt.tt.bean.BoTaskInitialPaymentTermBean">
        SELECT
        A.BO_TRANS_TASK_ID boTransTaskId,
        B.CONTENT
        FROM
        T_BO_TRANS_TASK A
        JOIN T_BO_TRANS_REQUIRE_DETAIL B ON
        A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TASK_COMMON_RELATED C ON
        C.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID
        AND C.IS_DEL = 0
        WHERE
        A.ORG_ID IN (
        SELECT
        DISTINCT ORG_ID
        FROM
        T_TAX_OPERATION_REQ_CONFIG
        WHERE
        IS_DEL = 0
        AND OPERATION_TYPE = 11
        AND OPERATION_CONTENT LIKE '%"configType":"1"%')
        AND A.IS_DEL = 0
        AND B.IS_DEL = 0
        AND B.TYPE = 11
        AND B.CONTENT LIKE '%"configType":"1"%'
        AND C.PAYMENT_TERM_TYPE IS NULL
    </select>


</mapper>

