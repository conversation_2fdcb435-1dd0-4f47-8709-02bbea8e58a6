package com.wtyt.reviews.service.impl;

import com.wtyt.bo.bean.BoTaskDetailBean;
import com.wtyt.bo.mapper.syf.BoTaskDetailMapper;
import com.wtyt.reviews.service.ReviewsSubjectExtendService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年03月15日 18时46分
 */
@Service
public class TransTaskExtendServiceImpl implements ReviewsSubjectExtendService {

    private BoTaskDetailMapper boTaskDetailMapper;

    @Override
    public Map<String, ?> getSubjectExtend(String subjectId) {
        if (StringUtils.isNotBlank(subjectId)) {
            Map<String, Map<String, ?>> subjectExtendMap = this.getSubjectExtend(Collections.singletonList(subjectId));
            if (MapUtils.isNotEmpty(subjectExtendMap)) {
                return subjectExtendMap.get(subjectId);
            }
        }
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Map<String, ?>> getSubjectExtend(List<String> subjectIdList) {
        if (CollectionUtils.isNotEmpty(subjectIdList)) {
            subjectIdList = subjectIdList.stream()
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, Map<String, ?>> subjectExtendMap = new HashMap<>(subjectIdList.size());
            List<BoTaskDetailBean> taskDetailList = this.boTaskDetailMapper.getAllTaskDetailByTransTaskIdList(subjectIdList);
            List<String> orgIdList = taskDetailList.stream()
                    .map(BoTaskDetailBean::getOrgId)
                    .collect(Collectors.toList());
            Map<String, String> orgNameMap = this.getOrgName(orgIdList);
            for (BoTaskDetailBean taskDetail : taskDetailList) {
                String orgId = taskDetail.getOrgId();
                Map<String, Object> extendMap = new HashMap<>(16);
                extendMap.put("orgId", orgId);
                extendMap.put("orgName", orgNameMap.get(orgId));
                extendMap.put("startCityName", taskDetail.getStartCityName());
                extendMap.put("startCountyName", taskDetail.getStartCountyName());
                extendMap.put("endCityName", taskDetail.getEndCityName());
                extendMap.put("endCountyName", taskDetail.getEndCountyName());
                extendMap.put("goodsName", taskDetail.getGoodsName());
                extendMap.put("goodsAmount", taskDetail.getGoodsAmount());
                extendMap.put("goodsAmountType", taskDetail.getGoodsAmountType());
                extendMap.put("hybReceivedTime", taskDetail.getHybReceivedTime());
                subjectExtendMap.put(taskDetail.getBoTransTaskId(), extendMap);
            }
            return subjectExtendMap;
        }
        return Collections.emptyMap();
    }

    @Autowired
    public void setBoTaskDetailMapper(BoTaskDetailMapper boTaskDetailMapper) {
        this.boTaskDetailMapper = boTaskDetailMapper;
    }
}
