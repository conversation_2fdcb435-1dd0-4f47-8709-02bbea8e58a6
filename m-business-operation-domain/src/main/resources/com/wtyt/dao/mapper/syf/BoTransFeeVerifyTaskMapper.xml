<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransFeeVerifyTaskMapper">

    <select id="selectVerifyTask" resultType="com.wtyt.dao.bean.syf.BoTransFeeVerifyTaskBean">
        SELECT
            BO_TRANS_FEE_VERIFY_TASK_ID boTransFeeVerifyTaskId
        FROM T_BO_TRANS_FEE_VERIFY_TASK
        WHERE IS_DEL = 0
        AND ORG_ID = #{orgId}
        <if test="wbItem != null and wbItem != ''">
            AND WB_ITEM = #{wbItem}
        </if>
        <if test="wbItem == null or wbItem == ''">
            AND WB_ITEM IS NULL
        </if>
        <if test="cartBadgeNo != null and cartBadgeNo != ''">
            AND CART_BADGE_NO = #{cartBadgeNo}
        </if>
        <if test="cartBadgeNo == null or cartBadgeNo == ''">
            AND CART_BADGE_NO IS NULL
        </if>
        <if test="verifyTaskType != null and verifyTaskType != ''">
            AND VERIFY_TASK_TYPE = #{verifyTaskType}
        </if>
    </select>

    <insert id="insertVerifyTask">
        INSERT INTO T_BO_TRANS_FEE_VERIFY_TASK (BO_TRANS_FEE_VERIFY_TASK_ID, ORG_ID, WB_ITEM, CART_BADGE_NO,
        VERIFY_TASK_TYPE, BO_TRANS_TASK_ID, CREATED_USER_ID, CREATED_USER_NAME
        <if test="createdTime != null and createdTime != ''">
            , CREATED_TIME
        </if>
        )
        VALUES (#{boTransFeeVerifyTaskId}, #{orgId}, #{wbItem,javaType=string}, #{cartBadgeNo,javaType=string},
        #{verifyTaskType}, #{boTransTaskId,javaType=string,jdbcType=NUMERIC}, #{createdUserId}, #{createdUserName}
        <if test="createdTime != null and createdTime != ''">
            , to_date(#{createdTime}, 'yyyy-MM-dd HH24:mi:ss')
        </if>
        )
    </insert>

    <update id="updateVerifyTaskCreateUserAndTime">
        UPDATE T_BO_TRANS_FEE_VERIFY_TASK SET CREATED_USER_ID = #{createdUserId}, CREATED_USER_NAME = #{createdUserName}, CREATED_TIME = sysdate WHERE BO_TRANS_FEE_VERIFY_TASK_ID = #{boTransFeeVerifyTaskId}
    </update>

    <select id="selectVerifyTaskById" resultType="com.wtyt.dao.bean.syf.BoTransFeeVerifyTaskBean">
        SELECT
        BO_TRANS_FEE_VERIFY_TASK_ID boTransFeeVerifyTaskId,
        BO_TRANS_TASK_ID boTransTaskId
        FROM T_BO_TRANS_FEE_VERIFY_TASK
        WHERE IS_DEL = 0
        AND BO_TRANS_FEE_VERIFY_TASK_ID = #{boTransFeeVerifyTaskId}
    </select>

    <select id="selectVerifyTaskByTransTaskId" resultType="com.wtyt.dao.bean.syf.BoTransFeeVerifyTaskBean">
        SELECT
            BO_TRANS_FEE_VERIFY_TASK_ID boTransFeeVerifyTaskId,
            BO_TRANS_TASK_ID boTransTaskId
        FROM T_BO_TRANS_FEE_VERIFY_TASK
        WHERE IS_DEL = 0
          AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>
</mapper>