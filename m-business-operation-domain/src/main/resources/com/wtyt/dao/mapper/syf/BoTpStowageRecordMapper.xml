<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTpStowageRecordMapper">

    <insert id="proNewInsert">
	INSERT
		INTO
		T_BO_TP_STOWAGE_RECORD (
		BO_TP_STOWAGE_RECORD_ID,
		ORG_ID,
		USER_ID,
		STOWAGE_TYPE,
		PLAN_COUNT,
		ORDER_COUNT,
		COMPLETE_ORDER_COUNT,
		REMAIN_ORDER_COUNT,
		CART_COUNT,
		AUTO_CREATE_TASK_FLAG,
		STATE,
		EXCEPTION_INFO,
		IS_DEL,
		CREATED_TIME,
		LAST_MODIFIED_TIME,
		NOTE)
	VALUES (
		#{boTpStowageRecordId},
		#{orgId},
		#{userId},
		#{stowageType},
		#{planCount},
		#{orderCount},
		#{completeOrderCount},
		#{remainOrderCount},
		#{cartCount},
		#{autoCreateTaskFlag},
		#{state},
		#{exceptionInfo},
		0,
		SYSDATE,
		SYSDATE,
		#{note})
    </insert>

	<insert id="insert">
	INSERT
		INTO
		T_BO_TP_STOWAGE_RECORD (
		BO_TP_STOWAGE_RECORD_ID,
		ORG_ID,
		USER_ID,
		STOWAGE_TYPE,
		PLAN_COUNT,
		ORDER_COUNT,
		COMPLETE_ORDER_COUNT,
		REMAIN_ORDER_COUNT,
		CART_COUNT,
		AUTO_CREATE_TASK_FLAG,
		STATE,
		EXCEPTION_INFO,
		IS_DEL,
		CREATED_TIME,
		LAST_MODIFIED_TIME,
		NOTE)
	VALUES (
		#{boTpStowageRecordId},
		#{orgId},
		#{userId},
		#{stowageType},
		#{planCount},
		#{orderCount},
		#{completeOrderCount},
		#{remainOrderCount},
		#{cartCount},
		#{autoCreateTaskFlag},
		#{state},
		#{exceptionInfo},
		0,
		SYSDATE,
		SYSDATE,
		#{note})
    </insert>

	<update id="update">
		UPDATE
		T_BO_TP_STOWAGE_RECORD
		SET
		<if test="completeOrderCount != null and completeOrderCount != ''">
			COMPLETE_ORDER_COUNT = #{completeOrderCount},
		</if>
		<if test="remainOrderCount != null and remainOrderCount != ''">
			REMAIN_ORDER_COUNT = #{remainOrderCount},
		</if>
		<if test="cartCount != null and cartCount != ''">
			CART_COUNT = #{cartCount},
		</if>
		<if test="autoCreateTaskFlag != null and autoCreateTaskFlag != ''">
			AUTO_CREATE_TASK_FLAG = #{autoCreateTaskFlag},
		</if>
		<if test="state != null and state != ''">
			STATE = #{state},
		</if>
		<if test="exceptionInfo != null and exceptionInfo != ''">
			EXCEPTION_INFO = #{exceptionInfo},
		</if>
		LAST_MODIFIED_TIME = SYSDATE
		WHERE
		BO_TP_STOWAGE_RECORD_ID = #{boTpStowageRecordId}
	</update>

	<update id="delByPkIdList">
		UPDATE
		T_BO_TP_STOWAGE_RECORD
		SET
		IS_DEL = 1,
		LAST_MODIFIED_TIME = SYSDATE
		WHERE
		BO_TP_STOWAGE_RECORD_ID IN
		<foreach collection="list" item="item" close=")" open="(" separator=",">
		#{item}
	</foreach>
	</update>

	<select id="selectByPkId" resultType="com.wtyt.dao.bean.syf.BoTpStowageRecordBean">
		SELECT
			BO_TP_STOWAGE_RECORD_ID boTpStowageRecordId,
			ORG_ID orgId,
			USER_ID userId,
			STOWAGE_TYPE stowageType,
			PLAN_COUNT planCount,
			ORDER_COUNT orderCount,
			COMPLETE_ORDER_COUNT completeOrderCount,
			REMAIN_ORDER_COUNT remainOrderCount,
			CART_COUNT cartCount,
			AUTO_CREATE_TASK_FLAG autoCreateTaskFlag,
			STATE state,
			EXCEPTION_INFO exceptionInfo
		FROM
			T_BO_TP_STOWAGE_RECORD
			WHERE IS_DEL = 0
			AND BO_TP_STOWAGE_RECORD_ID = #{pkId}
	</select>

	<select id="selectByPkIdList" resultType="com.wtyt.dao.bean.syf.BoTpStowageRecordBean">
		SELECT
			BO_TP_STOWAGE_RECORD_ID boTpStowageRecordId,
			ORG_ID orgId,
			USER_ID userId,
			STOWAGE_TYPE stowageType,
			PLAN_COUNT planCount,
			ORDER_COUNT orderCount,
			COMPLETE_ORDER_COUNT completeOrderCount,
			REMAIN_ORDER_COUNT remainOrderCount,
			CART_COUNT cartCount,
			AUTO_CREATE_TASK_FLAG autoCreateTaskFlag,
			STATE state,
			EXCEPTION_INFO exceptionInfo
		FROM
			T_BO_TP_STOWAGE_RECORD
		WHERE IS_DEL = 0
		  AND BO_TP_STOWAGE_RECORD_ID IN
		<foreach collection="list" item="item" close=")" open="(" separator=",">
		#{item}
		</foreach>
	</select>

</mapper>