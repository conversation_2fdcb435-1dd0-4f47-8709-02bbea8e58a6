<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoWaybillBossNodeMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoWaybillBossNodeBean" id="baseResultMap">
        <result property="boWaybillBossNodeId" column="BO_WAYBILL_BOSS_NODE_ID" jdbcType="VARCHAR"/>
        <result property="bossNodeCode" column="BOSS_NODE_CODE" jdbcType="VARCHAR"/>
        <result property="bossWaybillHasException" column="BOSS_WAYBILL_HAS_EXCEPTION" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="taxWaybillId" column="TAX_WAYBILL_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <update id="batchUpdateWaybillException">
        UPDATE T_BO_WAYBILL_BOSS_NODE T
            SET T.BOSS_WAYBILL_HAS_EXCEPTION =
                CASE
                    WHEN (
                        SELECT COUNT(1)
                        FROM T_BO_TRANS_NODE_ALARM A,
                             T_BO_TRANS_TASK B
                        WHERE A.IS_DEL = 0
                        AND A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID
                        AND A.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
                        AND A.ALARM_PROCESS_RESULT IN (0, 3)
                        AND (A.NODE_ID, A.ALARM_TYPE) NOT IN (('200', '0'))
                        AND (A.ALARM_TYPE IN (1, 2) OR (A.ALARM_TYPE = 0 AND A.NODE_DATA_TYPE = 4))
                        AND NOT EXISTS (
                            SELECT 1
                            FROM T_BO_BOSS_NODE_ALARM_IGNOR BNAI
                            WHERE BNAI.IS_DEL = 0
                            AND BNAI.ORG_ID = B.ORG_ID
                            AND BNAI.NODE_DATA_TYPE = A.NODE_DATA_TYPE
                        )
                    ) > 0 THEN 1
                    ELSE 0
                END,
                T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateBossNode">
        UPDATE T_BO_WAYBILL_BOSS_NODE
        SET BOSS_NODE_CODE = #{bossNodeCode}, LAST_MODIFIED_TIME = SYSDATE
        WHERE
        BO_TRANS_TASK_ID = #{boTransTaskId}
        <![CDATA[AND BOSS_NODE_CODE<#{bossNodeCode}]]>
    </update>

    <update id="updateBossNodeByBoTransTaskId">
        UPDATE T_BO_WAYBILL_BOSS_NODE
        SET BOSS_NODE_CODE = #{bossNodeCode}, LAST_MODIFIED_TIME = SYSDATE
        WHERE
        BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>
    <select id="queryBossNodeCodeByBoTransTaskId" resultType="java.lang.String"
            parameterType="java.lang.String">
        select BOSS_NODE_CODE from T_BO_WAYBILL_BOSS_NODE where
        BO_TRANS_TASK_ID = #{boTransTaskId}
        AND IS_DEL = 0
    </select>

    <select id="queryBossNodeCodeByTransTaskId" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT
            T.BOSS_NODE_CODE
        FROM T_BO_WAYBILL_BOSS_NODE T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>

    <insert id="insert" parameterType="BoWaybillBossNodeBean">
        INSERT INTO T_BO_WAYBILL_BOSS_NODE(
            BO_WAYBILL_BOSS_NODE_ID,
            TAX_WAYBILL_ID,
            BO_TRANS_TASK_ID,
            BOSS_NODE_CODE,
            BOSS_WAYBILL_HAS_EXCEPTION,
            IS_DEL,
            CREATED_TIME,
            LAST_MODIFIED_TIME)
        VALUES (
            #{boWaybillBossNodeId},
            #{taxWaybillId},
            #{boTransTaskId},
            #{bossNodeCode},
            0,
            0,
            SYSDATE,
            SYSDATE
            )
    </insert>

</mapper>
