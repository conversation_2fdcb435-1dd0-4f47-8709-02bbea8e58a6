package com.wtyt.feeitem.controller;

import com.wtyt.common.annotation.ExcludeAppComponent;
import com.wtyt.common.annotation.ExcludeJobComponent;
import com.wtyt.commons.annotation.Logger;
import com.wtyt.commons.bean.BaseValidBean;
import com.wtyt.feeitem.bean.request.Req5330293IBean;
import com.wtyt.feeitem.bean.response.Resp5330293OBean;
import com.wtyt.feeitem.service.TaskFeeItemService;
import com.wtyt.money.commons.bean.ResDataBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 费用明细清单
 */
@RestController
@ExcludeAppComponent
@ExcludeJobComponent
@RequestMapping(value = "/fee/item/")
public class TaskFeeItemController {

    @Autowired
    private TaskFeeItemService taskFeeItemService;


    /**
     * 5330293-运输任务-查询运费费用明细清单
     *
     * @param bean
     * @return
     * @throws Exception
     */
    @PostMapping(value = "queryTaskFeeItemDetail")
    @Logger("5330293-运输任务-查询运费费用明细清单")
    public ResDataBean<Resp5330293OBean> queryTaskFeeItemDetail(@RequestBody BaseValidBean<Req5330293IBean> bean) throws Exception {
        Resp5330293OBean result = this.taskFeeItemService.queryTaskFeeItemDetail(bean.getData());
        return new ResDataBean<Resp5330293OBean>().success(result);
    }
}
