<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransDataRecordMapper">


    <select id="getTransRecordByWaybillId" resultType="com.wtyt.dao.bean.syf.BoTransDataRecordBean">
       SELECT M.* from(
 		select
        BO_TRANS_DATA_RECORD_ID boTransDataRecordId,
        TAX_WAYBILL_ID taxWaybillId,
        USER_ID userId,
        ORG_ID orgId,
        RECORD_TYPE recordType,
        FROM_SOURCE fromSource,
        BO_TRANS_PLAN_ID boTransPlanId,
        TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
         ROW_NUMBER() OVER(PARTITION BY BO_TRANS_PLAN_ID ORDER BY BO_TRANS_ORDER_ID DESC) RN
        from T_BO_TRANS_DATA_RECORD
        where TAX_WAYBILL_ID = #{taxWaybillId}
        and RECORD_TYPE = 1
        order by CREATED_TIME)M
        WHERE M.RN = 1
    </select>

	<select id="getTransRecordByOrderIds" resultType="com.wtyt.dao.bean.syf.BoTransDataRecordBean">
		 SELECT M.* from(
		 	  select
				BO_TRANS_DATA_RECORD_ID boTransDataRecordId,
				TAX_WAYBILL_ID taxWaybillId,
				USER_ID userId,
				ORG_ID orgId,
				RECORD_TYPE recordType,
				FROM_SOURCE fromSource,
				BO_TRANS_PLAN_ID boTransPlanId,
				TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
				ROW_NUMBER() OVER(PARTITION BY BO_TRANS_PLAN_ID ORDER BY BO_TRANS_ORDER_ID) RN
			   from T_BO_TRANS_DATA_RECORD
			   where BO_TRANS_ORDER_ID IN
				<foreach collection="orderIds" open="(" close=")" separator="," item="item">
					#{item}
				</foreach>
				AND RECORD_TYPE = 1
			   order by CREATED_TIME)M
			   WHERE M.RN = 1
	</select>

	<select id="getTransDeleteRecordByOrderIds" resultType="BoTransDataRecordBean">
		select
		BO_TRANS_DATA_RECORD_ID boTransDataRecordId,
		TAX_WAYBILL_ID taxWaybillId,
		USER_ID userId,
		ORG_ID orgId,
		RECORD_TYPE recordType,
		FROM_SOURCE fromSource,
		INFO info,
		TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
		from T_BO_TRANS_DATA_RECORD
		where BO_TRANS_ORDER_ID IN
		<foreach collection="orderIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		AND RECORD_TYPE = 3
		order by CREATED_TIME
	</select>

	<select id="getModifyRecordRecordByOrderIds" resultType="BoTransDataRecordBean">
		select
		BO_TRANS_DATA_RECORD_ID boTransDataRecordId,
		TAX_WAYBILL_ID taxWaybillId,
		USER_ID userId,
		ORG_ID orgId,
		RECORD_TYPE recordType,
		FROM_SOURCE fromSource,
		BO_TRANS_PLAN_ID boTransPlanId,
		INFO info,
		TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
		from T_BO_TRANS_DATA_RECORD
		where BO_TRANS_ORDER_ID IN
		<foreach collection="orderIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		AND RECORD_TYPE = 2
		order by CREATED_TIME
	</select>

	<select id="showRecode" resultType="int">
		select
		count(1)
		from T_BO_TRANS_DATA_RECORD
		where BO_TRANS_ORDER_ID IN
		<foreach collection="orderIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		AND IS_DEL = 0
	</select>

	<select id="getRecordByOrderIdsAndType" resultType="BoTransDataRecordBean">
		select
		BO_TRANS_DATA_RECORD_ID boTransDataRecordId,
		USER_ID userId,
		ORG_ID orgId,
		RECORD_TYPE recordType,
		FROM_SOURCE fromSource,
		BO_TRANS_PLAN_ID boTransPlanId,
		INFO info,
		TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
		from T_BO_TRANS_DATA_RECORD
		where BO_TRANS_ORDER_ID IN
		<foreach collection="orderIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		AND RECORD_TYPE IN
		<foreach collection="typeList" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		order by CREATED_TIME
	</select>

	<select id="getDistributeRecordByOrderIds" resultType="BoTransDataRecordBean">
		select
		BO_TRANS_DATA_RECORD_ID boTransDataRecordId,
		USER_ID userId,
		ORG_ID orgId,
		RECORD_TYPE recordType,
		FROM_SOURCE fromSource,
		BO_TRANS_PLAN_ID boTransPlanId,
		INFO info,
		TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
		from T_BO_TRANS_DATA_RECORD
		where BO_TRANS_ORDER_ID IN
		<foreach collection="orderIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		AND RECORD_TYPE = 4
		order by CREATED_TIME
	</select>

	<select id="getPzRecordByOrderIds" resultType="BoTransDataRecordBean">
		select
		BO_TRANS_DATA_RECORD_ID boTransDataRecordId,
		USER_ID userId,
		ORG_ID orgId,
		RECORD_TYPE recordType,
		FROM_SOURCE fromSource,
		BO_TRANS_PLAN_ID boTransPlanId,
		INFO info,
		TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
		from T_BO_TRANS_DATA_RECORD
		where BO_TRANS_ORDER_ID IN
		<foreach collection="orderIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		AND RECORD_TYPE in(12,13)
		order by CREATED_TIME
	</select>

	<select id="getZfRecordByOrderIds" resultType="BoTransDataRecordBean">
		select
		BO_TRANS_DATA_RECORD_ID boTransDataRecordId,
		USER_ID userId,
		ORG_ID orgId,
		RECORD_TYPE recordType,
		FROM_SOURCE fromSource,
		BO_TRANS_PLAN_ID boTransPlanId,
		INFO info,
		TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
		from T_BO_TRANS_DATA_RECORD
		where BO_TRANS_ORDER_ID IN
		<foreach collection="orderIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		AND RECORD_TYPE = 16
		order by CREATED_TIME
	</select>

	<select id="getDistributeUserIdByOrderIds" resultType="java.lang.String">
			SELECT * FROM(
				SELECT
					USER_ID userId
				FROM
					T_BO_TRANS_DATA_RECORD
				WHERE
					BO_TRANS_ORDER_ID IN
					<foreach collection="orderIds" open="(" close=")" separator="," item="item">
						#{item}
				   </foreach>
					AND RECORD_TYPE = 4 AND  IS_DEL=0
				ORDER BY
					CREATED_TIME DESC)
			WHERE	ROWNUM = 1
	</select>

	<select id="getLotteryRecordByOrderIds" resultType="BoTransDataRecordBean">
		select
		BO_TRANS_DATA_RECORD_ID boTransDataRecordId,
		USER_ID userId,
		ORG_ID orgId,
		REAL_NAME realName,
		RECORD_TYPE recordType,
		FROM_SOURCE fromSource,
		BO_TRANS_PLAN_ID boTransPlanId,
		INFO info,
		TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime
		from T_BO_TRANS_DATA_RECORD
		where BO_TRANS_ORDER_ID IN
		<foreach collection="orderIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		AND RECORD_TYPE = 17
		order by CREATED_TIME desc
	</select>


	<insert id="insert">
	INSERT
		INTO
		T_BO_TRANS_DATA_RECORD (BO_TRANS_DATA_RECORD_ID,
		BO_TRANS_ORDER_ID,
		TAX_WAYBILL_ID,
		USER_ID,
		ORG_ID,
		REAL_NAME,
		RECORD_TYPE,
		BO_TRANS_PLAN_ID,
		FROM_SOURCE,
		INFO,
		IS_DEL,
		CREATED_TIME,
		LAST_MODIFIED_TIME,
		NOTE)
	VALUES (#{boTransDataRecordId} ,
	#{boTransOrderId},
	#{taxWaybillId} ,
	#{userId} ,
	#{orgId} ,
	#{realName} ,
	#{recordType} ,
	#{boTransPlanId} ,
	#{fromSource} ,
	#{info} ,
	0,
	SYSDATE,
	SYSDATE,
	NULL)
	</insert>


	<insert id="batchInsert">
	INSERT
		INTO
		T_BO_TRANS_DATA_RECORD (BO_TRANS_DATA_RECORD_ID,
		BO_TRANS_ORDER_ID,
		TAX_WAYBILL_ID,
		USER_ID,
		ORG_ID,
		REAL_NAME,
		RECORD_TYPE,
		BO_TRANS_PLAN_ID,
		FROM_SOURCE,
		INFO,
		IS_DEL,
		CREATED_TIME,
		LAST_MODIFIED_TIME,
		NOTE)
		SELECT
		A.*
		FROM (
		<foreach item="item" index="index" collection="list" separator="UNION ALL">
			SELECT #{item.boTransDataRecordId} boTransDataRecordId,
			#{item.boTransOrderId} boTransOrderId,
			#{item.taxWaybillId} taxWaybillId,
			#{item.userId} userId,
			#{item.orgId} orgId,
			#{item.realName} realName,
			#{item.recordType} recordType,
			#{item.boTransPlanId} boTransPlanId,
			#{item.fromSource} fromSource,
			#{item.info} info,
			0 isDel,
			SYSDATE createdTime,
			SYSDATE lastModifiedTime,
			NULL note
			FROM DUAL
		</foreach>
		) A
	</insert>
</mapper>