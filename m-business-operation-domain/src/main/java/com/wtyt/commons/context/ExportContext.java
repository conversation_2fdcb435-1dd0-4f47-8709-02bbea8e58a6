package com.wtyt.commons.context;

import com.wtyt.bo.bean.BoTaskExportBean;
import com.wtyt.common.rpc.bean.*;
import com.wtyt.commons.bean.ReqExportBean;
import com.wtyt.commons.bean.export.User;
import com.wtyt.commons.bean.export.UserGroup;
import com.wtyt.dao.bean.syf.BoShippingListBean;
import com.wtyt.dao.bean.syf.BoTaskCusFieldBean;
import com.wtyt.dao.bean.syf.BoTransTaskFeeBean;
import com.wtyt.dao.bean.syf.BoTransTaskGroupRelBean;
import com.wtyt.settle.bean.BoTaskVerifyBean;
import com.wtyt.tt.bean.LocationBean;
import lombok.Getter;
import lombok.Setter;

import java.util.*;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2025年07月09日 14时25分
 */
@Setter
@Getter
public class ExportContext {

    private ReqExportBean data;
    private List<?> exportList;

    private volatile List<String> transTaskIdList;
    private Map<String, User> xcyMap;
    private Map<String, String> orgSceneIdMap;

    private Map<String, Rpc5214029OBean.Rpc5214029OSubBean> driverAuthMap;
    private UserGroup userGroup;
    private Map<String, Rpc5351013OBean> transTaskInsure;
    private Map<String, LocationBean> transTaskCurrentLocation;
    private Map<String, BoTaskVerifyBean> transTaskVerify;
    private Map<String, Rpc5341007OBean.ProjectInfo> transTaskProjects;
    private Map<String, Rpc19110023OBean> transTaskVoucherMap;

    private Map<String, String> diffOrgNameMap;
    private Map<String, List<BoTransTaskFeeBean>> transTaskFeeMap;
    private Map<String, List<BoShippingListBean>> transTaskShippingListMap;
    private Map<String, List<BoTransTaskGroupRelBean>> transTaskGroupMap;
    private Map<String, Map<String, BoTaskCusFieldBean>> cusFieldMap;
    private Map<String, Set<String>> nextNodeIdMap;
    private Map<String, Set<String>> transTaskNodeWarnMap;

    private Map<String, Future<?>> futureMap;

    private List<String> exportExcelHeader;
    private String exportExcelPath;

    public void clear() {
        if (this.exportList != null) {
            this.exportList.clear();
            this.exportList = null;
        }
        if (this.transTaskIdList != null) {
            this.transTaskIdList.clear();
            this.transTaskIdList = null;
        }
        if (this.transTaskInsure != null) {
            this.transTaskInsure.clear();
            this.transTaskInsure = null;
        }
        if (this.transTaskCurrentLocation != null) {
            this.transTaskCurrentLocation.clear();
            this.transTaskCurrentLocation = null;
        }
        if (this.transTaskVerify != null) {
            this.transTaskVerify.clear();
            this.transTaskVerify = null;
        }
        if (this.transTaskVoucherMap != null) {
            this.transTaskVoucherMap.clear();
            this.transTaskVoucherMap = null;
        }
        if (this.transTaskFeeMap != null) {
            this.transTaskFeeMap.clear();
            this.transTaskFeeMap = null;
        }
        if (this.transTaskShippingListMap != null) {
            this.transTaskShippingListMap.clear();
            this.transTaskShippingListMap = null;
        }
        if (this.transTaskGroupMap != null) {
            this.transTaskGroupMap.clear();
            this.transTaskGroupMap = null;
        }
        if (this.cusFieldMap != null) {
            this.cusFieldMap.clear();
            this.cusFieldMap = null;
        }
        if (this.userGroup != null) {
            this.userGroup = null;
        }
        if (this.futureMap != null) {
            this.futureMap.clear();
            this.futureMap = null;
        }
    }

    public void initialize(int initialCapacity) {
        this.exportList = new ArrayList<>(initialCapacity);
        if (this.futureMap == null) {
            this.futureMap = new HashMap<>(16);
        }
    }

    public <T> void addExportItem(T item) {
        if (this.exportList == null) {
            throw new IllegalArgumentException("导出集合未进行初始化");
        }
        @SuppressWarnings("unchecked")
        List<T> list = (List<T>) this.exportList;
        list.add(item);
    }

    @SuppressWarnings("unchecked")
    public <T> List<T> getExportList(Class<T> clazz) {
        if (clazz == null) {
            throw new IllegalArgumentException("class类型不可为空");
        }
        return (List<T>) this.exportList;
    }
}
