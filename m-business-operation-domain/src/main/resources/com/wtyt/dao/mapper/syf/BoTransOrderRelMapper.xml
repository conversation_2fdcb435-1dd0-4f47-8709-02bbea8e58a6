<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransOrderRelMapper">

	<insert id="insert">
	INSERT INTO T_BO_TRANS_ORDER_REL (
		BO_TRANS_ORDER_REL_ID,
		USER_ID,
		ORG_ID,
		BO_TRANS_ORDER_ID,
		TAX_WAYBILL_ID,
		GOODS_CASE_PACK,
		<if test="goodsCasePackUnit != null and goodsCasePackUnit.length > 0">
			GOODS_CASE_PACK_UNIT,
		</if>
		GOODS_WEIGHT,
		<if test="goodsWeightUnit != null and goodsWeightUnit.length > 0">
			GOODS_WEIGHT_UNIT,
		</if>
		GOODS_VOLUME,
		<if test="goodsVolumeUnit != null and goodsVolumeUnit.length > 0">
			GOODS_VOLUME_UNIT,
		</if>
		BO_TRANS_TASK_ID,
		DISTRIBUTE_STATE,
		UP_USER_FREIGHT,
		SPILT_UNIT_FIELD,
		IS_DEL,
		CREATED_TIME,
		LAST_MODIFIED_TIME,
		NOTE
	) VALUES (
		#{boTransOrderRelId},
		#{userId},
		#{orgId},
		#{boTransOrderId},
		#{taxWaybillId},
		#{goodsCasePack},
		<if test="goodsCasePackUnit != null and goodsCasePackUnit.length > 0">
			UPPER(#{goodsCasePackUnit}),
		</if>
		#{goodsWeight},
		<if test="goodsWeightUnit != null and goodsWeightUnit.length > 0">
			UPPER(#{goodsWeightUnit}),
		</if>
		#{goodsVolume},
		<if test="goodsVolumeUnit != null and goodsVolumeUnit.length > 0">
			UPPER(#{goodsVolumeUnit}),
		</if>
		#{boTransTaskId},
		#{distributeState},
		#{upUserFreight},
		#{spiltUnitField},
		0,
		SYSDATE,
		SYSDATE,
		NULL
	)
	</insert>

	<insert id="batchInsert">
		INSERT
		INTO
		T_BO_TRANS_ORDER_REL (
		BO_TRANS_ORDER_REL_ID,
		USER_ID,
		ORG_ID,
		BO_TRANS_ORDER_ID,
		TAX_WAYBILL_ID,
		GOODS_CASE_PACK,
		GOODS_CASE_PACK_UNIT,
		GOODS_WEIGHT,
	    GOODS_WEIGHT_UNIT,
		GOODS_VOLUME,
		GOODS_VOLUME_UNIT,
		BO_TRANS_TASK_ID,
		DISTRIBUTE_STATE,
		UP_USER_FREIGHT,
		IS_DEL,
		CREATED_TIME,
		LAST_MODIFIED_TIME,
		NOTE)
		SELECT
		A.*
		FROM (
		<foreach item="item" index="index" collection="list" separator="UNION ALL">
			SELECT
			#{item.boTransOrderRelId} boTransOrderRelId,
			#{item.userId} userId,
			#{item.orgId} orgId,
			#{item.boTransOrderId} boTransOrderId,
			#{item.taxWaybillId} taxWaybillId,
			#{item.goodsCasePack} goodsCasePack,
			UPPER(#{item.goodsCasePackUnit}) goodsCasePackUnit,
			#{item.goodsWeight} goodsWeight,
			UPPER(#{item.goodsWeightUnit}) goodsWeightUnit,
	   	 	#{item.goodsVolume} goodsVolume,
	    	UPPER(#{item.goodsVolumeUnit}) goodsVolumeUnit,
			#{item.boTransTaskId} boTransTaskId,
			#{item.distributeState} distributeState,
			#{item.upUserFreight} upUserFreight,
			0 isDel,
			SYSDATE createdTime,
			SYSDATE lastModifiedTime,
			NULL note
			FROM DUAL
		</foreach>
		) A
	</insert>

	<update id="updateDistributeState">
		UPDATE T_BO_TRANS_ORDER_REL SET DISTRIBUTE_STATE = 1 ,LAST_MODIFIED_TIME = SYSDATE
		WHERE BO_TRANS_ORDER_REL_ID IN
		<foreach collection="list" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</update>

	<delete id="delByPkId">
		UPDATE T_BO_TRANS_ORDER_REL SET IS_DEL = 1 ,LAST_MODIFIED_TIME = SYSDATE
		WHERE BO_TRANS_ORDER_REL_ID IN
		<foreach collection="list" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</delete>

	<select id="selectByTaxWaybillNoList" resultType="com.wtyt.dao.bean.syf.BoTransOrderRelBean">
		SELECT
			T.TAX_WAYBILL_NO taxWaybillNo,
			O.BO_TRANS_ORDER_ID boTransOrderId,
			T.TAX_WAYBILL_ID taxWaybillId,
			R.BO_TRANS_ORDER_REL_ID boTransOrderRelId
		FROM
			T_BO_TRANS_ORDER_REL R
		INNER JOIN T_BO_TRANS_TASK T ON
			R.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
		INNER JOIN T_BO_TRANS_ORDER O ON O.BO_TRANS_ORDER_ID = R.BO_TRANS_ORDER_ID
		WHERE
			R.IS_DEL = 0
			AND T.IS_DEL = 0
			AND O.IS_DEL = 0
			AND R.ORG_ID = #{orgId}
			AND T.TAX_WAYBILL_NO IN
			<foreach collection="list" open="(" close=")" separator="," item="item">
				#{item}
			</foreach>
	</select>

	<select id="selectTaskHasMerge" resultType="com.wtyt.dao.bean.syf.BoTaskHasMergeOBean">
		SELECT
		TAX_WAYBILL_ID taxWaybillId,
		BO_TRANS_TASK_ID boTransTaskId,
		decode(COUNT(*), 1, 0, 1) mergeDispatch
		FROM
		(
		SELECT
		R.TAX_WAYBILL_ID,
		R.BO_TRANS_TASK_ID,
		O.BO_TRANS_ORDER_ID
		FROM
		T_BO_TRANS_ORDER_REL R
		INNER JOIN T_BO_TRANS_ORDER O ON
		O.BO_TRANS_ORDER_ID = R.BO_TRANS_ORDER_ID
		WHERE
		R.IS_DEL = 0
		AND O.IS_DEL = 0
		AND R.BO_TRANS_TASK_ID IN
		<foreach collection="boTransTaskIdList" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		)
		GROUP BY
		TAX_WAYBILL_ID,BO_TRANS_TASK_ID
	</select>

	<resultMap id="TfOrderMap" type="BOTFOrderInfoBean">
		<result column="taxWaybillId" property="taxWaybillId"/>
		<result column="boTransTaskId" property="boTransTaskId"/>
		<collection property="orderList" ofType="com.wtyt.dao.bean.syf.BOTFOrderInfoBean$OrderInfo">
			<result column="boTransOrderId" property="boTransOrderId"/>
			<result column="customerName" property="customerName"/>
			<result column="customerOrderNo" property="customerOrderNo"/>
			<result column="goodsCasePack" property="goodsCasePack"/>
		</collection>

	</resultMap>

	<select id="getTfOrderInfo" resultMap="TfOrderMap">
		SELECT
		R.TAX_WAYBILL_ID taxWaybillId,
		R.BO_TRANS_TASK_ID boTransTaskId,
		O.BO_TRANS_ORDER_ID boTransOrderId,
		O.CUSTOMER_ORDER_NO customerOrderNo,
		O.CUSTOMER_NAME customerName,
		TO_CHAR(R.GOODS_CASE_PACK,'FM999999990.0000') goodsCasePack
		FROM T_BO_TRANS_ORDER_REL R
		INNER JOIN T_BO_TRANS_ORDER O ON O.BO_TRANS_ORDER_ID = R.BO_TRANS_ORDER_ID
		WHERE R.IS_DEL = 0
		AND O.IS_DEL = 0
		AND R.BO_TRANS_TASK_ID IN
		<foreach collection="boTransTaskIdList" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</select>

    <select id="selectByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoTransOrderRelBean">
		SELECT
		R.BO_TRANS_ORDER_REL_ID boTransOrderRelId,
		R.ORG_ID orgId,
		R.USER_ID userId,
		R.BO_TRANS_ORDER_ID boTransOrderId,
		R.TAX_WAYBILL_ID taxWaybillId,
		R.BO_TRANS_TASK_ID boTransTaskId,
		TO_CHAR(R.GOODS_CASE_PACK,'FM999999990.0000') goodsCasePack,
		TO_CHAR(R.GOODS_WEIGHT,'FM999999990.0000') goodsWeight,
		TO_CHAR(R.GOODS_VOLUME,'FM999999990.0000') goodsVolume,
		T.TAX_WAYBILL_NO taxWaybillNo
		FROM
		T_BO_TRANS_ORDER_REL R
		LEFT JOIN T_BO_TRANS_ORDER O ON O.BO_TRANS_ORDER_ID = R.BO_TRANS_ORDER_ID
		LEFT JOIN T_BO_TRANS_TASK T ON T.BO_TRANS_TASK_ID = R.BO_TRANS_TASK_ID
		WHERE
		R.IS_DEL = 0
		AND O.IS_DEL = 0
		AND O.SPILT_FLAG = 0
		AND R.BO_TRANS_ORDER_ID IN
		<foreach collection="orderIdList" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</select>

	<select id="selectRelByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoTransOrderRelBean">
		SELECT
			R.BO_TRANS_ORDER_REL_ID boTransOrderRelId,
			R.ORG_ID orgId,
			R.USER_ID userId,
			R.BO_TRANS_ORDER_ID boTransOrderId,
			R.TAX_WAYBILL_ID taxWaybillId,
			R.BO_TRANS_TASK_ID boTransTaskId,
			TO_CHAR(R.GOODS_CASE_PACK, 'FM999999990.0000') goodsCasePack,
			TO_CHAR(R.GOODS_WEIGHT, 'FM999999990.0000') goodsWeight,
			TO_CHAR(R.GOODS_VOLUME, 'FM999999990.0000') goodsVolume
		FROM
			T_BO_TRANS_ORDER O
		INNER JOIN T_BO_TRANS_ORDER_REL R ON
			O.BO_TRANS_ORDER_ID = R.BO_TRANS_ORDER_ID
		WHERE
			R.IS_DEL = 0
			AND O.IS_DEL = 0
			AND R.BO_TRANS_TASK_ID = #{boTransTaskId}
			AND R.BO_TRANS_ORDER_ID IN
			<foreach collection="orderIdList" open="(" close=")" separator="," item="item">
				#{item}
			</foreach>
	</select>

	<select id="selectByBoTaskIds" resultType="com.wtyt.dao.bean.syf.BoTransOrderRelBean">
		SELECT
		R.BO_TRANS_ORDER_REL_ID boTransOrderRelId,
		R.ORG_ID orgId,
		R.USER_ID userId,
		R.BO_TRANS_ORDER_ID boTransOrderId,
		R.TAX_WAYBILL_ID taxWaybillId,
		TO_CHAR(R.GOODS_CASE_PACK,'FM999999990.0000') goodsCasePack,
		TO_CHAR(R.GOODS_WEIGHT,'FM999999990.0000') goodsWeight,
		TO_CHAR(R.GOODS_VOLUME,'FM999999990.0000') goodsVolume,
		T.TAX_WAYBILL_NO taxWaybillNo,
		R.DISTRIBUTE_STATE distributeState,
		R.CANCEL_DISPATCH_MARK cancelDispatchMark
		FROM
		T_BO_TRANS_ORDER_REL R LEFT JOIN T_BO_TRANS_TASK T ON T.BO_TRANS_TASK_ID = R.BO_TRANS_TASK_ID
		WHERE
		R.IS_DEL = 0
		AND R.BO_TRANS_TASK_ID IN
		<foreach collection="boTransTaskIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		order by R.BO_TRANS_ORDER_ID
	</select>

	<select id="selectRestoreOrderRel" resultType="com.wtyt.dao.bean.syf.BoTransOrderRelBean">
		SELECT
			a.BO_TRANS_ORDER_ID boTransOrderId,
			a.BO_TRANS_ORDER_REL_ID boTransOrderRelId
		FROM
			T_BO_TRANS_ORDER_REL a
		LEFT JOIN T_BO_TRANS_ORDER b ON
			a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID
		WHERE
			b.IS_DEL = 0
			<!-- 大宗的场景暂时不考虑 -->
			AND b.FROM_SOURCE != 4
			AND a.BO_TRANS_TASK_ID IN
			<foreach collection="boTransTaskIdList" open="(" close=")" separator="," item="item">
				#{item}
			</foreach>
	</select>

	<select id="selectBandingRelOrderIds" resultType="string">
		SELECT
			 a.BO_TRANS_ORDER_ID
		FROM
			T_BO_TRANS_ORDER_REL a
		WHERE
			a.IS_DEL = 0
			AND BO_TRANS_ORDER_ID IN
			<foreach collection="orderIdList" open="(" close=")" separator="," item="item">
				#{item}
			</foreach>
			AND BO_TRANS_TASK_ID IS NOT NULL
	</select>
	<select id="queryUpUserFreightList" resultType="java.lang.String">
		SELECT
			NVL(UP_USER_FREIGHT, 0)
		FROM
			T_BO_TRANS_ORDER_REL R
		WHERE
			R.IS_DEL = 0
			AND R.BO_TRANS_ORDER_ID = #{boTransOrderId}
	</select>
	<select id="querySplitUnitFieldName" resultType="java.lang.String">
		SELECT
			DISTINCT SPILT_UNIT_FIELD
		FROM
			T_BO_TRANS_ORDER_REL R
		WHERE
			R.IS_DEL = 0
			AND r.BO_TRANS_ORDER_ID IN
			<foreach collection="orderIdList" open="(" close=")" separator="," item="item">
				#{item}
			</foreach>
			AND r.SPILT_UNIT_FIELD IS NOT NULL
	</select>

	<update id="restoreOrderRel">
		UPDATE T_BO_TRANS_ORDER_REL SET IS_DEL = 0 ,LAST_MODIFIED_TIME = SYSDATE, NOTE = '恢复运输任务与订单关系'
		WHERE BO_TRANS_ORDER_REL_ID IN
		<foreach collection="boTransOrderRelIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</update>

	<update id="cancelDispatchMark">
		UPDATE T_BO_TRANS_ORDER_REL SET CANCEL_DISPATCH_MARK = #{cancelDispatchMark}, LAST_MODIFIED_TIME = SYSDATE
		WHERE IS_DEL = 0 and   BO_TRANS_ORDER_REL_ID IN
		<foreach collection="boTransOrderRelIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</update>

</mapper>
