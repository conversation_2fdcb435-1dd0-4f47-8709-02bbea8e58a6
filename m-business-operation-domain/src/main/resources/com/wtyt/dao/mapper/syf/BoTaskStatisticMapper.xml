<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskStatisticMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTaskStatisticBean" id="BoTaskStatisticMap">
        <result property="boTaskStatisticId" column="BO_TASK_STATISTIC_ID" jdbcType="VARCHAR"/>
        <result property="statisticDimension" column="STATISTIC_DIMENSION" jdbcType="VARCHAR"/>
        <result property="statisticItem" column="STATISTIC_ITEM" jdbcType="VARCHAR"/>
        <result property="statisticEntityId" column="STATISTIC_ENTITY_ID" jdbcType="VARCHAR"/>
        <result property="statisticKey" column="STATISTIC_KEY" jdbcType="VARCHAR"/>
        <result property="statisticValue" column="STATISTIC_VALUE" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryList" resultMap="BoTaskStatisticMap">
        SELECT
            BO_TASK_STATISTIC_ID,
            STATISTIC_ENTITY_ID,
            STATISTIC_KEY,
            STATISTIC_VALUE
        FROM
            T_BO_TASK_STATISTIC
        WHERE
            STATISTIC_DIMENSION = #{statisticDimension}
            AND STATISTIC_ITEM = #{statisticItem}
            <if test="searchStatisticEntityIds != null and searchStatisticEntityIds.size() > 0">
                AND STATISTIC_ENTITY_ID IN
                <foreach collection="searchStatisticEntityIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            AND IS_DEL = 0
    </select>

    <insert id="insert">
        INSERT INTO T_BO_TASK_STATISTIC
        (BO_TASK_STATISTIC_ID, STATISTIC_DIMENSION, STATISTIC_ITEM, STATISTIC_ENTITY_ID, STATISTIC_KEY, STATISTIC_VALUE)
        VALUES(#{boTaskStatisticId}, #{statisticDimension}, #{statisticItem}, #{statisticEntityId}, #{statisticKey}, #{statisticValue})
    </insert>

    <update id="update">
        UPDATE T_BO_TASK_STATISTIC
        SET STATISTIC_VALUE= #{statisticValue},LAST_MODIFIED_TIME=SYSDATE
        WHERE BO_TASK_STATISTIC_ID=#{boTaskStatisticId}
    </update>

    <select id="query" resultMap="BoTaskStatisticMap">
        SELECT
            STATISTIC_KEY,
            STATISTIC_VALUE
        FROM
            T_BO_TASK_STATISTIC
        WHERE
            STATISTIC_DIMENSION = #{statisticDimension}
            AND STATISTIC_ITEM = #{statisticItem}
            AND STATISTIC_ENTITY_ID = #{statisticEntityId}
            AND IS_DEL = 0
    </select>

</mapper>
