package com.wtyt.reviews.mapper.syf;

import com.wtyt.reviews.bean.ReviewsBean;
import com.wtyt.reviews.bean.ReviewsCollectBean;
import com.wtyt.reviews.bean.ReviewsDbParameterBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年03月15日 15时37分
 */
@Repository
@Mapper
public interface ReviewsCollectMapper {

    /**
     * 获取已完成评价数
     * @param reviewerIdentity
     * @param reviewerId
     * @return
     */
    int getFinishedReviewsCount(@Param("reviewerIdentity") String reviewerIdentity,
                                @Param("reviewerId")  String reviewerId);

    /**
     * 获取历史已完成评价数
     * @param reviewerIdentity
     * @param reviewerId
     * @return
     */
    int getHistoryFinishedReviewsCount(@Param("reviewerIdentity") String reviewerIdentity,
                                       @Param("reviewerId")  String reviewerId);

    /**
     * 获取未完成评价数
     * @param scope
     * @param scopeId
     * @return
     */
    int getUnfinishedReviewsCount(@Param("scope") String scope,
                                  @Param("scopeId")  String scopeId);

    /**
     * 获取已完成评价列表
     * @param reviewerIdentity
     * @param reviewerId
     * @return
     */
    List<ReviewsCollectBean> getFinishedReviewsList(@Param("reviewerIdentity") String reviewerIdentity,
                                                    @Param("reviewerId")  String reviewerId);

    /**
     * 获取未完成评价列表
     * @param scope
     * @param scopeId
     * @return
     */
    List<ReviewsCollectBean> getUnfinishedReviewsList(@Param("scope") String scope,
                                                      @Param("scopeId")  String scopeId);

    /**
     * 获取评价统计数据
     * 根据评价对象ID集合统计人均评价总数以及总得分
     * @param reviewsDbParameterBean
     * @return
     */
    List<ReviewsCollectBean> getReviewsSummaryData1(ReviewsDbParameterBean reviewsDbParameterBean);

    /**
     * 获取评价对象的评价列表
     * @param reviewsDbParameterBean
     * @return
     */
    List<ReviewsCollectBean> getReviewsListByReviewsTarget(ReviewsDbParameterBean reviewsDbParameterBean);

    ReviewsCollectBean getReviewsLimitByReviewsTarget(ReviewsDbParameterBean reviewsDbParameterBean);

    /**
     * 获取评价统计数据
     * 根据评价对象获取评分分布
     * @param reviewsDbParameterBean
     * @return
     */
    List<ReviewsCollectBean> getReviewsSummaryData2(ReviewsDbParameterBean reviewsDbParameterBean);

    /**
     * 根据主体获取得分
     * @param subjectType
     * @param subjectIdList
     * @return
     */
    List<ReviewsCollectBean> getReviewsScoreListBySubject(@Param("subjectType") String subjectType,
                                                          @Param("subjectIdList") List<String> subjectIdList);
}
