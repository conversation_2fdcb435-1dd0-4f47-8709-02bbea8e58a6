<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.OrderCpdShuntRelMapper">


    <insert id="batchInsertOrderCpdShuntRel">
            INSERT INTO T_BO_ORDER_CPD_SHUNT_REL(BO_ORDER_CPD_SHUNT_REL_ID, MERGE_ID, CPD_SHUNT_RECORD_ID)
            <foreach item="item" index="index" collection="list" separator="UNION ALL">
                SELECT
                #{item.boOrderCpdShuntRelId} BO_ORDER_CPD_SHUNT_REL_ID,
                #{item.mergeId} MERGE_ID,
                #{item.cpdShuntRecordId} CPD_SHUNT_RECORD_ID
                FROM
                DUAL
            </foreach>
    </insert>
    <update id="batchDelete">
        <foreach collection="list" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_ORDER_CPD_SHUNT_REL T
            SET T.IS_DEL = 1,
                T.LAST_MODIFIED_TIME = SYSDATE
            WHERE T.IS_DEL = 0
            AND T.BO_ORDER_CPD_SHUNT_REL_ID = #{item.boOrderCpdShuntRelId}
        </foreach>
    </update>
    <update id="batchUpdate">
        <foreach collection="list" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_ORDER_CPD_SHUNT_REL T
            SET T.STATE = #{item.state},
                T.CPD_SHUNT_BOOKING_ID = #{item.cpdShuntBookingId},
                T.CPD_DRIVER_REP_ID = #{item.cpdDriverRepId},
                T.BO_TRANS_TASK_ID = #{item.boTransTaskId},
                T.DRIVER_NAME = #{item.driverName},
                T.DRIVER_MOBILE_NO = #{item.driverMobileNo},
                T.CART_LENGTH = #{item.cartLength},
                T.CART_TYPE = #{item.cartType},
                T.CART_BADGE_NO = #{item.cartBadgeNo},
                T.CART_BADGE_COLOR = #{item.cartBadgeColor},
                T.LAST_MODIFIED_TIME = SYSDATE
            WHERE T.IS_DEL = 0
            AND T.CPD_SHUNT_RECORD_ID = #{item.cpdShuntRecordId}
            AND T.MERGE_ID = #{item.mergeId}
        </foreach>
    </update>
    <update id="clearMatchInfo">
        UPDATE
            T_BO_ORDER_CPD_SHUNT_REL
        SET
            STATE = 0,
            CPD_SHUNT_BOOKING_ID = NULL ,
            CPD_DRIVER_REP_ID = NULL,
            BO_TRANS_TASK_ID = NULL ,
            DRIVER_NAME = NULL ,
            DRIVER_MOBILE_NO = NULL ,
            CART_LENGTH = NULL ,
            CART_TYPE = NULL ,
            CART_BADGE_NO = NULL ,
            CART_BADGE_COLOR = NULL,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND (MERGE_ID IN
                <foreach collection="mergeIds" index="index" item="item" open="(" close=")" separator=",">
                    <if test="(index % 999) == 998"> NULL) OR MERGE_ID IN(</if> #{item}
                </foreach>
            )
    </update>
    <update id="updateMergeId">
        UPDATE
            T_BO_ORDER_CPD_SHUNT_REL
        SET
            MERGE_ID = #{mergeId},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND CPD_SHUNT_RECORD_ID = #{cpdShuntRecordId}
    </update>
    <update id="clearDriverInfo">
        UPDATE
            T_BO_ORDER_CPD_SHUNT_REL
        SET
            STATE = 0,
            CPD_SHUNT_BOOKING_ID = NULL ,
            CPD_DRIVER_REP_ID = NULL,
            DRIVER_NAME = NULL ,
            DRIVER_MOBILE_NO = NULL ,
            CART_LENGTH = NULL ,
            CART_TYPE = NULL ,
            CART_BADGE_NO = NULL ,
            CART_BADGE_COLOR = NULL,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND (MERGE_ID IN
                <foreach collection="mergeIds" index="index" item="item" open="(" close=")" separator=",">
                    <if test="(index % 999) == 998"> NULL) OR MERGE_ID IN(</if> #{item}
                </foreach>
            )
    </update>
    <select id="queryOrderCpdShuntRelList" resultType="com.wtyt.bo.bean.BoOrderCpdShuntRelBean">
        SELECT
            BO_ORDER_CPD_SHUNT_REL_ID boOrderCpdShuntRelId,
            MERGE_ID mergeId,
            CPD_SHUNT_RECORD_ID cpdShuntRecordId,
            STATE state,
            CPD_SHUNT_BOOKING_ID cpdShuntBookingId,
            CPD_DRIVER_REP_ID cpdDriverRepId,
            BO_TRANS_TASK_ID boTransTaskId,
            DRIVER_NAME driverName,
            DRIVER_MOBILE_NO driverMobileNo,
            CART_LENGTH cartLength,
            CART_TYPE cartType,
            CART_BADGE_NO cartBadgeNo,
            CART_BADGE_COLOR cartBadgeColor
        FROM
            T_BO_ORDER_CPD_SHUNT_REL WHERE IS_DEL=0 AND MERGE_ID IN
            <foreach collection="list" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
    </select>
    <select id="queryOrderCpdShuntRelByRepId" resultType="com.wtyt.bo.bean.BoOrderCpdShuntRelBean">
        SELECT
            BO_ORDER_CPD_SHUNT_REL_ID boOrderCpdShuntRelId,
            MERGE_ID mergeId,
            CPD_SHUNT_RECORD_ID cpdShuntRecordId,
            STATE state,
            CPD_SHUNT_BOOKING_ID cpdShuntBookingId,
            CPD_DRIVER_REP_ID cpdDriverRepId,
            BO_TRANS_TASK_ID boTransTaskId,
            DRIVER_NAME driverName,
            DRIVER_MOBILE_NO driverMobileNo,
            CART_LENGTH cartLength,
            CART_TYPE cartType,
            CART_BADGE_NO cartBadgeNo,
            CART_BADGE_COLOR cartBadgeColor
        FROM
            T_BO_ORDER_CPD_SHUNT_REL WHERE IS_DEL=0 AND CPD_DRIVER_REP_ID = #{cpdDriverRepId}
    </select>
    <select id="queryOrderCpdShuntRelByCpdShuntRecordId" resultType="com.wtyt.bo.bean.BoOrderCpdShuntRelBean">
        SELECT
            BO_ORDER_CPD_SHUNT_REL_ID boOrderCpdShuntRelId,
            MERGE_ID mergeId,
            CPD_SHUNT_RECORD_ID cpdShuntRecordId,
            STATE state,
            CPD_SHUNT_BOOKING_ID cpdShuntBookingId,
            CPD_DRIVER_REP_ID cpdDriverRepId,
            BO_TRANS_TASK_ID boTransTaskId,
            DRIVER_NAME driverName,
            DRIVER_MOBILE_NO driverMobileNo,
            CART_LENGTH cartLength,
            CART_TYPE cartType,
            CART_BADGE_NO cartBadgeNo,
            CART_BADGE_COLOR cartBadgeColor
        FROM
            T_BO_ORDER_CPD_SHUNT_REL WHERE IS_DEL=0 AND CPD_SHUNT_RECORD_ID = #{cpdShuntRecordId} and rownum=1
    </select>


</mapper>
