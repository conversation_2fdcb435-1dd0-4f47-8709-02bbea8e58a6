<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoStartRemindAlarmMapper">
    
    <select id="getStartRemindAlarmWaybillList" resultType="BoXxlJobAlarmBean">
        select *
        from (
          select T.TAX_WAYBILL_ID taxWaybillId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.ORG_ID orgId,
            T.CART_BADGE_NO cartBadgeNo,
            T.DRIVER_NAME driverName,
            T.START_PROVINCE_NAME startProvinceName,
            T.END_PROVINCE_NAME endProvinceName,
            T.START_CITY_NAME startCityName,
            T.END_CITY_NAME endCityName,
            T.START_COUNTY_NAME startCountyName,
            T.END_COUNTY_NAME endCountyName,
            T.MOBILE_NO mobileNo,
            T.CREATED_USER_ID klbUserId,
            T.Node_Id nodeId,
            TO_CHAR(T.DEAD_LINE_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
            TO_CHAR(T.DISPATCH_TIME, 'YYYY-MM-DD HH24:MI:SS') dispatchTime,
            TO_CHAR(dc.created_time, 'YYYY-MM-DD HH24:MI:SS') arriveTime,
            TO_CHAR(T.START_TIME, 'YYYY-MM-DD HH24:MI:SS') startTime,
        ROW_NUMBER() OVER(PARTITION BY t.BO_TRANS_TASK_ID ORDER BY dc.created_time DESC) WRN
        from (select *
            from (select T.TAX_WAYBILL_ID,
                    T.BO_TRANS_TASK_ID,
                    T.ORG_ID,
                    T.CART_BADGE_NO,
                    T.DRIVER_NAME,
                    T.START_PROVINCE_NAME,
                    T.END_PROVINCE_NAME,
                    T.START_CITY_NAME,
                    T.END_CITY_NAME,
                    T.START_COUNTY_NAME,
                    T.END_COUNTY_NAME,
                    T.MOBILE_NO,
                    T.CREATED_USER_ID,
                    L.Node_Id,
                    L.DEAD_LINE_TIME,
                    R.CREATED_TIME DISPATCH_TIME,
                    T.START_TIME,
                    T.START_LONGITUDE,
                    T.START_LATITUDE,
                    ROW_NUMBER() OVER(PARTITION BY R.BO_TRANS_TASK_ID ORDER BY R.CREATED_TIME DESC) RN
                from    T_BO_TRANS_NODE_DEAD_LINE L,
                        T_BO_TRANS_NODE_RECORD    R,
                        T_BO_TRANS_TASK             T
                where L.BO_TRANS_TASK_ID = R.BO_TRANS_TASK_ID
                        and L.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
                        and T.org_id = #{orgId}
                        AND L.NODE_ID = '500'
                        AND R.Node_Id = '200'
                        AND L.Dead_Line_Time &lt;=
                        to_date(#{time}||' 23:59:59',
                        'yyyy-mm-dd hh24:mi:ss')
                        AND L.DEAD_LINE_TIME >=
                        to_date(#{time}||' 00:00:00',
                        'yyyy-mm-dd hh24:mi:ss')
                        AND T.Cart_Badge_No is not null
                        AND T.IS_DEL = 0
                        AND L.is_DEL = 0
                        AND R.IS_DEL = 0)
                where rn = 1) t
            left join T_BO_TRANS_NODE_RECORD dc
            on t.BO_TRANS_TASK_ID = dc.BO_TRANS_TASK_ID
            and t.node_id = dc.node_id
            and dc.node_id = '500'
            and dc.created_time >= t.DISPATCH_TIME
            and dc.is_del = 0
            and to_char(dc.created_time, 'yyyy-mm-dd') =  #{time}
        )
        where WRN = 1
    </select>

</mapper>