<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskGroupRelMapper">

    <insert id="batchSaveGroupList">
        INSERT INTO T_BO_TRANS_TASK_GROUP_REL(
            BO_TRANS_TASK_GROUP_REL_ID,
            BO_TRANS_TASK_ID,
            GROUP_ID,
            ORG_ID,
            SUPPLIER_TYPE
        ) SELECT
        A.*
        FROM(
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()} BO_TRANS_TASK_GROUP_REL_ID,
            #{item.boTransTaskId} BO_TRANS_TASK_ID,
            #{item.groupId} GROUP_ID,
            #{item.orgId} ORG_ID,
            #{item.supplierType} SUPPLIER_TYPE
            FROM
            DUAL
        </foreach>) A

    </insert>
    <insert id="saveOrUpdate">
        MERGE INTO T_BO_TRANS_TASK_GROUP_REL A
        USING (
            <foreach collection="list" index="index" item="item" open=""
                     close="" separator="union">
                SELECT #{item.boTransTaskGroupRelId} BO_TRANS_TASK_GROUP_REL_ID,#{item.boTransTaskId} BO_TRANS_TASK_ID,#{item.groupId} GROUP_ID FROM DUAL
            </foreach>
        ) B
        ON (A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID AND A.GROUP_ID = B.GROUP_ID)
        WHEN MATCHED THEN
            UPDATE SET
            A.IS_DEL = 0,
            A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
            INSERT (BO_TRANS_TASK_GROUP_REL_ID,
            BO_TRANS_TASK_ID,
            GROUP_ID)
            VALUES
            (B.BO_TRANS_TASK_GROUP_REL_ID,
            B.BO_TRANS_TASK_ID,
            B.GROUP_ID)
    </insert>
    <update id="deleteSupplierRight">
        UPDATE T_BO_TRANS_TASK_GROUP_REL SET IS_DEL = 1 WHERE IS_DEL=0 AND BO_TRANS_TASK_ID =#{taskId} AND SUPPLIER_TYPE IS NOT NULL AND SUPPLIER_TYPE !=0
    </update>

    <select id="queryGroupIdByTaskId" resultType="java.lang.String">
        SELECT GROUP_ID FROM T_BO_TRANS_TASK_GROUP_REL WHERE IS_DEL =0 AND BO_TRANS_TASK_ID =#{taskId}
    </select>
    <select id="querySupplierTypes" resultType="java.lang.String">
        SELECT SUPPLIER_TYPE FROM T_BO_TRANS_TASK_GROUP_REL WHERE IS_DEL =0 AND BO_TRANS_TASK_ID = #{boTransTaskId} AND SUPPLIER_TYPE IS NOT NULL
    </select>
    <select id="geTransTaskGroupRelByTransTaskId" resultType="com.wtyt.dao.bean.syf.BoTransTaskGroupRelBean">
        SELECT
            BO_TRANS_TASK_GROUP_REL_ID boTransTaskGroupRelId,
            BO_TRANS_TASK_ID boTransTaskId,
            GROUP_ID groupId,
            ORG_ID orgId,
            SUPPLIER_TYPE supplierType
        FROM
            T_BO_TRANS_TASK_GROUP_REL
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID = #{boTransTaskId}
            AND SUPPLIER_TYPE IN (1, 2)
    </select>
    <select id="geTransTaskGroupRelByTransTaskIdAndOrgId"
            resultType="com.wtyt.dao.bean.syf.BoTransTaskGroupRelBean">
        SELECT
            BO_TRANS_TASK_GROUP_REL_ID boTransTaskGroupRelId,
            BO_TRANS_TASK_ID boTransTaskId,
            GROUP_ID groupId,
            ORG_ID orgId,
            SUPPLIER_TYPE supplierType
        FROM
            T_BO_TRANS_TASK_GROUP_REL
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID = #{boTransTaskId}
            AND ORG_ID = #{orgId}
            AND SUPPLIER_TYPE IN (1, 2)
    </select>
    <select id="queryTaskGroupRel" resultType="com.wtyt.dao.bean.syf.BoTransTaskGroupRelBean">
        SELECT
            BO_TRANS_TASK_GROUP_REL_ID boTransTaskGroupRelId,
            BO_TRANS_TASK_ID boTransTaskId,
            GROUP_ID groupId,
            ORG_ID orgId,
            SUPPLIER_TYPE supplierType
        FROM
            T_BO_TRANS_TASK_GROUP_REL
        WHERE
            IS_DEL = 0
          AND BO_TRANS_TASK_ID = #{boTransTaskId}
          AND ORG_ID = #{orgId}
          AND SUPPLIER_TYPE IN (1, 2)
    </select>
    <select id="queryByTaskId" resultType="com.wtyt.dao.bean.syf.BoTransTaskGroupRelBean">
        SELECT
        BO_TRANS_TASK_GROUP_REL_ID boTransTaskGroupRelId,
        BO_TRANS_TASK_ID boTransTaskId,
        GROUP_ID groupId,
        ORG_ID orgId,
        SUPPLIER_TYPE supplierType
        FROM
        T_BO_TRANS_TASK_GROUP_REL
        WHERE
        IS_DEL = 0
        AND BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>
    <select id="queryByTaskIds" resultType="com.wtyt.dao.bean.syf.BoTransTaskGroupRelBean">
        SELECT
        BO_TRANS_TASK_GROUP_REL_ID boTransTaskGroupRelId,
        BO_TRANS_TASK_ID boTransTaskId,
        GROUP_ID groupId,
        ORG_ID orgId,
        SUPPLIER_TYPE supplierType
        FROM
        T_BO_TRANS_TASK_GROUP_REL
        WHERE
        IS_DEL = 0
        AND BO_TRANS_TASK_ID IN
        <foreach collection="taskIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="queryTaskGroupRelWithSupplierList" resultType="com.wtyt.dao.bean.syf.BoTransTaskGroupRelBean">
        SELECT
            BO_TRANS_TASK_GROUP_REL_ID boTransTaskGroupRelId,
            BO_TRANS_TASK_ID boTransTaskId,
            GROUP_ID groupId,
            ORG_ID orgId,
            SUPPLIER_TYPE supplierType
        FROM
            T_BO_TRANS_TASK_GROUP_REL
        WHERE
            IS_DEL = 0
          AND BO_TRANS_TASK_ID = #{boTransTaskId}
          <if test="supplierList != null and supplierList.size > 0">
              AND SUPPLIER_TYPE IN
              <foreach collection="supplierList" index="index" item="item" open="(" close=")" separator=",">
                  #{item}
              </foreach>
          </if>
    </select>
    <select id="querySupplierTypesByBoTransTaskIdList" resultType="com.wtyt.dao.bean.syf.BoTransTaskGroupRelBean"
            parameterType="java.util.List">
        SELECT BO_TRANS_TASK_ID boTransTaskId,SUPPLIER_TYPE supplierType FROM T_BO_TRANS_TASK_GROUP_REL WHERE IS_DEL =0 AND SUPPLIER_TYPE IS NOT NULL
              AND BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIdList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
    </select>


    <select id="queryGroupIdListByTaskIdList" resultType="com.wtyt.dao.bean.syf.BoTransTaskGroupRelBean">
        select t.boTransTaskId, t.groupId
        from
        (
            SELECT
                BO_TRANS_TASK_ID boTransTaskId,
                GROUP_ID groupId,
                ROW_NUMBER() OVER (PARTITION BY BO_TRANS_TASK_ID ORDER BY CREATED_TIME DESC) rn
            FROM
                T_BO_TRANS_TASK_GROUP_REL
            WHERE IS_DEL = 0
                AND SUPPLIER_TYPE IN (1, 2)
                AND BO_TRANS_TASK_ID IN
                <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
        ) t
        where t.rn = 1
    </select>

    <update id="deleteTaskGroupByTaskId">
        UPDATE T_BO_TRANS_TASK_GROUP_REL SET IS_DEL =1,LAST_MODIFIED_TIME =SYSDATE WHERE BO_TRANS_TASK_ID =#{taskId} AND IS_DEL =0
    </update>
    <update id="batchTransferTaskGroupRel">
        UPDATE T_BO_TRANS_TASK_GROUP_REL SET ORG_ID = #{data.toOrgId}, GROUP_ID = #{data.groupId}, LAST_MODIFIED_TIME = SYSDATE WHERE IS_DEL = 0 AND ORG_ID = #{data.fromOrgId} AND BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIds" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updatePersonalSupplierGroupId">
        UPDATE T_BO_TRANS_TASK_GROUP_REL T
        SET T.GROUP_ID = #{groupId},
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.SUPPLIER_TYPE = 1
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

</mapper>
