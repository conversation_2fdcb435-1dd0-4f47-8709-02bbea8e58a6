<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoChatSomeTimeoutAlarmMapper">
    
    <select id="getChatAcceptTimeoutWaybill" resultType="BoXxlJobAlarmBean">
        SELECT
        T.TAX_WAYBILL_ID taxWaybillId,
        T.BO_TRANS_TASK_ID boTransTaskId,
        L.NODE_ID nodeId,
        T.ORG_ID orgId,
        T.CART_BADGE_NO cartBadgeNo,
        T.DRIVER_NAME driverName,
        T.MOBILE_NO mobileNo,
        T.START_PROVINCE_NAME startProvinceName,
        T.END_PROVINCE_NAME endProvinceName,
        T.START_CITY_NAME startCityName,
        T.END_CITY_NAME endCityName,
        T.START_COUNTY_NAME startCountyName,
        T.END_COUNTY_NAME endCountyName,
        T.START_LONGITUDE startLongitude,
        T.START_LATITUDE startLatitude,
        TO_CHAR(L.DEAD_LINE_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
        TO_CHAR(L.DEAD_LINE_TIME, 'YYYY-MM-DD HH24:MI:SS') comparedTime,
        T.IS_PARTAKE_OPERATE isPartakeOperate
        FROM T_BO_TRANS_NODE_DEAD_LINE L,T_BO_TRANS_TASK T,T_BO_TRANS_TASK_EXTRA E
        WHERE L.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        AND T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        AND T.CART_BADGE_NO IS NOT NULL
        AND L.NODE_ID = 500
        AND L.DEAD_LINE_TIME &lt; SYSDATE
        AND E.HYB_RECEIVED_TIME is NULL
        AND E.ARRIVE_TIME is NULL
        AND (T.PAY_STATE!=2 and T.TRANS_MODE in (1,2))
        AND L.IS_DEL = 0
        AND T.IS_DEL = 0
        <if test="list !=null and list.size()>0">
            AND T.ORG_ID IN
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    
    <select id="getChatSendUploadTimeout" resultType="com.wtyt.bo.bean.BoXxlJobAlarmBean">
        SELECT
        T.TAX_WAYBILL_ID taxWaybillId,
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.ORG_ID orgId,
        T.CART_BADGE_NO cartBadgeNo,
        T.DRIVER_NAME driverName,
        T.MOBILE_NO mobileNo,
        T.START_PROVINCE_NAME startProvinceName,
        T.END_PROVINCE_NAME endProvinceName,
        T.START_CITY_NAME startCityName,
        T.END_CITY_NAME endCityName,
        T.START_COUNTY_NAME startCountyName,
        T.END_COUNTY_NAME endCountyName,
        T.START_LONGITUDE startLongitude,
        T.START_LATITUDE startLatitude,
        TO_CHAR(E.ARRIVE_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
        TO_CHAR(T.START_TIME, 'YYYY-MM-DD HH24:MI:SS') startTime,
        T.IS_PARTAKE_OPERATE isPartakeOperate
        FROM T_BO_TRANS_TASK T
        ,T_BO_TRANS_TASK_EXTRA E
        WHERE T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        AND T.CART_BADGE_NO IS NOT NULL
        AND (T.PAY_STATE!=2 and T.TRANS_MODE in (1,2))
        AND (T.START_TIME IS NULL or not exists (select r.BO_TRANS_TASK_ID from T_BO_TRANS_NODE_RECORD r where
        r.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID and r.is_del=0 and r.node_id in (510)))
        AND sysdate > (E.ARRIVE_TIME + #{afterHour}/24)
        AND T.IS_DEL = 0
        <if test="list !=null and list.size()>0">
            AND T.ORG_ID IN
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    
    <select id="getChatUploadPicTimeout" resultType="com.wtyt.bo.bean.BoXxlJobAlarmBean">
        
        SELECT
        T.TAX_WAYBILL_ID taxWaybillId,
        T.BO_TRANS_TASK_ID boTransTaskId,
        T.ORG_ID orgId,
        T.CART_BADGE_NO cartBadgeNo,
        T.DRIVER_NAME driverName,
        T.MOBILE_NO mobileNo,
        T.START_PROVINCE_NAME startProvinceName,
        T.END_PROVINCE_NAME endProvinceName,
        T.START_CITY_NAME startCityName,
        T.END_CITY_NAME endCityName,
        T.START_COUNTY_NAME startCountyName,
        T.END_COUNTY_NAME endCountyName,
        T.START_LONGITUDE startLongitude,
        T.START_LATITUDE startLatitude,
        TO_CHAR(R.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') deadLineTime,
        T.IS_PARTAKE_OPERATE isPartakeOperate
        FROM T_BO_TRANS_TASK T
        inner join T_BO_TRANS_NODE_RECORD r on r.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID and r.node_id = '650' and r.is_del = 0
        ,T_BO_TRANS_TASK_EXTRA E
        WHERE T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        AND T.CART_BADGE_NO IS NOT NULL
        AND (T.PAY_STATE!=2 and T.TRANS_MODE in (1,2))
        and T.FIRST_RECEIPT_TIME is null
        AND sysdate > (R.CREATED_TIME + #{afterHour}/24)
        AND T.IS_DEL = 0
        <if test="list !=null and list.size()>0">
            AND T.ORG_ID IN
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>