<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoOrderRecognizeRelMapper">


    <insert id="batchSave">
        INSERT
        INTO
        T_BO_ORDER_RECOGNIZE_REL (BO_ORDER_RECOGNIZE_REL_ID,
        BO_TRANS_ORDER_ID,
        BO_ORDER_RECOGNIZE_ID)
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT #{item.boOrderRecognizeRelId} boOrderRecognizeRelId,
            #{item.boTransOrderId} boTransOrderId,
            #{item.boOrderRecognizeId} boOrderRecognizeId
            FROM DUAL
        </foreach>
        ) A
    </insert>


</mapper>
