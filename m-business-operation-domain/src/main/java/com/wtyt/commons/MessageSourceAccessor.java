package com.wtyt.commons;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Component
public class MessageSourceAccessor {

    @Autowired
    MessageSource messageSource;

    public String getMessage(String result, Object... params) {
        String message = "";
        try {
            message = messageSource.getMessage(result, params, Locale.CHINA);
        } catch (Exception e) {
            return "";
        }
        return message;
    }

    public String getMessage(String result) {
        return getMessage(result, "");
    }

}
