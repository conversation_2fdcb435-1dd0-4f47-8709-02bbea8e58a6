<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="debug">

    <!-- appender配置 -->
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout charset="utf-8" pattern="[%d][%p][%t][%c:%M:%L] %m%n" />
        </Console>
    </Appenders>
    <Loggers>
        <!--过滤掉spring和mybatis的一些无用的DEBUG信息 -->
        <logger name="org.springframework" level="DEBUG"></logger>
        <logger name="org.mybatis" level="DEBUG"></logger>
        <logger name="org.apache.ibatis" level="DEBUG"></logger>
        <logger name="org.apache.http" level="DEBUG"></logger>
        <logger name="com.netflix.discovery" level="DEBUG"></logger>
        <logger name="org.dbunit" level="DEBUG"></logger>
        <logger name="java.sql.Connection" level="DEBUG"></logger>
        <logger name="java.sql.Statement" level="DEBUG"></logger>
        <logger name="java.sql.PreparedStatement" level="DEBUG"></logger>
        <logger name="java.sql.ResultSet" level="DEBUG"></logger>

        <logger name="com.wtyt.dao.mapper.syf" level="DEBUG" additivity="false">
            <appender-ref ref="Console"/>
        </logger>

        <Root level="debug">
            <AppenderRef ref="Console" />
        </Root>

    </Loggers>
</Configuration>
