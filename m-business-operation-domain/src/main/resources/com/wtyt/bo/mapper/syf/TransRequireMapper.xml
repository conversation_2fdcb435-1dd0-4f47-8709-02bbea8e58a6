<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.TransRequireMapper">
    <delete id="deleteByBoTransTaskId" parameterType="java.lang.String">
        DELETE FROM T_BO_TRANS_REQUIRE_DETAIL WHERE BO_TRANS_TASK_ID = #{boTransTaskId} AND IS_DEL = 0
    </delete>
    <delete id="batchDeleteByIds" parameterType="java.util.List">
        DELETE FROM T_BO_TRANS_REQUIRE_DETAIL WHERE IS_DEL = 0
        AND BO_TRANS_REQUIRE_DETAIL_ID IN
        <foreach collection="idList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <select id="queryListByBoTransTaskIdOrTaxWaybillId" parameterType="string" resultType="TransRequireBean">
        SELECT
        T1.BO_TRANS_REQUIRE_DETAIL_ID boTransRequireDetailId,
        T1.TAX_WAYBILL_ID taxWaybillId,
        T1.BO_TRANS_TASK_ID boTransTaskId,
        T1.CATEGORY_CODE categoryCode,
        T1.CATEGORY_NAME categoryName,
        T1.TYPE,
        T1.CONTENT,
        T1.SORT sort,
        T1.KEYWORD keyword,
        T1.TIME_FLAG timeFlag,
        T1.CONFIG_TYPE configType,
        T1.NOTE note
    FROM T_BO_TRANS_REQUIRE_DETAIL T1
    WHERE T1.IS_DEL = 0
        AND T1.BO_TRANS_TASK_ID = #{boTransTaskId}
    ORDER BY T1.CATEGORY_CODE ASC,T1.TYPE ASC,T1.SORT ASC
    </select>

    <select id="queryListByBoTransTaskId" parameterType="string" resultType="BoTransRequireDetailBean">
        SELECT
            T1.CATEGORY_CODE categoryCode,
            T1.CATEGORY_NAME categoryName,
            T1.BO_TRANS_REQUIRE_DETAIL_ID boTransRequireDetailId,
            T1.TYPE,
            T1.CONTENT,
            T1.KEYWORD keyword,
            T1.TIME_FLAG timeFlag
        FROM T_BO_TRANS_REQUIRE_DETAIL T1
        WHERE T1.IS_DEL = 0
          AND T1.BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY T1.CATEGORY_CODE ASC,T1.TYPE ASC,T1.SORT ASC
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO T_BO_TRANS_REQUIRE_DETAIL
        (BO_TRANS_REQUIRE_DETAIL_ID,
        TAX_WAYBILL_ID,
        BO_TRANS_TASK_ID,
        CATEGORY_CODE,
        CATEGORY_NAME,
        TYPE,
        CONTENT,
        SORT,
        KEYWORD,
        TIME_FLAG,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        CONFIG_TYPE,
        NOTE)
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boTransRequireDetailId} BO_TRANS_REQUIRE_DETAIL_ID,
            #{item.taxWaybillId} TAX_WAYBILL_ID,
            #{item.boTransTaskId} BO_TRANS_TASK_ID,
            #{item.categoryCode} CATEGORY_CODE,
            #{item.categoryName} CATEGORY_NAME,
            #{item.type} TYPE,
            #{item.content} CONTENT,
            #{item.sort} SORT,
            #{item.keyword} KEYWORD,
            #{item.timeFlag} timeFlag,
            0 IS_DEL,
            SYSDATE CREATED_TIME,
            SYSDATE LAST_MODIFIED_TIME,
            #{item.configType} CONFIG_TYPE,
            #{item.note} NOTE
            FROM
            DUAL
        </foreach>
    </insert>

    <update id="batchUpdateTextContent" parameterType="java.util.List">
        UPDATE T_BO_TRANS_REQUIRE_DETAIL SET
        CONTENT =
        <foreach item="item" index="index" collection="list" separator=" " open="CASE BO_TRANS_REQUIRE_DETAIL_ID" close="END">
            WHEN TO_NUMBER(#{item.requireDetailId}) THEN #{item.value}
        </foreach>,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_REQUIRE_DETAIL_ID IN
        <foreach item="item" index="index" collection="list" separator="," open="(" close=")">
            #{item.requireDetailId}
        </foreach>
    </update>


    <select id="queryTransRequireBeanByCondition"  resultType="BoTransRequireDetailBean">
        SELECT
        T1.CATEGORY_CODE categoryCode,
        T1.CATEGORY_NAME categoryName,
        T1.BO_TRANS_REQUIRE_DETAIL_ID boTransRequireDetailId,
        T1.TYPE,
        T1.CONTENT,
        T1.KEYWORD keyword,
        T1.TIME_FLAG timeFlag,
        T1.TAX_WAYBILL_ID taxWaybillId
        FROM T_BO_TRANS_REQUIRE_DETAIL T1
        WHERE T1.BO_TRANS_TASK_ID = #{boTransTaskId}
        and T1.IS_DEL = 0
    <if test="categoryCode != null and categoryCode !=''">
        and CATEGORY_CODE = #{categoryCode}
    </if>
    </select>

    <update id="update">
        UPDATE T_BO_TRANS_REQUIRE_DETAIL SET
        CONTENT = #{content},LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL=0 AND BO_TRANS_REQUIRE_DETAIL_ID = #{boTransRequireDetailId}
    </update>

</mapper>
