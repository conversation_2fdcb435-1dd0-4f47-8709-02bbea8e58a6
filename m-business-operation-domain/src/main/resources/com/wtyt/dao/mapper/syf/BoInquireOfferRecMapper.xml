<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoInquireOfferRecMapper">



	<insert id="insertOfferRec" parameterType="BoInquireOfferRecBean">
		INSERT INTO T_BO_INQUIRE_OFFER_REC(
            BO_INQUIRE_OFFER_REC_ID,
            BO_TRANS_ORDER_ID,
            BO_INQUIRE_OFFER_ID,
            CREATED_USER_ID,
            ALL_FEE,
            OIL_FEE,
		    OIL_RATIO,
            IS_DEL,
            CREATED_TIME,
            LAST_MODIFIED_TIME)
        VALUES (
            #{boInquireOfferRecId},
            #{boTransOrderId},
            #{boInquireOfferId},
            #{createdUserId},
            TO_CHAR(#{allFee}, 'FM999999990.00'),
		    TO_CHAR( #{oilFee}, 'FM999999990.00'),
            #{oilRatio},
            0,
            SYSDATE,
            SYSDATE
            )
	</insert>
    <select id="selectByOfferId" resultType="com.wtyt.dao.bean.syf.BoInquireOfferRecBean">
    SELECT
        CREATED_USER_ID createdUserId,
        TO_CHAR(ALL_FEE, 'FM999999990.00') allFee,
		TO_CHAR(OIL_FEE, 'FM999999990.00') oilFee,
        OIL_RATIO oilRatio,
        TO_CHAR(CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        BO_INQUIRE_OFFER_ID boInquireOfferId,
        BO_INQUIRE_OFFER_REC_ID boInquireOfferRecId
    FROM
        T_BO_INQUIRE_OFFER_REC
    WHERE
        IS_DEL = 0
        AND BO_INQUIRE_OFFER_ID = #{offerId}
        ORDER BY CREATED_TIME DESC
    </select>

</mapper>