<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.tbl.mapper.syf.TaskBusinessLineMapper">



    <select id="countTaskByLine" resultType="com.wtyt.tbl.bean.BusinessLineTaskCountBean">
        SELECT
        bt.HYB_STATE hybState,
        bt.STATE,
        COUNT(*) count
        FROM
        T_BO_TRANS_TASK bt
        WHERE
        bt.IS_DEL = 0
        AND bt.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
        GROUP BY
        bt.HYB_STATE, bt.STATE
    </select>

    <select id="page" resultType="com.wtyt.tbl.bean.Resp5329572Bean">
        SELECT
            bt.BO_TRANS_TASK_ID boTransTaskId,
            bt.TAX_WAYBILL_NO taxWaybillNo,
            bt.DRIVER_NAME driverName,
            bt.MOBILE_NO MOBILE_NO,
            bt.CART_BADGE_NO cartBadgeNo,
            bt.HYB_STATE hybState,
            bte.CART_TYPE cartType,
            bte.CART_LENGTH cartLength
        FROM
            T_BO_TRANS_TASK bt
        JOIN T_BO_TRANS_TASK_EXTRA bte ON
            bte.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
            AND bte.IS_DEL = 0
        WHERE
            bt.IS_DEL = 0
            AND bt.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
            <if test="searchType==1">
                AND bt.CREATED_TIME > TRUNC(SYSDATE)
            </if>
            AND bte.DISPATCH_CAR_RECORD_ID IS NOT NULL
        ORDER BY
            bt.CREATED_TIME DESC
    </select>

    <select id="countTaskAllByLine" resultType="int">
        SELECT
            COUNT(*) count
        FROM
        T_BO_TRANS_TASK bt
        WHERE
        bt.IS_DEL = 0
        AND bt.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
    </select>
</mapper>
