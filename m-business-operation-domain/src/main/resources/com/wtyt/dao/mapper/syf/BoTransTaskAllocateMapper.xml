<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransTaskAllocateMapper">

    <resultMap type="com.wtyt.dao.bean.syf.BoTransTaskAllocateBean" id="baseResultMap">
        <result property="boTransTaskAllocateId" column="BO_TRANS_TASK_ALLOCATE_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
        <result property="taxWaybillId" column="TAX_WAYBILL_ID" jdbcType="VARCHAR"/>
        <result property="settleMode" column="SETTLE_MODE" jdbcType="VARCHAR"/>
        <result property="nodeId" column="NODE_ID" jdbcType="VARCHAR"/>
        <result property="nodeTime" column="NODE_TIME" jdbcType="VARCHAR"/>
        <result property="payState" column="PAY_STATE" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="allFreight" column="ALL_FREIGHT" jdbcType="VARCHAR"/>
        <result property="userFreight" column="USER_FREIGHT" jdbcType="VARCHAR"/>
        <result property="freightIncr" column="FREIGHT_INCR" jdbcType="VARCHAR"/>
        <result property="lossFee" column="LOSS_FEE" jdbcType="VARCHAR"/>
        <result property="unitPrice" column="UNIT_PRICE" jdbcType="VARCHAR"/>
        <result property="prepaymentsOilcard" column="PREPAYMENTS_OILCARD" jdbcType="VARCHAR"/>
        <result property="prepaymentsGascard" column="PREPAYMENTS_GASCARD" jdbcType="VARCHAR"/>
        <result property="prepayments" column="PREPAYMENTS" jdbcType="VARCHAR"/>
        <result property="payType" column="PAY_TYPE" jdbcType="VARCHAR"/>
        <result property="payName" column="PAY_NAME" jdbcType="VARCHAR"/>
        <result property="payBankNo" column="PAY_BANK_NO" jdbcType="VARCHAR"/>
        <result property="payBankName" column="PAY_BANK_NAME" jdbcType="VARCHAR"/>
        <result property="payMobileNo" column="PAY_MOBILE_NO" jdbcType="VARCHAR"/>
        <result property="payIdCard" column="PAY_ID_CARD" jdbcType="VARCHAR"/>
        <result property="province" column="PROVINCE" jdbcType="VARCHAR"/>
        <result property="cityName" column="CITY_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="save">
        INSERT INTO T_BO_TRANS_TASK_ALLOCATE
            (BO_TRANS_TASK_ALLOCATE_ID,
            BO_TRANS_TASK_ID,
            ORG_ID,
            PAY_STATE,
            NODE_ID,
            NODE_TIME)
        VALUES(
            #{boTransTaskAllocateId},
            #{boTransTaskId},
            #{orgId},
            #{payState},
            #{nodeId},
            TO_DATE(#{nodeTime},'YYYY-MM-DD HH24:MI:SS')
        )
    </insert>
    <insert id="saveByCondition">
        INSERT INTO T_BO_TRANS_TASK_ALLOCATE
        (BO_TRANS_TASK_ALLOCATE_ID,
         BO_TRANS_TASK_ID,
         ORG_ID,
         <if test="taxWaybillId != null and taxWaybillId != ''">
            TAX_WAYBILL_ID,
         </if>
        <if test="settleMode != null and settleMode != ''">
            SETTLE_MODE,
        </if>
        <if test="nodeId != null and nodeId != ''">
            NODE_ID,
        </if>
        <if test="payState != null and payState != ''">
            PAY_STATE,
        </if>
        <if test="allFreight != null and allFreight != ''">
            ALL_FREIGHT,
        </if>
        <if test="userFreight != null and userFreight != ''">
            USER_FREIGHT,
        </if>
        <if test="freightIncr != null and freightIncr != ''">
            FREIGHT_INCR,
        </if>
        <if test="lossFee != null and lossFee != ''">
            LOSS_FEE,
        </if>
        <if test="unitPrice != null and unitPrice != ''">
            UNIT_PRICE,
        </if>
        <if test="prepaymentsOilcard != null and prepaymentsOilcard != ''">
            PREPAYMENTS_OILCARD,
        </if>
        <if test="prepaymentsGascard != null and prepaymentsGascard != ''">
            PREPAYMENTS_GASCARD,
        </if>
        <if test="prepaymentsBuyOil != null and prepaymentsBuyOil != ''">
            PREPAYMENTS_BUY_OIL,
        </if>
        <if test="prepaymentsBuyGas != null and prepaymentsBuyGas != ''">
            PREPAYMENTS_BUY_GAS,
        </if>
        <if test="prepayments != null and prepayments != ''">
            PREPAYMENTS,
        </if>
        <if test="payType != null and payType != ''">
            PAY_TYPE,
        </if>
        <if test="payName != null and payName != ''">
            PAY_NAME,
        </if>
        <if test="payBankNo != null and payBankNo != ''">
            PAY_BANK_NO,
        </if>
        <if test="payBankName != null and payBankName != ''">
            PAY_BANK_NAME,
        </if>
        <if test="payMobileNo != null and payMobileNo != ''">
            PAY_MOBILE_NO,
        </if>
        <if test="payIdCard != null and payIdCard != ''">
            PAY_ID_CARD,
        </if>
        <if test="province != null and province != ''">
            PROVINCE,
        </if>
        <if test="cityName != null and cityName != ''">
            CITY_NAME,
        </if>
        <if test="note != null and note != ''">
            NOTE,
        </if>
        <if test="nodeTime != null and nodeTime != ''">
            NODE_TIME,
        </if>
        <if test="backFee != null and backFee != ''">
            BACK_FEE,
        </if>
        <if test="payOverTime != null and payOverTime != ''">
            PAY_OVER_TIME,
        </if>
        <if test="fixedCosts != null and fixedCosts != ''">
            FIXED_COSTS,
        </if>
        <if test="goodsCost != null and goodsCost != ''">
            GOODS_COST,
        </if>
        <if test="freightGuarantee != null and freightGuarantee != ''">
            FREIGHT_GUARANTEE,
        </if>
        <if test="oilCardNo != null and oilCardNo != ''">
            OIL_CARD_NO,
        </if>
        <if test="gasCardNo != null and gasCardNo != ''">
            GAS_CARD_NO,
        </if>
        <if test="settleFlag != null and settleFlag != ''">
            SETTLE_FLAG,
        </if>
        <if test="taskFeeVerifyState != null and taskFeeVerifyState != ''">
            TASK_FEE_VERIFY_STATE,
        </if>
        IS_DEL,
        CREATED_TIME
         )
        VALUES(
                  #{boTransTaskAllocateId},
                  #{boTransTaskId},
                  #{orgId},
                <if test="taxWaybillId != null and taxWaybillId != ''">
                    #{taxWaybillId},
                </if>
                <if test="settleMode != null and settleMode != ''">
                    #{settleMode},
                </if>
                <if test="nodeId != null and nodeId != ''">
                    #{nodeId},
                </if>
                <if test="payState != null and payState != ''">
                    #{payState},
                </if>
                <if test="allFreight != null and allFreight != ''">
                    #{allFreight},
                </if>
                <if test="userFreight != null and userFreight != ''">
                    #{userFreight},
                </if>
                <if test="freightIncr != null and freightIncr != ''">
                    #{freightIncr},
                </if>
                <if test="lossFee != null and lossFee != ''">
                    #{lossFee},
                </if>
                <if test="unitPrice != null and unitPrice != ''">
                    #{unitPrice},
                </if>
                <if test="prepaymentsOilcard != null and prepaymentsOilcard != ''">
                    #{prepaymentsOilcard},
                </if>
                <if test="prepaymentsGascard != null and prepaymentsGascard != ''">
                    #{prepaymentsGascard},
                </if>
                <if test="prepaymentsBuyOil != null and prepaymentsBuyOil != ''">
                    #{prepaymentsBuyOil},
                </if>
                <if test="prepaymentsBuyGas != null and prepaymentsBuyGas != ''">
                    #{prepaymentsBuyGas},
                </if>
                <if test="prepayments != null and prepayments != ''">
                    #{prepayments},
                </if>
                <if test="payType != null and payType != ''">
                    #{payType},
                </if>
                <if test="payName != null and payName != ''">
                    #{payName},
                </if>
                <if test="payBankNo != null and payBankNo != ''">
                    #{payBankNo},
                </if>
                <if test="payBankName != null and payBankName != ''">
                    #{payBankName},
                </if>
                <if test="payMobileNo != null and payMobileNo != ''">
                    #{payMobileNo},
                </if>
                <if test="payIdCard != null and payIdCard != ''">
                    #{payIdCard},
                </if>
                <if test="province != null and province != ''">
                    #{province},
                </if>
                <if test="cityName != null and cityName != ''">
                    #{cityName},
                </if>
                <if test="note != null and note != ''">
                    #{note},
                </if>
                <if test="nodeTime != null and nodeTime != ''">
                    TO_DATE(#{nodeTime}, 'YYYY-MM-DD HH24:MI:SS'),
                </if>
                <if test="backFee != null and backFee != ''">
                    #{backFee},
                </if>
                <if test="payOverTime != null and payOverTime != ''">
                    #{payOverTime},
                </if>
                <if test="fixedCosts != null and fixedCosts != ''">
                    #{fixedCosts},
                </if>
                <if test="goodsCost != null and goodsCost != ''">
                    #{goodsCost},
                </if>
                <if test="freightGuarantee != null and freightGuarantee != ''">
                    #{freightGuarantee},
                </if>
                <if test="oilCardNo != null and oilCardNo != ''">
                    #{oilCardNo},
                </if>
                <if test="gasCardNo != null and gasCardNo != ''">
                    #{gasCardNo},
                </if>
                <if test="settleFlag != null and settleFlag != ''">
                    #{settleFlag},
                </if>
                <if test="taskFeeVerifyState != null and taskFeeVerifyState != ''">
                    #{taskFeeVerifyState},
                </if>
                  0,
                  SYSDATE
              )
    </insert>
    <update id="deleteByTaskId">
        UPDATE T_BO_TRANS_TASK_ALLOCATE SET IS_DEL = 1,LAST_MODIFIED_TIME=SYSDATE WHERE IS_DEL=0 AND BO_TRANS_TASK_ID =#{taskId}
    </update>
    <update id="updateByTaskId">
        UPDATE
        T_BO_TRANS_TASK_ALLOCATE
        SET
        <if test="orgId != null and orgId != ''">
            ORG_ID = #{orgId},
        </if>
        <if test="taxWaybillId != null and taxWaybillId != ''">
            TAX_WAYBILL_ID = #{taxWaybillId},
        </if>
        <if test="settleMode != null and settleMode != ''">
            SETTLE_MODE = #{settleMode},
        </if>
        <if test="nodeId != null and nodeId != ''">
            NODE_ID = #{nodeId},
        </if>
        <if test="payState != null and payState != ''">
            PAY_STATE = #{payState},
        </if>
        <if test="allFreight != null and allFreight != ''">
            ALL_FREIGHT = #{allFreight},
        </if>
        <if test="userFreight != null and userFreight != ''">
            USER_FREIGHT = #{userFreight},
        </if>
        <if test="freightIncr != null and freightIncr != ''">
            FREIGHT_INCR = #{freightIncr},
        </if>
        <if test="lossFee != null and lossFee != ''">
            LOSS_FEE = #{lossFee},
        </if>
        <if test="unitPrice != null and unitPrice != ''">
            UNIT_PRICE = #{unitPrice},
        </if>
        <if test="prepaymentsOilcard != null and prepaymentsOilcard != ''">
            PREPAYMENTS_OILCARD = #{prepaymentsOilcard},
        </if>
        <if test="prepaymentsGascard != null and prepaymentsGascard != ''">
            PREPAYMENTS_GASCARD = #{prepaymentsGascard},
        </if>
        <if test="oilCardNo != null">
            OIL_CARD_NO = #{oilCardNo},
        </if>
        <if test="gasCardNo != null">
            GAS_CARD_NO = #{gasCardNo},
        </if>
        <if test="prepayments != null and prepayments != ''">
            PREPAYMENTS = #{prepayments},
        </if>
        <if test="payType != null and payType != ''">
            PAY_TYPE = #{payType},
        </if>
        <if test="payName != null">
            PAY_NAME = #{payName},
        </if>
        <if test="payBankNo != null">
            PAY_BANK_NO = #{payBankNo},
        </if>
        <if test="payBankName != null">
            PAY_BANK_NAME = #{payBankName},
        </if>
        <if test="payMobileNo != null">
            PAY_MOBILE_NO = #{payMobileNo},
        </if>
        <if test="payIdCard != null">
            PAY_ID_CARD = #{payIdCard},
        </if>
        <if test="province != null">
            PROVINCE = #{province},
        </if>
        <if test="cityName != null">
            CITY_NAME = #{cityName},
        </if>
        <if test="note != null and note != ''">
            NOTE = #{note},
        </if>
        <if test="nodeTime != null and nodeTime != ''">
            NODE_TIME = #{nodeTime},
        </if>
        <if test="backFee != null and backFee != ''">
            BACK_FEE = #{backFee},
        </if>
        <if test="goodsCost != null">
            GOODS_COST = #{goodsCost},
        </if>
        <if test="freightGuarantee != null">
            FREIGHT_GUARANTEE = #{freightGuarantee},
        </if>
        <if test="prepaymentsBuyOil!=null">
            PREPAYMENTS_BUY_OIL = #{prepaymentsBuyOil},
        </if>
        <if test="prepaymentsBuyGas!=null">
            PREPAYMENTS_BUY_GAS = #{prepaymentsBuyGas},
        </if>
        <if test="taskFeeVerifyState!=null and taskFeeVerifyState!=''">
            TASK_FEE_VERIFY_STATE= CASE WHEN TASK_FEE_VERIFY_STATE IS NULL THEN TO_NUMBER(#{taskFeeVerifyState}) ELSE TASK_FEE_VERIFY_STATE END,
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TRANS_TASK_ID = #{boTransTaskId}
            AND IS_DEL = 0
    </update>
    <update id="updateTaskAllocateNodeId">
        UPDATE T_BO_TRANS_TASK_ALLOCATE
        SET NODE_ID = #{nodeId},
        NODE_TIME = SYSDATE,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        <choose>
            <when test="boTransTaskId != null and boTransTaskId !=''">
                BO_TRANS_TASK_ID = #{boTransTaskId}
            </when>
            <when test="taxWaybillId != null and taxWaybillId !=''">
                TAX_WAYBILL_ID =#{taxWaybillId}
            </when>
            <otherwise>
                1 = 0
            </otherwise>
        </choose>
        <if test="upNodeIdList != null and upNodeIdList.size() > 0">
            AND NODE_ID IN
            <foreach collection="upNodeIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>
    <update id="updateTaskAllocateWhenCancelDispatch">
        UPDATE
            T_BO_TRANS_TASK_ALLOCATE
        SET
            TAX_WAYBILL_ID = null,
            SETTLE_MODE = null,
            PAY_STATE = null,
            GOODS_COST = 0,
            FREIGHT_GUARANTEE = NULL,
            SETTLE_FLAG = NULL,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID =#{boTransTaskId}
    </update>

    <select id="queryByTaskId" resultType="com.wtyt.dao.bean.syf.BoTransTaskAllocateBean">
        SELECT
            BO_TRANS_TASK_ALLOCATE_ID boTransTaskAllocateId,
            BO_TRANS_TASK_ID boTransTaskId,
            ORG_ID orgId,
            TAX_WAYBILL_ID taxWaybillId,
            SETTLE_MODE settleMode,
            SETTLE_FLAG settleFlag,
            NODE_ID nodeId,
            TO_CHAR(NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
            PAY_STATE payState,
            TO_CHAR(ALL_FREIGHT, 'FM999999990.00') allFreight,
            TO_CHAR(USER_FREIGHT, 'FM999999990.00') userFreight,
            TO_CHAR(FREIGHT_INCR, 'FM999999990.00') freightIncr,
            TO_CHAR(LOSS_FEE, 'FM999999990.00') lossFee,
            TO_CHAR(UNIT_PRICE, 'FM999999990.00') unitPrice,
            TO_CHAR(PREPAYMENTS_OILCARD, 'FM999999990.00') prepaymentsOilcard,
            TO_CHAR(PREPAYMENTS_GASCARD, 'FM999999990.00') prepaymentsGascard,
            TO_CHAR(PREPAYMENTS_BUY_OIL, 'FM999999990.00') prepaymentsBuyOil,
            TO_CHAR(PREPAYMENTS_BUY_GAS, 'FM999999990.00') prepaymentsBuyGas,
            OIL_CARD_NO oilCardNo,
            GAS_CARD_NO gasCardNo,
            TO_CHAR(PREPAYMENTS, 'FM999999990.00') prepayments,
            TO_CHAR(BACK_FEE, 'FM999999990.00') backFee,
            PAY_TYPE payType,
            PAY_NAME payName,
            PAY_BANK_NO payBankNo,
            PAY_BANK_NAME payBankName,
            PAY_MOBILE_NO payMobileNo,
            PAY_ID_CARD payIdCard,
            PROVINCE,
            CITY_NAME cityName,
            TO_CHAR(NODE_TIME, 'YYYY-MM-DD HH24:MI:SS') nodeTime,
            TO_CHAR(GOODS_COST, 'FM999999990.00') goodsCost,
            TO_CHAR(FREIGHT_GUARANTEE, 'FM999999990.00') freightGuarantee,
            CASE
                WHEN INS_STATE = 1 THEN TO_CHAR(NVL(ALL_FREIGHT, 0) + NVL(INS_FEE, 0), 'FM999999990.00')
                ELSE TO_CHAR(NVL(ALL_FREIGHT, 0), 'FM999999990.00')
            END payableFreight,
            CASE
                WHEN INS_STATE = 1 THEN TO_CHAR(NVL(INS_FEE, 0), 'FM999999990.00')
                ELSE '0.00'
            END insFee,
            TASK_FEE_VERIFY_STATE taskFeeVerifyState,
            FIXED_COSTS fixedCosts
        FROM
            T_BO_TRANS_TASK_ALLOCATE
        WHERE
            IS_DEL = 0
            AND BO_TRANS_TASK_ID = #{taskId}
    </select>

    <select id="queryAllocateBeanByTaskIds" resultMap="baseResultMap" parameterType="java.util.List">
        select * from T_BO_TRANS_TASK_ALLOCATE where IS_DEL = 0
        and BO_TRANS_TASK_ID in
        <foreach collection="boTransTaskIdList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <sql id="commonColumn">
        BO_TRANS_TASK_ALLOCATE_ID boTransTaskAllocateId,
        BO_TRANS_TASK_ID boTransTaskId,
        ORG_ID orgId,
        TAX_WAYBILL_ID taxWaybillId,
        SETTLE_MODE settleMode,
        NODE_ID nodeId,
        PAY_STATE payState,
        TO_CHAR(ALL_FREIGHT, 'FM999999990.00') allFreight,
        TO_CHAR(USER_FREIGHT, 'FM999999990.00') userFreight,
        TO_CHAR(FREIGHT_INCR, 'FM999999990.00') freightIncr,
        TO_CHAR(LOSS_FEE, 'FM999999990.00') lossFee,
        TO_CHAR(BACK_FEE, 'FM999999990.00') backFee,
        TO_CHAR(UNIT_PRICE, 'FM999999990.00') unitPrice,
        TO_CHAR(PREPAYMENTS_OILCARD, 'FM999999990.00') prepaymentsOilcard,
        TO_CHAR(PREPAYMENTS_GASCARD, 'FM999999990.00') prepaymentsGascard,
        TO_CHAR(PREPAYMENTS_BUY_OIL, 'FM999999990.00') prepaymentsBuyOil,
        TO_CHAR(PREPAYMENTS_BUY_GAS, 'FM999999990.00') prepaymentsBuyGas,
        OIL_CARD_NO oilCardNo,
        GAS_CARD_NO gasCardNo,
        TO_CHAR(PREPAYMENTS, 'FM999999990.00') prepayments,
        TO_CHAR(NODE_TIME, 'yyyy-mm-dd hh24:mi:ss') nodeTime,
        CASE
            WHEN INS_STATE = 1 THEN TO_CHAR(NVL(ALL_FREIGHT, 0) + NVL(INS_FEE, 0), 'FM999999990.00')
            ELSE TO_CHAR(NVL(ALL_FREIGHT, 0), 'FM999999990.00')
        END payableFreight,
        CASE
            WHEN INS_STATE = 1 THEN TO_CHAR(NVL(INS_FEE, 0), 'FM999999990.00')
            ELSE '0.00'
        END insFee,
        PAY_TYPE payType,
        PAY_NAME payName,
        PAY_BANK_NO payBankNo,
        PAY_BANK_NAME payBankName,
        PAY_MOBILE_NO payMobileNo,
        PAY_ID_CARD payIdCard,
        PROVINCE province,
        CITY_NAME cityName,
        IS_DEL isDel,
        CREATED_TIME createdTime,
        LAST_MODIFIED_TIME lastModifiedTime,
        NOTE note,
        SETTLE_FLAG settleFlag,
        FIXED_COSTS fixedCosts
    </sql>

    <select id="queryTaskAllocateByBoTransTaskId" resultType="com.wtyt.dao.bean.syf.BoTransTaskAllocateBean" parameterType="java.lang.String">
        select
        <include refid="commonColumn"/>
        from T_BO_TRANS_TASK_ALLOCATE
        where BO_TRANS_TASK_ID = #{boTransTaskId} and is_del = 0
    </select>
    <select id="queryTaskAllocateByBoTaxWaybillId" resultType="com.wtyt.dao.bean.syf.BoTransTaskAllocateBean"
            parameterType="java.lang.String">
        select
        <include refid="commonColumn"/>
        from T_BO_TRANS_TASK_ALLOCATE
        where TAX_WAYBILL_ID = #{taxWaybillId} and is_del = 0
    </select>

    <update id="syncWaybillInfo">
        UPDATE T_BO_TRANS_TASK_ALLOCATE
        SET PAY_STATE           = #{payState},
            ALL_FREIGHT         = #{allFreight},
            USER_FREIGHT        = #{userFreight},
            FREIGHT_INCR        = #{freightIncr},
            LOSS_FEE            = #{lossFee},
            UNIT_PRICE          = #{unitPrice},
            PREPAYMENTS_OILCARD = #{prepaymentsOilcard},
            PREPAYMENTS_GASCARD = #{prepaymentsGascard},
            PREPAYMENTS_BUY_OIL  = #{prepaymentsBuyOil},
            PREPAYMENTS_BUY_GAS  = #{prepaymentsBuyGas},
            OIL_CARD_NO = #{oilCardNo},
            GAS_CARD_NO = #{gasCardNo},
            PREPAYMENTS         = #{prepayments},
            BACK_FEE            = #{backFee},
            PAY_OVER_TIME       = TO_DATE(#{payOverTime}, 'YYYY-MM-DD HH24:MI:SS'),
            PAY_TYPE            = #{payType},
            PAY_NAME            = #{payName},
            PAY_BANK_NO         = #{payBankNo},
            PAY_BANK_NAME       = #{payBankName},
            PAY_MOBILE_NO       = #{payMobileNo},
            PAY_ID_CARD         = #{payIdCard},
            PROVINCE            = #{province},
            CITY_NAME           = #{cityName},
            GOODS_COST          = #{goodsCost},
            FREIGHT_GUARANTEE   = #{freightGuarantee},
            LAST_MODIFIED_TIME  = SYSDATE
        WHERE BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <update id="syncTransTaskEnsure">
        UPDATE T_BO_TRANS_TASK_ALLOCATE T
        SET T.INS_STATE = #{insState},
            <if test="insFee != null">
                T.INS_FEE = #{insFee},
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <select id="queryTaxWaybillIdList" resultType="java.lang.String">
        SELECT TAX_WAYBILL_ID
        FROM (SELECT A.TAX_WAYBILL_ID, ROWNUM RN
              FROM (SELECT T.TAX_WAYBILL_ID
                    FROM T_BO_TRANS_TASK_ALLOCATE T
                    WHERE T.TAX_WAYBILL_ID IS NOT NULL
                      AND T.IS_DEL = 0
                    ORDER BY T.CREATED_TIME) A)
        WHERE RN >= #{startRow}
          AND RN &lt; #{endRow}
    </select>
    <select id="queryTaskAllocate" resultType="com.wtyt.dao.bean.syf.BoTransTaskAllocateBean"
            parameterType="com.wtyt.bo.bean.request.Req1735216Bean">
        select
        <include refid="commonColumn"/>
        from T_BO_TRANS_TASK_ALLOCATE
        where is_del = 0
            <if test="taxWaybillId != null and taxWaybillId != ''">
                AND TAX_WAYBILL_ID = #{taxWaybillId}
            </if>
            <if test="boTransTaskId != null and boTransTaskId != ''">
                AND BO_TRANS_TASK_ID = #{boTransTaskId}
            </if>
    </select>
    <insert id="saveOrUpdate">
        MERGE INTO T_BO_TRANS_TASK_ALLOCATE A
        USING (
            SELECT #{item.boTransTaskId} BO_TRANS_TASK_ID,#{item.orgId} ORG_ID FROM DUAL
        ) B
        ON (A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID AND A.IS_DEL = 0)
        WHEN MATCHED THEN
        UPDATE SET
            A.ORG_ID = B.ORG_ID,
            A.LAST_MODIFIED_TIME = SYSDATE
        WHEN NOT MATCHED THEN
        INSERT
            (BO_TRANS_TASK_ALLOCATE_ID,
            BO_TRANS_TASK_ID,
            ORG_ID,
            PAY_TYPE)
        VALUES
            (
            ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},
            B.BO_TRANS_TASK_ID,
            B.ORG_ID,
            '0'
            )
    </insert>

    <update id="updateSettleMode">
        UPDATE T_BO_TRANS_TASK_ALLOCATE T
        SET T.SETTLE_MODE = 2,
            T.TAX_WAYBILL_ID = NULL,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
        AND T.SETTLE_MODE = 1
        AND T.TAX_WAYBILL_ID IS NOT NULL
        AND T.BO_TRANS_TASK_ALLOCATE_ID = #{boTransTaskAllocateId}
    </update>

</mapper>
