<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoInquireAskMapper">

    <resultMap id="offerResultMap" type="BoAskOBean">
        <result property="boTransOrderId" column="boTransOrderId"/>
        <result property="time" column="time"/>
        <result property="orderCreatedTime" column="orderCreatedTime"/>
        <result property="startProvinceName" column="startProvinceName"/>
        <result property="startCityName" column="startCityName"/>
        <result property="startCountyName" column="startCountyName"/>
        <result property="startAddress" column="startAddress"/>
        <result property="endProvinceName" column="endProvinceName"/>
        <result property="endCityName" column="endCityName"/>
        <result property="endCountyName" column="endCountyName"/>
        <result property="endAddress" column="endAddress"/>
        <result property="mileage" column="mileage"/>
        <result property="goodsInfo" column="goodsInfo"/>
        <result property="goodsName" column="goodsName"/>
        <result property="goodsWeight" column="goodsWeight"/>
        <result property="goodsVolume" column="goodsVolume"/>
        <result property="offerId" column="offerId"/>
        <result property="customerOrderNo" column="customerOrderNo"/>
        <result property="boInquireAskId" column="boInquireAskId"/>
        <result property="applyState" column="applyState"/>
        <result property="customerName" column="customerName"/>
        <result property="userId" column="userId"/>
        <result property="orgId" column="orgId"/>
        <association property="offerInfo" javaType="com.wtyt.dao.bean.syf.BoAskOBean$OfferInfo">
            <result property="allFee" column="allFee"/>
            <result property="oilFee" column="oilFee"/>
            <result property="oilRatio" column="oilRatio"/>
        </association>
    </resultMap>

    <insert id="insert">
        INSERT INTO
        T_BO_INQUIRE_ASK (BO_INQUIRE_ASK_ID,
        BO_TRANS_ORDER_ID,
        CREATED_USER_ID,
        USER_ID,
        ASK_STATE,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE)
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="dataList" separator="UNION ALL">
            SELECT #{item.boInquireAskId} boInquireAskId,
            #{item.boTransOrderId} boTransPlanId,
            #{item.createdUserId} createdUserId,
            #{item.userId} userId,
            #{item.askState} askState,
            0 isDel,
            SYSDATE createdTime,
            SYSDATE lastModifiedTime,
            NULL note
            FROM DUAL
        </foreach>
        ) A
    </insert>
    
    <select id="getOfferList" parameterType="BoAskIBean" resultMap="offerResultMap">
        SELECT
            tbto.BO_TRANS_ORDER_ID boTransOrderId,
            tbto.CUSTOMER_ORDER_NO customerOrderNo,
            bia.BO_INQUIRE_ASK_ID boInquireAskId,
            <if test="state == '0'.toString()">
                TO_CHAR(bia.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') time,
            </if>
            <if test="state == '1'.toString()">
                TO_CHAR(bio.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') time,
                TO_CHAR(bio.ALL_FEE, 'FM999999990.00') allFee,
                TO_CHAR(bio.OIL_FEE, 'FM999999990.00') oilFee,
                bio.OIL_RATIO oilRatio,
                bio.APPLY_STATE applyState,
                bio.BO_INQUIRE_OFFER_ID offerId,
                bio.APPLY_STATE,
            </if>
            tbto.START_PROVINCE_NAME startProvinceName,
            tbto.START_CITY_NAME startCityName,
            tbto.START_COUNTY_NAME startCountyName,
            tbto.START_ADDRESS startAddress,
            tbto.END_PROVINCE_NAME endProvinceName,
            tbto.END_CITY_NAME endCityName,
            tbto.END_COUNTY_NAME endCountyName,
            tbto.END_ADDRESS endAddress,
            tbto.MILEAGE mileage,
            tbto.GOODS_NAME goodsName,
            NVL2(tbto.GOODS_WEIGHT,TO_CHAR(tbto.GOODS_WEIGHT,'FM999999990.0000'),'') goodsWeight,
            NVL2(tbto.GOODS_VOLUME,TO_CHAR(tbto.GOODS_VOLUME,'FM999999990.0000'),'') goodsVolume
        FROM
            T_BO_INQUIRE_ASK bia
        LEFT JOIN T_BO_TRANS_ORDER tbto ON
            bia.BO_TRANS_ORDER_ID = tbto.BO_TRANS_ORDER_ID
        LEFT JOIN T_BO_INQUIRE tbi ON
            tbi.BO_TRANS_ORDER_ID = tbto.BO_TRANS_ORDER_ID
        <if test="state == '1'.toString()">
            LEFT JOIN T_BO_INQUIRE_OFFER bio ON
            bio.BO_INQUIRE_ASK_ID = bia.BO_INQUIRE_ASK_ID and bio.IS_DEL = 0
        </if>
        <include refid="offerListWhereCondition"/>
        <if test="state == '0'.toString()">
            order by bia.CREATED_TIME
        </if>
        <if test="state == '1'.toString()">
            order by bio.CREATED_TIME desc
        </if>
    </select>

    <sql id="offerListWhereCondition">
        where bia.is_del = 0
        and tbto.is_del = 0
        <if test="state == '1'.toString()">
            <if test="applyState != null and applyState != ''">
               AND bio.APPLY_STATE = #{applyState}
            </if>
        </if>
        <if test="customerOrderNoList != null and customerOrderNoList.size() > 0">
            and
            <foreach item="item" index="index" collection="customerOrderNoList" separator="OR" open="(" close=")">
                (tbto.CUSTOMER_ORDER_NO LIKE '%' || #{item} || '%')
            </foreach>
        </if>
        and bia.user_id = #{userId}
        and bia.ASK_STATE = #{state}
        and tbto.ORG_ID = #{orgId}
        <if test="state == '0'.toString()">
            and tbi.INQUIRE_STATE = 2
        </if>
        <if test="state == '1'.toString()">
            and tbi.INQUIRE_STATE in(2,3)
            and INQUIRE_OVER_TIME_STATE = 0
        </if>
    </sql>

    <select id="getOfferListCount" resultType="java.lang.Integer">
        select count(1)
        FROM
        T_BO_INQUIRE_ASK bia
        LEFT JOIN T_BO_TRANS_ORDER tbto ON
        bia.BO_TRANS_ORDER_ID = tbto.BO_TRANS_ORDER_ID
        LEFT JOIN T_BO_INQUIRE tbi ON
        tbi.BO_TRANS_ORDER_ID = tbto.BO_TRANS_ORDER_ID
        <if test="state == '1'.toString()">
            LEFT JOIN T_BO_INQUIRE_OFFER bio ON
            bio.BO_INQUIRE_ASK_ID = bia.BO_INQUIRE_ASK_ID and bio.IS_DEL = 0
        </if>
        <include refid="offerListWhereCondition"/>
    </select>

    <select id="getOfferListForDebang" parameterType="BoAskIBean" resultMap="offerResultMap">
        SELECT
        tbto.BO_TRANS_ORDER_ID boTransOrderId,
        tbto.CUSTOMER_ORDER_NO customerOrderNo,
        bia.BO_INQUIRE_ASK_ID boInquireAskId,
        TO_CHAR(tbto.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') orderCreatedTime,
        <if test="state == '0'.toString()">
            TO_CHAR(bia.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') time,
        </if>
        <if test="state == '1'.toString()">
            TO_CHAR(bio.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') time,
            TO_CHAR(bio.ALL_FEE, 'FM999999990.00') allFee,
            TO_CHAR(bio.OIL_FEE, 'FM999999990.00') oilFee,
            bio.OIL_RATIO oilRatio,
            bio.APPLY_STATE applyState,
            bio.BO_INQUIRE_OFFER_ID offerId,
            bio.APPLY_STATE,
        </if>
        tbto.START_PROVINCE_NAME startProvinceName,
        tbto.START_CITY_NAME startCityName,
        tbto.START_COUNTY_NAME startCountyName,
        tbto.START_ADDRESS startAddress,
        tbto.END_PROVINCE_NAME endProvinceName,
        tbto.END_CITY_NAME endCityName,
        tbto.END_COUNTY_NAME endCountyName,
        tbto.END_ADDRESS endAddress,
        tbto.MILEAGE mileage,
        tbto.GOODS_NAME goodsName,
        tbto.CUSTOMER_NAME customerName,
        NVL2(tbto.GOODS_WEIGHT,TO_CHAR(tbto.GOODS_WEIGHT,'FM999999990.0000'),'') goodsWeight,
        NVL2(tbto.GOODS_VOLUME,TO_CHAR(tbto.GOODS_VOLUME,'FM999999990.0000'),'') goodsVolume
        FROM
        T_BO_INQUIRE_ASK bia
        LEFT JOIN T_BO_TRANS_ORDER tbto ON
        bia.BO_TRANS_ORDER_ID = tbto.BO_TRANS_ORDER_ID
        <if test="state == '1'.toString()">
            LEFT JOIN T_BO_INQUIRE_OFFER bio ON
            bio.BO_INQUIRE_ASK_ID = bia.BO_INQUIRE_ASK_ID and bio.IS_DEL = 0
        </if>
        <include refid="debangOfferListWhereCondition"/>
        <if test="state == '0'.toString()">
            order by tbto.CREATED_TIME desc
        </if>
        <if test="state == '1'.toString()">
            order by bio.CREATED_TIME desc
        </if>
    </select>

    <sql id="debangOfferListWhereCondition">
        where bia.is_del = 0
        and tbto.is_del = 0
        <if test="state == '1'.toString()">
            <if test="applyState != null and applyState != ''">
                AND bio.APPLY_STATE = #{applyState}
            </if>
        </if>
        <if test="customerOrderNoList != null and customerOrderNoList.size() > 0">
            and
            <foreach item="item" index="index" collection="customerOrderNoList" separator="OR" open="(" close=")">
                (tbto.CUSTOMER_ORDER_NO LIKE '%' || #{item} || '%')
            </foreach>
        </if>
        and bia.ASK_STATE = #{state}
        and tbto.ORG_ID = #{orgId}
    </sql>

    <select id="getOfferListCountForDebang" parameterType="BoAskIBean" resultType="java.lang.Integer">
        select COUNT(1)
        FROM
        T_BO_INQUIRE_ASK bia
        LEFT JOIN T_BO_TRANS_ORDER tbto ON
        bia.BO_TRANS_ORDER_ID = tbto.BO_TRANS_ORDER_ID
        <if test="state == '1'.toString()">
            LEFT JOIN T_BO_INQUIRE_OFFER bio ON
            bio.BO_INQUIRE_ASK_ID = bia.BO_INQUIRE_ASK_ID and bio.IS_DEL = 0
        </if>
        <include refid="debangOfferListWhereCondition"/>
    </select>


    <select id="getAskInfo" resultMap="offerResultMap">
        SELECT
        tbio.CREATED_USER_ID userId,
        TO_CHAR(tbio.ALL_FEE, 'FM999999990.00') allFee,
        tbto.org_id orgId,
        tbto.BO_TRANS_ORDER_ID boTransOrderId,
        tbto.CUSTOMER_ORDER_NO customerOrderNo,
        tbto.START_PROVINCE_NAME startProvinceName,
        tbto.START_CITY_NAME startCityName,
        tbto.START_COUNTY_NAME startCountyName,
        tbto.START_ADDRESS startAddress,
        tbto.END_PROVINCE_NAME endProvinceName,
        tbto.END_CITY_NAME endCityName,
        tbto.END_COUNTY_NAME endCountyName,
        tbto.END_ADDRESS endAddress,
        tbto.MILEAGE mileage,
        tbto.GOODS_NAME goodsName,
        tbto.CUSTOMER_NAME customerName,
        NVL2(tbto.GOODS_WEIGHT,TO_CHAR(tbto.GOODS_WEIGHT,'FM999999990.0000'),'') goodsWeight,
        NVL2(tbto.GOODS_VOLUME,TO_CHAR(tbto.GOODS_VOLUME,'FM999999990.0000'),'') goodsVolume
        FROM
        T_BO_INQUIRE_OFFER tbio
        LEFT JOIN T_BO_TRANS_ORDER tbto ON
        tbio.BO_TRANS_ORDER_ID = tbto.BO_TRANS_ORDER_ID
        where tbio.is_del = 0
        and tbto.is_del = 0
        and tbio.BO_TRANS_ORDER_ID = #{boTransOrderId}
        and rownum = 1
    </select>

    <select id="checkOfferStateByOrderId" resultType="string">
        SELECT
            BO_INQUIRE_OFFER_ID
        FROM
            T_BO_INQUIRE_OFFER
        WHERE
            BO_TRANS_ORDER_ID = #{boTransOrderId}
            AND IS_DEL = 0
            AND rownum = 1
    </select>

</mapper>