<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardOverviewMapper">


    <select id="countAll" parameterType="com.wtyt.board.bean.BoardOverviewQueryBean" resultType="com.wtyt.board.bean.BossNodeCountBean">
        SELECT
            D.BOSS_NODE_CODE bossNodeCode,
            COUNT(1) bossNodeCount,
            SUM(A.BOSS_WAYBILL_HAS_EXCEPTION) exceptionCount
        FROM T_BO_WAYBILL_BOSS_NODE A
            JOIN T_BO_TRANS_TASK B ON A.BO_TRANS_TASK_ID =B.BO_TRANS_TASK_ID AND B.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT C ON A.BOSS_NODE_CODE = C.BOSS_NODE_CODE AND C.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT D ON C.BOSS_NODE_PID = D.BO_TRANS_BOSS_NODE_DICT_ID AND D.IS_DEL =0
        WHERE A.IS_DEL =0 AND B.ORG_ID IN
            <foreach collection="queryOrgIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            <if test="beginTime!=null and  beginTime!=''">
                AND B.CREATED_TIME &gt;= TO_DATE(#{beginTime}||'00:00:00','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endTime!=null and  endTime!=''">
                AND B.CREATED_TIME &lt;= TO_DATE(#{endTime}||'23:59:59','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="queryWbItem != null and queryWbItem !=''">
                AND B.WB_ITEM = #{queryWbItem}
            </if>
        GROUP BY D.BOSS_NODE_CODE
    </select>

    <select id="mouthPlanCountList" parameterType="com.wtyt.board.bean.BoardOverviewQueryBean" resultType="com.wtyt.board.bean.BossMouthPlanCountBean">
        SELECT
        TO_CHAR(B.CREATED_TIME,'YYYY-MM') AS yearMonth,
        COUNT(1) planCount
        FROM T_BO_WAYBILL_BOSS_NODE A
        JOIN T_BO_TRANS_TASK B ON A.BO_TRANS_TASK_ID =B.BO_TRANS_TASK_ID AND B.IS_DEL =0
        WHERE A.IS_DEL =0 AND B.ORG_ID IN
        <foreach collection="queryOrgIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="beginTime!=null and  beginTime!=''">
            AND B.CREATED_TIME &gt;= TO_DATE(#{beginTime}||'00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime!=null and  endTime!=''">
            AND B.CREATED_TIME &lt;= TO_DATE(#{endTime}||'23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="queryWbItem != null and queryWbItem !=''">
            AND B.WB_ITEM = #{queryWbItem}
        </if>
        GROUP BY TO_CHAR(B.CREATED_TIME,'YYYY-MM')
        order by TO_CHAR(B.CREATED_TIME,'YYYY-MM')
    </select>



    <select id="findBoardWaybill" resultType="com.wtyt.board.bean.TaxWaybillBossNodeBean">
        select
        w.BO_TRANS_TASK_ID boTransTaskId,
        w.TAX_WAYBILL_ID taxWaybillId,
        w.TAX_WAYBILL_NO taxWaybillNo,
        w.MOBILE_NO mobileNo,
        w.ORG_ID orgId,
        w.START_PROVINCE_NAME startProvinceName,
        w.START_CITY_NAME startCityName,
        w.START_COUNTY_NAME startCountyName,
        w.END_PROVINCE_NAME endProvinceName,
        w.END_CITY_NAME endCityName,
        w.END_COUNTY_NAME endCountyName,
        w.PAY_STATE payState,
        w.DRIVER_NAME driverName,
        w.CART_BADGE_NO cartBadgeNo,
        w.GOODS_NAME goodsName,
        CASE WHEN to_char(w.GOODS_AMOUNT_TYPE) = '0' THEN TO_CHAR(w.GOODS_AMOUNT, 'FM999999990.0000') ELSE TO_CHAR(w.GOODS_AMOUNT, 'FM999999990.00') END goodsAmount,
        w.GOODS_AMOUNT_TYPE goodsAmountType,
        TO_CHAR(w.PAY_OVER_TIME,'YYYY-MM-DD HH24:MI:SS') payOverTime,
        TO_CHAR(NVL(w.USER_FREIGHT, 0), 'FM999999990.00') userFreight,
        e.LOADING_ADDRESS_NAME loadingAddressName,
        e.UNLOADING_ADDRESS_NAME unLoadingAddressName,
        w.XCY_USER_ID xcyUserId,
        NVL(w.TRANS_PATTERN,1) transPattern,
        r.SYS_ROLE_TYPE sysRoleType,
        r.user_id createdUserId,
        N.BOSS_NODE_CODE bossNodeCode,
        e.CART_TYPE cartType,
        e.CART_LENGTH cartLength,
        TO_CHAR(L.DEAD_LINE_TIME, 'YYYY-MM-DD HH24:MI:SS') promisedArriveTime
        from T_BO_TRANS_TASK w  left  join
        T_BO_TRANS_NODE_RECORD R on R.BO_TRANS_TASK_ID = W.BO_TRANS_TASK_ID and r.node_id=100 and r.is_del = 0
        left join T_BO_TRANS_NODE_DEAD_LINE L on L.BO_TRANS_TASK_ID = W.BO_TRANS_TASK_ID and L.is_del=0 and L.node_id = 500,
        T_BO_WAYBILL_BOSS_NODE N,
        T_BO_TRANS_TASK_EXTRA E
        where
        w.BO_TRANS_TASK_ID = N.BO_TRANS_TASK_ID
        and w.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
        and w.is_del = 0
        and n.is_del = 0
        <choose>
            <when test="boTransTaskId != null and boTransTaskId != ''">
                and w.BO_TRANS_TASK_ID = #{boTransTaskId}
            </when>
            <otherwise>
                and w.tax_waybill_id = #{taxWaybillId}
            </otherwise>
        </choose>
    </select>
    <select id="countRecent" parameterType="com.wtyt.board.bean.BoardOverviewQueryBean" resultType="com.wtyt.board.bean.BossNodeCountBean">
        SELECT
            TO_CHAR(B.CREATED_TIME,'YYYY-MM-DD') planDate,
            D.BOSS_NODE_CODE bossNodeCode,
            COUNT(1) bossNodeCount,
            SUM(A.BOSS_WAYBILL_HAS_EXCEPTION) exceptionCount
        FROM T_BO_WAYBILL_BOSS_NODE A
            JOIN T_BO_TRANS_TASK B ON A.BO_TRANS_TASK_ID =B.BO_TRANS_TASK_ID AND B.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT C ON A.BOSS_NODE_CODE = C.BOSS_NODE_CODE AND C.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT D ON C.BOSS_NODE_PID = D.BO_TRANS_BOSS_NODE_DICT_ID AND D.IS_DEL =0
        WHERE A.IS_DEL =0 AND B.ORG_ID IN
            <foreach collection="queryOrgIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            <if test="queryWbItem != null and queryWbItem !=''">
                AND B.WB_ITEM = #{queryWbItem}
            </if>
            AND B.CREATED_TIME &gt;= TO_DATE(#{beginTime}||'00:00:00','YYYY-MM-DD HH24:MI:SS')
        GROUP BY TO_CHAR(B.CREATED_TIME,'YYYY-MM-DD'),D.BOSS_NODE_CODE
    </select>

    <select id="countSecondAll" parameterType="com.wtyt.board.bean.BoardOverviewQueryBean" resultType="com.wtyt.board.bean.BossNodeCountBean">
        SELECT
            A.BOSS_NODE_CODE bossNodeCode,
            COUNT(1) bossNodeCount,
            SUM(A.BOSS_WAYBILL_HAS_EXCEPTION) exceptionCount
        FROM T_BO_WAYBILL_BOSS_NODE A
            JOIN T_BO_TRANS_TASK B ON A.BO_TRANS_TASK_ID =B.BO_TRANS_TASK_ID AND B.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT C ON A.BOSS_NODE_CODE = C.BOSS_NODE_CODE AND C.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT D ON C.BOSS_NODE_PID = D.BO_TRANS_BOSS_NODE_DICT_ID AND D.IS_DEL =0
            LEFT JOIN T_BO_TRANS_NODE_RECORD E ON A.BO_TRANS_TASK_ID =E.BO_TRANS_TASK_ID AND E.IS_DEL =0 AND E.NODE_ID =100
        WHERE A.IS_DEL =0 AND B.ORG_ID IN
            <foreach collection="queryOrgIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            <if test="beginTime!=null and  beginTime!=''">
                AND B.CREATED_TIME &gt;= TO_DATE(#{beginTime}||'00:00:00','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endTime!=null and  endTime!=''">
                AND B.CREATED_TIME &lt;= TO_DATE(#{endTime}||'23:59:59','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="childNodeCode!=null and childNodeCode!=''">
                AND A.BOSS_NODE_CODE = #{childNodeCode}
            </if>
            <if test="nodeCode!=null and nodeCode!=''">
                AND D.BOSS_NODE_CODE = #{nodeCode}
            </if>
            <if test="driverName!=null and driverName!=''">
                AND B.DRIVER_NAME = #{driverName}
            </if>
            <if test="cartBadgeNo!=null and cartBadgeNo!=''">
                AND B.CART_BADGE_NO = #{cartBadgeNo}
            </if>
            <if test="taxWaybillNo!=null and taxWaybillNo!=''">
                AND B.TAX_WAYBILL_NO = #{taxWaybillNo}
            </if>
            <if test="sceneUserId!=null and sceneUserId!=''">
                AND (B.XCY_USER_ID = #{sceneUserId} OR (E.USER_ID = #{sceneUserId} AND E.SYS_ROLE_TYPE = 11))
            </if>
            <if test="dispatchUserId!=null and dispatchUserId!=''">
                AND E.USER_ID = #{dispatchUserId}
            </if>
            <if test="waybillState==1">
                AND A.BOSS_WAYBILL_HAS_EXCEPTION =0
            </if>
            <if test="waybillState==2">
                AND A.BOSS_WAYBILL_HAS_EXCEPTION =1
            </if>
            <if test="queryWbItem != null and queryWbItem !=''">
                AND B.WB_ITEM = #{queryWbItem}
            </if>
        GROUP BY A.BOSS_NODE_CODE
    </select>
    <select id="getPlanDate" parameterType="com.wtyt.board.bean.BoardOverviewQueryBean" resultType="com.wtyt.board.bean.BossNodeCountBean">
        SELECT
            TO_CHAR(B.CREATED_TIME,'YYYY-MM-DD') planDate
        FROM T_BO_WAYBILL_BOSS_NODE A
            JOIN T_BO_TRANS_TASK B ON A.BO_TRANS_TASK_ID =B.BO_TRANS_TASK_ID AND B.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT C ON A.BOSS_NODE_CODE = C.BOSS_NODE_CODE AND C.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT D ON C.BOSS_NODE_PID = D.BO_TRANS_BOSS_NODE_DICT_ID AND D.IS_DEL =0
            LEFT JOIN T_BO_TRANS_NODE_RECORD E ON A.BO_TRANS_TASK_ID =E.BO_TRANS_TASK_ID AND E.IS_DEL =0 AND E.NODE_ID =100
        WHERE A.IS_DEL =0 AND B.ORG_ID IN
            <foreach collection="queryOrgIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            <if test="beginTime!=null and  beginTime!=''">
                AND B.CREATED_TIME &gt;= TO_DATE(#{beginTime}||'00:00:00','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endTime!=null and  endTime!=''">
                AND B.CREATED_TIME &lt;= TO_DATE(#{endTime}||'23:59:59','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="childNodeCode!=null and childNodeCode!=''">
                AND A.BOSS_NODE_CODE = #{childNodeCode}
            </if>
            <if test="nodeCode!=null and nodeCode!=''">
                AND D.BOSS_NODE_CODE = #{nodeCode}
            </if>
            <if test="driverName!=null and driverName!=''">
                AND B.DRIVER_NAME = #{driverName}
            </if>
            <if test="cartBadgeNo!=null and cartBadgeNo!=''">
                AND B.CART_BADGE_NO = #{cartBadgeNo}
            </if>
            <if test="taxWaybillNo!=null and taxWaybillNo!=''">
                AND B.TAX_WAYBILL_NO = #{taxWaybillNo}
            </if>
            <if test="sceneUserId!=null and sceneUserId!=''">
                AND (B.XCY_USER_ID = #{sceneUserId} OR (E.USER_ID = #{sceneUserId} AND E.SYS_ROLE_TYPE = 11))
            </if>
            <if test="dispatchUserId!=null and dispatchUserId!=''">
                AND E.USER_ID = #{dispatchUserId}
                AND E.SYS_ROLE_TYPE = 12000
            </if>
            <if test="waybillState==1">
                AND A.BOSS_WAYBILL_HAS_EXCEPTION =0
            </if>
            <if test="waybillState==2">
                AND A.BOSS_WAYBILL_HAS_EXCEPTION =1
            </if>
            <if test="queryWbItem != null and queryWbItem !=''">
                AND B.WB_ITEM = #{queryWbItem}
            </if>
        GROUP BY TO_CHAR(B.CREATED_TIME,'YYYY-MM-DD')
        ORDER BY TO_CHAR(B.CREATED_TIME,'YYYY-MM-DD') DESC
    </select>
    <select id="countByPlanDateAndOrg" parameterType="com.wtyt.board.bean.BoardOverviewQueryBean" resultType="com.wtyt.board.bean.BossNodeCountBean">
        SELECT
            TO_CHAR(B.CREATED_TIME,'YYYY-MM-DD') planDate,
            B.ORG_ID orgId,
            B.TAX_WAYBILL_ID taxWaybillId,
            B.BO_TRANS_TASK_ID boTransTaskId,
            A.BOSS_NODE_CODE bossNodeCode,
            1 bossNodeCount,
            SUM(A.BOSS_WAYBILL_HAS_EXCEPTION) exceptionCount
        FROM T_BO_WAYBILL_BOSS_NODE A
            JOIN T_BO_TRANS_TASK B ON A.BO_TRANS_TASK_ID =B.BO_TRANS_TASK_ID AND B.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT C ON A.BOSS_NODE_CODE = C.BOSS_NODE_CODE AND C.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT D ON C.BOSS_NODE_PID = D.BO_TRANS_BOSS_NODE_DICT_ID AND D.IS_DEL =0
            LEFT JOIN T_BO_TRANS_NODE_RECORD E ON A.BO_TRANS_TASK_ID =E.BO_TRANS_TASK_ID AND E.IS_DEL =0 AND E.NODE_ID =100
        WHERE <include refid="secondQueryCondition"></include>
        GROUP BY TO_CHAR(B.CREATED_TIME,'YYYY-MM-DD'),B.ORG_ID,B.TAX_WAYBILL_ID,B.BO_TRANS_TASK_ID,A.BOSS_NODE_CODE
        ORDER BY TO_CHAR(B.CREATED_TIME,'YYYY-MM-DD') DESC,B.ORG_ID ASC
    </select>
    <sql id="secondQueryCondition">
        A.IS_DEL =0 AND B.ORG_ID IN
        <foreach collection="queryOrgIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="beginTime!=null and  beginTime!=''">
            AND B.CREATED_TIME &gt;=to_date(#{beginTime}||'00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime!=null and  endTime!=''">
            AND B.CREATED_TIME &lt;=to_date(#{endTime}||'23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="childNodeCode!=null and childNodeCode!=''">
            AND A.BOSS_NODE_CODE = #{childNodeCode}
        </if>
        <if test="nodeCode!=null and nodeCode!=''">
            AND D.BOSS_NODE_CODE = #{nodeCode}
        </if>
        <if test="driverName!=null and driverName!=''">
            AND B.DRIVER_NAME = #{driverName}
        </if>
        <if test="cartBadgeNo!=null and cartBadgeNo!=''">
            AND B.CART_BADGE_NO = #{cartBadgeNo}
        </if>
        <if test="taxWaybillNo!=null and taxWaybillNo!=''">
            AND B.TAX_WAYBILL_NO = #{taxWaybillNo}
        </if>
        <if test="sceneUserId!=null and sceneUserId!=''">
            AND (B.XCY_USER_ID = #{sceneUserId} OR (E.USER_ID = #{sceneUserId} AND E.SYS_ROLE_TYPE = 11))
        </if>
        <if test="dispatchUserId!=null and dispatchUserId!=''">
            AND E.USER_ID = #{dispatchUserId}
        </if>
        <if test="waybillState==1">
            AND A.BOSS_WAYBILL_HAS_EXCEPTION =0
        </if>
        <if test="waybillState==2">
            AND A.BOSS_WAYBILL_HAS_EXCEPTION =1
        </if>
        <if test="queryWbItem != null and queryWbItem !=''">
            AND B.WB_ITEM = #{queryWbItem}
        </if>
    </sql>

    <select id="queryWayBillNodeInfo" resultType="com.wtyt.board.bean.BoardWaybillInfoBean">
        SELECT
            A.BO_TRANS_TASK_ID boTransTaskId,
            A.ORG_ID orgId,
            NVL(A.TAX_WAYBILL_ID, TA.TAX_WAYBILL_ID) waybillId,
            A.TAX_WAYBILL_NO waybillNo,
            A.START_CITY_NAME startCityName,
            A.START_COUNTY_NAME startCountyName,
            A.END_CITY_NAME endCityName,
            A.END_COUNTY_NAME endCountyName,
            A.GOODS_NAME goodsName,
            A.GOODS_AMOUNT goodsAmount,
            A.GOODS_AMOUNT_TYPE goodsAmountType,
            B.CART_TYPE cartType,
            B.CART_LENGTH cartLength,
            D.BOSS_NODE_NAME childNodeName,
            E.BOSS_NODE_NAME nodeName,
            A.CREATED_USER_SYS_ROLE_TYPE dispatchSysRoleType,
            A.CREATED_USER_ID dispatchUserId,
            A.CREATED_USER_JOB_NAME jobName
        FROM T_BO_TRANS_TASK A
            JOIN T_BO_TRANS_TASK_EXTRA B ON A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID AND B.IS_DEL =0
            JOIN T_BO_WAYBILL_BOSS_NODE C ON C.BO_TRANS_TASK_ID = A.BO_TRANS_TASK_ID AND C.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT D ON C.BOSS_NODE_CODE = D.BOSS_NODE_CODE AND D.IS_DEL =0
            JOIN T_BO_TRANS_BOSS_NODE_DICT E ON D.BOSS_NODE_PID = E.BO_TRANS_BOSS_NODE_DICT_ID AND E.IS_DEL =0
            LEFT JOIN T_BO_TRANS_TASK_ALLOCATE TA ON A.BO_TRANS_TASK_ID = TA.BO_TRANS_TASK_ID AND TA.IS_DEL=0
        WHERE A.IS_DEL =0
        <choose>
            <when test="boTransTaskIds!=null and boTransTaskIds.size > 0">
                AND A.BO_TRANS_TASK_ID IN
                <foreach collection="boTransTaskIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND A.TAX_WAYBILL_ID IN
                <foreach collection="taxWaybillIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        ORDER BY C.BOSS_WAYBILL_HAS_EXCEPTION DESC,C.BOSS_NODE_CODE ASC,A.TAX_WAYBILL_NO DESC
    </select>
    <select id="queryWayBillUnDealExceptionInfo" resultType="com.wtyt.board.bean.BoardWaybillInfoBean">
        SELECT
            A.BO_TRANS_TASK_ID boTransTaskId,
            TO_CHAR(A.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
            A.NODE_DATA_TYPE nodeDataType,
            A.ALARM_TYPE alarmType,
            CASE WHEN A.ALARM_TYPE = 0 THEN ROUND((NVL(A.ALARM_START_TIME, SYSDATE) - SYSDATE) * 24 * 3600)
                 WHEN ALARM_TYPE = 1 THEN ROUND((NVL(A.ALARM_END_TIME, SYSDATE) - NVL(A.ALARM_START_TIME, A.CREATED_TIME)) * 24 * 3600)
            END overTime
        FROM
            T_BO_TRANS_NODE_ALARM A
        JOIN T_BO_ROLE_ALARM_TYPE_REL B ON
            A.NODE_DATA_TYPE = B.NODE_DATA_TYPE
            AND B.ROLE_ID = 12002
            AND B.IS_DEL = 0
        WHERE
            A.IS_DEL = 0
            AND A.ALARM_PROCESS_RESULT = 0
            AND (A.NODE_DATA_TYPE = 4 OR A.ALARM_TYPE!=0)
            AND A.BO_TRANS_TASK_ID IN
            <foreach collection="boTransTaskIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
    </select>


    <select id="queryOrgIgnoreAlarm" resultType="String">
        SELECT
            NODE_DATA_TYPE as nodeDataType
        FROM
        T_BO_BOSS_NODE_ALARM_IGNOR A  where  A.ORG_ID = #{orgId}  and A.IS_DEL =0
    </select>
    <resultMap id="boardExceptionMap" type="com.wtyt.board.bean.BoardExceptionTaskBean">
        <id column="BO_TRANS_TASK_ID" property="boTransTaskId"></id>
        <result column="ORG_ID" property="orgId"></result>
        <result column="CREATED_TIME" property="createdTime"></result>
        <result column="TAX_WAYBILL_NO" property="taxWaybillNo"></result>
        <result column="CREATED_USER_JOB_NAME" property="createdUserJobName"></result>
        <result column="CREATED_USER_ID" property="createdUserId"></result>
        <result column="GOODS_NAME" property="goodsName"></result>
        <result column="GOODS_AMOUNT" property="goodsAmount"></result>
        <result column="GOODS_AMOUNT_TYPE" property="goodsAmountType"></result>
        <result column="XCY_USER_ID" property="xcyUserId"></result>
        <result column="CART_BADGE_NO" property="cartBadgeNo"></result>
        <result column="DRIVER_NAME" property="driverName"></result>
        <result column="MOBILE_NO" property="driverMobileNo"></result>
        <result column="LOADING_ADDRESS_NAME" property="loadingAddressName"></result>
        <result column="UNLOADING_ADDRESS_NAME" property="unloadingAddressName"></result>
        <result column="CART_TYPE" property="cartType"></result>
        <result column="CART_LENGTH" property="cartLength"></result>
        <collection property="currentAlarms" ofType="com.wtyt.dao.bean.syf.BoTransNodeAlarmBean">
            <id column="BO_TRANS_NODE_ALARM_ID" property="boTransNodeAlarmId"></id>
            <result column="ALARM_TYPE" property="alarmType"></result>
            <result column="NODE_DATA_TYPE" property="nodeDataType"></result>
            <result column="OVER_TIME" property="overTime"></result>
        </collection>
    </resultMap>
    <select id="queryBoardExceptionTasks" resultMap="boardExceptionMap" >
        SELECT
            B.BO_TRANS_TASK_ID ,
            B.ORG_ID ,
            TO_CHAR(B.CREATED_TIME, 'YYYY/MM/DD HH24:MI:SS') CREATED_TIME,
            B.TAX_WAYBILL_NO ,
            B.CREATED_USER_JOB_NAME ,
            B.CREATED_USER_SYS_ROLE_TYPE ,
            B.CREATED_USER_ID ,
            B.GOODS_NAME ,
            B.GOODS_AMOUNT ,
            B.GOODS_AMOUNT_TYPE ,
            B.XCY_USER_ID ,
            B.CART_BADGE_NO ,
            B.DRIVER_NAME ,
            B.MOBILE_NO ,
            B.START_PROVINCE_NAME || B.START_CITY_NAME || B.START_COUNTY_NAME || F.LOADING_ADDRESS_NAME  LOADING_ADDRESS_NAME,
            B.END_PROVINCE_NAME || B.END_CITY_NAME || B.END_COUNTY_NAME || F.UNLOADING_ADDRESS_NAME UNLOADING_ADDRESS_NAME,
            F.CART_TYPE ,
            F.CART_LENGTH ,
            G.BO_TRANS_NODE_ALARM_ID ,
            G.ALARM_TYPE ,
            G.NODE_DATA_TYPE ,
            CASE WHEN G.ALARM_TYPE = 0 THEN ROUND((NVL(G.ALARM_START_TIME, SYSDATE) - SYSDATE) * 24 * 3600)
                 WHEN G.ALARM_TYPE = 1 THEN ROUND((NVL(G.ALARM_END_TIME, SYSDATE) - NVL(G.ALARM_START_TIME, G.CREATED_TIME)) * 24 * 3600)
            END OVER_TIME
        FROM
            T_BO_WAYBILL_BOSS_NODE A
            JOIN T_BO_TRANS_TASK B ON A.BO_TRANS_TASK_ID = B.BO_TRANS_TASK_ID AND B.IS_DEL = 0
            JOIN T_BO_TRANS_BOSS_NODE_DICT C ON A.BOSS_NODE_CODE = C.BOSS_NODE_CODE AND C.IS_DEL = 0
            JOIN T_BO_TRANS_BOSS_NODE_DICT D ON C.BOSS_NODE_PID = D.BO_TRANS_BOSS_NODE_DICT_ID AND D.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD E ON A.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID AND E.IS_DEL = 0 AND E.NODE_ID = 100
            LEFT JOIN T_BO_TRANS_TASK_EXTRA F ON B.BO_TRANS_TASK_ID = F.BO_TRANS_TASK_ID AND F.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_ALARM G ON A.BO_TRANS_TASK_ID = G.BO_TRANS_TASK_ID AND G.IS_DEL = 0 AND G.ALARM_PROCESS_RESULT = 0 AND (G.NODE_DATA_TYPE = 4 OR G.ALARM_TYPE != 0)
        WHERE
            A.IS_DEL = 0 AND B.ORG_ID IN
            <foreach collection="queryOrgIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            <if test="beginTime!=null and  beginTime!=''">
                AND B.CREATED_TIME &gt;= TO_DATE(#{beginTime}||'00:00:00','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endTime!=null and  endTime!=''">
                AND B.CREATED_TIME &lt;= TO_DATE(#{endTime}||'23:59:59','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="childNodeCode!=null and childNodeCode!=''">
                AND A.BOSS_NODE_CODE = #{childNodeCode}
            </if>
            <if test="nodeCode!=null and nodeCode!=''">
                AND D.BOSS_NODE_CODE = #{nodeCode}
            </if>
            <if test="driverName!=null and driverName!=''">
                AND B.DRIVER_NAME = #{driverName}
            </if>
            <if test="cartBadgeNo!=null and cartBadgeNo!=''">
                AND B.CART_BADGE_NO = #{cartBadgeNo}
            </if>
            <if test="taxWaybillNo!=null and taxWaybillNo!=''">
                AND B.TAX_WAYBILL_NO = #{taxWaybillNo}
            </if>
            <if test="sceneUserId!=null and sceneUserId!=''">
                AND (B.XCY_USER_ID = #{sceneUserId} OR (E.USER_ID = #{sceneUserId} AND E.SYS_ROLE_TYPE = 11))
            </if>
            <if test="dispatchUserId!=null and dispatchUserId!=''">
                AND E.USER_ID = #{dispatchUserId}
            </if>
            <if test="waybillState==1">
                AND A.BOSS_WAYBILL_HAS_EXCEPTION =0
            </if>
                AND A.BOSS_WAYBILL_HAS_EXCEPTION =1
            ORDER BY B.CREATED_TIME DESC, G.CREATED_TIME ASC
    </select>

</mapper>
