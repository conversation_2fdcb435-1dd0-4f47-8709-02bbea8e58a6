package com.wtyt.commons.toolkits;

import com.wtyt.common.constants.Constants;
import com.wtyt.common.rpc.bean.Rpc1556007IBean;

/**
 * <AUTHOR>
 * @date 2023/8/17 15:51
 * @description 发送消息工具类
 */
public class SendMsgToolkit {

    /**
     * 组装运输任务发送消息的业务拓展信息
     * @param businessSource
     * @param boTransTaskId
     * @return
     */
    public static Rpc1556007IBean buildMsgBusinessExpand(String businessSource, String boTransTaskId) {
        Rpc1556007IBean businessExpand = new Rpc1556007IBean();
        businessExpand.setBusinessSource(String.format(Constants.FORMAT_TF,businessSource));
        businessExpand.setBusinessCode(Constants.YWYZ_MSG_BUSINESS_CODE);
        businessExpand.setEntityCode(Constants.YWYZ_MSG_ENTITY_CODE_TT);
        businessExpand.setEntityId(boTransTaskId);

        return businessExpand;
    }

    public static Rpc1556007IBean buildOrgMsgBusinessExpand(String businessSource, String orgId) {
        Rpc1556007IBean businessExpand = new Rpc1556007IBean();
        businessExpand.setBusinessSource(String.format(Constants.FORMAT_TF,businessSource));
        businessExpand.setBusinessCode(Constants.YWYZ_MSG_BUSINESS_CODE);
        businessExpand.setEntityCode(Constants.YWYZ_MSG_ENTITY_CODE_PROJ);
        businessExpand.setEntityId(orgId);

        return businessExpand;
    }
}
