package com.wtyt.reviews.bean.request;

import cn.hutool.core.date.DateTime;
import com.wtyt.common.rpc.bean.base.BoTokenBean;
import com.wtyt.common.toolkits.NumberToolkit;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年03月15日 17时51分
 */
@Setter
@Getter
public class Req5329580Bean extends BoTokenBean implements Serializable {

    private static final long serialVersionUID = -1123757966012259182L;
    private String pageSize;
    private String pageNo;
    private String reviewsTarget;
    private String reviewsTargetId;
    private String month;
    private String score;
    private String searchType;
    private DateTime minDateTime;
    private DateTime maxDateTime;

    public int getPageSize() {
        return Math.min(NumberToolkit.toInt(this.pageSize, 20), 50);
    }

    public int getPageNo() {
        return NumberToolkit.toInt(this.pageNo, 1);
    }
}
