<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.bo.mapper.syf.BoTransOrderJoinMapper">

    <select id="existsTimeIntersection" resultType="java.lang.String">
    SELECT
        1
    FROM
        DUAL
    WHERE
        EXISTS (
        SELECT
            1
        FROM
            T_BO_TRANS_ORDER O
        INNER JOIN T_BO_TRANS_ORDER_PROGRESS P ON
            P.BO_TRANS_ORDER_ID = O.BO_TRANS_ORDER_ID
        WHERE
            O.IS_DEL = 0
            <if test="boTransOrderId != null and boTransOrderId != ''">
                AND O.BO_TRANS_ORDER_ID != #{boTransOrderId}
            </if>
            AND P.STATE = 1
            AND P.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
            AND (TO_DATE(#{startTime}, 'yyyy-mm-dd HH24:MI:ss') &lt;= END_TIME AND TO_DATE(#{endTime}, 'yyyy-mm-dd HH24:MI:ss') &gt;= START_TIME ))
    </select>

    <resultMap id="bookMap" type="com.wtyt.bo.bean.response.Req5329157OBean$Stat">
        <result column="statDate" property="statDate"/>
        <collection property="driverList" ofType="com.wtyt.bo.bean.response.Req5329157OBean$Driver">
            <result column="boTaskId" property="boTaskId"/>
        </collection>
    </resultMap>

    <select id="bookDriverList" resultMap="bookMap">
        SELECT
            TO_CHAR(CREATED_TIME, 'YYYY-MM-DD') statDate,
            BO_TRANS_TASK_ID boTaskId
        FROM
            T_BO_TRANS_ORDER_REL
        WHERE
            BO_TRANS_ORDER_ID = #{boTransOrderId}
            <if test="statDate != null and statDate != ''">
                AND TO_CHAR(CREATED_TIME, 'YYYY-MM-DD') = #{statDate}
            </if>
            AND IS_DEL = 0
        ORDER BY
            CREATED_TIME DESC
    </select>

    <select id="getOrderDzDetail" resultType="BoOrderDzBean">
        SELECT
            a.MAX_RESERVE_MILEAGE maxReserveMileage,
            a.MAX_RESERVE_HOUR maxReserveHour,
            TO_CHAR(a.START_TIME, 'yyyy-mm-dd') startTime,
            TO_CHAR(a.END_TIME, 'yyyy-mm-dd') endTime,
            a.CONSIGNMENT_NAME consignmentName,
            a.CONSIGNMENT_CONTACT consignmentContact,
            a.REMARK remark,
            a.BO_TRANS_ORDER_ID boTransOrderId,
            a.org_id orgId,
            a.USER_ID userId,
            a.SYS_ROLE_TYPE sysRoleType,
            b.STATE state,
            b.TOTAL_USE_CART_NUM totalUseCartNum,
            b.BO_BUSINESS_LINE_ID boBusinessLineId
        FROM
            T_BO_TRANS_ORDER a
            LEFT JOIN T_BO_TRANS_ORDER_PROGRESS b ON
            a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
            where a.IS_DEL = 0
            AND a.BO_TRANS_ORDER_ID = #{boTransOrderId}
    </select>

    <select id="getProgressingOrderDz" resultType="BoOrderDzBean">
         SELECT
            a.MAX_RESERVE_MILEAGE maxReserveMileage,
            a.MAX_RESERVE_HOUR maxReserveHour,
            TO_CHAR(a.START_TIME, 'yyyy-mm-dd') startTime,
            TO_CHAR(a.END_TIME, 'yyyy-mm-dd') endTime,
            a.CONSIGNMENT_NAME consignmentName,
            a.CONSIGNMENT_CONTACT consignmentContact,
            a.REMARK remark,
            a.USER_ID userId,
            a.SYS_ROLE_TYPE sysRoleType,
            b.STATE state,
            b.TOTAL_USE_CART_NUM totalUseCartNum,
            b.BO_BUSINESS_LINE_ID boBusinessLineId,
            a.IS_DEL isDel
        FROM
            T_BO_TRANS_ORDER a
            LEFT JOIN T_BO_TRANS_ORDER_PROGRESS b ON
            a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID
            where b.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
            order by a.created_time desc
    </select>

    <select id="queryTransPlan" parameterType="Req5330223IBean" resultType="Req5330223OBean">
        SELECT
            a.MAX_RESERVE_MILEAGE maxReserveMileage,
            a.MAX_RESERVE_HOUR maxReserveHour,
            TO_CHAR(a.START_TIME, 'yyyy-mm-dd') startTime,
            TO_CHAR(a.END_TIME, 'yyyy-mm-dd') endTime,
            a.REMARK remark,
            a.BO_TRANS_ORDER_ID boTransOrderId,
            a.ORG_ID orgId,
            a.USER_ID userId,
            a.SYS_ROLE_TYPE sysRoleType,
            b.STATE state,
            b.TOTAL_USE_CART_NUM totalUseCartNum,
            b.BO_BUSINESS_LINE_ID boBusinessLineId
        FROM
            T_BO_TRANS_ORDER a
            INNER JOIN T_BO_TRANS_ORDER_PROGRESS b ON
            a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
            WHERE a.is_del = 0
            and a.FROM_SOURCE = 4
            <if test="state != null and state != ''">
                and b.state = #{state}
                <!-- 如果来源是好运宝，并且查的计划是进行中的，则需要校验时间 -->
                <if test="fromSource != null and fromSource == '1'.toString() and state == '1'.toString()">
                    and a.START_TIME &lt;= SYSDATE
                    and a.END_TIME &gt; SYSDATE
                </if>
            </if>
            <if test="boBusinessLineIds != null and boBusinessLineIds.size() > 0">
                and b.BO_BUSINESS_LINE_ID in
                <foreach collection="boBusinessLineIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="boTransOrderIds != null and boTransOrderIds.size() > 0">
                and a.BO_TRANS_ORDER_ID in
                <foreach collection="boTransOrderIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
    </select>

    <select id="queryTransPlanTime" parameterType="Req5330224IBean" resultType="BoOrderDzBean">
        SELECT
            TO_CHAR(MIN(a.START_TIME), 'yyyy-mm-dd') startTime,
            TO_CHAR(MAX(a.END_TIME), 'yyyy-mm-dd') endTime
        FROM
            T_BO_TRANS_ORDER a
            LEFT JOIN T_BO_ORDER_GROUP_REL c ON c.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID AND c.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_ORDER_PROGRESS P ON
            P.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID
            AND P.IS_DEL = 0
            WHERE a.is_del = 0
            <if test="groupIds != null and groupIds.size() > 0">
                and c.GROUP_ID in
                <foreach collection="groupIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="boBusinessLineId != null and boBusinessLineId != ''">
                AND P.BO_BUSINESS_LINE_ID = #{boBusinessLineId}
            </if>
    </select>

    <select id="getOrderDzProgressingExpiredList" resultType="string">
        SELECT
        b.BO_TRANS_ORDER_PROGRESS_ID
        FROM
        T_BO_TRANS_ORDER a
        INNER JOIN T_BO_TRANS_ORDER_PROGRESS b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
        WHERE a.is_del = 0
        AND b.STATE = 1
        AND a.END_TIME &lt; SYSDATE
    </select>

    <select id="getOrderDzList" resultType="BoOrderDzBean" parameterType="Req5329151IBean">
        SELECT
            TO_CHAR(a.START_TIME, 'yyyy-mm-dd') startTime,
            TO_CHAR(a.END_TIME, 'yyyy-mm-dd') endTime,
            a.REMARK remark,
            a.BO_TRANS_ORDER_ID boTransOrderId,
            b.STATE state,
            b.TOTAL_USE_CART_NUM totalUseCartNum,
            b.BO_BUSINESS_LINE_ID boBusinessLineId,
            DECODE(b.STATE,1,0,0,1,2,2,3) sort
        FROM
            T_BO_TRANS_ORDER a
            INNER JOIN T_BO_TRANS_ORDER_PROGRESS b ON
            a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
            LEFT JOIN T_BO_ORDER_GROUP_REL c
            ON a.BO_TRANS_ORDER_ID = c.BO_TRANS_ORDER_ID and c.IS_DEL = 0
            WHERE a.is_del = 0
            and a.ORG_ID = #{orgId}
            and a.FROM_SOURCE = 4
            <if test="state != null and state != ''">
                and b.state = #{state}
            </if>
            <if test="startState != null and startState == '1'.toString">
                and a.START_TIME &lt;= SYSDATE
                and a.END_TIME &gt; SYSDATE
            </if>
            <if test="boBusinessLineIds != null and boBusinessLineIds.size() > 0">
                and b.BO_BUSINESS_LINE_ID in
                <foreach collection="boBusinessLineIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="permissionNewList != null and permissionNewList.size() > 0">
                AND (
                <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                    (
                    <if test="item.scopeList != null and item.scopeList.size() > 0">
                        (
                        <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                            (
                            c.GROUP_ID =#{scopeItem.groupId}
                            <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                                AND a.USER_ID = #{scopeItem.userId}
                            </if>
                            )
                        </foreach>
                        )
                    </if>
                    <if test="item.condition != null and item.condition !=''">
                        AND ${item.condition}
                    </if>
                    )
                </foreach>
                )
            </if>
            order by sort,a.created_time desc
    </select>

    <select id="selectByBoTransTaskNoList" resultType="com.wtyt.dao.bean.syf.BoTransOrderRelBean">
        SELECT
        T.TAX_WAYBILL_NO taxWaybillNo,
        O.BO_TRANS_ORDER_ID boTransOrderId,
        T.TAX_WAYBILL_ID taxWaybillId,
        R.BO_TRANS_ORDER_REL_ID boTransOrderRelId
        FROM
        T_BO_TRANS_ORDER_REL R
        INNER JOIN T_BO_TRANS_TASK T ON
        R.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        INNER JOIN T_BO_TRANS_ORDER O ON O.BO_TRANS_ORDER_ID = R.BO_TRANS_ORDER_ID
        WHERE
        R.IS_DEL = 0
        AND T.IS_DEL = 0
        AND O.IS_DEL = 0
        AND R.ORG_ID = #{orgId}
        AND T.TAX_WAYBILL_NO IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectByBoTransTaskIdList" resultType="com.wtyt.dao.bean.syf.BoTransOrderRelBean">
        SELECT
        R.BO_TRANS_ORDER_REL_ID boTransOrderRelId,
        R.ORG_ID orgId,
        R.USER_ID userId,
        R.BO_TRANS_ORDER_ID boTransOrderId,
        R.TAX_WAYBILL_ID taxWaybillId,
        R.BO_TRANS_TASK_ID boTransTaskId,
        R.BO_TRANS_ORDER_ID boTransOrderId,
        TO_CHAR(R.GOODS_CASE_PACK,'FM999999990.0000') goodsCasePack,
        TO_CHAR(R.GOODS_WEIGHT,'FM999999990.0000') goodsWeight,
        TO_CHAR(R.GOODS_VOLUME,'FM999999990.0000') goodsVolume,
        T.TAX_WAYBILL_NO taxWaybillNo
        FROM
        T_BO_TRANS_ORDER_REL R LEFT JOIN T_BO_TRANS_TASK T ON R.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
        WHERE
        R.IS_DEL = 0
        AND R.BO_TRANS_TASK_ID IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="getOrderByBoTransTaskId" parameterType="String" resultType="BoTransOrderBean">
        SELECT
               T.BO_TRANS_ORDER_ID boTransOrderId,
               T.CUSTOMER_NAME customerName,
               T.STORAGE_PLACE storagePlace,
               T.END_ADDRESS endAddress,
               TO_CHAR(r.GOODS_CASE_PACK, 'FM999999990.0000') goodsCasePack,
               r.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
               TO_CHAR(r.GOODS_WEIGHT, 'FM999999990.0000') goodsWeight,
               r.GOODS_WEIGHT_UNIT goodsWeightUnit,
               TO_CHAR(r.GOODS_VOLUME, 'FM999999990.0000') goodsVolume,
               r.GOODS_VOLUME_UNIT goodsVolumeUnit,
               T.CUSTOMER_ORDER_NO customerOrderNo,
               T.THIRD_ORDER_NO thirdOrderNo,
               T.USER_ID userId,
               T.SYS_ROLE_TYPE sysRoleType,
               T.JOB_NAME jobName,
               TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
               T.FROM_SOURCE fromSource,
               T.CONSIGNEE_UNIT consigneeUnit,
               T.REMARK remark,
               T.GOODS_SPEC goodsSpec,
               T.CONSIGNMENT_CONTACT consignmentContact,
               T.CONSIGNMENT_NAME consignmentName,
               T.FROM_SOURCE fromSource,
               T.SERVICE_REQUIRE serviceRequire
        FROM T_BO_TRANS_ORDER T
        left join T_BO_TRANS_ORDER_REL r on t.BO_TRANS_ORDER_ID = r.BO_TRANS_ORDER_ID
        WHERE r.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND T.IS_DEL = 0 and r.IS_DEL = 0
        ORDER BY T.CREATED_TIME
    </select>

    <select id="getOrderListByTaskUnloadingAddress" parameterType="BoTransOrderBean" resultType="BoTransOrderBean">
        SELECT
            T.BO_TRANS_ORDER_ID boTransOrderId,
            T.CUSTOMER_ORDER_NO customerOrderNo,
            T.THIRD_ORDER_NO thirdOrderNo,
            T.DT_INSIDE_ORDER_MARK dtInsideOrderMark,
            T.DT_ELECTRONIC_RECEIPT_MARK dtElectronicReceiptMark
        FROM T_BO_TRANS_ORDER T
                 left join T_BO_TRANS_ORDER_REL r on t.BO_TRANS_ORDER_ID = r.BO_TRANS_ORDER_ID
        WHERE r.BO_TRANS_TASK_ID = #{boTransTaskId}
         <if test="endProvinceName != null and endProvinceName != ''">
            AND T.END_PROVINCE_NAME = #{endProvinceName}
        </if>
        <if test="endCityName != null and endCityName != ''">
            AND T.END_CITY_NAME = #{endCityName}
        </if>
        <if test="endCountyName != null and endCountyName != ''">
            AND T.END_COUNTY_NAME = #{endCountyName}
        </if>
          <if test="endAddress != null and endAddress != ''">
            AND T.END_ADDRESS = #{endAddress}
        </if>
        AND T.IS_DEL = 0 and r.IS_DEL = 0
        ORDER BY T.CREATED_TIME
    </select>


    <select id="getBoTaskIdByCustomerNo" resultType="java.lang.String">
        SELECT BO_TRANS_TASK_ID FROM (SELECT b.BO_TRANS_TASK_ID FROM T_BO_TRANS_ORDER a JOIN T_BO_TRANS_ORDER_REL b ON a.BO_TRANS_ORDER_ID=b.BO_TRANS_ORDER_ID WHERE a.IS_DEL =0 AND b.IS_DEL =0
        AND a.ORG_ID=#{orgId}
        <if test="customerOrderNo!=null and customerOrderNo!=''">
            AND a.CUSTOMER_ORDER_NO = #{customerOrderNo}
        </if>
        <if test="boTransOrderId!=null and boTransOrderId!=''">
            AND a.BO_TRANS_ORDER_ID = #{boTransOrderId}
        </if>
        <if test="thirdOrderNo!=null and thirdOrderNo!=''">
            AND a.THIRD_ORDER_NO =#{thirdOrderNo}
        </if>
        AND b.BO_TRANS_TASK_ID IS NOT NULL) temp WHERE rownum=1
    </select>


    <select id="getOrderStatCount" resultType="BoOrderStatBean" parameterType="Req5329151IBean">
        SELECT
 			b.STATE state,
            count(0) statCount
        FROM
            T_BO_TRANS_ORDER a
            INNER JOIN T_BO_TRANS_ORDER_PROGRESS b ON
            a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
            LEFT JOIN T_BO_ORDER_GROUP_REL c
            ON a.BO_TRANS_ORDER_ID = c.BO_TRANS_ORDER_ID and c.IS_DEL = 0
        WHERE a.is_del = 0
        and a.ORG_ID = #{orgId}
        and a.FROM_SOURCE = 4
        <if test="boBusinessLineIds != null and boBusinessLineIds.size() > 0">
            and b.BO_BUSINESS_LINE_ID in
            <foreach collection="boBusinessLineIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                        (
                        c.GROUP_ID =#{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND a.USER_ID = #{scopeItem.userId}
                        </if>
                        )
                    </foreach>
                    )
                </if>
                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
                )
            </foreach>
            )
        </if>
        GROUP BY b.STATE
    </select>

    <select id="getOrderRelByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoTransOrderRelBean">
        SELECT
        R.BO_TRANS_ORDER_REL_ID boTransOrderRelId,
        R.ORG_ID orgId,
        R.USER_ID userId,
        R.BO_TRANS_ORDER_ID boTransOrderId,
        R.TAX_WAYBILL_ID taxWaybillId,
        R.BO_TRANS_TASK_ID boTransTaskId,
        R.DISTRIBUTE_STATE distributeState,
        TO_CHAR(R.GOODS_CASE_PACK,'FM999999990.0000') goodsCasePack,
        TO_CHAR(R.GOODS_WEIGHT,'FM999999990.0000') goodsWeight,
        TO_CHAR(R.GOODS_VOLUME,'FM999999990.0000') goodsVolume,
        T.TAX_WAYBILL_NO taxWaybillNo
        FROM
        T_BO_TRANS_ORDER_REL R
        LEFT JOIN T_BO_TRANS_ORDER O ON O.BO_TRANS_ORDER_ID = R.BO_TRANS_ORDER_ID
        LEFT JOIN T_BO_TRANS_TASK T ON T.BO_TRANS_TASK_ID = R.BO_TRANS_TASK_ID
        WHERE
        R.IS_DEL = 0
        AND O.IS_DEL = 0
        AND R.BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <resultMap id="JYTaskMap" type="com.wtyt.dao.bean.syf.BoTransInfoBean">
        <result property="taxWaybillNo" column="taxWaybillNo"/>
        <result property="nodeId" column="nodeId"/>
        <result property="boTransTaskId" column="boTransTaskId"/>
        <result property="unitPrice" column="unitPrice"/>
        <result property="distributeStatus" column="distributeStatus"/>
        <result property="dispatchState" column="dispatchState"/>
        <result property="transportState" column="transportState"/>
        <result property="showOptRecord" column="showOptRecord"/>
        <result property="mergeId" column="mergeId"/>
        <result property="supplierId" column="supplierId"/>
        <result property="disPatchType" column="disPatchType"/>
        <result property="disPatcherUserId" column="disPatcherUserId"/>
        <result property="disPatcherSupplierId" column="disPatcherSupplierId"/>
        <result property="overTimeState" column="overTimeState"/>
        <result property="cartType" column="cartType"/>
        <result property="cartLength" column="cartLength"/>
        <result property="stowageState" column="stowageState"/>
        <result property="boTpStowageRecordId" column="boTpStowageRecordId"/>
        <result property="driverName" column="driverName"/>
        <result property="driverMobileNo" column="driverMobileNo"/>
        <result property="cartBadgeNo" column="cartBadgeNo"/>
        <collection property="orderList" ofType="com.wtyt.dao.bean.syf.BoTransOrderBean">
            <result property="boTransOrderId" column="boTransOrderId"/>
            <result property="customerOrderNo" column="customerOrderNo"/>
            <result property="createdTime" column="createdTime"/>
            <result property="customerName" column="customerName"/>
            <result property="storagePlace" column="storagePlace"/>
            <result property="loadingPlace" column="loadingPlace"/>
            <result property="unloadingPlace" column="unloadingPlace"/>
            <result property="loadingArea" column="loadingArea"/>
            <result property="unloadingArea" column="unloadingArea"/>
            <result property="goodsCasePack" column="goodsCasePack"/>
            <result property="goodsCasePackUnit" column="goodsCasePackUnit"/>
            <result property="goodsWeight" column="goodsWeight"/>
            <result property="goodsWeightUnit" column="goodsWeightUnit"/>
            <result property="goodsVolume" column="goodsVolume"/>
            <result property="goodsVolumeUnit" column="goodsVolumeUnit"/>
            <result property="remark" column="remark"/>
            <result property="serviceRequire" column="serviceRequire"/>
            <result property="transDate" column="transDate"/>
            <result property="goodsName" column="goodsName"/>
            <result property="goodsSpec" column="goodsSpec"/>
            <result property="thirdOrderNo" column="thirdOrderNo"/>
            <result property="consigneeUnit" column="consigneeUnit"/>
            <result property="startProvinceName" column="startProvinceName"/>
            <result property="startCityName" column="startCityName"/>
            <result property="startCountyName" column="startCountyName"/>
            <result property="rawStartAddress" column="rawStartAddress"/>
            <result property="endProvinceName" column="endProvinceName"/>
            <result property="endCityName" column="endCityName"/>
            <result property="endCountyName" column="endCountyName"/>
            <result property="rawEndAddress" column="rawEndAddress"/>
            <result property="urgentMark" column="urgentMark"/>
            <result property="consigneeName" column="consigneeName"/>
            <result property="consigneeContact" column="consigneeContact"/>
            <result property="consigneeMobileNo" column="consigneeMobileNo"/>
            <result property="upstreamState" column="upstreamState"/>
            <result property="cartLength" column="orderCartLength"/>
            <result property="cartType" column="orderCartType"/>
        </collection>
    </resultMap>

    <sql id="queryList">
        '-1' overTimeState,
        a.LAST_MODIFIED_TIME ,
        c.TAX_WAYBILL_NO taxWaybillNo,
        c.BO_TRANS_TASK_ID boTransTaskId,
        c.NODE_ID nodeId,
        c.UNIT_PRICE unitPrice,
        c.DRIVER_NAME driverName,
        c.MOBILE_NO driverMobileNo,
        c.CART_BADGE_NO cartBadgeNo,
        decode(b.DISTRIBUTE_STATE, 1, 1, 0) distributeStatus,
        DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) dispatchState,
        <if test="configId != null and configId == '104'.toString()">
            DECODE(c.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 2, 500, 3, 600, 4, 650, 4, 700, 5, 800, 5, 1000, 5, 1100, 5, 1200, 6, 1) transportState,
        </if>
        <if test="configId != null and configId == '105'.toString()">
            DECODE(c.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 3, 501, 2, 600, 4, 650, 4, 700, 5, 800, 5, 1000, 5, 1100, 5, 1200, 6, 1) transportState,
        </if>
        '1' showOptRecord,
        a.BO_TRANS_ORDER_ID boTransOrderId,
        a.CUSTOMER_ORDER_NO customerOrderNo,
        TO_CHAR(a.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        a.CUSTOMER_NAME customerName,
        a.STORAGE_PLACE storagePlace,
        TO_CHAR(a.TRANS_DATE, 'YYYY-MM-DD') transDate,
        a.START_PROVINCE_NAME || a.START_CITY_NAME || a.START_COUNTY_NAME || a.START_ADDRESS loadingPlace,
        a.END_PROVINCE_NAME || a.END_CITY_NAME || a.END_COUNTY_NAME || a.END_ADDRESS unloadingPlace,
        a.START_CITY_NAME || a.START_COUNTY_NAME loadingArea,
        a.END_CITY_NAME || a.END_COUNTY_NAME unloadingArea,
        a.RAW_START_ADDRESS rawStartAddress,
        a.RAW_END_ADDRESS rawEndAddress,
        a.GOODS_NAME goodsName,
        a.GOODS_SPEC goodsSpec,
        decode(a.UPSTREAM_STATE,0,1,1,2,0) upstreamState,
        NVL2(a.GOODS_CASE_PACK,TO_CHAR(a.GOODS_CASE_PACK,'FM999999990.0000'),'0') goodsCasePack,
        a.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        NVL2(a.GOODS_WEIGHT,TO_CHAR(a.GOODS_WEIGHT,'FM999999990.0000'),'0') goodsWeight,
        a.GOODS_WEIGHT_UNIT goodsWeightUnit,
        NVL2(a.GOODS_VOLUME,TO_CHAR(a.GOODS_VOLUME,'FM999999990.0000'),'0') goodsVolume,
        a.GOODS_VOLUME_UNIT goodsVolumeUnit,
        a.REMARK remark,
        a.SERVICE_REQUIRE serviceRequire,
        CASE
        WHEN c.OWNER_ORG_ID != g.ORG_ID
        THEN '2'
        WHEN c.OWNER_ORG_ID = g.ORG_ID AND g.SYS_ROLE_TYPE = 12005
        THEN '2'
        ELSE '1'
        END disPatchType,
        g.USER_ID disPatcherUserId,
        g.GROUP_ID disPatcherSupplierId,
        DECODE(SOR.STOWAGE_STATE, 2, 2, 3, 2, 4, 1,NULL,1) stowageState,
        CASE WHEN SOR.STOWAGE_STATE = 4 THEN NULL ELSE
        SOR.BO_TP_STOWAGE_RECORD_ID END boTpStowageRecordId,
        nvl2(f.CART_TYPE, f.CART_TYPE, '') AS cartType,
        nvl2(f.CART_LENGTH, f.CART_LENGTH || '米', '') AS cartLength,
        a.CART_LENGTH orderCartLength,
        a.MERGE_ID mergeId,
        a.THIRD_ORDER_NO thirdOrderNo,
        a.CONSIGNEE_UNIT consigneeUnit,
        a.START_PROVINCE_NAME startProvinceName,
        a.START_CITY_NAME startCityName,
        a.START_COUNTY_NAME startCountyName,
        a.END_PROVINCE_NAME endProvinceName,
        a.END_CITY_NAME endCityName,
        a.END_COUNTY_NAME endCountyName,
        a.URGENT_MARK urgentMark,
        a.CONSIGNEE_NAME consigneeName,
        a.CONSIGNEE_CONTACT consigneeContact,
        a.CONSIGNEE_MOBILE_NO consigneeMobileNo
    </sql>

    <sql id="queryListForDistributeOrder">
        '-1' overTimeState,
        a.LAST_MODIFIED_TIME ,
        c.TAX_WAYBILL_NO taxWaybillNo,
        c.BO_TRANS_TASK_ID boTransTaskId,
        c.NODE_ID nodeId,
        c.UNIT_PRICE unitPrice,
        c.DRIVER_NAME driverName,
        c.MOBILE_NO driverMobileNo,
        c.CART_BADGE_NO cartBadgeNo,
        NVL2(a.CARRIER, 1, 0) distributeStatus,
        DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) dispatchState,
        <if test="configId != null and configId == '104'.toString()">
            DECODE(c.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 2, 500, 3, 600, 4, 650, 4, 700, 5, 800, 5, 1000, 5, 1100, 5, 1200, 6, 1) transportState,
        </if>
        <if test="configId != null and configId == '105'.toString()">
            DECODE(c.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 3, 501, 2, 600, 4, 650, 4, 700, 5, 800, 5, 1000, 5, 1100, 5, 1200, 6, 1) transportState,
        </if>
        '1' showOptRecord,
        a.BO_TRANS_ORDER_ID boTransOrderId,
        a.CUSTOMER_ORDER_NO customerOrderNo,
        TO_CHAR(a.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        a.CUSTOMER_NAME customerName,
        a.STORAGE_PLACE storagePlace,
        TO_CHAR(a.TRANS_DATE, 'YYYY-MM-DD') transDate,
        a.START_PROVINCE_NAME || a.START_CITY_NAME || a.START_COUNTY_NAME || a.START_ADDRESS loadingPlace,
        a.END_PROVINCE_NAME || a.END_CITY_NAME || a.END_COUNTY_NAME || a.END_ADDRESS unloadingPlace,
        a.START_CITY_NAME || a.START_COUNTY_NAME loadingArea,
        a.END_CITY_NAME || a.END_COUNTY_NAME unloadingArea,
        a.RAW_START_ADDRESS rawStartAddress,
        a.RAW_END_ADDRESS rawEndAddress,
        a.GOODS_NAME goodsName,
        a.GOODS_SPEC goodsSpec,
        decode(a.UPSTREAM_STATE,0,1,1,2,0) upstreamState,
        NVL2(a.GOODS_CASE_PACK,TO_CHAR(a.GOODS_CASE_PACK,'FM999999990.0000'),'0') goodsCasePack,
        a.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        NVL2(a.GOODS_WEIGHT,TO_CHAR(a.GOODS_WEIGHT,'FM999999990.0000'),'0') goodsWeight,
        a.GOODS_WEIGHT_UNIT goodsWeightUnit,
        NVL2(a.GOODS_VOLUME,TO_CHAR(a.GOODS_VOLUME,'FM999999990.0000'),'0') goodsVolume,
        a.GOODS_VOLUME_UNIT goodsVolumeUnit,
        a.REMARK remark,
        a.SERVICE_REQUIRE serviceRequire,
        CASE
        WHEN c.OWNER_ORG_ID != g.ORG_ID
        THEN '2'
        WHEN c.OWNER_ORG_ID = g.ORG_ID AND g.SYS_ROLE_TYPE = 12005
        THEN '2'
        ELSE '1'
        END disPatchType,
        g.USER_ID disPatcherUserId,
        g.GROUP_ID disPatcherSupplierId,
        DECODE(SOR.STOWAGE_STATE, 2, 2, 3, 2, 4, 1,NULL,1) stowageState,
        CASE WHEN SOR.STOWAGE_STATE = 4 THEN NULL ELSE
        SOR.BO_TP_STOWAGE_RECORD_ID END boTpStowageRecordId,
        nvl2(f.CART_TYPE, f.CART_TYPE, '') AS cartType,
        nvl2(f.CART_LENGTH, f.CART_LENGTH || '米', '') AS cartLength,
        a.CART_LENGTH orderCartLength,
        a.CART_TYPE orderCartType,
        a.MERGE_ID mergeId,
        a.THIRD_ORDER_NO thirdOrderNo,
        a.CONSIGNEE_UNIT consigneeUnit,
        a.START_PROVINCE_NAME startProvinceName,
        a.START_CITY_NAME startCityName,
        a.START_COUNTY_NAME startCountyName,
        a.END_PROVINCE_NAME endProvinceName,
        a.END_CITY_NAME endCityName,
        a.END_COUNTY_NAME endCountyName,
        a.URGENT_MARK urgentMark,
        a.CONSIGNEE_NAME consigneeName,
        a.CONSIGNEE_CONTACT consigneeContact,
        a.CONSIGNEE_MOBILE_NO consigneeMobileNo
    </sql>

    <sql id="getMergeId">
        SELECT
        distinct a.merge_id
        FROM
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_REL b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK c ON
        b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID and c.IS_DEL = 0
        INNER JOIN T_BO_ORDER_GROUP_REL d ON
        a.BO_TRANS_ORDER_ID = d.BO_TRANS_ORDER_ID and d.IS_DEL = 0
        <!-- 如果需要根据计划接收者筛选，需要关联T_BO_TRANS_TASK_GROUP_REL -->
        <if test="planReceiverList != null and planReceiverList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_GROUP_REL e ON
            c.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID AND e.IS_DEL = 0
        </if>
        <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
        <if test="planDispatcherList != null and planDispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA f ON
            f.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID AND f.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD g ON
            f.DISPATCH_CAR_RECORD_ID = g.BO_TRANS_NODE_RECORD_ID and g.IS_DEL = 0 AND g.NODE_ID = 200
        </if>
        <!-- 如果需要根据配载状态查询 -->
        <if test="stowageState != null and stowageState !=''">
            LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
            SOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
        </if>
        where   a.ORG_ID = #{orgId} AND a.ORDER_TYPE =1 AND
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                a.IS_DEL = 1
            </when>
            <otherwise>
                a.IS_DEL = 0
            </otherwise>
        </choose>
        <choose>
            <when test="state != null and state == '1'.toString()">
                AND (c.NODE_ID IN (0, 100, 300) or c.NODE_ID is null)
            </when>
            <when test="state != null and state == '2'.toString()">
                <choose>
                    <when test="configId != null and configId == '104'.toString()">
                        AND c.NODE_ID IN (200, 400)
                    </when>
                    <when test="configId != null and configId == '105'.toString()">
                        AND c.NODE_ID IN(200,501)
                    </when>
                </choose>
            </when>
            <when test="state != null and state == '3'.toString()">
                <choose>
                    <when test="configId != null and configId == '104'.toString()">
                        AND c.NODE_ID = 500
                    </when>
                    <when test="configId != null and configId == '105'.toString()">
                        AND c.NODE_ID = 400
                    </when>
                </choose>
            </when>
            <when test="state != null and state == '4'.toString()">
                AND c.NODE_ID IN (600,650)
            </when>
            <when test="state != null and state == '5'.toString()">
                AND c.NODE_ID IN (700,800,1000,1100)
            </when>
            <when test="state != null and state == '6'.toString()">
                AND c.NODE_ID = 1200
            </when>
            <!-- 7待派车-一手货找车中   -->
            <when test="state != null and state == '7'.toString()">
                AND (c.NODE_ID IN (0, 100, 300) or c.NODE_ID is null)
                AND EXISTS(
                SELECT 1 FROM T_BO_TO_FH_ORDER_REL TBTFOR
                WHERE TBTFOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID
                AND TBTFOR.IS_DEL = 0 AND TBTFOR.FH_STATE = 0
                )
            </when>
            <!-- 8：待派车-一手货已订车   -->
            <when test="state != null and state == '8'.toString()">
                AND (TBTT.NODE_ID IN (0, 100, 300) or TBTT.NODE_ID IS NULL)
                AND EXISTS(
                SELECT 1 FROM T_BO_TO_FH_ORDER_REL TBTFOR
                WHERE TBTFOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID
                AND TBTFOR.IS_DEL = 0 AND TBTFOR.FH_STATE = 1
                )
            </when>
        </choose>
        <if test="startDate != null and startDate !=''">
            AND a.TRANS_DATE &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate !=''">
            AND a.TRANS_DATE &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startTime != null and startTime !=''">
            AND a.CREATED_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND a.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startPlace != null and startPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="startPlace" separator="or">
                (
                <if test="item.provinceName != null and item.provinceName != ''">
                    a.START_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and a.START_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and a.START_COUNTY_NAME = #{item.areaName}</if>
                )
            </foreach>)
        </if>
        <if test="endPlace != null and endPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="endPlace" separator="or">
                (
                <if test="item.provinceName != null and item.provinceName != ''">
                    a.END_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and a.END_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and a.END_COUNTY_NAME = #{item.areaName}
                </if>
                )
            </foreach>)
        </if>
        <if test="lineList != null and lineList.size() > 0">
            AND(
            <foreach item="item" index="index" collection="lineList" separator="or">
                (
                <if test="item.startProvinceName != null and item.startProvinceName != ''">
                    a.START_PROVINCE_NAME = #{item.startProvinceName}
                </if>
                <if test="item.startCityName != null and item.startCityName != ''">
                    and a.START_CITY_NAME = #{item.startCityName}
                </if>
                <if test="item.startCountyName != null and item.startCountyName != ''">
                    and a.START_COUNTY_NAME = #{item.startCountyName}
                </if>
                <if test="item.endProvinceName != null and item.endProvinceName != ''">
                    and a.END_PROVINCE_NAME = #{item.endProvinceName}
                </if>
                <if test="item.endCityName != null and item.endCityName != ''">
                    and a.END_CITY_NAME = #{item.endCityName}
                </if>
                <if test="item.endCountyName != null and item.endCountyName != ''">
                    and a.END_COUNTY_NAME = #{item.endCountyName}
                </if>
                )
            </foreach>)
        </if>

        <if test="searchContentList != null and searchContentList.size() > 0 ">
        <!-- 如果是金颜的话，则 -->
            <if test="terminalType != null and terminalType =='PC-BUS'.toString()">
                AND
                <foreach collection="searchContentList" open="(" close=")" separator="or" item="item">
                    c.TAX_WAYBILL_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.THIRD_ORDER_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.SERVICE_REQUIRE like CONCAT(CONCAT('%',#{item}),'%')
                </foreach>
            </if>
            <if test="terminalType != null and terminalType =='APP-XDL'.toString()">
                AND
                <foreach collection="searchContentList" open="(" close=")" separator="or" item="item">
                    c.TAX_WAYBILL_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.THIRD_ORDER_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.SERVICE_REQUIRE like CONCAT(CONCAT('%',#{item}),'%')
                    or a.CUSTOMER_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_PROVINCE_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_CITY_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_COUNTY_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_ADDRESS like CONCAT(CONCAT('%',#{item}),'%')
                </foreach>
            </if>
            <if test="terminalType != null and terminalType =='PC-CPD'.toString()">
                AND
                <foreach collection="searchContentList" open="(" close=")" separator="or" item="item">
                     a.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.CUSTOMER_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_PROVINCE_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_CITY_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_COUNTY_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_ADDRESS like CONCAT(CONCAT('%',#{item}),'%')
                </foreach>
            </if>
        </if>
        <if test="customerOrderNoList != null and customerOrderNoList.size() > 0 ">
            AND a.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="customerNameList != null and customerNameList.size() > 0 ">
            AND a.CUSTOMER_NAME IN
            <foreach collection="customerNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="thirdOrderNoList != null and thirdOrderNoList.size() > 0 ">
            AND a.THIRD_ORDER_NO IN
            <foreach collection="thirdOrderNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="consigneeInfoList != null and consigneeInfoList.size() > 0 ">
            AND
            <foreach collection="consigneeInfoList" open="(" close=")" separator="or" item="item">
                a.CONSIGNEE_UNIT like CONCAT(CONCAT('%',#{item}),'%')
                or a.CONSIGNEE_NAME like CONCAT(CONCAT('%',#{item}),'%')
                or a.CONSIGNEE_MOBILE_NO like CONCAT(CONCAT('%',#{item}),'%')
            </foreach>
        </if>
<!--        <if test="thirdOrderNo != null and thirdOrderNo != ''">-->
<!--            AND a.THIRD_ORDER_NO like CONCAT(CONCAT('%',#{thirdOrderNo}),'%')-->
<!--        </if>-->
        <!-- 湛江智能配载新增开始 -->
        <if test="orderNoCusNameVague != null and orderNoCusNameVague !=''">
            AND (a.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{orderNoCusNameVague}),'%')
                or a.CUSTOMER_NAME like CONCAT(CONCAT('%',#{orderNoCusNameVague}),'%') )
        </if>
        <if test="goodsNameVague != null and goodsNameVague !=''">
            AND a.GOODS_NAME like CONCAT(CONCAT('%',#{goodsNameVague}),'%')
        </if>
        <if test="endPlaceVague != null and endPlaceVague !=''">
            AND (a.END_PROVINCE_NAME like CONCAT(CONCAT('%',#{endPlaceVague}),'%')
                or a.END_CITY_NAME like CONCAT(CONCAT('%',#{endPlaceVague}),'%')
                or a.END_COUNTY_NAME like CONCAT(CONCAT('%',#{endPlaceVague}),'%'))
        </if>
        <if test="stowageState != null and stowageState !=''">
            AND DECODE(SOR.STOWAGE_STATE, 2, 2, 3, 2, 4, 1,NULL,1) = #{stowageState}
        </if>
        <!-- 湛江智能配载新增结束 -->

        <if test="planReceiverList != null and planReceiverList.size() > 0">
            and e.GROUP_ID in
            <foreach collection="planReceiverList" open="(" close=")" separator="," item="item">
                #{item.supplierId}
            </foreach>
        </if>
        <if test="ddDispatcherList != null and ddDispatcherList.size() > 0">
            and g.USER_ID in
            <foreach collection="ddDispatcherList" open="(" close=")" separator="," item="item">
                #{item.userId}
            </foreach>
        </if>
        <if test="gysDispatcherList != null and gysDispatcherList.size() > 0">
            and g.GROUP_ID in
            <foreach collection="gysDispatcherList" open="(" close=")" separator="," item="item">
                #{item.supplierId}
            </foreach>
        </if>
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                    <if test="item.scopeList != null and item.scopeList.size() > 0">
                         (
                        <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                            (
                                d.GROUP_ID = #{scopeItem.groupId}
                                <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                                    AND a.USER_ID = #{scopeItem.userId}
                                </if>
                            )
                        </foreach>
                        )
                    </if>

                    <if test="item.condition != null and item.condition !=''">
                        AND ${item.condition}
                    </if>
                )
            </foreach>
            )
        </if>
        <if test="distributeStatus != null and distributeStatus !=''">
            AND decode(b.DISTRIBUTE_STATE, 1, 1, 0) = #{distributeStatus}
        </if>
        <choose>
            <when test="tabState != null and tabState == '8'.toString()">
                AND decode(b.DISTRIBUTE_STATE, 1, 1, 0) = 0
            </when>
            <when test="tabState != null and tabState == '9'.toString()">
                AND decode(b.DISTRIBUTE_STATE, 1, 1, 0) = 1
            </when>
            <when test="tabState != null and tabState == '10'.toString()">
                AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0
            </when>
            <when test="tabState != null and tabState == '11'.toString()">
                AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 1
            </when>
            <when test="tabState != null and tabState == '12'.toString()">
                AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0  AND NVL(a.LOTTERY_STATE,0) =0   <include refid="cpdLotteryLineId"/>
            </when>
            <when test="tabState != null and tabState == '13'.toString()">
                AND a.LOTTERY_STATE =1  <include refid="cpdLotteryLineId"/>
            </when>
            <when test="tabState != null and tabState == '14'.toString()">
                AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0  AND a.LOTTERY_STATE =2  <include refid="cpdLotteryLineId"/>
            </when>
        </choose>
        <if test="customerName != null and customerName !=''">
            AND a.CUSTOMER_NAME like CONCAT(CONCAT('%',#{customerName}),'%')
        </if>
        <choose>
            <when test="upstreamState != null and upstreamState == '1'.toString()">
                AND a.UPSTREAM_STATE = 0
            </when>
            <when test="upstreamState != null and upstreamState == '2'.toString()">
                AND a.UPSTREAM_STATE = 1
            </when>
        </choose>
        <if test="urgentMark != null and urgentMark !=''">
            AND a.URGENT_MARK  = #{urgentMark}
        </if>
    </sql>

    <sql id="getMergeIdForDistributeOrder">
        SELECT
        distinct a.merge_id
        FROM
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_REL b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK c ON
        b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID and c.IS_DEL = 0
        INNER JOIN T_BO_ORDER_GROUP_REL d ON
        a.BO_TRANS_ORDER_ID = d.BO_TRANS_ORDER_ID and d.IS_DEL = 0
        <!-- 如果需要根据计划接收者筛选，需要关联T_BO_TRANS_TASK_GROUP_REL -->
        <if test="planReceiverList != null and planReceiverList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_GROUP_REL e ON
            c.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID AND e.IS_DEL = 0
        </if>
        <!-- 如果需要根据派车人员筛选，需要关联T_BO_TRANS_TASK_EXTRA、T_BO_TRANS_NODE_RECORD -->
        <if test="planDispatcherList != null and planDispatcherList.size() > 0">
            LEFT JOIN T_BO_TRANS_TASK_EXTRA f ON
            f.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID AND f.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_NODE_RECORD g ON
            f.DISPATCH_CAR_RECORD_ID = g.BO_TRANS_NODE_RECORD_ID and g.IS_DEL = 0 AND g.NODE_ID = 200
        </if>
        <!-- 如果需要根据配载状态查询 -->
        <if test="stowageState != null and stowageState !=''">
            LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
            SOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
        </if>
        where
            a.ORG_ID = #{orgId} AND a.ORDER_TYPE =1 AND
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                a.IS_DEL = 1
            </when>
            <otherwise>
                a.IS_DEL = 0
            </otherwise>
        </choose>
        <choose>
            <when test="state != null and state == '1'.toString()">
                AND (c.NODE_ID IN (0, 100, 300) or c.NODE_ID is null)
                <if test="queryFhOrderMark != null and queryFhOrderMark != ''">
                         AND NOT EXISTS(
                    SELECT 1 FROM T_BO_TO_FH_ORDER_REL TBTFOR
                    WHERE TBTFOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID
                     AND TBTFOR.IS_DEL = 0)
                 </if>

            </when>
            <when test="state != null and state == '2'.toString()">
                <choose>
                    <when test="configId != null and configId == '104'.toString()">
                        AND c.NODE_ID IN (200, 400)
                    </when>
                    <when test="configId != null and configId == '105'.toString()">
                        AND c.NODE_ID IN(200,501)
                    </when>
                </choose>
            </when>
            <when test="state != null and state == '3'.toString()">
                <choose>
                    <when test="configId != null and configId == '104'.toString()">
                        AND c.NODE_ID = 500
                    </when>
                    <when test="configId != null and configId == '105'.toString()">
                        AND c.NODE_ID = 400
                    </when>
                </choose>
            </when>
            <when test="state != null and state == '4'.toString()">
                AND c.NODE_ID IN (600,650)
            </when>
            <when test="state != null and state == '5'.toString()">
                AND c.NODE_ID IN (700,800,1000,1100)
            </when>
            <when test="state != null and state == '6'.toString()">
                AND c.NODE_ID = 1200
            </when>
            <!-- 7待派车-一手货找车中   -->
            <when test="state != null and state == '7'.toString()">
                AND (c.NODE_ID IN (0, 100, 300) or c.NODE_ID is null)
                AND EXISTS(
                SELECT 1 FROM T_BO_TO_FH_ORDER_REL TBTFOR
                WHERE TBTFOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID
                AND TBTFOR.IS_DEL = 0 AND TBTFOR.FH_STATE = 0
                )
            </when>
            <!-- 8：待派车-一手货已订车   -->
            <when test="state != null and state == '8'.toString()">
                AND (c.NODE_ID IN (0, 100, 300) or c.NODE_ID is null)
                AND EXISTS(
                SELECT 1 FROM T_BO_TO_FH_ORDER_REL TBTFOR
                WHERE TBTFOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID
                AND TBTFOR.IS_DEL = 0 AND TBTFOR.FH_STATE = 1
                )
            </when>
        </choose>
        <if test="startDate != null and startDate !=''">
            AND a.TRANS_DATE &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate !=''">
            AND a.TRANS_DATE &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startTime != null and startTime !=''">
            AND a.CREATED_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND a.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startPlace != null and startPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="startPlace" separator="or">
                (
                <if test="item.provinceName != null and item.provinceName != ''">
                    a.START_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and a.START_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and a.START_COUNTY_NAME = #{item.areaName}</if>
                )
            </foreach>)
        </if>
        <if test="endPlace != null and endPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="endPlace" separator="or">
                (
                <if test="item.provinceName != null and item.provinceName != ''">
                    a.END_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and a.END_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and a.END_COUNTY_NAME = #{item.areaName}
                </if>
                )
            </foreach>)
        </if>
        <if test="lineList != null and lineList.size() > 0">
            AND(
            <foreach item="item" index="index" collection="lineList" separator="or">
                (
                <if test="item.startProvinceName != null and item.startProvinceName != ''">
                    a.START_PROVINCE_NAME = #{item.startProvinceName}
                </if>
                <if test="item.startCityName != null and item.startCityName != ''">
                    and a.START_CITY_NAME = #{item.startCityName}
                </if>
                <if test="item.startCountyName != null and item.startCountyName != ''">
                    and a.START_COUNTY_NAME = #{item.startCountyName}
                </if>
                <if test="item.endProvinceName != null and item.endProvinceName != ''">
                    and a.END_PROVINCE_NAME = #{item.endProvinceName}
                </if>
                <if test="item.endCityName != null and item.endCityName != ''">
                    and a.END_CITY_NAME = #{item.endCityName}
                </if>
                <if test="item.endCountyName != null and item.endCountyName != ''">
                    and a.END_COUNTY_NAME = #{item.endCountyName}
                </if>
                )
            </foreach>)
        </if>

        <if test="searchContentList != null and searchContentList.size() > 0 ">
            <!-- 如果是金颜的话，则 -->
            <if test="terminalType != null and terminalType =='PC-BUS'.toString()">
                AND
                <foreach collection="searchContentList" open="(" close=")" separator="or" item="item">
                    c.TAX_WAYBILL_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.THIRD_ORDER_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.SERVICE_REQUIRE like CONCAT(CONCAT('%',#{item}),'%')
                </foreach>
            </if>
            <if test="terminalType != null and terminalType =='APP-XDL'.toString()">
                AND
                <foreach collection="searchContentList" open="(" close=")" separator="or" item="item">
                    c.TAX_WAYBILL_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.THIRD_ORDER_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.SERVICE_REQUIRE like CONCAT(CONCAT('%',#{item}),'%')
                    or a.CUSTOMER_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_PROVINCE_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_CITY_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_COUNTY_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_ADDRESS like CONCAT(CONCAT('%',#{item}),'%')
                </foreach>
            </if>
            <if test="terminalType != null and terminalType =='PC-CPD'.toString()">
                AND
                <foreach collection="searchContentList" open="(" close=")" separator="or" item="item">
                    a.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{item}),'%')
                    or a.CUSTOMER_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_PROVINCE_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_CITY_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_COUNTY_NAME like CONCAT(CONCAT('%',#{item}),'%')
                    or a.END_ADDRESS like CONCAT(CONCAT('%',#{item}),'%')
                </foreach>
            </if>
        </if>
        <if test="customerOrderNoList != null and customerOrderNoList.size() > 0 ">
            AND a.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="customerNameList != null and customerNameList.size() > 0 ">
            AND a.CUSTOMER_NAME IN
            <foreach collection="customerNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="thirdOrderNoList != null and thirdOrderNoList.size() > 0 ">
            AND a.THIRD_ORDER_NO IN
            <foreach collection="thirdOrderNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="consigneeInfoList != null and consigneeInfoList.size() > 0 ">
            AND
            <foreach collection="consigneeInfoList" open="(" close=")" separator="or" item="item">
                a.CONSIGNEE_UNIT like CONCAT(CONCAT('%',#{item}),'%')
                or a.CONSIGNEE_NAME like CONCAT(CONCAT('%',#{item}),'%')
                or a.CONSIGNEE_MOBILE_NO like CONCAT(CONCAT('%',#{item}),'%')
            </foreach>
        </if>
        <!--        <if test="thirdOrderNo != null and thirdOrderNo != ''">-->
        <!--            AND a.THIRD_ORDER_NO like CONCAT(CONCAT('%',#{thirdOrderNo}),'%')-->
        <!--        </if>-->
        <!-- 湛江智能配载新增开始 -->
        <if test="orderNoCusNameVague != null and orderNoCusNameVague !=''">
            AND (a.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{orderNoCusNameVague}),'%')
            or a.CUSTOMER_NAME like CONCAT(CONCAT('%',#{orderNoCusNameVague}),'%') )
        </if>
        <if test="goodsNameVague != null and goodsNameVague !=''">
            AND a.GOODS_NAME like CONCAT(CONCAT('%',#{goodsNameVague}),'%')
        </if>
        <if test="endPlaceVague != null and endPlaceVague !=''">
            AND (a.END_PROVINCE_NAME like CONCAT(CONCAT('%',#{endPlaceVague}),'%')
            or a.END_CITY_NAME like CONCAT(CONCAT('%',#{endPlaceVague}),'%')
            or a.END_COUNTY_NAME like CONCAT(CONCAT('%',#{endPlaceVague}),'%'))
        </if>
        <if test="stowageState != null and stowageState !=''">
            AND DECODE(SOR.STOWAGE_STATE, 2, 2, 3, 2, 4, 1,NULL,1) = #{stowageState}
        </if>
        <!-- 湛江智能配载新增结束 -->

        <if test="planReceiverList != null and planReceiverList.size() > 0">
            and e.GROUP_ID in
            <foreach collection="planReceiverList" open="(" close=")" separator="," item="item">
                #{item.supplierId}
            </foreach>
        </if>
        <if test="ddDispatcherList != null and ddDispatcherList.size() > 0">
            and g.USER_ID in
            <foreach collection="ddDispatcherList" open="(" close=")" separator="," item="item">
                #{item.userId}
            </foreach>
        </if>
        <if test="gysDispatcherList != null and gysDispatcherList.size() > 0">
            and g.GROUP_ID in
            <foreach collection="gysDispatcherList" open="(" close=")" separator="," item="item">
                #{item.supplierId}
            </foreach>
        </if>
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                        (
                        d.GROUP_ID = #{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND a.USER_ID = #{scopeItem.userId}
                        </if>
                        )
                    </foreach>
                    )
                </if>

                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
                )
            </foreach>
            )
        </if>
        <if test="distributeStatus != null and distributeStatus !=''">
            AND NVL2(a.CARRIER, 1, 0) = #{distributeStatus}
        </if>
        <choose>
            <when test="tabState != null and tabState == '8'.toString()">
                AND decode(b.DISTRIBUTE_STATE, 1, 1, 0) = 0
            </when>
            <when test="tabState != null and tabState == '9'.toString()">
                AND decode(b.DISTRIBUTE_STATE, 1, 1, 0) = 1
            </when>
            <when test="tabState != null and tabState == '10'.toString()">
                AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0
            </when>
            <when test="tabState != null and tabState == '11'.toString()">
                AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 1
            </when>
            <when test="tabState != null and tabState == '12'.toString()">
                AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0  AND NVL(a.LOTTERY_STATE,0) =0   <include refid="cpdLotteryLineId"/>
            </when>
            <when test="tabState != null and tabState == '13'.toString()">
                AND a.LOTTERY_STATE =1  <include refid="cpdLotteryLineId"/>
            </when>
            <when test="tabState != null and tabState == '14'.toString()">
                AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0  AND a.LOTTERY_STATE =2  <include refid="cpdLotteryLineId"/>
            </when>
        </choose>
        <if test="customerName != null and customerName !=''">
            AND a.CUSTOMER_NAME like CONCAT(CONCAT('%',#{customerName}),'%')
        </if>
        <choose>
            <when test="upstreamState != null and upstreamState == '1'.toString()">
                AND a.UPSTREAM_STATE = 0
            </when>
            <when test="upstreamState != null and upstreamState == '2'.toString()">
                AND a.UPSTREAM_STATE = 1
            </when>
        </choose>
         <if test="urgentMark != null and urgentMark !=''">
            AND a.URGENT_MARK  = #{urgentMark}
        </if>
    </sql>

    <sql id="cpdLotteryLineId">
        <if test="lotteryLineList != null and lotteryLineList.size() > 0">
            AND(
            <foreach item="item" index="index" collection="lotteryLineList" separator="or">
                (
                <if test="item.startProvinceName != null and item.startProvinceName != ''">
                    a.START_PROVINCE_NAME = #{item.startProvinceName}
                </if>
                <if test="item.startCityName != null and item.startCityName != ''">
                    and a.START_CITY_NAME = #{item.startCityName}
                </if>
                <if test="item.startCountyName != null and item.startCountyName != ''">
                    and a.START_COUNTY_NAME = #{item.startCountyName}
                </if>
                <if test="item.endProvinceName != null and item.endProvinceName != ''">
                    and a.END_PROVINCE_NAME = #{item.endProvinceName}
                </if>
                <if test="item.endCityName != null and item.endCityName != ''">
                    and a.END_CITY_NAME = #{item.endCityName}
                </if>
                <if test="item.endCountyName != null and item.endCountyName != ''">
                    and a.END_COUNTY_NAME = #{item.endCountyName}
                </if>
                <include refid="shieldConsignee"/>
                <if test="item.boTransOrderIds != null and item.boTransOrderIds.size() > 0">
                     a.BO_TRANS_ORDER_ID IN
                    <foreach collection="item.boTransOrderIds" index="index" open="(" close=")" item="id" separator=",">
                        <if test="(index % 999) == 998"> NULL) OR a.BO_TRANS_ORDER_ID IN(</if>#{id}
                    </foreach>
                </if>
                )
            </foreach>)
        </if>

    </sql>

    <sql id="shieldConsignee">
        <if test="item.shieldConsigneeList != null and item.shieldConsigneeList.size() > 0">
            AND NOT (
            <foreach item="item1" index="index" collection="item.shieldConsigneeList" separator="or">
                (
                 a.CONSIGNEE_UNIT = #{item1.consigneeName} and a.CONSIGNEE_UNIT is not null
                <if test="item1.endProvinceName != null and item1.endProvinceName != ''">
                    and a.END_PROVINCE_NAME = #{item1.endProvinceName}
                </if>
                <if test="item1.endCityName != null and item1.endCityName != ''">
                    and a.END_CITY_NAME = #{item1.endCityName}
                </if>
                <if test="item1.endCountyName != null and item1.endCountyName != ''">
                    and a.END_COUNTY_NAME = #{item1.endCountyName}
                </if>
                <if test="item1.endAddress != null and item1.endAddress != ''">
                    and a.END_ADDRESS = #{item1.endAddress}
                </if>
                )
            </foreach>)
        </if>
    </sql>


    <select id="getJYList" resultMap="JYTaskMap" parameterType="com.wtyt.dao.bean.syf.BoTransPlanIBean">
        select
        <include refid="queryList"/>
        from
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_REL b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK c ON
        b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID and c.IS_DEL = 0
        INNER JOIN
        (<include refid="getMergeId"/>) d
        on a.merge_id = d.merge_id
        LEFT JOIN T_BO_TRANS_TASK_EXTRA f ON
        f.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID AND f.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD g ON
        f.DISPATCH_CAR_RECORD_ID = g.BO_TRANS_NODE_RECORD_ID and g.IS_DEL = 0 AND g.NODE_ID = 200
        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
        WHERE a.ORG_ID = #{orgId} AND a.ORDER_TYPE =1 AND
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                a.IS_DEL = 1
            </when>
            <otherwise>
                a.IS_DEL = 0
            </otherwise>
        </choose>
        ORDER BY
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                a.LAST_MODIFIED_TIME DESC
            </when>
            <when test="tabState != null and tabState == '10'.toString()">
                a.MERGE_ID,a.CREATED_TIME
            </when>
            <when test="tabState != null and tabState == '11'.toString()">
                g.CREATED_TIME DESC,a.MERGE_ID DESC,a.CREATED_TIME DESC
            </when>
            <when test="tabState != null and tabState == '12'.toString()">
               a.URGENT_MARK desc, a.MERGE_ID,a.CREATED_TIME
            </when>
            <otherwise>
                a.MERGE_ID DESC,a.CREATED_TIME DESC
            </otherwise>
        </choose>
    </select>

    <select id="getJYListForDistributeOrder" resultMap="JYTaskMap" parameterType="com.wtyt.dao.bean.syf.BoTransPlanIBean">
        select
        <include refid="queryListForDistributeOrder"/>
        from
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_REL b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK c ON
        b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID and c.IS_DEL = 0
        INNER JOIN
        (<include refid="getMergeIdForDistributeOrder"/>) d
        on a.merge_id = d.merge_id
        LEFT JOIN T_BO_TRANS_TASK_EXTRA f ON
        f.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID AND f.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD g ON
        f.DISPATCH_CAR_RECORD_ID = g.BO_TRANS_NODE_RECORD_ID and g.IS_DEL = 0 AND g.NODE_ID = 200
        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
        WHERE
            a.ORG_ID = #{orgId} AND a.ORDER_TYPE =1 AND
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                a.IS_DEL = 1
            </when>
            <otherwise>
                a.IS_DEL = 0
            </otherwise>
        </choose>
        ORDER BY
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                a.LAST_MODIFIED_TIME DESC
            </when>
            <when test="tabState != null and tabState == '10'.toString()">
                a.MERGE_ID,a.CREATED_TIME
            </when>
            <when test="tabState != null and tabState == '11'.toString()">
                g.CREATED_TIME DESC,a.MERGE_ID DESC,a.CREATED_TIME DESC
            </when>
            <when test="tabState != null and tabState == '12'.toString()">
                a.URGENT_MARK desc, a.MERGE_ID,a.CREATED_TIME
            </when>
            <otherwise>
                a.MERGE_ID DESC,a.CREATED_TIME DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectJYCount" resultType="java.lang.String" parameterType="com.wtyt.dao.bean.syf.BoTransPlanIBean">
        select
            count(1)
        from
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_REL b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK c ON
        b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID and c.IS_DEL = 0
        INNER JOIN
        (<include refid="getMergeId"/>) d
        on a.merge_id = d.merge_id
        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA f ON
        f.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID AND f.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD g ON
        f.DISPATCH_CAR_RECORD_ID = g.BO_TRANS_NODE_RECORD_ID and g.IS_DEL = 0 AND g.NODE_ID = 200
        WHERE
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                a.IS_DEL = 1
            </when>
            <otherwise>
                a.IS_DEL = 0
            </otherwise>
        </choose>
    </select>

    <select id="selectJYCountForDistributeOrder" resultType="java.lang.String" parameterType="com.wtyt.dao.bean.syf.BoTransPlanIBean">
        select
        count(1)
        from
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_REL b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK c ON
        b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID and c.IS_DEL = 0
        INNER JOIN
        (<include refid="getMergeIdForDistributeOrder"/>) d
        on a.merge_id = d.merge_id
        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA f ON
        f.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID AND f.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD g ON
        f.DISPATCH_CAR_RECORD_ID = g.BO_TRANS_NODE_RECORD_ID and g.IS_DEL = 0 AND g.NODE_ID = 200
        WHERE a.ORG_ID = #{orgId} and a.ORDER_TYPE =1  AND
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                a.IS_DEL = 1
            </when>
            <otherwise>
                a.IS_DEL = 0
            </otherwise>
        </choose>
    </select>

    <select id="selectByPlanIdListNotStowage" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        <include refid="com.wtyt.dao.mapper.syf.BoTransOrderMapper.orderColumn"/>
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.IS_DEL = 0
        AND O.BO_TRANS_PLAN_ID IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        AND NOT EXISTS (
        SELECT
        1
        FROM
        T_BO_TP_STOWAGE_ORDER_REL
        WHERE
        IS_DEL = 0
        AND STOWAGE_STATE IN (2, 3)
        AND BO_TRANS_ORDER_ID = O.BO_TRANS_ORDER_ID)
    </select>

    <select id="selectAllOrderRel" resultType="com.wtyt.dao.bean.syf.BoTpInitStowageOrderRelBean">
    SELECT
        O.MERGE_ID mergeId,
        O.SPILT_FLAG spiltFlag,
        O.BO_TRANS_ORDER_ID boTransOrderId,
        O.BO_TRANS_PLAN_ID boTransPlanId,
        O.ORG_ID orgId,
        O.USER_ID userId
    FROM
        T_BO_TRANS_ORDER O
    INNER JOIN (
        SELECT
            MERGE_ID
        FROM
            SYF.T_BO_TRANS_ORDER M
        WHERE
            M.IS_DEL = 0
            AND M.MERGE_ID IS NOT NULL
            AND NOT EXISTS (
            SELECT
                1
            FROM
                T_BO_TP_STOWAGE_ORDER_REL
            WHERE
                IS_DEL = 0
                AND BO_TRANS_ORDER_ID = M.BO_TRANS_ORDER_ID)
        GROUP BY
            MERGE_ID) M ON
        O.MERGE_ID = M.MERGE_ID
    WHERE
        O.IS_DEL = 0
	</select>

    <select id="getOwnerOrderList" resultType="com.wtyt.bo.bean.response.Req5330234OBean" parameterType="com.wtyt.bo.bean.request.Req5330234IBean">
        SELECT
            a.BO_TRANS_ORDER_ID boTransOrderId,
            a.START_PROVINCE_NAME startProvinceName,
            a.START_CITY_NAME startCityName,
            a.START_COUNTY_NAME startCountyName,
            a.START_ADDRESS startAddress
        FROM
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_PROGRESS b ON a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0 AND a.FROM_SOURCE = 4
        WHERE a.IS_DEL = 0 AND a.ORDER_TYPE = 1
        <!-- 传了orgInfos参数走新逻辑 -->
        <if test="orgInfos != null and orgInfos.size() > 0">
            AND(
            <foreach collection="orgInfos" open="(" close=")" separator="or" item="item">
                a.ORG_ID = #{item.orgId}
                <if test="item.businessCustomerList != null and item.businessCustomerList.size() > 0">
                    AND a.CUSTOMER_NAME IN
                    <foreach collection="item.businessCustomerList" open="(" close=")" separator="," item="itemName">
                        #{itemName}
                    </foreach>
                </if>
            </foreach>
            )
        </if>

        <!-- 没有传orgInfos参数走老逻辑 -->
        <if test="orgInfos == null">
            AND a.ORG_ID IN
            <foreach collection="orgIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            <if test="businessCustomerList != null and businessCustomerList.size() > 0">
                AND a.CUSTOMER_NAME IN
                <foreach collection="businessCustomerList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="startProvinceName != null and startProvinceName != ''">
            AND a.START_PROVINCE_NAME = #{startProvinceName}
        </if>
        <if test="startCityName != null and startCityName != ''">
            AND a.START_CITY_NAME = #{startCityName}
        </if>
        <if test="startCountyName != null and startCountyName != ''">
            AND a.START_COUNTY_NAME = #{startCountyName}
        </if>
        AND (
                (
                    a.FROM_SOURCE != 4
                    <if test="startDate != null and startDate != ''">
                        AND a.CREATED_TIME &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
                    </if>
                    <if test="endDate != null and endDate != ''">
                        AND a.CREATED_TIME &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
                    </if>
                )
                or
                (
                    a.FROM_SOURCE = 4
                    <if test="isUnderway != null and isUnderway == '1'.toString()">
                        AND b.STATE = 1
                    </if>
                    <if test="isUnderway != null and isUnderway == '0'.toString()">
                        <if test="startDate != null and startDate != ''">
                            AND a.CREATED_TIME &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
                        </if>
                        <if test="endDate != null and endDate != ''">
                            AND a.CREATED_TIME &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
                        </if>
                    </if>
                )
        )
    </select>

    <select id="getBusinessCustomerList" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT
            DISTINCT(TRIM(CUSTOMER_NAME))
        FROM
            T_BO_TRANS_ORDER
        WHERE IS_DEL = 0
        AND ORG_ID = #{orgId}
        AND CUSTOMER_NAME IS NOT NULL
    </select>

    <select id="getTransTaskIdListByTransOrderId" resultType="java.lang.String">
        SELECT
            T2.BO_TRANS_TASK_ID
        FROM T_BO_TRANS_ORDER T1
        JOIN T_BO_TRANS_ORDER_REL T2
            ON T2.BO_TRANS_ORDER_ID = T1.BO_TRANS_ORDER_ID AND T2.IS_DEL = 0
        WHERE T1.IS_DEL = 0
        AND T2.BO_TRANS_TASK_ID IS NOT NULL
        AND T2.BO_TRANS_ORDER_ID = #{transOrderId}
    </select>

    <select id="selectLotteryDriver" resultType="com.wtyt.dao.bean.syf.BoTransOrderLotteryRelBean">
        SELECT
        BO_TRANS_TASK_ID boTransTaskId , DRIVER_NAME driverName , DRIVER_MOBILE_NO mobileNo, CART_BADGE_NO cartBadgeNo
        FROM
        T_BO_ORDER_LOTTERY_REL O
        WHERE
        O.IS_DEL = 0  and LOTTERY_STATE =1
        AND O.BO_TRANS_TASK_ID IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectLotteryDriverAssign" resultType="com.wtyt.dao.bean.syf.BoLotteryDriverAssignBean">
        SELECT
        BO_LOTTERY_DRIVER_ASSIGN_ID boLotteryDriverAssignId,
        BO_TRANS_ORDER_ID boTransOrderId ,
        CPD_DRIVER_MOBILE_POOL_ID cpdDriverMobilePoolId ,
        DRIVER_NAME driverName ,
        DRIVER_MOBILE_NO driverMobileNo,
        CART_BADGE_NO cartBadgeNo
        FROM
        T_BO_LOTTERY_DRIVER_ASSIGN O
        WHERE
        O.IS_DEL = 0
        AND O.BO_TRANS_ORDER_ID IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>


    <select id="selectUnDispatchedMergeIdList" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT
        distinct a.MERGE_ID
        FROM
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_REL b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK c ON
        b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID and c.IS_DEL = 0
        WHERE
        a.IS_DEL = 0  AND a.ORG_ID= #{orgId}   and nvl(a.LOTTERY_STATE,0)  =0
        AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0
        AND a.MERGE_ID IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectOrderByMergeIdList" parameterType="java.lang.String" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        O.BO_TRANS_ORDER_ID boTransOrderId,
        O.MERGE_ID mergeId,
        O.START_PROVINCE_NAME startProvinceName,
        O.START_CITY_NAME startCityName,
        O.START_COUNTY_NAME startCountyName,
        O.END_PROVINCE_NAME endProvinceName,
        O.END_CITY_NAME endCityName,
        O.END_COUNTY_NAME endCountyName
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.IS_DEL = 0 AND O.ORG_ID= #{orgId}
        AND O.MERGE_ID IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        order  by o.BO_TRANS_ORDER_ID
    </select>



    <insert id="batchInsertLotteryRel">
        INSERT
        INTO
        T_BO_ORDER_LOTTERY_REL (BO_ORDER_LOTTERY_REL_ID,
        MERGE_ID,
        BO_TRANS_TASK_ID,
        LOTTERY_STATE,
        CPD_DRIVER_LOTTERY_REPID,
        DRIVER_NAME,
        DRIVER_MOBILE_NO,
        CART_BADGE_NO,
        CAPACITY_TYPE,
        CAPACITY_TYPE_NAME,
        CPD_POOL_GROUP_ID,
        IS_PARTAKE_OPERATE,
        CART_LENGTH,
        CART_TYPE,
        CART_BADGE_COLOR,
        CART_TONNAGE,
        SETTLE_MODE,
        XCY_USER_ID,
        XCY_USER_NAME,
        UNLOAD_TIME,
        USER_FREIGHT,
        GOODS_AMOUNT_TYPE,
        UNIT_PRICE,
        UN_LOTTERY_FROM,
        UN_LOTTERY_DESC )
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT #{item.boOrderLotteryRelId} boOrderLotteryRelId,
            #{item.mergeId} mergeId,
            #{item.boTransTaskId} boTransTaskId,
            #{item.lotteryState} lotteryState,
            #{item.cpdDriverLotteryRepId} cpdDriverLotteryRepId,
            #{item.driverName} driverName,
            #{item.mobileNo} driverMobileNo,
            #{item.cartBadgeNo} cartBadgeNo,
            #{item.capacityType} capacityType,
            #{item.capacityTypeName} capacityTypeName,
            #{item.orgCpdPoolGroupId} orgCpdPoolGroupId,
            #{item.isPartakeOperate} isPartakeOperate,
            #{item.cartLength} cartLength,
            #{item.cartType} cartType,
            #{item.cartBadgeColor} cartBadgeColor,
            #{item.cartTonnage} cartTonnage,
            #{item.settleMode} settleMode,
            #{item.xcyUserId} xcyUserId,
            #{item.xcyUserName} xcyUserName,
            TO_DATE(#{item.unLoadTime}, 'yyyy-mm-dd HH24:MI:ss') unLoadTime,
            #{item.userFreight} userFreight,
            #{item.goodsAmountType} goodsAmountType,
            #{item.unitPrice} unitPrice,
            #{item.unLotteryFrom} unLotteryFrom,
            #{item.unLotteryDesc} unLotteryDesc
            FROM DUAL
        </foreach>
        ) A
    </insert>

    <insert id="batchInsertLotteryRelDetail">
        INSERT
        INTO
        T_BO_LOTTERY_REL_DETAIL (BO_LOTTERY_REL_DETAIL_ID,
        BO_ORDER_LOTTERY_REL_ID,
        BO_TRANS_ORDER_ID)
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT #{item.boLotteryRelDetailId} boLotteryRelDetailId,
            #{item.boOrderLotteryRelId} boOrderLotteryRelId,
            #{item.boTransOrderId} boTransOrderId
            FROM DUAL
        </foreach>
        ) A
    </insert>

    <insert id="insertLotteryRelDetail">
        INSERT
        INTO
            T_BO_LOTTERY_REL_DETAIL (BO_LOTTERY_REL_DETAIL_ID,
                                     BO_ORDER_LOTTERY_REL_ID,
                                     BO_TRANS_ORDER_ID,
                                     LOTTERY_FROM_SOURCE)
        VALUES (
                   #{boLotteryRelDetailId},
                   #{boOrderLotteryRelId},
                   #{boTransOrderId},
                   #{lotteryFromSource})
    </insert>

    <update id="updateLotteryRel">
        UPDATE
        T_BO_ORDER_LOTTERY_REL
        SET
        <if test="boTransTaskId != null and boTransTaskId != ''">
            BO_TRANS_TASK_ID=#{boTransTaskId},
        </if>
        <if test="lotteryState != null and lotteryState != ''">
            LOTTERY_STATE=#{lotteryState},
        </if>
        <if test="unLotteryFrom != null and unLotteryFrom != ''">
            UN_LOTTERY_FROM=#{unLotteryFrom},
        </if>
        <if test="unLotteryDesc != null and unLotteryDesc != ''">
            UN_LOTTERY_DESC=#{unLotteryDesc},
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_ORDER_LOTTERY_REL_ID = #{boOrderLotteryRelId}
    </update>

    <update id="updateOrderLotteryState">
        UPDATE
            T_BO_TRANS_ORDER
        SET
            LOTTERY_STATE = #{lotteryState},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            MERGE_ID = #{mergeId}
    </update>

    <update id="updateOrderLotteryStateByOrderId">
        UPDATE
            T_BO_TRANS_ORDER
        SET
            LOTTERY_STATE = #{lotteryState},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>

    <update id="batchUpdateOrderLotteryState">
        UPDATE
            T_BO_TRANS_ORDER
        SET
            LOTTERY_STATE = #{lotteryState},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TRANS_ORDER_ID  in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>
    <update id="batchUpdateOrderByBoBusinessLineIds">
        DECLARE
        CNT NUMBER(10);
        BEGIN
        CNT := 0;
        FOR TEMP IN (
            SELECT
                o.BO_TRANS_ORDER_ID AS PRIMARY_KEY
            FROM
                T_BO_TRANS_ORDER_PROGRESS p JOIN T_BO_TRANS_ORDER o ON p.BO_TRANS_ORDER_ID = o.BO_TRANS_ORDER_ID
            where p.BO_BUSINESS_LINE_ID IN
                <foreach collection="boBusinessLineIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND (o.CUSTOMER_NAME IS NULL OR o.CUSTOMER_NAME != #{customerName})
        )
        LOOP
            UPDATE
                T_BO_TRANS_ORDER T
            SET
                T.CUSTOMER_NAME = #{customerName},
                T.LAST_MODIFIED_TIME = sysdate
            WHERE
                T.BO_TRANS_ORDER_ID = TEMP.PRIMARY_KEY;
        CNT := CNT + 1;
        IF (MOD(CNT, 1000) = 0) THEN
        COMMIT;
        END IF;
        END LOOP;
        COMMIT;
        END;
    </update>


    <select id="selectLotteryRelList" resultType="com.wtyt.dao.bean.syf.BoTransOrderLotteryRelBean">
        SELECT
        CPD_DRIVER_LOTTERY_REPID cpdDriverLotteryRepid,
        BO_TRANS_TASK_ID boTransTaskId
        FROM
        T_BO_ORDER_LOTTERY_REL O
        where IS_DEL =0
        AND  LOTTERY_STATE =1
        AND O.CPD_DRIVER_LOTTERY_REPID IN
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectLotteryRelByTaskId" resultType="com.wtyt.dao.bean.syf.BoTransOrderLotteryRelBean">
        SELECT
        BO_ORDER_LOTTERY_REL_ID boOrderLotteryRelId,
        MERGE_ID mergeId,
        BO_TRANS_TASK_ID boTransTaskId,
        CPD_DRIVER_LOTTERY_REPID cpdDriverLotteryRepid
        FROM
        T_BO_ORDER_LOTTERY_REL
        where IS_DEL =0
        AND  LOTTERY_STATE =1
        AND BO_TRANS_TASK_ID = #{boTransTaskId}
        AND ROWNUM=1
    </select>

    <resultMap id="JYLotteryMap" type="com.wtyt.dao.bean.syf.BoJYLotteryResultBean">
        <result property="mergeId" column="mergeId"/>
        <result property="driverName" column="driverName"/>
        <result property="driverMobileNo" column="driverMobileNo"/>
        <result property="cartBadgeNo" column="cartBadgeNo"/>
        <result property="lotteryState" column="lotteryState"/>
        <result property="dispatchCarResult" column="dispatchCarResult"/>
        <collection property="orderList" ofType="com.wtyt.dao.bean.syf.BoTransOrderBean">
            <result property="boTransOrderId" column="boTransOrderId"/>
            <result property="transDate" column="transDate"/>
            <result property="customerOrderNo" column="customerOrderNo"/>
            <result property="customerName" column="customerName"/>
            <result property="consigneeUnit" column="consigneeUnit"/>
            <result property="consigneeName" column="consigneeName"/>
            <result property="consigneeContact" column="consigneeContact"/>
            <result property="consigneeMobileNo" column="consigneeMobileNo"/>
            <result property="loadingPlace" column="loadingPlace"/>
            <result property="unloadingPlace" column="unloadingPlace"/>
            <result property="loadingArea" column="loadingArea"/>
            <result property="unloadingArea" column="unloadingArea"/>
            <result property="goodsCasePack" column="goodsCasePack"/>
            <result property="goodsWeight" column="goodsWeight"/>
            <result property="goodsVolume" column="goodsVolume"/>
            <result property="serviceRequire" column="serviceRequire"/>
            <result property="goodsName" column="goodsName"/>
        </collection>
    </resultMap>

    <select id="getJYLotteryList" resultMap="JYLotteryMap">
        select
        a.MERGE_ID mergeId,
        a.BO_TRANS_ORDER_ID boTransOrderId,
        TO_CHAR(a.TRANS_DATE, 'YYYY-MM-DD') transDate,
        a.CUSTOMER_ORDER_NO customerOrderNo,
        a.CUSTOMER_NAME customerName,
        a.CONSIGNEE_UNIT consigneeUnit,
        a.CONSIGNEE_NAME consigneeName,
        a.CONSIGNEE_CONTACT consigneeContact,
        a.CONSIGNEE_MOBILE_NO consigneeMobileNo,
        a.START_PROVINCE_NAME || a.START_CITY_NAME || a.START_COUNTY_NAME || a.START_ADDRESS loadingPlace,
        a.END_PROVINCE_NAME || a.END_CITY_NAME || a.END_COUNTY_NAME || a.END_ADDRESS unloadingPlace,
        a.START_CITY_NAME || a.START_COUNTY_NAME loadingArea,
        a.END_CITY_NAME || a.END_COUNTY_NAME unloadingArea,
        a.SERVICE_REQUIRE serviceRequire,
        a.GOODS_NAME goodsName,
        NVL2(a.GOODS_CASE_PACK,TO_CHAR(a.GOODS_CASE_PACK,'FM999999990.0000'),'0') goodsCasePack,
        NVL2(a.GOODS_WEIGHT,TO_CHAR(a.GOODS_WEIGHT,'FM999999990.0000'),'0') goodsWeight,
        NVL2(a.GOODS_VOLUME,TO_CHAR(a.GOODS_VOLUME,'FM999999990.0000'),'0') goodsVolume
        from
        T_BO_TRANS_ORDER a
        WHERE  a.IS_DEL = 0
        and a.MERGE_ID in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        ORDER BY
        a.URGENT_MARK desc, a.MERGE_ID,a.CREATED_TIME

    </select>

    <resultMap id="JYLotteryOrderMap" type="com.wtyt.dao.bean.syf.BoJYLotteryOrderBean">
        <result property="mergeId" column="mergeId"/>
        <collection property="orderList" ofType="com.wtyt.dao.bean.syf.BoTransOrderBean">
            <result property="orgId" column="orgId"/>
            <result property="boTransOrderId" column="boTransOrderId"/>
            <result property="urgentMark" column="urgentMark"/>
            <result property="startProvinceName" column="startProvinceName"/>
            <result property="startCityName" column="startCityName"/>
            <result property="startCountyName" column="startCountyName"/>
            <result property="endProvinceName" column="endProvinceName"/>
            <result property="endCityName" column="endCityName"/>
            <result property="endCountyName" column="endCountyName"/>
            <result property="endAddress" column="endAddress"/>
        </collection>
    </resultMap>



    <select id="getJYLotteryOrderList" resultMap="JYLotteryOrderMap">
        select
         a.MERGE_ID mergeId,
         a.BO_TRANS_ORDER_ID boTransOrderId,
         a.START_PROVINCE_NAME startProvinceName,
         a.START_CITY_NAME  startCityName,
         a.START_COUNTY_NAME  startCountyName,
         a.END_PROVINCE_NAME  endProvinceName,
         a.END_CITY_NAME endCityName,
         a.END_COUNTY_NAME endCountyName,
         a.END_ADDRESS endAddress
        from
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_REL b ON a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK c ON b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID and c.IS_DEL = 0
        WHERE  a.IS_DEL = 0
        AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0  AND NVL(a.LOTTERY_STATE,0) = #{lotteryState}
        <if test="lineList != null and lineList.size() > 0">
            AND(
            <foreach item="item" index="index" collection="lineList" separator="or">
                (
                 a.ORG_ID = #{item.orgId}
                <if test="item.startProvinceName != null and item.startProvinceName != ''">
                  and  a.START_PROVINCE_NAME = #{item.startProvinceName}
                </if>
                <if test="item.startCityName != null and item.startCityName != ''">
                    and a.START_CITY_NAME = #{item.startCityName}
                </if>
                <if test="item.startCountyName != null and item.startCountyName != ''">
                    and a.START_COUNTY_NAME = #{item.startCountyName}
                </if>
                <if test="item.endProvinceName != null and item.endProvinceName != ''">
                    and a.END_PROVINCE_NAME = #{item.endProvinceName}
                </if>
                <if test="item.endCityName != null and item.endCityName != ''">
                    and a.END_CITY_NAME = #{item.endCityName}
                </if>
                <if test="item.endCountyName != null and item.endCountyName != ''">
                    and a.END_COUNTY_NAME = #{item.endCountyName}
                </if>
                <if test="item.boTransOrderIds != null and item.boTransOrderIds.size() > 0">
                    and a.BO_TRANS_ORDER_ID IN
                    <foreach collection="item.boTransOrderIds" index="index" open="(" close=")" item="id" separator=",">
                        <if test="(index % 999) == 998"> NULL) OR a.BO_TRANS_ORDER_ID IN(</if>#{id}
                    </foreach>
                </if>
                )
            </foreach>)
        </if>
        ORDER BY
        a.URGENT_MARK desc, a.MERGE_ID,a.CREATED_TIME

    </select>

    <select id="selectJYLotteryMergeOrderList" resultMap="JYLotteryOrderMap">
        select
        a.MERGE_ID mergeId,
        a.ORG_ID  orgId,
        a.BO_TRANS_ORDER_ID boTransOrderId,
        a.URGENT_MARK urgentMark,
        a.START_PROVINCE_NAME startProvinceName,
        a.START_CITY_NAME  startCityName,
        a.START_COUNTY_NAME  startCountyName,
        a.END_PROVINCE_NAME  endProvinceName,
        a.END_CITY_NAME endCityName,
        a.END_COUNTY_NAME endCountyName,
        a.END_ADDRESS endAddress
        from
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_REL b ON a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK c ON b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID and c.IS_DEL = 0
        WHERE  a.IS_DEL = 0
        AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0
        and a.MERGE_ID in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        ORDER BY
        a.URGENT_MARK desc, a.MERGE_ID,a.CREATED_TIME
    </select>



    <select id="selectByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoTransOrderRelBean">
        SELECT
        R.BO_TRANS_ORDER_REL_ID boTransOrderRelId,
        R.ORG_ID orgId,
        R.USER_ID userId,
        R.BO_TRANS_ORDER_ID boTransOrderId,
        R.TAX_WAYBILL_ID taxWaybillId,
        R.BO_TRANS_TASK_ID boTransTaskId,
        TO_CHAR(R.GOODS_CASE_PACK,'FM999999990.0000') goodsCasePack,
        TO_CHAR(R.GOODS_WEIGHT,'FM999999990.0000') goodsWeight,
        TO_CHAR(R.GOODS_VOLUME,'FM999999990.0000') goodsVolume,
        T.TAX_WAYBILL_NO taxWaybillNo,
        R.DISTRIBUTE_STATE distributeState
        FROM
        T_BO_TRANS_ORDER_REL R
        LEFT JOIN T_BO_TRANS_ORDER O ON O.BO_TRANS_ORDER_ID = R.BO_TRANS_ORDER_ID
        LEFT JOIN T_BO_TRANS_TASK T ON T.BO_TRANS_TASK_ID = R.BO_TRANS_TASK_ID
        WHERE
        R.IS_DEL = 0
        AND O.IS_DEL = 0
        AND R.BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="queryJYUnDispatchOrderList" resultType="com.wtyt.bo.bean.JYUnDispatchOrder">
        SELECT
            o.BO_TRANS_ORDER_ID boTransOrderId,
            o.GOODS_NAME goodsName,
            o.GOODS_WEIGHT goodsWeight,
            o.GOODS_CASE_PACK goodsCasePack,
            o.START_COUNTY_NAME startCountyName,
            o.END_COUNTY_NAME endCountyName
            <if test="queryAllFields">
                ,TO_CHAR(o.CREATED_TIME, 'YYYYMMDD') createdDate,
                TO_CHAR(o.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
                o.CUSTOMER_NAME customerName,
                o.START_ADDRESS startAddress,
                o.END_ADDRESS endAddress,
                o.START_PROVINCE_NAME startProvinceName,
                o.START_CITY_NAME startCityName,
                o.START_COUNTY_NAME startCountyName,
                o.END_PROVINCE_NAME endProvinceName,
                o.END_CITY_NAME endCityName,
                o.GOODS_VOLUME goodsVolume,
                o.SERVICE_REQUIRE serviceRequire,
                o.CUSTOMER_ORDER_NO customerOrderNo,
                o.CONSIGNEE_NAME consigneeName,
                o.CONSIGNEE_UNIT consigneeUnit,
                o.CONSIGNEE_MOBILE_NO consigneeMobileNo,
                o.CONSIGNMENT_NAME consignmentName,
                o.CONSIGNMENT_MOBILE_NO consignmentMobileNo,
                o.GOODS_CASE_PACK_REMAIN goodsCasePackRemain,
                o.GOODS_WEIGHT_REMAIN goodsWeightRemain,
                o.GOODS_VOLUME_REMAIN goodsVolumeRemain,
                o.THIRD_ORDER_NO thirdOrderNo,
                o.MILEAGE,
                CASE WHEN  TO_NUMBER(TO_CHAR(o.CREATED_TIME, 'HH24')) >= 12
                THEN '12点后'
                ELSE '12点前' END timeNode,
                E.BO_TRANS_TASK_ID boTransTaskId,
                CASE WHEN E.BO_TRANS_TASK_ID IS NULL THEN '否' ELSE '是' END AS isCreatedTask,
                TO_CHAR(E.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') taskCreatedTime
            </if>
        FROM
            T_BO_TRANS_ORDER O
        LEFT JOIN T_BO_TRANS_ORDER_REL R ON o.bo_trans_order_id = r.bo_trans_order_id AND r.is_del = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA E ON r.bo_trans_task_id = e.bo_trans_task_id AND e.is_del = 0
        WHERE
            o.is_del = 0
        AND o.org_id = #{orgId}
        AND e.DISPATCH_CAR_RECORD_ID IS NULL
        <if test="customerNameList != null and customerNameList.size() > 0">
            AND o.CUSTOMER_NAME IN
            <foreach collection="customerNameList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startDate != null and startDate !=''">
            AND o.created_time >= TO_DATE(#{startDate} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate !=''">
            AND o.created_time &lt;= TO_DATE(#{endDate} || '23:59:59','YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="receivePlanNode != null and receivePlanNode !=''">
            <choose>
                <when test="receivePlanNode == '1'.toString()">
                    AND TO_NUMBER(TO_CHAR(o.CREATED_TIME, 'HH24')) >= 12
                </when>
                <otherwise>
                    AND TO_NUMBER(TO_CHAR(o.CREATED_TIME, 'HH24')) &lt; 12
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="queryOrderByMergeId"  resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        O.BO_TRANS_ORDER_ID boTransOrderId,
        O.MERGE_ID mergeId,
        O.CUSTOMER_ORDER_NO customerOrderNo
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.IS_DEL = 0
        <if test="mergeId != null and mergeId != ''">
            AND O.MERGE_ID = #{mergeId}
        </if>
        <if test="orderIdList != null and orderIdList.size() > 0">
            AND O.BO_TRANS_ORDER_ID IN
            <foreach collection="orderIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        order  by o.BO_TRANS_ORDER_ID
    </select>

    <select id="selectLotteryRelOrderIdList" resultType="String">
        SELECT
         BO_TRANS_ORDER_ID boTransOrderId
        FROM
        T_BO_LOTTERY_REL_DETAIL
        where IS_DEL =0
        AND BO_ORDER_LOTTERY_REL_ID = #{boOrderLotteryRelId}
        ORDER BY BO_TRANS_ORDER_ID
    </select>


    <select id="getJoinLotteryOrderList" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        select
        a.MERGE_ID mergeId,
        a.BO_TRANS_ORDER_ID boTransOrderId
        from
        T_BO_TRANS_ORDER a LEFT JOIN T_BO_TRANS_ORDER_REL b ON a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID and b.IS_DEL = 0
                           LEFT JOIN T_BO_TRANS_TASK c ON b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID and c.IS_DEL = 0
        WHERE  a.IS_DEL = 0
        and a.MERGE_ID = #{mergeId}
        AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0   <include refid="cpdLotteryLineId"/>
    </select>


    <select id="queryOrgConsigneeUnit"  resultType="String">
        select consigneeUnit from ( SELECT
        O.CONSIGNEE_UNIT consigneeUnit ,count(1)
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.IS_DEL = 0 AND O.ORG_ID= #{orgId}
        AND O.CONSIGNEE_UNIT IS NOT NULL
        <if test="consigneeUnit != null and consigneeUnit != ''">
            AND O.CONSIGNEE_UNIT LIKE '%' || #{consigneeUnit} || '%'
        </if>
        and O.CREATED_TIME >= ADD_MONTHS(SYSDATE, - 6)
        GROUP by CONSIGNEE_UNIT
        ORDER BY count(1) desc) where ROWNUM &lt;= 50
    </select>

    <select id="queryOrgEndAddressList"  resultType="com.wtyt.bo.bean.BoOrderEndAddressBean">
        select provinceName,cityName,countyName,addressName,latitude,longitude
        from ( SELECT
        O.END_PROVINCE_NAME provinceName,O.END_CITY_NAME cityName ,O.END_COUNTY_NAME countyName , O.END_ADDRESS
        addressName ,MAX(O.END_LAT) latitude,MAX(O.END_LNG) longitude,COUNT(1)
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.IS_DEL = 0 AND O.ORG_ID= #{orgId}
        AND O.END_ADDRESS IS NOT NULL
        AND O.END_LAT IS NOT NULL
        AND O.END_LNG IS NOT NULL
        <if test="consigneeUnit != null and consigneeUnit != ''">
            AND O.CONSIGNEE_UNIT LIKE '%' || #{consigneeUnit} || '%'
        </if>
        <if test="storageAddress != null and storageAddress != ''">
            AND O.END_ADDRESS LIKE '%' || #{storageAddress} || '%'
        </if>
        and O.CREATED_TIME >= ADD_MONTHS(SYSDATE, - 6)
        GROUP by O.END_ADDRESS ,O.END_PROVINCE_NAME ,O.END_CITY_NAME ,O.END_COUNTY_NAME
        ORDER BY count(1) desc) where ROWNUM &lt;= 50
    </select>
    <select id="selectUnDispatchedOrderIdList" resultType="java.lang.String">
        SELECT
            a.BO_TRANS_ORDER_ID
        FROM
            T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_TRANS_ORDER_REL b ON
            a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID
            AND b.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK c ON
            b.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID
            AND c.IS_DEL = 0
        WHERE
            a.IS_DEL = 0
            AND DECODE(c.NODE_ID, 0, 0, 100, 0, 300, 0, NULL, 0, 1) = 0
            AND a.BO_TRANS_ORDER_ID IN
            <foreach collection="list" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
    </select>
    <select id="queryPredictLateTaskList" resultType="com.wtyt.board.bean.PredictLateTaskBean">
        SELECT
            t.TAX_WAYBILL_NO taxWaybillNo,
            t.ORG_ID orgId,
            r.USER_ID dispatchUserId,
            t.START_PROVINCE_NAME || t.START_CITY_NAME || t.START_COUNTY_NAME || e.LOADING_ADDRESS_NAME startPlace,
            t.END_PROVINCE_NAME || t.END_CITY_NAME || t.END_COUNTY_NAME || e.UNLOADING_ADDRESS_NAME endPlace,
            t.DRIVER_NAME driverName,
            t.MOBILE_NO mobileNo,
            t.CART_BADGE_NO cartBadgeNo,
            a.EXTEND overtimeStr,
            TO_CHAR(t.CREATED_TIME, 'YYYY-MM-DD') createdTime
        FROM
            syf.T_BO_TRANS_TASK t
        JOIN syf.T_BO_TRANS_TASK_EXTRA e ON e.IS_DEL = 0 AND t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
        JOIN syf.T_BO_TRANS_NODE_RECORD r ON e.DISPATCH_CAR_RECORD_ID = r.BO_TRANS_NODE_RECORD_ID
        JOIN syf.T_BO_TRANS_NODE_ALARM a ON
            a.IS_DEL = 0
            AND a.ALARM_TYPE = 0
            AND a.ALARM_PROCESS_RESULT = 0
            AND a.NODE_DATA_TYPE = #{nodeDataType}
            AND t.BO_TRANS_TASK_ID = a.BO_TRANS_TASK_ID
        WHERE
            t.IS_DEL = 0
            AND a.IS_DEL = 0
            AND t.ORG_ID IN
            <foreach collection="orgIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="beginTime != null and beginTime !=''">
                AND t.CREATED_TIME &gt;= TO_DATE(#{beginTime} || '00:00:00','YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endTime != null and endTime !=''">
                AND t.CREATED_TIME &lt;= TO_DATE(#{endTime} || ' 23:59:59','YYYY-MM-DD HH24:MI:SS')
            </if>
            AND a.EXTEND IS NOT NULL
            ORDER BY t.CREATED_TIME DESC
    </select>
    <select id="queryUnDispatchTaskIds" resultType="java.lang.String">
        SELECT
            e.BO_TRANS_TASK_ID
        FROM
            T_BO_TRANS_ORDER o
        JOIN T_BO_TRANS_ORDER_REL r ON
            o.BO_TRANS_ORDER_ID = r.BO_TRANS_ORDER_ID
        JOIN T_BO_TRANS_TASK_EXTRA e ON
            r.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
        WHERE
            o.IS_DEL = 0
            AND r.IS_DEL = 0
            AND e.IS_DEL = 0
            AND e.DISPATCH_CAR_RECORD_ID IS NULL
            AND o.BO_TRANS_ORDER_ID = #{boTransOrderId}
    </select>
    <select id="queryOrderGoodsAmount" resultType="com.wtyt.common.beans.OrderGoodsAmountBean">
        SELECT
            TO_CHAR(o.GOODS_CASE_PACK, 'FM999999990.0000') goodsCasePack,
            TO_CHAR(o.GOODS_WEIGHT, 'FM999999990.0000') goodsWeight,
            TO_CHAR(o.GOODS_VOLUME, 'FM999999990.0000') goodsVolume,
            TO_CHAR(o.GOODS_CASE_PACK_REMAIN, 'FM999999990.0000') goodsCasePackRemain,
            TO_CHAR(o.GOODS_VOLUME_REMAIN, 'FM999999990.0000') goodsVolumeRemain,
            TO_CHAR(o.GOODS_WEIGHT_REMAIN, 'FM999999990.0000') goodsWeightRemain,
            TO_CHAR(NVL(o.LOADING_CAR_NUM, 0), 'FM999999990') loadingCarNum,
            (NVL(o.LOADING_CAR_NUM, 0) - NVL(r.DISPATCH_CAR_NUM, 0)) LoadingCarNumRemain,
            r.SPILT_UNIT_FIELD spiltUnitField
        FROM
            T_BO_TRANS_ORDER o
        LEFT JOIN (
            SELECT
                BO_TRANS_ORDER_ID ,
                count(*) DISPATCH_CAR_NUM ,
                max(SPILT_UNIT_FIELD) SPILT_UNIT_FIELD
            FROM
                T_BO_TRANS_ORDER_REL
            WHERE
                IS_DEL = 0
                AND BO_TRANS_ORDER_ID = #{boTransOrderId}
            GROUP BY
                BO_TRANS_ORDER_ID
                    ) r ON
            o.BO_TRANS_ORDER_ID = r.BO_TRANS_ORDER_ID
        WHERE
            o.IS_DEL = 0
            AND o.BO_TRANS_ORDER_ID = #{boTransOrderId}
    </select>


</mapper>
