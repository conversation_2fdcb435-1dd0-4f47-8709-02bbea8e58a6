<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dl.mapper.syf.DlExportMapper">
    <resultMap type="com.wtyt.dl.bean.DlExportBean" id="DlExportMap">
        <result property="dlExportId" column="DL_EXPORT_ID" jdbcType="VARCHAR"/>
        <result property="dlCategoryId" column="DL_CATEGORY_ID" jdbcType="VARCHAR"/>
        <result property="exportKey" column="EXPORT_KEY" jdbcType="VARCHAR"/>
        <result property="exportName" column="EXPORT_NAME" jdbcType="VARCHAR"/>
        <result property="selectedMode" column="SELECTED_MODE" jdbcType="VARCHAR"/>
        <result property="forceSelect" column="FORCE_SELECT" jdbcType="VARCHAR"/>
        <result property="exportSql" column="EXPORT_SQL" jdbcType="VARCHAR"/>
        <result property="exportWidth" column="EXPORT_WIDTH" jdbcType="VARCHAR"/>
        <result property="pages" column="PAGES" jdbcType="VARCHAR"/>
        <result property="scopes" column="SCOPES" jdbcType="VARCHAR"/>
        <result property="scopesExplain" column="SCOPES_EXPLAIN" jdbcType="VARCHAR"/>
        <result property="renderConfig" column="RENDER_CONFIG" jdbcType="VARCHAR"/>
        <result property="sortNo" column="SORT_NO" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="getListByCategory" resultMap="DlExportMap">
        SELECT
            T.DL_EXPORT_ID ,
            T.DL_CATEGORY_ID,
            T.EXPORT_KEY,
            T.EXPORT_NAME,
            T.EXPORT_WIDTH,
            T.FORCE_SELECT,
            T.SELECTED_MODE,
            T.EXPORT_SQL,
            T.PAGES,
            T.SCOPES,
            T.RENDER_CONFIG
        FROM
            T_DL_EXPORT T
        WHERE
            T.IS_DEL = 0
            AND (T.PAGES IS NULL OR INSTR(',' || T.PAGES || ',', ',' || #{page} || ',') > 0)
            AND T.DL_CATEGORY_ID IN
            <foreach collection="categoryIdList" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        ORDER BY
            T.SORT_NO ASC
    </select>
</mapper>