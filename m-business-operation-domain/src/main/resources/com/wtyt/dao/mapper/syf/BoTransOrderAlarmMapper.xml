<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransOrderAlarmMapper">

    <sql id="baseColumn" >
            BO_TRANS_ORDER_ALARM_ID boTransOrderAlarmId,
            BO_TRANS_ORDER_ID boTransOrderId,
            ALARM_TYPE alarmType,
            ALARM_PROCESS_RESULT alarmProcessResult,
            TO_CHAR(ALARM_START_TIME, 'YYYY-MM-DD HH24:MI:SS') alarmStartTime,
            TO_CHAR(ALARM_END_TIME, 'YYYY-MM-DD HH24:MI:SS') alarmEndTime
    </sql>


    <select id="selectByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoTransOrderAlarmBean">
        SELECT
        <include refid="baseColumn"/>
        FROM
        T_BO_TRANS_ORDER_ALARM
        WHERE
        IS_DEL = 0
        AND BO_TRANS_ORDER_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>


    <update id="batchUpdateOrderAlarmEndTime">
        MERGE INTO T_BO_TRANS_ORDER_ALARM t
        USING (
             <foreach collection="list" item="item" separator=" UNION ALL ">
                SELECT
                    #{item.boTransOrderAlarmId} AS boTransOrderAlarmId,
                    #{item.alarmProcessResult} AS alarmProcessResult
                FROM DUAL
            </foreach>
            ) tmp
        ON (t.BO_TRANS_ORDER_ALARM_ID = tmp.boTransOrderAlarmId AND t.IS_DEL = 0)
        WHEN MATCHED THEN
        UPDATE SET
        t.ALARM_PROCESS_RESULT = tmp.alarmProcessResult,
        t.ALARM_END_TIME = SYSDATE,
        t.LAST_MODIFIED_TIME = SYSDATE
   </update>

    <update id="batchUpdateOrderAlarmEndTimeIsNull">
        MERGE INTO T_BO_TRANS_ORDER_ALARM t
        USING (
             <foreach collection="list" item="item" separator=" UNION ALL ">
                SELECT
                    #{item.boTransOrderAlarmId} AS boTransOrderAlarmId,
                    #{item.alarmProcessResult} AS alarmProcessResult
                FROM DUAL
            </foreach>
            ) tmp
        ON (t.BO_TRANS_ORDER_ALARM_ID = tmp.boTransOrderAlarmId AND t.IS_DEL = 0)
        WHEN MATCHED THEN
        UPDATE SET
        t.ALARM_PROCESS_RESULT = tmp.alarmProcessResult,
        t.ALARM_END_TIME = null,
        t.LAST_MODIFIED_TIME = SYSDATE
   </update>

    <select id="getAlarmStateByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoTransOrderAlarmBean">
        SELECT
            ALARM_TYPE alarmType,
            CASE WHEN ALARM_TYPE = 0 THEN ROUND((NVL(ALARM_START_TIME, SYSDATE) - SYSDATE) * 24 * 3600)
                 WHEN ALARM_TYPE = 1 THEN ROUND((NVL(ALARM_END_TIME, SYSDATE) - NVL(ALARM_START_TIME, CREATED_TIME)) * 24 * 3600)
             END overTime,
            BO_TRANS_ORDER_ID boTransOrderId
        FROM
        T_BO_TRANS_ORDER_ALARM
        WHERE
        BO_TRANS_ORDER_ID IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND IS_DEL = 0
        AND (ALARM_TYPE = 1 OR (ALARM_TYPE=0 AND ALARM_PROCESS_RESULT = 0))
        ORDER BY
        ALARM_TYPE DESC,
        overTime desc
    </select>

    <update id="updateWarningProcessed">
        UPDATE T_BO_TRANS_ORDER_ALARM T
        SET T.ALARM_PROCESS_RESULT = 1,
            T.ALARM_END_TIME =
                CASE
                    WHEN ALARM_START_TIME IS NOT NULL AND ALARM_END_TIME IS NULL
                        THEN SYSDATE
                    ELSE ALARM_END_TIME
                    END,
            T.LAST_MODIFIED_TIME = SYSDATE
        WHERE T.IS_DEL = 0
          AND ALARM_PROCESS_RESULT = 0
          AND ALARM_TYPE = 0
          AND BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>


    <select id="getAlarmIsExist" resultType="java.lang.Boolean">
        select case when
                exists(SELECT 1 FROM T_BO_TRANS_ORDER_ALARM T WHERE T.IS_DEL = 0
                        AND T.ALARM_TYPE = #{alarmType}  AND T.ALARM_PROCESS_RESULT IN (0,2)
                        AND T.BO_TRANS_ORDER_ID = #{boTransOrderId})
                then 1
            else 0 end
        from dual
    </select>

    <insert id="insert" parameterType="com.wtyt.dao.bean.syf.BoTransOrderAlarmBean">
        INSERT INTO T_BO_TRANS_ORDER_ALARM (
            BO_TRANS_ORDER_ALARM_ID ,
            BO_TRANS_ORDER_ID,
            ALARM_TYPE,
            <if test="alarmStartTime != null and alarmStartTime != ''">
                ALARM_START_TIME,
            </if>
            <if test="alarmEndTime != null and alarmEndTime != ''">
                ALARM_END_TIME,
            </if>
            ALARM_PROCESS_RESULT
        ) VALUES (
            #{boTransOrderAlarmId},
            #{boTransOrderId},
            #{alarmType},
            <if test="alarmStartTime != null and alarmStartTime != ''">
                TO_DATE(#{alarmStartTime}, 'YYYY-MM-DD HH24:MI:SS'),
            </if>
            <if test="alarmEndTime != null and alarmEndTime != ''">
                TO_DATE(#{alarmEndTime}, 'YYYY-MM-DD HH24:MI:SS'),
            </if>
            #{alarmProcessResult}
        )
    </insert>

    <select id="queryWarningAlarmList" resultType="com.wtyt.dao.bean.syf.BoTransOrderAlarmBean">
       SELECT
        <include refid="baseColumn"/>
        FROM
        T_BO_TRANS_ORDER_ALARM
        WHERE
        IS_DEL = 0
        AND ALARM_TYPE = 0
        AND ALARM_PROCESS_RESULT =0
        AND BO_TRANS_ORDER_ID = #{boTransOrderId}
        ORDER BY CREATED_TIME DESC
    </select>

     <update id="updateProcessResult">
        UPDATE
            T_BO_TRANS_ORDER_ALARM
        SET
            ALARM_PROCESS_RESULT = 1,
            <if test="alarmEndTime != null and alarmEndTime != ''">
                ALARM_END_TIME = TO_DATE(#{alarmEndTime}, 'YYYY-MM-DD HH24:MI:SS'),
            </if>
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TRANS_ORDER_ALARM_ID = #{boTransOrderAlarmId}
            AND IS_DEL = 0
            AND ALARM_PROCESS_RESULT  =0
    </update>
</mapper>