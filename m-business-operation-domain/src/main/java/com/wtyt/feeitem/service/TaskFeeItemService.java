package com.wtyt.feeitem.service;

import com.wtyt.bo.bean.BoTaskDetailBean;
import com.wtyt.bo.mapper.syf.BoTaskDetailMapper;
import com.wtyt.common.toolkits.ListToolkit;
import com.wtyt.dao.bean.syf.BoTransTaskFeeItemBean;
import com.wtyt.dao.mapper.syf.BoTransTaskFeeItemMapper;
import com.wtyt.feeitem.bean.TaskFeeItemBean;
import com.wtyt.feeitem.bean.request.Req5330293IBean;
import com.wtyt.feeitem.bean.response.Resp5330293OBean;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 费用明细清单
 *
 * <AUTHOR>
 * @since 2023-04-06 10:04:22
 */
@Service
public class TaskFeeItemService {

    private static final Logger logger = LoggerFactory.getLogger(TaskFeeItemService.class);

    @Autowired
    private BoTaskDetailMapper boTaskDetailMapper;

    @Autowired
    private BoTransTaskFeeItemMapper boTransTaskFeeItemMapper;

    /**
     * 查询运输任务对应费用明细清单
     *
     * @param data
     * @return
     */
    public Resp5330293OBean queryTaskFeeItemDetail(Req5330293IBean data) {
        Resp5330293OBean result = new Resp5330293OBean();
        BoTaskDetailBean detailBean = boTaskDetailMapper.getTaskDetailById(data.getBoTransTaskId(), data.getTaxWaybillId());
        if (detailBean == null) {
            return result;
        }
        //否则判定是取哪里的数据
        boolean isAllocate = false;
        if (StringUtils.isNotBlank(data.getTaxWaybillId())
                && StringUtils.equals(data.getTaxWaybillId(), detailBean.getAllocateTaxWaybillId())) {
            //传过来运单id,且运单id和分配表的一致
            isAllocate = true;
        } else if (StringUtils.isNotBlank(data.getOrgId())
                && StringUtils.isNotBlank(data.getBoTransTaskId())
                && !StringUtils.equals(data.getOrgId(), detailBean.getOwnerOrgId())) {
            //传过来项目ID和任务ID并且项目是和货主项目不一致，找下游的
            isAllocate = true;
        }
        //查询清单
        List<BoTransTaskFeeItemBean> taskFeeList = boTransTaskFeeItemMapper.queryFeeItemByTaskId(detailBean.getBoTransTaskId(), isAllocate ? "1" : "0", data.getFeeItemType());
        try {
            List<TaskFeeItemBean> resultList = ListToolkit.copyProp(taskFeeList, TaskFeeItemBean.class);
            result.setFeeItemList(resultList);
        } catch (Exception e) {
            logger.info("拷贝对象异常，暂不返回数据");
        }
        return result;
    }
}
