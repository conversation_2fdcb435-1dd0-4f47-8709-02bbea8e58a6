package com.wtyt.dao.bean.syf;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/5/30 10:53
 */
public class CommonlyUsedLinesBean implements Serializable {
    private static final long serialVersionUID = 6420576559336197628L;
    /**
     * 主键
     */
    private String mDriverOrgCommonLineId;
    /**
     * 企业id
     */
    private String orgId;
    /**
     * 司机手机号
     */
    private String mobileNo;
    /**
     * 车牌号
     */
    private String cartBadgeNo;
    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 起始地省份
     */
    private String startProvinceName;
    /**
     * 起始地省份code
     */
    private String startProvinceCode;
    /**
     * 起始地城市
     */
    private String startCityName;
    /**
     * 起始地城市code
     */
    private String startCityCode;
    /**
     * 目的地省份
     */
    private String endProvinceName;
    /**
     * 目的地省份code
     */
    private String endProvinceCode;
    /**
     * 目的地城市
     */
    private String endCityName;
    /**
     * 目的地城市code
     */
    private String endCityCode;

    public String getmDriverOrgCommonLineId() {
        return mDriverOrgCommonLineId;
    }

    public void setmDriverOrgCommonLineId(String mDriverOrgCommonLineId) {
        this.mDriverOrgCommonLineId = mDriverOrgCommonLineId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getCartBadgeNo() {
        return cartBadgeNo;
    }

    public void setCartBadgeNo(String cartBadgeNo) {
        this.cartBadgeNo = cartBadgeNo;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getStartProvinceName() {
        return startProvinceName;
    }

    public void setStartProvinceName(String startProvinceName) {
        this.startProvinceName = startProvinceName;
    }

    public String getStartProvinceCode() {
        return startProvinceCode;
    }

    public void setStartProvinceCode(String startProvinceCode) {
        this.startProvinceCode = startProvinceCode;
    }

    public String getStartCityName() {
        return startCityName;
    }

    public void setStartCityName(String startCityName) {
        this.startCityName = startCityName;
    }

    public String getStartCityCode() {
        return startCityCode;
    }

    public void setStartCityCode(String startCityCode) {
        this.startCityCode = startCityCode;
    }

    public String getEndProvinceName() {
        return endProvinceName;
    }

    public void setEndProvinceName(String endProvinceName) {
        this.endProvinceName = endProvinceName;
    }

    public String getEndProvinceCode() {
        return endProvinceCode;
    }

    public void setEndProvinceCode(String endProvinceCode) {
        this.endProvinceCode = endProvinceCode;
    }

    public String getEndCityName() {
        return endCityName;
    }

    public void setEndCityName(String endCityName) {
        this.endCityName = endCityName;
    }

    public String getEndCityCode() {
        return endCityCode;
    }

    public void setEndCityCode(String endCityCode) {
        this.endCityCode = endCityCode;
    }
}
