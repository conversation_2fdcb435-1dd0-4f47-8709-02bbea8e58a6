<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoInquireOfferMapper">

    <select id="selectByOrderId" resultType="com.wtyt.dao.bean.syf.BoInquireOfferBean">
	SELECT
		OFR.BO_INQUIRE_OFFER_ID boInquireOfferId,
		OFR.BO_TRANS_ORDER_ID boTransOrderId,
		OFR.BO_INQUIRE_ASK_ID boInquireAskId,
		OFR.CREATED_USER_ID createdUserId,
		OFR.APPLY_USER_ID applyUserId,
		OFR.APPLY_STATE applyState,
		TO_CHAR(OFR.ALL_FEE, 'FM999999990.00') allFee,
		TO_CHAR(OFR.OIL_FEE, 'FM999999990.00') oilFee,
		OFR.OIL_RATIO oilRatio,
		E.BO_INQUIRE_ID boInquireId,
		TO_CHAR(OFR.OFFER_TIME, 'YYYY-MM-DD HH24:MI:SS') offerTime,
		O.CUSTOMER_ORDER_NO customerOrderNo
	FROM
		T_BO_INQUIRE_OFFER OFR
	INNER JOIN T_BO_TRANS_ORDER O ON
		O.BO_TRANS_ORDER_ID = OFR.BO_TRANS_ORDER_ID
	INNER JOIN T_BO_INQUIRE E ON
		E.BO_TRANS_ORDER_ID = O.BO_TRANS_ORDER_ID
	WHERE
		OFR.IS_DEL = 0
		AND O.IS_DEL = 0
		<if test="inquireState != null and inquireState != ''">
			AND E.INQUIRE_STATE = 2
		</if>
		AND OFR.BO_TRANS_ORDER_ID = #{orderId}
		ORDER BY OFR.OFFER_TIME DESC
    </select>

	<select id="selectByPkId" resultType="com.wtyt.dao.bean.syf.BoInquireOfferBean">
	SELECT
		OFR.BO_INQUIRE_OFFER_ID boInquireOfferId,
		OFR.BO_TRANS_ORDER_ID boTransOrderId,
		OFR.BO_INQUIRE_ASK_ID boInquireAskId,
		OFR.CREATED_USER_ID createdUserId,
		OFR.APPLY_USER_ID applyUserId,
		TO_CHAR(OFR.ALL_FEE, 'FM999999990.00') allFee,
		TO_CHAR(OFR.OIL_FEE, 'FM999999990.00') oilFee,
		OFR.APPLY_STATE applyState,
		TO_CHAR(OFR.OIL_RATIO, 'FM999999990.0000') oilRatio,
		OFR.OFFER_TIME offerTime,
		E.INQUIRE_STATE inquireState,
		E.BO_INQUIRE_ID boInquireId,
		TO_CHAR(OFR.LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI:SS') applyTime,
		TO_CHAR(E.ASK_TIME, 'YYYY-MM-DD HH24:MI:SS') askTime,
		E.ASK_USER_ID askUserId
	FROM
		T_BO_INQUIRE_OFFER OFR
	INNER JOIN T_BO_TRANS_ORDER O ON
		O.BO_TRANS_ORDER_ID = OFR.BO_TRANS_ORDER_ID
	INNER JOIN T_BO_INQUIRE E ON
		E.BO_TRANS_ORDER_ID = O.BO_TRANS_ORDER_ID
	WHERE
		OFR.IS_DEL = 0
		AND O.IS_DEL = 0
		AND OFR.BO_INQUIRE_OFFER_ID = #{offerId}
    </select>


	<update id="update" parameterType="BoInquireOfferBean">
		UPDATE T_BO_INQUIRE_OFFER SET
		<if test="applyState != null and applyState != '' ">
			APPLY_STATE = #{applyState},
		</if>
		<if test="applyUserId != null and applyUserId != '' ">
			APPLY_USER_ID = #{applyUserId},
		</if>
		<if test="allFee != null">
			ALL_FEE = #{allFee},
		</if>
		<if test="oilFee != null">
			OIL_FEE = #{oilFee},
		</if>
		<if test="oilRatio != null">
			OIL_RATIO = #{oilRatio},
		</if>
		<if test="offerTime != null and offerTime != '' ">
			OFFER_TIME = to_date(#{offerTime},'YYYY-MM-DD HH24:MI:SS'),
		</if>
		<if test="agreeTime != null ">
			AGREE_TIME = SYSDATE,
		</if>
		LAST_MODIFIED_TIME = SYSDATE
		WHERE BO_INQUIRE_OFFER_ID = #{boInquireOfferId}
	</update>

	<select id="selectApplyList" resultType="com.wtyt.dao.bean.syf.Req5329126OBean">
	SELECT
		O.BO_TRANS_ORDER_ID boTransOrderId,
		<if test="type == 1">
			(SELECT COUNT(1) FROM T_BO_INQUIRE_ASK WHERE BO_TRANS_ORDER_ID = I.BO_TRANS_ORDER_ID) allOfferNum,
			(SELECT COUNT(1) FROM T_BO_INQUIRE_OFFER WHERE BO_TRANS_ORDER_ID = I.BO_TRANS_ORDER_ID) offerNum,
		</if>
		O.CUSTOMER_ORDER_NO customerOrderNo,
		O.START_PROVINCE_NAME startProvinceName,
		O.START_CITY_NAME startCityName,
		O.START_COUNTY_NAME startCountyName,
		O.START_ADDRESS startAddress,
		O.END_PROVINCE_NAME endProvinceName,
		O.END_CITY_NAME endCityName,
		O.END_COUNTY_NAME endCountyName,
		O.END_ADDRESS endAddress,
		O.MILEAGE mileage,
		O.GOODS_NAME goodsName,
		O.GOODS_VOLUME goodsVolume,
		O.GOODS_WEIGHT goodsWeight,
		O.GOODS_CASE_PACK goodsCasePack,
		I.BO_INQUIRE_OFFER_ID boInquireOfferId
	FROM
		T_BO_INQUIRE I
	INNER JOIN T_BO_TRANS_ORDER O ON O.BO_TRANS_ORDER_ID = I.BO_TRANS_ORDER_ID
	<if test="type == 2">
		LEFT JOIN T_BO_INQUIRE_OFFER OFR ON OFR.BO_INQUIRE_OFFER_ID  = I.BO_INQUIRE_OFFER_ID
	</if>
	WHERE
	I.IS_DEL = 0
	AND O.IS_DEL = 0
	AND O.ORG_ID = #{orgId}
	<if test="orderNo != null and orderNo != ''">
		AND O.CUSTOMER_ORDER_NO = #{orderNo}
	</if>
	<if test="dispatcherId != null and dispatcherId != ''">
		AND EXISTS (SELECT
		1
		FROM
		T_BO_INQUIRE I
		INNER JOIN T_BO_TRANS_ORDER OO ON
		OO.BO_TRANS_ORDER_ID = I.BO_TRANS_ORDER_ID
		INNER JOIN T_BO_INQUIRE_OFFER OFR ON
		<if test="type == 1">
			OFR.BO_TRANS_ORDER_ID = I.BO_TRANS_ORDER_ID
		</if>
		<if test="type == 2">
			OFR.BO_INQUIRE_OFFER_ID = I.BO_INQUIRE_OFFER_ID
		</if>
		WHERE
		OO.IS_DEL = 0
		AND OFR.IS_DEL = 0
		AND OO.BO_TRANS_ORDER_ID = O.BO_TRANS_ORDER_ID
		AND OFR.CREATED_USER_ID =#{dispatcherId})
	</if>
	<if test="type == 1">
		AND I.INQUIRE_STATE = 2
		AND EXISTS (SELECT 1 FROM T_BO_INQUIRE_OFFER WHERE IS_DEL = 0 AND BO_TRANS_ORDER_ID = O.BO_TRANS_ORDER_ID)
		AND I.FIRST_OFFER_TIME IS NOT NULL
		ORDER BY I.FIRST_OFFER_TIME
	</if>
	<if test="type == 2">
		AND I.INQUIRE_STATE = 3
		<if test="startTime != null and startTime != '' and endTime != null and endTime != '' ">
			AND OFR.AGREE_TIME BETWEEN TO_DATE(#{startTime}, 'yyyy-mm-dd HH24:MI:ss') AND TO_DATE(#{endTime}, 'yyyy-mm-dd HH24:MI:ss')
		</if>
		AND I.BO_INQUIRE_OFFER_ID IS NOT NULL
		ORDER BY OFR.AGREE_TIME  DESC
	</if>
    </select>

	<select id="selectAskDetailInfoList" resultType="com.wtyt.dao.bean.syf.OfferInfoBean">
	SELECT
		*
	FROM
	(
	SELECT
		TO_CHAR(OFR.ALL_FEE, 'FM999999990.00') allFee,
		TO_CHAR(OFR.OIL_FEE, 'FM999999990.00') oilFee,
		TO_CHAR(OFR.OIL_RATIO, 'FM999999990.0000') oilRatio,
		TO_CHAR(OFR.OFFER_TIME, 'YYYY-MM-DD HH24:MI:SS') offerTime,
		OFR.APPLY_STATE applyState,
		A.USER_ID userId,
		OFR.BO_INQUIRE_OFFER_ID boInquireOfferId,
		OFR.CREATED_USER_ID offerUserId,
		A.ASK_STATE askState,
		DECODE(APPLY_STATE, NULL,99, APPLY_STATE) sort
	FROM
		T_BO_INQUIRE_ASK A
	LEFT JOIN T_BO_INQUIRE_OFFER OFR ON
		A.BO_INQUIRE_ASK_ID = OFR.BO_INQUIRE_ASK_ID
	WHERE
		A.IS_DEL = 0
		AND A.BO_TRANS_ORDER_ID = #{boTransOrderId})
	ORDER BY
		sort ,offerTime DESC
	</select>

	<insert id="insertOffer" parameterType="BoInquireOfferBean">
		INSERT INTO T_BO_INQUIRE_OFFER(
            BO_INQUIRE_OFFER_ID,
            BO_TRANS_ORDER_ID,
            BO_INQUIRE_ASK_ID,
            CREATED_USER_ID,
		    APPLY_USER_ID,
		    APPLY_STATE,
			ALL_FEE,
			OIL_FEE,
		    OIL_RATIO,
		    OFFER_TIME,
            IS_DEL,
            CREATED_TIME,
            LAST_MODIFIED_TIME)
        VALUES (
            #{boInquireOfferId},
            #{boTransOrderId},
            #{boInquireAskId},
            #{createdUserId},
            #{applyUserId},
            #{applyState},
            TO_CHAR(#{allFee}, 'FM999999990.00'),
		    TO_CHAR( #{oilFee}, 'FM999999990.00'),
            #{oilRatio},
            SYSDATE,
            0,
            SYSDATE,
            SYSDATE
            )
	</insert>

</mapper>