package com.wtyt.commons.toolkits;

import com.wtyt.bo.service.EventProcessService;
import com.wtyt.common.enums.EventProcessorEnum;
import com.wtyt.common.enums.EventTypeEnum;
import com.wtyt.common.toolkits.GsonToolkit;
import com.wtyt.common.toolkits.SpringContextToolkit;
import com.wtyt.commons.bean.EventArguments;
import com.wtyt.commons.bean.EventMessage;
import com.wtyt.commons.context.ProcessContext;
import com.wtyt.commons.context.SubmitContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年06月15日 20时12分
 */
public class EventProcessToolkit {

    private static final Logger LOGGER = LoggerFactory.getLogger(EventProcessToolkit.class);

    private EventProcessToolkit() {
        throw new AssertionError("Cannot initialize");
    }

    /**
     * 提交任务
     * @param eventProcessor
     * @param eventArguments
     */
    public static void submit(EventProcessorEnum eventProcessor, EventArguments eventArguments) {
        String processorName = eventProcessor.getProcessorName();
        int processCounter = eventArguments.getProcessCounter();
        eventArguments.setProcessCounter(++processCounter);
        Integer retryCounter = eventArguments.getRetryCounter();
        if (retryCounter != null) {
            eventArguments.setRetryCounter(++retryCounter);
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("进入事件任务提交:eventName = {}、eventArguments = {}",
                    eventProcessor.name(), GsonToolkit.beanToJson(eventArguments));
        }
        EventProcessService eventProcessService = SpringContextToolkit.getBean(processorName, EventProcessService.class);
        SubmitContext submitContext = new SubmitContext();
        submitContext.setEventProcessor(eventProcessor);
        submitContext.setEventArguments(eventArguments);
        eventProcessService.submit(submitContext);
    }

    /**
     * 处理任务
     * @param eventMessage
     */
    public static void process(EventMessage eventMessage) throws Exception {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("进入事件任务处理:eventName = {}、eventTypes = {}、eventArguments = {}",
                    eventMessage.getEventName(), eventMessage.getEventTypes(), GsonToolkit.beanToJson(eventMessage.getEventArguments()));
        }
        String eventName = eventMessage.getEventName();
        String eventTypes = eventMessage.getEventTypes();
        List<EventTypeEnum> eventTypeList = new ArrayList<>(2);
        for (String eventType : StringUtils.split(eventTypes, ",")) {
            EventTypeEnum eventTypeEnum = EventTypeEnum.getEventTypeEnum(eventType);
            eventTypeList.add(eventTypeEnum);
        }
        EventProcessorEnum eventProcessor = EventProcessorEnum.getEventProcessorEnum(eventName);
        EventProcessService eventProcessService = SpringContextToolkit.getBean(eventProcessor.getProcessorName(), EventProcessService.class);
        ProcessContext processContext = new ProcessContext();
        processContext.setEventProcessor(eventProcessor);
        processContext.setEventTypeList(eventTypeList);
        processContext.setEventArguments(eventMessage.getEventArguments());
        processContext.setExpectExecTime(eventMessage.getExpectProcessTime());
        eventProcessService.process(processContext);
    }
}
