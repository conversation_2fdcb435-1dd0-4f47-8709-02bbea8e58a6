<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoShippingListMapper">
    <resultMap type="com.wtyt.dao.bean.syf.BoShippingListBean" id="BoShippingListMap">
        <result property="boShippingListId" column="BO_SHIPPING_LIST_ID" jdbcType="VARCHAR"/>
        <result property="boTransTaskId" column="BO_TRANS_TASK_ID" jdbcType="VARCHAR"/>
        <result property="boTransOrderId" column="BO_TRANS_ORDER_ID" jdbcType="VARCHAR"/>
        <result property="customerOrderNo" column="CUSTOMER_ORDER_NO" jdbcType="VARCHAR"/>
        <result property="customerName" column="CUSTOMER_NAME" jdbcType="VARCHAR"/>
        <result property="goodsName" column="GOODS_NAME" jdbcType="VARCHAR"/>
        <result property="goodsCasePack" column="GOODS_CASE_PACK" jdbcType="VARCHAR"/>
        <result property="loadingSequence" column="LOADING_SEQUENCE" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="VARCHAR"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="VARCHAR"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="goodsWeight" column="GOODS_WEIGHT" jdbcType="VARCHAR"/>
        <result property="goodsVolume" column="GOODS_VOLUME" jdbcType="VARCHAR"/>
        <result property="unloadingInfo" column="UNLOADING_INFO" jdbcType="VARCHAR"/>
        <result property="storagePlace" column="STORAGE_PLACE" jdbcType="VARCHAR"/>
        <result property="createdUserId" column="CREATED_USER_ID" jdbcType="VARCHAR"/>
        <result property="createdSysRoleType" column="CREATED_SYS_ROLE_TYPE" jdbcType="VARCHAR"/>
        <result property="lastModifiedUserId" column="LAST_MODIFIED_USER_ID" jdbcType="VARCHAR"/>
        <result property="lastModifiedSysRoleType" column="LAST_MODIFIED_SYS_ROLE_TYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="saveBatch">
        INSERT INTO T_BO_SHIPPING_LIST (
            BO_SHIPPING_LIST_ID,
            BO_TRANS_TASK_ID,
            BO_TRANS_ORDER_ID,
            BO_TRANS_PLAN_ID,
            CUSTOMER_ORDER_NO,
            CUSTOMER_NAME,
            GOODS_NAME,
            GOODS_SPEC,
            TRANS_DATE,
            GOODS_CASE_PACK,
            GOODS_CASE_PACK_UNIT,
            LOADING_SEQUENCE,
            GOODS_WEIGHT,
            GOODS_WEIGHT_UNIT,
            GOODS_VOLUME,
            GOODS_VOLUME_UNIT,
            UNLOADING_INFO,
            STORAGE_PLACE,
            THIRD_ORDER_NO,RECEIPT_NO,
            SERVICE_REQUIRE,
            LOADING_INFO,
            CREATED_USER_ID,
            CREATED_SYS_ROLE_TYPE,
            LAST_MODIFIED_USER_ID,
            LAST_MODIFIED_SYS_ROLE_TYPE,
            CONSIGNEE_UNIT,
            PRINT_STATUS
        )
        <foreach item="item" index="index" collection="list" separator="UNION ALL">
            SELECT
            #{item.boShippingListId} boShippingListId,
            #{item.boTransTaskId} boTransTaskId,
            #{item.boTransOrderId} boTransOrderId,
            #{item.boTransPlanId} boTransPlanId,
            #{item.customerOrderNo} customerOrderNo,
            #{item.customerName} customerName,
            #{item.goodsName} goodsName,
            #{item.goodsSpec} goodsSpec,
            to_date(#{item.transDate},'YYYY-MM-DD HH24:MI:SS'),
            #{item.goodsCasePack} goodsCasePack,
            <choose>
                <when test="item.goodsCasePackUnit != null and item.goodsCasePackUnit.length > 0">
                    #{item.goodsCasePackUnit} goodsCasePackUnit,
                </when>
                <otherwise>'件',</otherwise>
            </choose>
            #{item.loadingSequence} loadingSequence,
            #{item.goodsWeight} goodsWeight,
            <choose>
                <when test="item.goodsWeightUnit != null and item.goodsWeightUnit.length > 0">
                    #{item.goodsWeightUnit} goodsWeightUnit,
                </when>
                <otherwise>'吨',</otherwise>
            </choose>
            #{item.goodsVolume} goodsVolume,
            <choose>
                <when test="item.goodsVolumeUnit != null and item.goodsVolumeUnit.length > 0">
                    #{item.goodsVolumeUnit} goodsVolumeUnit,
                </when>
                <otherwise>'方',</otherwise>
            </choose>
            #{item.unloadingInfo} unloadingInfo,
            #{item.storagePlace} storagePlace,
            #{item.thirdOrderNo} thirdOrderNo,
            #{item.receiptNo} receiptNo,
            #{item.serviceRequire} serviceRequire,
            #{item.loadingInfo},
            #{item.createdUserId} createdUserId,
            #{item.createdSysRoleType} createdSysRoleType,
            #{item.lastModifiedUserId} lastModifiedUserId,
            #{item.lastModifiedSysRoleType} lastModifiedSysRoleType,
            #{item.consigneeUnit} consigneeUnit,
            0
            FROM
            DUAL
        </foreach>
    </insert>
    <update id="updateBatchFull">
        <foreach collection="list" index="index" item="item" open="begin" close=";end;" separator=";">
            UPDATE T_BO_SHIPPING_LIST
            SET
                CUSTOMER_ORDER_NO = #{item.customerOrderNo},
                GOODS_CASE_PACK = #{item.goodsCasePack},
                GOODS_WEIGHT = #{item.goodsWeight},
                GOODS_VOLUME = #{item.goodsVolume},
                GOODS_NAME = #{item.goodsName},
                LOADING_INFO = #{item.loadingInfo},
                UNLOADING_INFO = #{item.unloadingInfo},
                CUSTOMER_NAME = #{item.customerName},
                STORAGE_PLACE = #{item.storagePlace},
                LAST_MODIFIED_TIME = SYSDATE,
                LAST_MODIFIED_USER_ID = #{item.lastModifiedUserId},
                LAST_MODIFIED_SYS_ROLE_TYPE = #{item.lastModifiedSysRoleType}
            WHERE BO_SHIPPING_LIST_ID = #{item.boShippingListId}
        </foreach>
    </update>

    <select id="getShippingListByTaskId" resultType="com.wtyt.dao.bean.syf.BoShippingListBean">
        SELECT
            <include refid="commonField"/>
        FROM T_BO_SHIPPING_LIST
        WHERE IS_DEL = 0
        AND BO_TRANS_TASK_ID = #{boTransTaskId}
        ORDER BY CREATED_TIME DESC, BO_SHIPPING_LIST_ID ASC
    </select>

    <sql id="commonField">
        BO_SHIPPING_LIST_ID boShippingListId,
        BO_TRANS_TASK_ID boTransTaskId,
        BO_TRANS_ORDER_ID boTransOrderId,
        CUSTOMER_ORDER_NO customerOrderNo,
        THIRD_ORDER_NO thirdOrderNo,
        RECEIPT_NO receiptNo,
        SERVICE_REQUIRE serviceRequire,
        CUSTOMER_NAME customerName,
        to_char(TRANS_DATE, 'YYYY-MM-DD') transDate,
        GOODS_NAME goodsName,
        NVL2(GOODS_CASE_PACK,TO_CHAR(GOODS_CASE_PACK,'FM999999990.0099'),'0') goodsCasePack,
        GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        NVL2(GOODS_WEIGHT,TO_CHAR(GOODS_WEIGHT,'FM999999990.0099'),'0') goodsWeight,
        GOODS_WEIGHT_UNIT goodsWeightUnit,
        NVL2(GOODS_VOLUME,TO_CHAR(GOODS_VOLUME,'FM999999990.0099'),'0') goodsVolume,
        GOODS_VOLUME_UNIT goodsVolumeUnit,
        UNLOADING_INFO unloadingInfo,
        LOADING_INFO loadingInfo,
        STORAGE_PLACE storagePlace,
        LOADING_SEQUENCE loadingSequence,
        GOODS_SPEC goodsSpec,
        CONSIGNEE_UNIT consigneeUnit
    </sql>

    <select id="getShippingListIgnoreTaskIsDel" resultType="com.wtyt.dao.bean.syf.BoShippingListBean">
        SELECT
            <include refid="commonField"/>
        FROM T_BO_SHIPPING_LIST
        WHERE
        BO_TRANS_TASK_ID = #{boTransTaskId}
        AND (IS_DEL = 0 OR DEL_TYPE=1)
        ORDER BY CREATED_TIME DESC, BO_SHIPPING_LIST_ID ASC
    </select>

    <select id="getShippingListById" resultType="com.wtyt.dao.bean.syf.BoShippingListBean">
        select
            BO_SHIPPING_LIST_ID boShippingListId,
            BO_TRANS_TASK_ID boTransTaskId,
            BO_TRANS_ORDER_ID boTransOrderId,
            CUSTOMER_ORDER_NO customerOrderNo,
            CUSTOMER_NAME customerName,
            GOODS_NAME goodsName,
            GOODS_CASE_PACK goodsCasePack,
            GOODS_CASE_PACK_UNIT goodsCasePackUnit,
            GOODS_WEIGHT goodsWeight,
            GOODS_WEIGHT_UNIT goodsWeightUnit,
            GOODS_VOLUME goodsVolume,
            GOODS_VOLUME_UNIT goodsVolumeUnit,
            UNLOADING_INFO unloadingInfo,
            STORAGE_PLACE storagePlace,
            LOADING_SEQUENCE loadingSequence,
            RECEIPT_NO receiptNo,
            SERVICE_REQUIRE serviceRequire,
            to_char(TRANS_DATE, 'YYYY-MM-DD') transDate,
            LOADING_INFO loadingInfo,
            THIRD_ORDER_NO thirdOrderNo,
            CONSIGNEE_UNIT consigneeUnit,
            GOODS_SPEC  goodsSpec
        from T_BO_SHIPPING_LIST
        where BO_SHIPPING_LIST_ID = #{boShippingListId}
          and is_del = 0
    </select>

    <update id="delShippingListById" parameterType="com.wtyt.dao.bean.syf.BoShippingListBean">
        UPDATE T_BO_SHIPPING_LIST
        SET IS_DEL                      = 1,
            LAST_MODIFIED_TIME          = SYSDATE,
            LAST_MODIFIED_USER_ID       = #{lastModifiedUserId},
            LAST_MODIFIED_SYS_ROLE_TYPE = #{lastModifiedSysRoleType}
            WHERE BO_SHIPPING_LIST_ID = #{boShippingListId}
    </update>
    <update id="batchDelShippingListById">
        UPDATE T_BO_SHIPPING_LIST
        SET IS_DEL = 1,
        LAST_MODIFIED_TIME = SYSDATE,
        LAST_MODIFIED_USER_ID = #{userId},
        LAST_MODIFIED_SYS_ROLE_TYPE = #{sysRoleType}
        WHERE BO_SHIPPING_LIST_ID IN
        <foreach collection="boShippingListIdList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND IS_DEL = 0
    </update>

    <update id="updateShippingListById" parameterType="com.wtyt.dao.bean.syf.BoShippingListBean">
        UPDATE T_BO_SHIPPING_LIST
        SET
        <if test="goodsCasePack != null and goodsCasePack != ''">
            GOODS_CASE_PACK = #{goodsCasePack},
        </if>
        <if test="goodsWeight != null and goodsWeight != ''">
            GOODS_WEIGHT = #{goodsWeight},
        </if>
        <if test="goodsVolume != null and goodsVolume != ''">
            GOODS_VOLUME = #{goodsVolume},
        </if>
        <if test="receiptNo != null and receiptNo != ''">
            RECEIPT_NO = #{receiptNo},
        </if>
        STORAGE_PLACE = #{storagePlace},
        LOADING_SEQUENCE = #{loadingSequence},
        LAST_MODIFIED_TIME = SYSDATE,
        LAST_MODIFIED_USER_ID = #{lastModifiedUserId},
        LAST_MODIFIED_SYS_ROLE_TYPE = #{lastModifiedSysRoleType}
        WHERE BO_SHIPPING_LIST_ID = #{boShippingListId}
    </update>

    <update id="batchUpdatePrintStatusByIds" parameterType="map">
        UPDATE T_BO_SHIPPING_LIST
        SET PRINT_STATUS = 1,
        LAST_MODIFIED_TIME = SYSDATE,
        LAST_MODIFIED_USER_ID = #{userId},
        LAST_MODIFIED_SYS_ROLE_TYPE = #{sysRoleType}
        WHERE BO_SHIPPING_LIST_ID IN
        <foreach collection="boShippingListIdList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND IS_DEL = 0
    </update>

    <select id="batchQueryHasShippingList" resultType="com.wtyt.dao.bean.syf.BoShippingListBean">
        SELECT
        bt.TAX_WAYBILL_ID taxWayBillId,
        max(bsl.BO_SHIPPING_LIST_ID)  boShippingListId
        FROM
        T_BO_TRANS_TASK bt
        LEFT JOIN T_BO_SHIPPING_LIST bsl ON bsl.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID  AND bsl.IS_DEL = 0
        WHERE
        bt.IS_DEL = 0
        AND bt.TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group  by  bt.TAX_WAYBILL_ID

        UNION ALL

        SELECT
        TA.TAX_WAYBILL_ID taxWayBillId,
        max(bsl.BO_SHIPPING_LIST_ID)  boShippingListId
        FROM
        T_BO_TRANS_TASK bt
        INNER JOIN T_BO_TRANS_TASK_ALLOCATE TA ON TA.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID AND TA.IS_DEL = 0 AND TA.TAX_WAYBILL_ID IS NOT NULL
        LEFT JOIN T_BO_SHIPPING_LIST bsl ON bsl.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID  AND bsl.IS_DEL = 0
        WHERE
        bt.IS_DEL = 0
        AND TA.TAX_WAYBILL_ID IN
        <foreach collection="taxWaybillIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group  by  TA.TAX_WAYBILL_ID
    </select>

    <select id="queryServiceRequire" resultType="String">
        SELECT
        e.SERVICE_REQUIRE serviceRequire
        from T_BO_TRANS_TASK_EXTRA e
        where e.is_del = 0
        and e.BO_TRANS_TASK_ID = #{boTransTaskId}

    </select>

    <select id="getTotalGoodsWeight" resultType="String">
        select
            TO_CHAR(NVL(SUM(GOODS_WEIGHT),0),'FM999999990.0099') as goodsWeight
        from T_BO_SHIPPING_LIST
        where BO_TRANS_TASK_ID = #{boTransTaskId}
          and is_del = 0
    </select>

    <select id="getArriveEndTime" resultType="com.wtyt.dao.bean.syf.BoTransTaskExtraBean">
        select
            TRAILER_CART_BADGE_NO trailerCartBadgeNo,
            TO_CHAR(ARRIVE_END_TIME, 'YYYY-MM-DD HH24:MI:SS') arriveEndTime,
            UNLOADING_ADDRESS_NAME unloadingAddressName
        from T_BO_TRANS_TASK_EXTRA
        where BO_TRANS_TASK_ID = #{boTransTaskId}
        and is_del = 0
    </select>


    <select id="getTotalGoodsAmountInfo" resultType="com.wtyt.dao.bean.syf.BoShippingListBean">
        select
            TO_CHAR(nvl(SUM(GOODS_CASE_PACK),0),'FM999999990.0099') as goodsCasePack,
            TO_CHAR(NVL(SUM(GOODS_WEIGHT),0),'FM999999990.0099') as goodsWeight,
            TO_CHAR(NVL(SUM(GOODS_VOLUME),0),'FM999999990.0099') as goodsVolume
        from T_BO_SHIPPING_LIST
        where BO_TRANS_TASK_ID = #{boTransTaskId}
          and is_del = 0
    </select>

    <update id="updateGoodsAmountById" parameterType="com.wtyt.dao.bean.syf.BoShippingListBean">
        UPDATE T_BO_SHIPPING_LIST
        SET
        <if test="goodsCasePack != null and goodsCasePack != ''">
            GOODS_CASE_PACK = #{goodsCasePack},
        </if>
        <if test="goodsWeight != null and goodsWeight != ''">
            GOODS_WEIGHT = #{goodsWeight},
        </if>
        <if test="goodsVolume != null and goodsVolume != ''">
            GOODS_VOLUME = #{goodsVolume},
        </if>
        <if test="storagePlace != null and storagePlace != ''">
            STORAGE_PLACE = #{storagePlace},
        </if>
        <if test="loadingInfo != null and loadingInfo != ''">
            LOADING_INFO = #{loadingInfo},
        </if>
        <if test="unloadingInfo != null and unloadingInfo != ''">
            UNLOADING_INFO = #{unloadingInfo},
        </if>
        <if test="receiptNo != null and receiptNo != ''">
            RECEIPT_NO = #{receiptNo},
        </if>
        <if test="thirdOrderNo != null and thirdOrderNo != ''">
            THIRD_ORDER_NO = #{thirdOrderNo},
        </if>
        <if test="serviceRequire != null and serviceRequire != ''">
            SERVICE_REQUIRE = #{serviceRequire},
        </if>
        <if test="customerOrderNo != null and customerOrderNo != ''">
            CUSTOMER_ORDER_NO = #{customerOrderNo},
        </if>
        <if test="customerName != null and customerName != ''">
            CUSTOMER_NAME = #{customerName},
        </if>
        <if test="transDate != null and transDate != ''">
            TRANS_DATE= to_date(#{transDate},'yyyy-mm-dd'),
        </if>
        <if test="goodsName != null and goodsName != ''">
            GOODS_NAME = #{goodsName},
        </if>
        <if test="goodsSpec != null and goodsSpec != ''">
            GOODS_SPEC = #{goodsSpec},
        </if>
        LAST_MODIFIED_TIME = SYSDATE,
        LAST_MODIFIED_USER_ID = #{lastModifiedUserId},
        LAST_MODIFIED_SYS_ROLE_TYPE = #{lastModifiedSysRoleType}
        WHERE BO_SHIPPING_LIST_ID = #{boShippingListId}
    </update>


    <select id="queryBoTaskFeeInfo" resultType="com.wtyt.dao.bean.syf.BoTaskFeeInfoBean">
        SELECT
            T.BO_TRANS_TASK_ID  boTransTaskId,
            TO_CHAR(NVL(T.FREIGHT_INCR,0),'FM999999990.00') freightIncr,
            TO_CHAR(NVL(T.LOSS_FEE,0),'FM999999990.00') lossFee,
            TO_CHAR(NVL(T.USER_FREIGHT, 0) , 'FM999999990.00')  userFreight,
            TO_CHAR(NVL(T.ALL_FREIGHT, 0) , 'FM999999990.00')  allFreight,
            TO_CHAR(NVL(TF.CONFIG_VALUE, 0) , 'FM999999990.00') fuelCostFee,
            TF.REASON oilCardNo,
            TO_CHAR(NVL(OH.CONFIG_VALUE, 0) , 'FM999999990.00') overhead
        FROM T_BO_TRANS_TASK T
                 LEFT JOIN T_BO_TRANS_TASK_FEE TF ON TF.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND TF.IS_DEL = 0 AND TF.CONFIG_KEY = 'fuelCostFee'
                 LEFT JOIN T_BO_TRANS_TASK_FEE OH ON OH.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND OH.IS_DEL = 0 AND OH.CONFIG_KEY = 'overhead'
        WHERE T.IS_DEL = 0
          AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </select>
    <select id="batchQueryByBoTransTaskIdList" resultType="com.wtyt.dao.bean.syf.BoShippingListBean"
            parameterType="java.util.List">
        SELECT
        bt.BO_TRANS_TASK_ID boTransTaskId,
        bsl.CUSTOMER_ORDER_NO customerOrderNo,
        bsl.BO_TRANS_ORDER_ID boTransOrderId,
        bsl.CUSTOMER_NAME customerName,
        bsl.BO_SHIPPING_LIST_ID boShippingListId,
        bsl.STORAGE_PLACE storagePlace
        FROM
        T_BO_TRANS_TASK bt
        LEFT JOIN T_BO_SHIPPING_LIST bsl ON bsl.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID AND bsl.IS_DEL = 0
        WHERE
        bt.IS_DEL = 0
        AND bt.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="queryBoTransTaskIdListByThirdOrderNo" resultType="java.lang.String"
            parameterType="java.lang.String">
        SELECT BO_TRANS_TASK_ID FROM T_BO_SHIPPING_LIST WHERE IS_DEL = 0 AND THIRD_ORDER_NO like '%' || #{thirdOrderNo} || '%' ORDER BY BO_TRANS_TASK_ID DESC
    </select>
    <select id="queryShippingListBeanByPermissionAndId" resultType="com.wtyt.dao.bean.syf.BoShippingListBean">
        SELECT
        BSL.BO_TRANS_TASK_ID boTransTaskId,
        BSL.CUSTOMER_ORDER_NO customerOrderNo,
        BSL.BO_TRANS_ORDER_ID boTransOrderId,
        BSL.BO_SHIPPING_LIST_ID boShippingListId,
        BSL.CUSTOMER_NAME customerName,
        BSL.RECEIPT_NO receiptNo,
        BSL.GOODS_CASE_PACK goodsCasePack,
        BSL.GOODS_WEIGHT goodsWeight,
        BSL.CONSIGNEE_UNIT consigneeUnit
        FROM
        T_BO_SHIPPING_LIST BSL
        WHERE
        BSL.IS_DEL = 0
        AND BSL.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND BSL.RECEIPT_NO IS NOT NULL
        <if test="shippingListPermissionList != null and shippingListPermissionList.size() > 0">
            AND (
            <foreach collection="shippingListPermissionList" item="shippingListPermission" open="(" separator="OR" close=")">
                ${shippingListPermission.condition}
            </foreach>
            )
        </if>
        <if test="customerNameList != null and customerNameList.size() > 0">
            AND BSL.CUSTOMER_NAME IN
            <foreach collection="customerNameList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryDistinctCustomerNameByPermission" resultType="java.lang.String">
        SELECT DISTINCT
        BSL.CUSTOMER_NAME customerName
        FROM
        T_BO_SHIPPING_LIST BSL
        WHERE
        BSL.IS_DEL = 0
        AND BSL.BO_TRANS_TASK_ID = #{boTransTaskId}
        AND (
        <foreach collection="shippingListPermissionList" item="shippingListPermission" open="(" separator="OR" close=")">
            ${shippingListPermission.condition}
        </foreach>
        )
        AND BSL.CUSTOMER_NAME IS NOT NULL
    </select>

    <select id="queryByBoTransTaskIdList" resultType="com.wtyt.dao.bean.syf.BoShippingListBean"
            parameterType="java.util.List">
        SELECT
        BSL.BO_TRANS_TASK_ID boTransTaskId,
        BSL.THIRD_ORDER_NO thirdOrderNo,
        BSL.CUSTOMER_ORDER_NO customerOrderNo,
        BSL.BO_TRANS_ORDER_ID boTransOrderId,
        BSL.BO_SHIPPING_LIST_ID boShippingListId,
        BSL.CUSTOMER_NAME customerName,
        BSL.RECEIPT_NO receiptNo,
        CASE WHEN TRUNC(BSL.GOODS_CASE_PACK)=BSL.GOODS_CASE_PACK THEN TO_CHAR(BSL.GOODS_CASE_PACK,'FM9999999990') ELSE TO_CHAR(BSL.GOODS_CASE_PACK,'FM9999999990.0999') END goodsCasePack,
        CASE WHEN TRUNC(BSL.GOODS_WEIGHT)=BSL.GOODS_WEIGHT THEN TO_CHAR(BSL.GOODS_WEIGHT,'FM9999999990') ELSE TO_CHAR(BSL.GOODS_WEIGHT,'FM9999999990.0999') END goodsWeight,
        CASE WHEN TRUNC(BSL.GOODS_VOLUME)=BSL.GOODS_VOLUME THEN TO_CHAR(BSL.GOODS_VOLUME,'FM9999999990') ELSE TO_CHAR(BSL.GOODS_VOLUME,'FM9999999990.0999') END goodsVolume,
        BSL.CONSIGNEE_UNIT consigneeUnit,
        BSL.PRINT_STATUS printStatus
        FROM
        T_BO_SHIPPING_LIST BSL
        WHERE
        BSL.IS_DEL = 0
        AND BSL.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" index="index" item="item" open="(" separator="," close=")">
            ${item}
        </foreach>
    </select>

    <select id="queryDeletedShippingListByTaskIdList" resultType="com.wtyt.dao.bean.syf.BoShippingListBean" parameterType="java.util.List">
        SELECT
            T.BO_SHIPPING_LIST_ID boShippingListId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.BO_TRANS_ORDER_ID boTransOrderId,
            T.LAST_MODIFIED_USER_ID lastModifiedUserId,
            T.NOTE note
        FROM T_BO_SHIPPING_LIST T
        WHERE T.IS_DEL = 1
        AND T.BO_TRANS_TASK_ID IN
        <foreach collection="transTaskIdList" item="transTaskId" open="(" separator="," close=")">
            #{transTaskId}
        </foreach>
    </select>

    <select id="getShippingListListTransOrderId" resultType="com.wtyt.dao.bean.syf.BoShippingListBean" parameterType="java.util.List">
        SELECT
            T.BO_SHIPPING_LIST_ID boShippingListId,
            T.BO_TRANS_TASK_ID boTransTaskId,
            T.BO_TRANS_ORDER_ID boTransOrderId,
            T.LAST_MODIFIED_USER_ID lastModifiedUserId,
            T.STORAGE_PLACE storagePlace,
            T.NOTE note
        FROM T_BO_SHIPPING_LIST T
        WHERE T.IS_DEL = 0
        AND T.BO_TRANS_ORDER_ID = #{transOrderId}
    </select>
    <select id="queryShippingListById" resultType="java.util.Map">
        SELECT
            to_char(BO_TRANS_ORDER_ID) BO_TRANS_ORDER_ID,
            CUSTOMER_ORDER_NO,
            CUSTOMER_NAME,
            GOODS_NAME,
            NVL2(GOODS_CASE_PACK,TO_CHAR(GOODS_CASE_PACK,'FM999999990.0099'),'0') GOODS_CASE_PACK,
            NVL2(GOODS_WEIGHT,TO_CHAR(GOODS_WEIGHT,'FM999999990.0099'),'0') GOODS_WEIGHT,
            NVL2(GOODS_VOLUME,TO_CHAR(GOODS_VOLUME,'FM999999990.0099'),'0') GOODS_VOLUME,
            UNLOADING_INFO END_ADDRESS,
            STORAGE_PLACE,
            LOADING_SEQUENCE,
            to_char(TRANS_DATE, 'YYYY-MM-DD') TRANS_DATE,
            THIRD_ORDER_NO,
            RECEIPT_NO,
            SERVICE_REQUIRE,
            CONSIGNEE_UNIT,
            LOADING_INFO START_ADDRESS
        FROM
            T_BO_SHIPPING_LIST WHERE IS_DEL =0 AND BO_SHIPPING_LIST_ID = #{boShippingListId}
    </select>

    <update id="delShippingListByTaskId" parameterType="com.wtyt.dao.bean.syf.BoShippingListBean">
        UPDATE T_BO_SHIPPING_LIST
        SET IS_DEL                      = 1,
            LAST_MODIFIED_TIME          = SYSDATE,
            LAST_MODIFIED_USER_ID       = #{lastModifiedUserId},
            LAST_MODIFIED_SYS_ROLE_TYPE = #{lastModifiedSysRoleType},
            <if test="delType!=null and delType!=''">
                DEL_TYPE = #{delType},
            </if>
            NOTE =#{note}
        WHERE BO_TRANS_TASK_ID = #{boTransTaskId} and is_del =0
    </update>

    <update id="updateThirdOrderNo" parameterType="com.wtyt.dao.bean.syf.BoShippingListBean">
        UPDATE T_BO_SHIPPING_LIST
        SET
        <if test="thirdOrderNo != null and thirdOrderNo != ''">
            THIRD_ORDER_NO = #{thirdOrderNo},
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_ORDER_ID = #{boTransOrderId} AND is_del =0
    </update>

    <update id="recoveryShippingList">
        UPDATE T_BO_SHIPPING_LIST T
        SET T.IS_DEL = 0,
            T.LAST_MODIFIED_USER_ID = #{lastModifiedUserId},
            <if test="lastModifiedSysRoleType != null and lastModifiedSysRoleType != ''">
                T.LAST_MODIFIED_SYS_ROLE_TYPE = #{lastModifiedSysRoleType},
            </if>
            T.LAST_MODIFIED_TIME = SYSDATE,
            T.NOTE = #{note}
        WHERE T.IS_DEL = 1
        AND T.NOTE = #{recoveryNote}
        AND T.LAST_MODIFIED_USER_ID = -1
        AND T.BO_TRANS_ORDER_ID = #{boTransOrderId}
        AND T.BO_TRANS_TASK_ID = #{boTransTaskId}
    </update>

    <select id="batchQueryTaskIdsByOrderNos" resultType="java.lang.String">
        SELECT
            DISTINCT sl.BO_TRANS_TASK_ID
        FROM
        T_BO_TRANS_TASK bt
        JOIN T_BO_SHIPPING_LIST sl ON
        sl.BO_TRANS_TASK_ID = bt.BO_TRANS_TASK_ID
        WHERE
        bt.IS_DEL = 0
        AND sl.IS_DEL = 0
        <if test="orgId!=null and orgId!=''">
            AND bt.ORG_ID = #{orgId}
        </if>
        <if test="thirdOrderNoList!=null and thirdOrderNoList.size>0">
            AND sl.THIRD_ORDER_NO IN
            <foreach collection="thirdOrderNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="customerOrderNoList!=null and customerOrderNoList.size>0">
            AND sl.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
