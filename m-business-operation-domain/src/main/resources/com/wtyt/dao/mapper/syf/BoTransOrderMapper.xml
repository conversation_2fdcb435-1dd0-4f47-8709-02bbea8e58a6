<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransOrderMapper">


    <insert id="insert">
        INSERT INTO T_BO_TRANS_ORDER (
        BO_TRANS_ORDER_ID,
        BO_TRANS_PLAN_ID,
        ORG_ID,
        USER_ID,
        SYS_ROLE_TYPE,
        JOB_NAME,
        CUSTOMER_NAME,
        TAX_WAYBILL_ID,
        TAX_WAYBILL_NO,
        TRANS_DATE,
        STORAGE_PLACE,
        START_ADDRESS,
        END_ADDRESS,
        START_PROVINCE_NAME,
        START_CITY_NAME,
        START_COUNTY_NAME,
        START_LAT,
        START_LNG,
        END_PROVINCE_NAME,
        END_CITY_NAME,
        END_COUNTY_NAME,
        END_LAT,
        END_LNG,
        GOODS_CASE_PACK,
        GOODS_CASE_PACK_UNIT,
        GOODS_WEIGHT,
        GOODS_WEIGHT_UNIT,
        GOODS_VOLUME,
        GOODS_VOLUME_UNIT,
        CART_TYPE,
        CART_LENGTH,
        GOODS_NAME,
        GOODS_SPEC,
        SERVICE_REQUIRE,
        CART_NUM_ID,
        CUSTOMER_ORDER_NO,
        ORDER_DATE,
        PROMISE_ARRIVE_TIME,
        CUSTOMER_CONTACT,
        CONSIGNEE_NAME,
        CONSIGNEE_CONTACT,
        CART_NUM,
        CONTRACT_PRICE,
        MERGE_ID,
        SPILT_FLAG,
        GOODS_CASE_PACK_REMAIN,
        GOODS_WEIGHT_REMAIN,
        GOODS_VOLUME_REMAIN,
        FROM_SOURCE,
        MILEAGE,
        RECEIPT_NO,
        REMARK,
        CONSIGNEE_UNIT,
        CONSIGNEE_MOBILE_NO,
        CONSIGNMENT_NAME,
        CONSIGNMENT_MOBILE_NO,
        CONSIGNMENT_CONTACT,
        USER_FREIGHT,
        BACK_FEE,
        CARRIER,
        LATEST_PRESENCE_TIME,
        MAX_ROUTE_TIME,
        MAX_RESERVE_MILEAGE,
        MAX_RESERVE_HOUR,
        START_TIME,
        END_TIME,
        END_STORAGE_PLACE,
        DRIVER_NAME,
        CART_BADGE_NO,
        DRIVER_MOBILE_NO,
        ALL_FREIGHT,
        FUEL_COST_FEE,
        OVERHEAD,
        INDUSTRIAL_PARK,
        WORKSHOP,
        STORAGE_LOCATION,
        BUSINESS_CUSTOMER,
        REAL_BUSINESS_CUSTOMER,
        THIRD_ORDER_NO,
        SETTLE_MODE,
        ARRIVE_END_TIME,
        PLAN_ROW_NUM,
        RAW_END_ADDRESS,
        RAW_START_ADDRESS,
        RECEIVABLE,
        SALESMAN_NAME,
        LOADING_CAR_NUM,
        IS_FINAL_END_ADDRESS,
        DRIVER_GUARANTEE_AMOUNT,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE,
        UNIT_PRICE,
        ORDER_TYPE,
        BUSINESS_GROUP,
        CUSTOMER_GROUP,
        DT_INSIDE_ORDER_MARK,
        DT_ELECTRONIC_RECEIPT_MARK,
        XCY_USER_ID,
        BELONG_DISPATCHER_ID,
        INCOME_SETTLE_MODE,
        HAS_INVOICE,
        HAS_CONTRACT,
        FUND_PROVIDER,
        LINE_CODE
        )
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="dataList" separator="UNION ALL">
            SELECT #{item.boTransOrderId} boTransOrderId,
            #{item.boTransPlanId} boTransPlanId,
            #{item.orgId} orgId,
            #{item.userId} userId,
            #{item.sysRoleType} sysRoleType,
            #{item.jobName} jobName,
            #{item.customerName} customerName,
            #{item.taxWaybillId} taxWaybillId,
            #{item.taxWaybillNo} taxWaybillNo,
            TO_DATE(#{item.transDate}, 'yyyy-mm-dd HH24:MI:ss') transDate,
            #{item.storagePlace} storagePlace,
            #{item.startAddress} startAddress,
            #{item.endAddress} endAddress,
            #{item.startProvinceName} startProvinceName,
            #{item.startCityName} startCityName,
            #{item.startCountyName} startCountyName,
            #{item.startLat} startLat,
            #{item.startLng} startLng,
            #{item.endProvinceName} endProvinceName,
            #{item.endCityName} endCityName,
            #{item.endCountyName} endCountyName,
            #{item.endLat} endLat,
            #{item.endLng} endLng,
            to_char(#{item.goodsCasePack},'fm9999999990.0000') goodsCasePack,
            <choose>
                <when test="item.goodsCasePackUnit != null and item.goodsCasePackUnit.length > 0">
                    UPPER(#{item.goodsCasePackUnit}) goodsCasePackUnit,
                </when>
                <otherwise>'件',</otherwise>
            </choose>
            to_char(#{item.goodsWeight},'fm9999999990.0000') goodsWeight,
            <choose>
                <when test="item.goodsWeightUnit != null and item.goodsWeightUnit.length > 0">
                    UPPER(#{item.goodsWeightUnit}) goodsWeightUnit,
                </when>
                <otherwise>'吨',</otherwise>
            </choose>
            to_char(#{item.goodsVolume},'fm9999999990.0000') goodsVolume,
            <choose>
                <when test="item.goodsVolumeUnit != null and item.goodsVolumeUnit.length > 0">
                    UPPER(#{item.goodsVolumeUnit}) goodsVolumeUnit,
                </when>
                <otherwise>'方',</otherwise>
            </choose>
            #{item.cartType} cartType,
            #{item.cartLength} cartLength,
            #{item.goodsName} goodsName,
            #{item.goodsSpec} goodsSpec,
            #{item.serviceRequire} serviceRequire,
            #{item.cartNumId} cartNumId,
            #{item.customerOrderNo} customerOrderNo,
            #{item.orderDate} orderDate,
            TO_DATE(#{item.promiseArriveTime}, 'yyyy-mm-dd HH24:MI:ss') promiseArriveTime,
            #{item.customerContact} customerContact,
            #{item.consigneeName} consigneeName,
            #{item.consigneeContact} consigneeContact,
            #{item.cartNum} cartNum,
            #{item.contractPrice} contractPrice,
            #{item.mergeId} mergeId,
            0,
            to_char(#{item.goodsCasePackRemain},'fm9999999990.0000') goodsCasePackRemain,
            to_char(#{item.goodsWeightRemain},'fm9999999990.0000') goodsWeightRemain,
            to_char(#{item.goodsVolumeRemain},'fm9999999990.0000') goodsVolumeRemain,
            #{item.fromSource} fromSource,
            #{item.mileage} mileage,
            #{item.receiptNo} receiptNo,
            #{item.remark} remark,
            #{item.consigneeUnit} consigneeUnit,
            #{item.consigneeMobileNo} consigneeMobileNo,
            #{item.consignmentName} consignmentName,
            #{item.consignmentMobileNo} consignmentMobileNo,
            #{item.consignmentContact} consignmentContact,
            #{item.userFreight} userFreight,
            #{item.backFee} backFee,
            #{item.carrier} carrier,
            TO_DATE(#{item.latestPresenceTime}, 'yyyy-mm-dd HH24:MI:ss') latestPresenceTime,
            #{item.maxRouteTime} maxRouteTime,
            #{item.maxReserveMileage} maxReserveMileage ,
            #{item.maxReserveHour} maxReserveHour,
            TO_DATE(#{item.startTime}, 'yyyy-mm-dd HH24:MI:ss') startTime,
            TO_DATE(#{item.endTime}, 'yyyy-mm-dd HH24:MI:ss') endTime,
            #{item.endStoragePlace} endStoragePlace,
            #{item.driverName} driverName,
            #{item.cartBadgeNo} cartBadgeNo,
            #{item.driverMobileNo} driverMobileNo,
            #{item.allFreight} allFreight,
            #{item.fuelCostFee} fuelCostFee,
            #{item.overhead} overhead,
            #{item.industrialPark} industrialPark,
            #{item.workshop} workshop,
            #{item.storageLocation} storageLocation,
            #{item.businessCustomer} businessCustomer,
            #{item.realBusinessCustomer} realBusinessCustomer,
            #{item.thirdOrderNo} thirdOrderNo,
            #{item.settleMode} settleMode,
            TO_DATE(#{item.arriveEndTime}, 'yyyy-mm-dd HH24:MI:ss') arriveEndTime,
            #{item.rowNum} planRowNum,
            #{item.rawEndAddress} rawEndAddress,
            #{item.rawStartAddress} rawStartAddress,
            #{item.receivable} receivable,
            #{item.salesmanName} salesmanName,
            #{item.loadingCarNum} loadingCarNum,
            #{item.isFinalEndAddress} isFinalEndAddress,
            #{item.driverGuaranteeAmount} driverGuaranteeAmount,
            0 isDel,
            SYSDATE createdTime,
            SYSDATE lastModifiedTime,
            NULL note,
            #{item.unitPrice} unitPrice,
            NVL(#{item.orderType}, 1) orderType,
            #{item.businessGroup},
            #{item.customerGroup},
            #{item.dtInsideOrderMark},
            #{item.dtElectronicReceiptMark},
            #{item.xcyUserId},
            #{item.belongDispatcherId},
            #{item.incomeSettleMode},
            #{item.hasInvoice},
            #{item.hasContract},
            #{item.fundProvider},
            #{item.lineCode}
            FROM DUAL
        </foreach>
        ) A
    </insert>

    <select id="taxWaybillNoExist" resultType="java.lang.String">
        SELECT 1
        FROM DUAL
        WHERE EXISTS (SELECT *
                      FROM T_BO_TRANS_ORDER
                      WHERE IS_DEL = 0
                        AND TAX_WAYBILL_NO = #{taxWaybillNo})
    </select>


    <resultMap id="transPlanMap" type="com.wtyt.dao.bean.syf.BoTransInfoBean">
        <result property="goodsName" column="goodsName"/>
        <result property="goodsAmount" column="goodsAmount"/>
        <result property="goodsAmountType" column="goodsAmountType"/>
        <result property="taxWaybillNo" column="taxWaybillNo"/>
        <result property="taxWaybillId" column="taxWaybillId"/>
        <result property="boTransTaskId" column="boTransTaskId"/>
        <result property="mergeId" column="mergeId"/>
        <result property="dispatchState" column="dispatchState"/>
        <result property="dispatchRecord" column="dispatchRecord"/>
        <result property="stowageState" column="stowageState"/>
        <result property="boTpStowageRecordId" column="boTpStowageRecordId"/>
        <result property="cartType" column="cartType"/>
        <result property="cartLength" column="cartLength"/>
        <result property="driverName" column="driverName"/>
        <result property="driverMobileNo" column="driverMobileNo"/>
        <result property="cartBadgeNo" column="cartBadgeNo"/>
        <result property="transportState" column="transportState"/>
        <result property="distributeStatus" column="distributeStatus"/>
        <collection property="orderList" ofType="com.wtyt.dao.bean.syf.BoTransOrderBean">
            <result property="boTransOrderId" column="boTransOrderId"></result>
            <result property="businessCustomer" column="businessCustomer"></result>
            <result property="endStoragePlace" column="endStoragePlace"></result>
            <result property="storagePlace" column="storagePlace"></result>
            <result property="storageAddress" column="storageAddress"></result>
            <result property="transDate" column="transDate"></result>
            <result property="endAddress" column="endAddress"></result>
            <result property="endProvinceName" column="endProvinceName"></result>
            <result property="endCityName" column="endCityName"></result>
            <result property="endCountyName" column="endCountyName"></result>
            <result property="startProvinceName" column="startProvinceName"></result>
            <result property="startCityName" column="startCityName"></result>
            <result property="startCountyName" column="startCountyName"></result>
            <result property="goodsCasePack" column="goodsCasePack"></result>
            <result property="goodsCasePackUnit" column="goodsCasePackUnit"></result>
            <result property="goodsWeight" column="goodsWeight"></result>
            <result property="goodsWeightUnit" column="goodsWeightUnit"></result>
            <result property="goodsVolume" column="goodsVolume"></result>
            <result property="goodsVolumeUnit" column="goodsVolumeUnit"></result>
            <result property="createdTime" column="createdTime"></result>
            <result property="orderGoodsName" column="orderGoodsName"></result>
            <result property="cartType" column="orderCartType"/>
            <result property="fromSource" column="fromSource"/>
            <result property="cartLength" column="cartLength"/>
            <result property="cartLength" column="orderCartLength"/>
            <result property="spiltFlag" column="spiltFlag"/>
            <result property="goodsCasePackRemain" column="goodsCasePackRemain"/>
            <result property="goodsWeightRemain" column="goodsWeightRemain"/>
            <result property="goodsVolumeRemain" column="goodsVolumeRemain"/>
            <result property="customerOrderNo" column="customerOrderNo"/>
            <result property="unloadingPlace" column="unloadingPlace"/>
            <result property="mileage" column="mileage"/>
            <result property="remark" column="remark"/>
            <result property="inquireState" column="inquireState"/>
            <result property="inquireOverTimeState" column="inquireOverTimeState"/>
            <result property="groupId" column="groupId"/>
            <result property="customerName" column="customerName"/>
            <result property="latestPresenceTime" column="latestPresenceTime"/>
            <result property="maxRouteTime" column="maxRouteTime"/>
            <result property="stowageState" column="stowageState"/>
            <result property="boTpStowageRecordId" column="boTpStowageRecordId"/>
            <result property="storageLocation" column="storageLocation"/>
            <result property="workshop" column="workshop"/>
            <result property="upstreamState" column="upstreamState"/>
        </collection>
    </resultMap>

    <select id="getAllTransPlanList" parameterType="com.wtyt.dao.bean.syf.BoTransPlanIBean" resultMap="transPlanMap">
        select TTC.* from
        (select T.*,
        ROW_NUMBER() OVER(PARTITION BY T.boTransOrderId ORDER BY T.boTransOrderId DESC) RN
        from (
        SELECT
        *
        FROM
        (
        SELECT
        <include refid="queryList"/>,
        /*DECODE(ttw.NODE_ID, 300, 1, 0) dispatchRecord*/
        100 DSort,
        tl.DEAD_LINE_TIME,
        ADD_MONTHS(SYSDATE ,-360) CREATED_TIME_SORT
        FROM
        <include refid="baseUnDisPatchedPlan"/>
        left join
        T_BO_TRANS_ORDER bo on bo.merge_id = M.merge_id
        AND
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                bo.IS_DEL = 1
            </when>
            <otherwise>
                bo.IS_DEL = 0
            </otherwise>
        </choose>
        left join
        T_BO_TRANS_ORDER_REL TCO
        ON TCO.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND TCO.IS_DEL = 0

        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0

        LEFT JOIN T_BO_TRANS_TASK ttw ON
        TCO.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID AND ttw.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA ttwe ON
        ttw.BO_TRANS_TASK_ID = ttwe.BO_TRANS_TASK_ID AND ttwe.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE tl ON
        ttw.BO_TRANS_TASK_ID = tl.BO_TRANS_TASK_ID AND tl.IS_DEL = 0
        AND tl.NODE_ID = 200
        left join T_BO_ORDER_GROUP_REL tbogr on bo.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        <!-- 新颜模式 -->
        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            left join T_BO_INQUIRE tbi on bo.BO_TRANS_ORDER_ID = tbi.BO_TRANS_ORDER_ID
        </if>
        <!-- 异常连异常表 -->
        <if test="tabState != null and tabState == '3'.toString()">
            INNER JOIN (
            SELECT
            BO_TRANS_TASK_ID,
            BO_TRANS_NODE_ALARM_ID,
            ALARM_TYPE
            FROM
            (
            SELECT
            tna.BO_TRANS_TASK_ID,
            tna.BO_TRANS_NODE_ALARM_ID,
            tna.ALARM_TYPE,
            ROW_NUMBER() OVER(PARTITION BY tna.BO_TRANS_TASK_ID, tna.NODE_ID ORDER BY tna.CREATED_TIME DESC) RN
            FROM
            T_BO_TRANS_NODE_ALARM tna
            WHERE
            tna.NODE_ID = 200
            and tna.NODE_DATA_TYPE = 1
            and tna.IS_DEL = 0)
            WHERE
            RN = 1
            )TT ON ttw.BO_TRANS_TASK_ID = TT.BO_TRANS_TASK_ID
        </if>)
        UNION ALL
        SELECT
        *
        FROM
        (
        SELECT
        <include refid="queryList"/>,
        1 DSort,
        ADD_MONTHS(SYSDATE ,12) DEAD_LINE_TIME,
        tnr.CREATED_TIME CREATED_TIME_SORT
        /*1 dispatchRecord*/
        FROM
        <include refid="baseDisPatchedPlan"/>
        left join
        T_BO_TRANS_ORDER bo on bo.MERGE_ID = M.MERGE_ID
        AND
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                bo.IS_DEL = 1
            </when>
            <otherwise>
                bo.IS_DEL = 0
            </otherwise>
        </choose>
        left join
        T_BO_TRANS_ORDER_REL TCO
        ON TCO.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND TCO.IS_DEL = 0

        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0

        LEFT JOIN T_BO_TRANS_TASK ttw ON
        TCO.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID AND ttw.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA ttwe ON
        ttw.BO_TRANS_TASK_ID = ttwe.BO_TRANS_TASK_ID AND ttwe.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD tnr ON
        ttw.BO_TRANS_TASK_ID = tnr.BO_TRANS_TASK_ID AND tnr.NODE_ID = 200 AND tnr.IS_DEL = 0
        left join T_BO_ORDER_GROUP_REL tbogr on bo.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        <!-- 新颜模式 -->
        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            left join T_BO_INQUIRE tbi on bo.BO_TRANS_ORDER_ID = tbi.BO_TRANS_ORDER_ID
        </if>
        <!-- 异常连异常表 -->
        <if test="tabState != null and tabState == '3'.toString()">
            INNER JOIN (
            SELECT
            BO_TRANS_TASK_ID,
            BO_TRANS_NODE_ALARM_ID,
            ALARM_TYPE
            FROM
            (
            SELECT
            tna.BO_TRANS_TASK_ID,
            tna.BO_TRANS_NODE_ALARM_ID,
            tna.ALARM_TYPE,
            ROW_NUMBER() OVER(PARTITION BY tna.BO_TRANS_TASK_ID, tna.NODE_ID ORDER BY tna.CREATED_TIME DESC) RN
            FROM
            T_BO_TRANS_NODE_ALARM tna
            WHERE
            tna.NODE_ID = 200
            <!-- 已派车的只取ALARM_TYPE为1的数据 -->
            AND tna.ALARM_TYPE = 1
            )
            WHERE
            RN = 1
            )TT ON ttw.BO_TRANS_TASK_ID = TT.BO_TRANS_TASK_ID
        </if>))T)TTC where TTC.RN = 1 ORDER BY
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                TTC.LAST_MODIFIED_TIME DESC ,
            </when>
            <otherwise>
                TTC.DSort desc,
                TTC.sort,
            </otherwise>
        </choose>
        <if test="isSFFlag != null and isSFFlag == '8'.toString()">
            TO_DATE(TTC.transDate, 'MM-DD HH24:MI'),
        </if>
        TTC.DEAD_LINE_TIME,
        TTC.CREATED_TIME_SORT DESC,
        TTC.taxWaybillNo
        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            ,TTC.createdTime
        </if>
    </select>


    <select id="getTransPlanListByState" parameterType="com.wtyt.dao.bean.syf.BoTransPlanIBean"
            resultMap="transPlanMap">

        select TTC.* from (select T.* ,
        ROW_NUMBER() OVER(PARTITION BY T.boTransOrderId ORDER BY T.boTransOrderId DESC) RN
        from (
        select
        <include refid="queryList"/>,
        <choose>
            <when test="state != null and state == '1'.toString()">
                tl.DEAD_LINE_TIME
            </when>
            <otherwise>
                ttw.NODE_TIME
            </otherwise>
        </choose>
        /* DECODE(ttw.NODE_ID, 0, 0, 100,0,1) dispatchRecord*/
        from
        <include refid="basePlan"/>
        left join T_BO_TRANS_ORDER bo ON
        M.MERGE_ID = bo.MERGE_ID
        AND
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                bo.IS_DEL = 1
            </when>
            <otherwise>
                bo.IS_DEL = 0
            </otherwise>
        </choose>
        left join
        <!-- 中间关联表随意关联一条最近记录，取出来的运单数据不展示到列表，在Java代码列表循环中取运输任务属性 -->
        T_BO_TRANS_ORDER_REL TCO
        ON TCO.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND TCO.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK ttw ON
        TCO.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID AND ttw.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA ttwe ON
        ttw.BO_TRANS_TASK_ID = ttwe.BO_TRANS_TASK_ID AND ttwe.IS_DEL = 0

        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
        <if test="state != null and state == '1'.toString()">
            LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE tl ON
            ttw.BO_TRANS_TASK_ID = tl.BO_TRANS_TASK_ID AND tl.IS_DEL = 0
            AND tl.NODE_ID = 200
            left join T_BO_ORDER_GROUP_REL tbogr on bo.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        </if>
        <!-- 新颜模式 -->
        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            left join T_BO_INQUIRE tbi on bo.BO_TRANS_ORDER_ID = tbi.BO_TRANS_ORDER_ID
        </if>
        ) T) TTC where TTC.RN = 1
        ORDER BY
        <if test="tabState != null and tabState == '100'.toString()">
            TTC.LAST_MODIFIED_TIME DESC
        </if>
        <if test="tabState == null or tabState != '100'.toString()">
            <choose>
                <when test="state != null and state == '1'.toString()">
                    TTC.sort,
                    TTC.DEAD_LINE_TIME,
                    <if test="isSFFlag != null and isSFFlag == '8'.toString()">
                        TO_DATE(TTC.transDate, 'MM-DD HH24:MI'),
                    </if>
                    <if test="xinYanMode != null and xinYanMode == '1'.toString()">
                        TTC.createdTime,
                    </if>
                    TTC.taxWaybillNo
                </when>
                <otherwise>
                    TTC.NODE_TIME desc,
                    TTC.taxWaybillNo
                </otherwise>
            </choose>
        </if>
    </select>

    <sql id="basePlan">
        (
        SELECT
        distinct
        bo.merge_id from
        (
        <!-- 查询所有或者带派车列表时候，加上没有生成运输任务的订单 -->
        <if test="state == '0'.toString() or state == '1'.toString()">
            SELECT
            bo.*,
            SOR.STOWAGE_STATE,
            '-100' CART_BADGE_NO_QUERY,
            '-101' TAX_WAYBILL_NO_QUERY,
            decode(rel.DISTRIBUTE_STATE, 1, 1, 0) distributeStatus
            FROM
            T_BO_TRANS_ORDER bo
            LEFT JOIN T_BO_TRANS_ORDER_REL rel
            ON rel.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND rel.IS_DEL = 0
            LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
            SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
            WHERE
            rel.BO_TRANS_ORDER_REL_ID IS NULL
            AND
            <choose>
                <when test="tabState != null and tabState == '100'.toString()">
                    bo.IS_DEL = 1
                </when>
                <otherwise>
                    bo.IS_DEL = 0
                </otherwise>
            </choose>
            <!-- 订单如果还有余量需要展示在带派车列表中 -->
            UNION ALL
            SELECT
            bo.*,
            SOR.STOWAGE_STATE ,
            '-100' CART_BADGE_NO_QUERY,
            '-101' TAX_WAYBILL_NO_QUERY,
            decode(rel.DISTRIBUTE_STATE, 1, 1, 0) distributeStatus
            FROM
            T_BO_TRANS_ORDER bo
            LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
            SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_ORDER_REL rel
            ON rel.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND rel.IS_DEL = 0
            WHERE
            <choose>
                <when test="tabState != null and tabState == '100'.toString()">
                    bo.IS_DEL = 1
                </when>
                <otherwise>
                    bo.IS_DEL = 0
                </otherwise>
            </choose>
            AND (
            (bo.GOODS_CASE_PACK > 0 AND bo.GOODS_CASE_PACK_REMAIN > 0)
            AND (bo.GOODS_VOLUME > 0 AND bo.GOODS_VOLUME_REMAIN > 0 )
            AND(bo.GOODS_WEIGHT > 0 AND bo.GOODS_WEIGHT_REMAIN > 0))
            UNION ALL
        </if>
        SELECT
        bo.*,
        SOR.STOWAGE_STATE,
        ttw.CART_BADGE_NO CART_BADGE_NO_QUERY,
        ttw.TAX_WAYBILL_NO TAX_WAYBILL_NO_QUERY,
        decode(rel.DISTRIBUTE_STATE, 1, 1, 0) distributeStatus
        FROM
        T_BO_TRANS_ORDER bo
        LEFT JOIN T_BO_TRANS_ORDER_REL rel
        ON rel.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND rel.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK ttw ON
        rel.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
        WHERE
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                bo.IS_DEL = 1
            </when>
            <otherwise>
                bo.IS_DEL = 0
            </otherwise>
        </choose>
        AND ttw.IS_DEL = 0
        <choose>
            <when test="state != null and state == '0'.toString()">
                <choose>
                    <when test="configId != null and configId == '104'.toString()">
                        AND ttw.NODE_ID IN (0,100,200,300,400,500,600,650,700,800,1000,1100,1200)
                    </when>
                    <when test="configId != null and configId == '105'.toString()">
                        AND ttw.NODE_ID IN (0,100,200,300,400,501,600,650,700,800,1000,1100,1200)
                    </when>
                </choose>
            </when>
            <when test="state != null and state == '1'.toString()">
                AND (ttw.NODE_ID IN (0, 100, 300) or ttw.NODE_ID IS NULL)
            </when>
            <when test="state != null and state == '2'.toString()">
                <choose>
                    <when test="configId != null and configId == '104'.toString()">
                        AND ttw.NODE_ID IN (200, 400)
                    </when>
                    <when test="configId != null and configId == '105'.toString()">
                        AND ttw.NODE_ID IN(200,501)
                    </when>
                </choose>
            </when>
            <when test="state != null and state == '3'.toString()">
                <choose>
                    <when test="configId != null and configId == '104'.toString()">
                        AND ttw.NODE_ID = 500
                    </when>
                    <when test="configId != null and configId == '105'.toString()">
                        AND ttw.NODE_ID = 400
                    </when>
                </choose>
            </when>
            <when test="state != null and state == '4'.toString()">
                AND ttw.NODE_ID IN (600,650)
            </when>
            <when test="state != null and state == '5'.toString()">
                AND ttw.NODE_ID IN (700,800,1000,1100)
            </when>
            <when test="state != null and state == '6'.toString()">
                AND ttw.NODE_ID = 1200
            </when>
        </choose>
        ) bo left join T_BO_ORDER_GROUP_REL tbogr on bo.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            inner join T_BO_INQUIRE_USER_REL iur
            on iur.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID
            and iur.USER_ID = #{userId}
            and iur.IS_DEL = 0
            left join T_BO_INQUIRE tbi on iur.BO_TRANS_ORDER_ID = tbi.BO_TRANS_ORDER_ID
        </if>
        <if test="isDebangMode != null and isDebangMode == '1'.toString()">
            inner join T_BO_INQUIRE_OFFER tbio
            on tbio.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID
            and tbio.CREATED_USER_ID = #{userId}
            and tbio.APPLY_STATE = 2
            and tbio.IS_DEL = 0
        </if>
        where
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                bo.IS_DEL = 1
            </when>
            <otherwise>
                bo.IS_DEL = 0
            </otherwise>
        </choose>
        <if test="stowageState != null and stowageState !=''">
            AND DECODE(bo.STOWAGE_STATE, 2, 2, 3, 2, 4, 1,NULL,1) = #{stowageState}
        </if>
        <choose>
            <when test="upstreamState != null and upstreamState == '1'.toString()">
                AND bo.UPSTREAM_STATE = 0
            </when>
            <when test="upstreamState != null and upstreamState == '2'.toString()">
                AND bo.UPSTREAM_STATE = 1
            </when>
        </choose>
        <if test="distributeStatus != null and distributeStatus !=''">
            AND bo.distributeStatus = #{distributeStatus}
        </if>
        <if test="startDate != null and startDate !=''">
            AND bo.TRANS_DATE &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate !=''">
            AND bo.TRANS_DATE &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startTime != null and startTime !=''">
            AND bo.CREATED_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND bo.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startTime != null and startTime !=''">
            AND bo.CREATED_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND bo.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!-- 智能配载新增模糊搜索项开始 -->
        <if test="customerName != null and customerName !=''">
            AND bo.CUSTOMER_NAME like CONCAT(CONCAT('%',#{customerName}),'%')
        </if>
        <if test="endStoragePlace != null and endStoragePlace !=''">
            AND bo.END_STORAGE_PLACE like CONCAT(CONCAT('%',#{endStoragePlace}),'%')
        </if>
        <if test="industrialPark != null and industrialPark !=''">
            AND bo.INDUSTRIAL_PARK like CONCAT(CONCAT('%',#{industrialPark}),'%')
        </if>
        <if test="workshop != null and workshop !=''">
            AND bo.WORKSHOP like CONCAT(CONCAT('%',#{workshop}),'%')
        </if>
        <!-- 智能配载新增模糊搜索项结束 -->
        <if test="orgId != null and orgId !=''">
            AND bo.ORG_ID = #{orgId}
        </if>
        <if test="customerOrderNoList != null and customerOrderNoList.size() > 0 !=''">
            AND bo.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="customerNameList != null and customerNameList.size() > 0 !=''">
            AND bo.CUSTOMER_NAME IN
            <foreach collection="customerNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="xinYanMode == null || xinYanMode != '1'.toString()">
            <!-- 我的查调度自己导入的运输计划 -->
            <if test="tabState != null and tabState == '1'.toString()">
                AND bo.USER_ID = #{userId}
            </if>
            <if test="searchContent != null and searchContent !=''">
                AND (
                bo.TAX_WAYBILL_NO_QUERY like CONCAT(CONCAT('%',#{searchContent}),'%')
                or bo.CART_BADGE_NO_QUERY like CONCAT(CONCAT('%',#{searchContent}),'%')
                or bo.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
                <if test="headerSearchSql != null and headerSearchSql !=''">
                    ${headerSearchSql}
                </if>
                )
            </if>
        </if>

        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            <if test="searchContent != null and searchContent !=''">
                AND (
                bo.TAX_WAYBILL_NO_QUERY like CONCAT(CONCAT('%',#{searchContent}),'%')
                or bo.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
                <if test="headerSearchSql != null and headerSearchSql !=''">
                    ${headerSearchSql}
                </if>
                )
            </if>
            <if test="inquireState != null and inquireState !='2'.toString() and inquireState != ''">
                AND tbi.INQUIRE_STATE = #{inquireState}
            </if>
            <if test="inquireState != null and inquireState =='2'.toString() and inquireState != ''">
                AND tbi.INQUIRE_STATE in (2,3)
            </if>
            <if test="minMileage != null and minMileage !=''">
                AND bo.MILEAGE &gt;= #{minMileage}
            </if>
            <if test="maxMileage != null and maxMileage !=''">
                AND bo.MILEAGE &lt;= #{maxMileage}
            </if>
        </if>
        <if test="startPlace != null and startPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="startPlace" separator="or" open="(" close=")">
                <if test="item.provinceName != null and item.provinceName != ''">
                    bo.START_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and bo.START_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and bo.START_COUNTY_NAME = #{item.areaName}
                </if>
            </foreach>
            )
        </if>
        <if test="endPlace != null and endPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="endPlace" separator="or" open="(" close=")">
                <if test="item.provinceName != null and item.provinceName != ''">
                    bo.END_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and bo.END_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and bo.END_COUNTY_NAME = #{item.areaName}
                </if>
            </foreach>
            )
        </if>
        <!-- 接入数据权限 -->
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                        (
                        tbogr.GROUP_ID = #{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND bo.USER_ID = #{scopeItem.userId}
                        </if>
                        )
                    </foreach>
                    )
                </if>

                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
                )
            </foreach>
            )
        </if>
        ) M
    </sql>
    <sql id="baseUnDisPatchedPlan">
        ( SELECT distinct
        bo.merge_id from
        (
        <!-- 查询所有或者带派车列表时候，加上没有生成运输任务的订单 -->
        <if test="state == '0'.toString() or state == '1'.toString()">
            SELECT
            bo.*,
            SOR.STOWAGE_STATE,
            '-100' CART_BADGE_NO_QUERY,
            '-101' TAX_WAYBILL_NO_QUERY,
            decode(rel.DISTRIBUTE_STATE, 1, 1, 0) distributeStatus
            FROM
            T_BO_TRANS_ORDER bo
            LEFT JOIN T_BO_TRANS_ORDER_REL rel
            ON rel.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND rel.IS_DEL = 0
            LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
            SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
            WHERE
            rel.BO_TRANS_ORDER_REL_ID IS NULL
            AND
            <choose>
                <when test="tabState != null and tabState == '100'.toString()">
                    bo.IS_DEL = 1
                </when>
                <otherwise>
                    bo.IS_DEL = 0
                </otherwise>
            </choose>
            <!-- 订单如果还有余量需要展示在带派车列表中 -->
            UNION ALL
            SELECT
            bo.*,
            SOR.STOWAGE_STATE,
            '-100' CART_BADGE_NO_QUERY,
            '-101' TAX_WAYBILL_NO_QUERY,
            decode(rel.DISTRIBUTE_STATE, 1, 1, 0) distributeStatus
            FROM
            T_BO_TRANS_ORDER bo
            LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
            SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
            LEFT JOIN T_BO_TRANS_ORDER_REL rel
            ON rel.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND rel.IS_DEL = 0
            WHERE
            <choose>
                <when test="tabState != null and tabState == '100'.toString()">
                    bo.IS_DEL = 1
                </when>
                <otherwise>
                    bo.IS_DEL = 0
                </otherwise>
            </choose>
            AND (
            (bo.GOODS_CASE_PACK > 0 AND bo.GOODS_CASE_PACK_REMAIN > 0)
            AND (bo.GOODS_VOLUME > 0 AND bo.GOODS_VOLUME_REMAIN > 0 )
            AND(bo.GOODS_WEIGHT > 0 AND bo.GOODS_WEIGHT_REMAIN > 0))
            UNION ALL
        </if>
        SELECT
        bo.*,
        SOR.STOWAGE_STATE,
        ttw.CART_BADGE_NO CART_BADGE_NO_QUERY,
        ttw.TAX_WAYBILL_NO TAX_WAYBILL_NO_QUERY,
        decode(rel.DISTRIBUTE_STATE, 1, 1, 0) distributeStatus
        FROM
        T_BO_TRANS_ORDER bo
        LEFT JOIN T_BO_TRANS_ORDER_REL rel
        ON rel.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND rel.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK ttw ON
        rel.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0
        WHERE
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                bo.IS_DEL = 1
            </when>
            <otherwise>
                bo.IS_DEL = 0
            </otherwise>
        </choose>
        AND ttw.IS_DEL = 0
        AND (ttw.NODE_ID IN (0, 100, 300) or ttw.NODE_ID IS NULL)
        ) bo left join T_BO_ORDER_GROUP_REL tbogr on bo.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            inner join T_BO_INQUIRE_USER_REL iur
            on iur.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID
            and iur.USER_ID = #{userId}
            and iur.IS_DEL = 0
            left join T_BO_INQUIRE tbi on iur.BO_TRANS_ORDER_ID = tbi.BO_TRANS_ORDER_ID
        </if>
        <if test="isDebangMode != null and isDebangMode == '1'.toString()">
            inner join T_BO_INQUIRE_OFFER tbio
            on tbio.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID
            and tbio.CREATED_USER_ID = #{userId}
            and tbio.APPLY_STATE = 2
            and tbio.IS_DEL = 0
        </if>
        where
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                bo.IS_DEL = 1
            </when>
            <otherwise>
                bo.IS_DEL = 0
            </otherwise>
        </choose>
        <if test="stowageState != null and stowageState !=''">
            AND DECODE(bo.STOWAGE_STATE, 2, 2, 3, 2, 4, 1,NULL,1) = #{stowageState}
        </if>
        <choose>
            <when test="upstreamState != null and upstreamState == '1'.toString()">
                AND bo.UPSTREAM_STATE = 0
            </when>
            <when test="upstreamState != null and upstreamState == '2'.toString()">
                AND bo.UPSTREAM_STATE = 1
            </when>
        </choose>
        <if test="distributeStatus != null and distributeStatus !=''">
            AND bo.distributeStatus = #{distributeStatus}
        </if>
        <if test="startDate != null and startDate !=''">
            AND bo.TRANS_DATE &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate !=''">
            AND bo.TRANS_DATE &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startTime != null and startTime !=''">
            AND bo.CREATED_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND bo.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!-- 智能配载新增模糊搜索项开始 -->
        <if test="customerName != null and customerName !=''">
            AND bo.CUSTOMER_NAME like CONCAT(CONCAT('%',#{customerName}),'%')
        </if>
        <if test="endStoragePlace != null and endStoragePlace !=''">
            AND bo.END_STORAGE_PLACE like CONCAT(CONCAT('%',#{endStoragePlace}),'%')
        </if>
        <if test="industrialPark != null and industrialPark !=''">
            AND bo.INDUSTRIAL_PARK like CONCAT(CONCAT('%',#{industrialPark}),'%')
        </if>
        <if test="workshop != null and workshop !=''">
            AND bo.WORKSHOP like CONCAT(CONCAT('%',#{workshop}),'%')
        </if>
        <!-- 智能配载新增模糊搜索项结束 -->
        <if test="orgId != null and orgId !=''">
            AND bo.ORG_ID = #{orgId}
        </if>
        <if test="customerOrderNoList != null and customerOrderNoList.size() > 0 !=''">
            AND bo.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="customerNameList != null and customerNameList.size() > 0 !=''">
            AND bo.CUSTOMER_NAME IN
            <foreach collection="customerNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="xinYanMode == null || xinYanMode != '1'.toString()">
            <if test="tabState != null and (tabState == '1'.toString() or tabState == '3'.toString())">
                AND bo.USER_ID = #{userId}
            </if>
            <if test="searchContent != null and searchContent !=''">
                AND (
                bo.TAX_WAYBILL_NO_QUERY like CONCAT(CONCAT('%',#{searchContent}),'%')
                or bo.CART_BADGE_NO_QUERY like CONCAT(CONCAT('%',#{searchContent}),'%')
                or bo.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
                <if test="headerSearchSql != null and headerSearchSql !=''">
                    ${headerSearchSql}
                </if>
                )
            </if>
        </if>

        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            <if test="searchContent != null and searchContent !=''">
                AND (
                bo.TAX_WAYBILL_NO_QUERY like CONCAT(CONCAT('%',#{searchContent}),'%')
                or bo.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
                <if test="headerSearchSql != null and headerSearchSql !=''">
                    ${headerSearchSql}
                </if>
                )
            </if>
            <if test="inquireState != null and inquireState !='2'.toString() and inquireState != ''">
                AND tbi.INQUIRE_STATE = #{inquireState}
            </if>
            <if test="inquireState != null and inquireState =='2'.toString() and inquireState != ''">
                AND tbi.INQUIRE_STATE in (2,3)
            </if>
            <if test="minMileage != null and minMileage !=''">
                AND bo.MILEAGE &gt;= #{minMileage}
            </if>
            <if test="maxMileage != null and maxMileage !=''">
                AND bo.MILEAGE &lt;= #{maxMileage}
            </if>
        </if>


        <if test="startPlace != null and startPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="startPlace" separator="or" open="(" close=")">
                <if test="item.provinceName != null and item.provinceName != ''">
                    bo.START_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and bo.START_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and bo.START_COUNTY_NAME = #{item.areaName}
                </if>
            </foreach>
            )
        </if>
        <if test="endPlace != null and endPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="endPlace" separator="or" open="(" close=")">
                <if test="item.provinceName != null and item.provinceName != ''">
                    bo.END_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and bo.END_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and bo.END_COUNTY_NAME = #{item.areaName}
                </if>
            </foreach>
            )
        </if>
        <!-- 接入数据权限 -->
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                        (
                        tbogr.GROUP_ID = #{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND bo.USER_ID = #{scopeItem.userId}
                        </if>
                        )
                    </foreach>
                    )
                </if>

                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
                )
            </foreach>
            )
        </if>
        ) M
    </sql>

    <sql id="baseDisPatchedPlan">
        ( SELECT
        distinct bo.MERGE_ID
        FROM
        T_BO_TRANS_ORDER bo
        LEFT JOIN T_BO_TRANS_ORDER_REL rel
        ON rel.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND rel.IS_DEL = 0

        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0

        LEFT JOIN T_BO_TRANS_TASK ttw ON
        rel.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID
        left join T_BO_ORDER_GROUP_REL tbogr on bo.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            inner join T_BO_INQUIRE_USER_REL iur
            on iur.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID
            and iur.USER_ID = #{userId}
            and iur.IS_DEL = 0
            left join T_BO_INQUIRE tbi on iur.BO_TRANS_ORDER_ID = tbi.BO_TRANS_ORDER_ID
        </if>
        <if test="isDebangMode != null and isDebangMode == '1'.toString()">
            inner join T_BO_INQUIRE_OFFER tbio
            on tbio.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID
            and tbio.CREATED_USER_ID = #{userId}
            and tbio.APPLY_STATE = 2
            and tbio.IS_DEL = 0
        </if>
        WHERE
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                bo.IS_DEL = 1
            </when>
            <otherwise>
                bo.IS_DEL = 0
            </otherwise>
        </choose>
        AND ttw.IS_DEL = 0
        <if test="configId != null and configId == '104'.toString()">
            AND ttw.NODE_ID IN (200, 400, 500, 600, 650, 700, 800, 1000, 1100, 1200)
        </if>
        <if test="configId != null and configId == '105'.toString()">
            AND ttw.NODE_ID IN (200, 400, 501, 600, 650, 700, 800, 1000, 1100, 1200)
        </if>
        <if test="stowageState != null and stowageState !=''">
            AND DECODE(SOR.STOWAGE_STATE,1,1,2,2,3,2,4,1,NULL,1) = #{stowageState}
        </if>
        <choose>
            <when test="upstreamState != null and upstreamState == '1'.toString()">
                AND bo.UPSTREAM_STATE = 0
            </when>
            <when test="upstreamState != null and upstreamState == '2'.toString()">
                AND bo.UPSTREAM_STATE = 1
            </when>
        </choose>
        <if test="distributeStatus != null and distributeStatus !=''">
            AND decode(rel.DISTRIBUTE_STATE, 1, 1, 0) = #{distributeStatus}
        </if>
        <if test="startDate != null and startDate !=''">
            AND bo.TRANS_DATE &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate !=''">
            AND bo.TRANS_DATE &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startTime != null and startTime !=''">
            AND bo.CREATED_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND bo.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startTime != null and startTime !=''">
            AND bo.CREATED_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND bo.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!-- 智能配载新增模糊搜索项开始 -->
        <if test="customerName != null and customerName !=''">
            AND bo.CUSTOMER_NAME like CONCAT(CONCAT('%',#{customerName}),'%')
        </if>
        <if test="endStoragePlace != null and endStoragePlace !=''">
            AND bo.END_STORAGE_PLACE like CONCAT(CONCAT('%',#{endStoragePlace}),'%')
        </if>
        <if test="industrialPark != null and industrialPark !=''">
            AND bo.INDUSTRIAL_PARK like CONCAT(CONCAT('%',#{industrialPark}),'%')
        </if>
        <if test="workshop != null and workshop !=''">
            AND bo.WORKSHOP like CONCAT(CONCAT('%',#{workshop}),'%')
        </if>
        <!-- 智能配载新增模糊搜索项结束 -->
        <if test="orgId != null and orgId !=''">
            AND bo.ORG_ID = #{orgId}
        </if>
        <if test="customerOrderNoList != null and customerOrderNoList.size() > 0 !=''">
            AND bo.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="customerNameList != null and customerNameList.size() > 0 !=''">
            AND bo.CUSTOMER_NAME IN
            <foreach collection="customerNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="xinYanMode == null || xinYanMode != '1'.toString()">
            <if test="tabState != null and (tabState == '1'.toString() or tabState == '3'.toString())">
                AND bo.USER_ID = #{userId}
            </if>
            <if test="searchContent != null and searchContent !=''">
                AND (
                ttw.TAX_WAYBILL_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
                or ttw.CART_BADGE_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
                or bo.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
                <if test="headerSearchSql != null and headerSearchSql !=''">
                    ${headerSearchSql}
                </if>
                )
            </if>
        </if>

        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            <if test="searchContent != null and searchContent !=''">
                AND (
                ttw.TAX_WAYBILL_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
                or bo.CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
                <if test="headerSearchSql != null and headerSearchSql !=''">
                    ${headerSearchSql}
                </if>
                )
            </if>
            <if test="inquireState != null and inquireState !='2'.toString() and inquireState != ''">
                AND tbi.INQUIRE_STATE = #{inquireState}
            </if>
            <if test="inquireState != null and inquireState =='2'.toString() and inquireState != ''">
                AND tbi.INQUIRE_STATE in (2,3)
            </if>
            <if test="minMileage != null and minMileage !=''">
                AND bo.MILEAGE &gt;= #{minMileage}
            </if>
            <if test="maxMileage != null and maxMileage !=''">
                AND bo.MILEAGE &lt;= #{maxMileage}
            </if>
        </if>
        <if test="startPlace != null and startPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="startPlace" separator="or" open="(" close=")">
                <if test="item.provinceName != null and item.provinceName != ''">
                    bo.START_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and bo.START_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and bo.START_COUNTY_NAME = #{item.areaName}
                </if>
            </foreach>
            )
        </if>
        <if test="endPlace != null and endPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="endPlace" separator="or" open="(" close=")">
                <if test="item.provinceName != null and item.provinceName != ''">
                    bo.END_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and bo.END_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and bo.END_COUNTY_NAME = #{item.areaName}
                </if>
            </foreach>
            )
        </if>
        <!-- 接入数据权限 -->
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                        (
                        tbogr.GROUP_ID = #{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND bo.USER_ID = #{scopeItem.userId}
                        </if>
                        )
                    </foreach>
                    )
                </if>

                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
                )
            </foreach>
            )
        </if>
        ) M
    </sql>

    <sql id="queryList">
        bo.LAST_MODIFIED_TIME ,
        bo.STORAGE_PLACE storagePlace,
        bo.START_PROVINCE_NAME || bo.START_CITY_NAME || bo.START_COUNTY_NAME || bo.START_ADDRESS storageAddress,
        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            bo.END_PROVINCE_NAME || bo.END_CITY_NAME || bo.END_COUNTY_NAME || bo.END_ADDRESS unloadingPlace,
            bo.MILEAGE mileage,
            DECODE(tbi.INQUIRE_STATE,0,0,1,1,2) inquireState,
            tbi.INQUIRE_OVER_TIME_STATE inquireOverTimeState,
            tbogr.GROUP_ID groupId,
        </if>
        <if test="isSFFlag != null and isSFFlag == '8'.toString()">
            TO_CHAR(bo.TRANS_DATE, 'MM-DD HH24:MI') transDate,
        </if>
        <if test="isSFFlag != null and isSFFlag == '9'.toString()">
            TO_CHAR(bo.TRANS_DATE, 'MM-DD') transDate,
        </if>
        NVL(bo.RAW_END_ADDRESS,bo.END_ADDRESS) endAddress,
        bo.END_PROVINCE_NAME endProvinceName,
        bo.END_CITY_NAME endCityName,
        bo.END_COUNTY_NAME endCountyName,
        bo.FROM_SOURCE fromSource,
        bo.START_PROVINCE_NAME startProvinceName,
        bo.START_CITY_NAME startCityName,
        bo.START_COUNTY_NAME startCountyName,
        NVL2(bo.GOODS_CASE_PACK,TO_CHAR(bo.GOODS_CASE_PACK,'FM999999990.0000'),'0') goodsCasePack,
        bo.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        NVL2(bo.GOODS_WEIGHT,TO_CHAR(bo.GOODS_WEIGHT,'FM999999990.0000'),'0') goodsWeight,
        bo.GOODS_WEIGHT_UNIT goodsWeightUnit,
        NVL2(bo.GOODS_VOLUME,TO_CHAR(bo.GOODS_VOLUME,'FM999999990.0000'),'0') goodsVolume,
        bo.GOODS_VOLUME_UNIT goodsVolumeUnit,
        bo.BO_TRANS_ORDER_ID boTransOrderId,
        bo.BUSINESS_CUSTOMER businessCustomer,
        bo.END_STORAGE_PLACE endStoragePlace,
        nvl2(ttwe.CART_TYPE, ttwe.CART_TYPE, '') AS cartType,
        nvl2(ttwe.CART_LENGTH, ttwe.CART_LENGTH || '米', '') AS cartLength,
        bo.CART_TYPE orderCartType,
        bo.CART_LENGTH orderCartLength,
        bo.GOODS_NAME orderGoodsName,
        TO_CHAR(bo.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        bo.MERGE_ID mergeId,
        bo.SPILT_FLAG spiltFlag,
        TO_CHAR(bo.LATEST_PRESENCE_TIME, 'MM-DD HH24:MI') latestPresenceTime,
        bo.MAX_ROUTE_TIME maxRouteTime,
        bo.CUSTOMER_NAME customerName,
        bo.STORAGE_LOCATION storageLocation,
        decode(bo.UPSTREAM_STATE,0,1,1,2,0) upstreamState,
        NVL2(bo.GOODS_CASE_PACK_REMAIN,TO_CHAR(bo.GOODS_CASE_PACK_REMAIN,'FM999999990.0000'),'0') goodsCasePackRemain,
        NVL2(bo.GOODS_WEIGHT_REMAIN,TO_CHAR(bo.GOODS_WEIGHT_REMAIN,'FM999999990.0000'),'0') goodsWeightRemain,
        NVL2(bo.GOODS_VOLUME_REMAIN,TO_CHAR(bo.GOODS_VOLUME_REMAIN,'FM999999990.0000'),'0') goodsVolumeRemain,
        bo.CUSTOMER_ORDER_NO customerOrderNo,
        ttw.TAX_WAYBILL_NO taxWaybillNo,
        ttw.DRIVER_NAME driverName,
        ttw.MOBILE_NO driverMobileNo,
        ttw.CART_BADGE_NO cartBadgeNo,
        ttw.GOODS_NAME goodsName,
        TO_CHAR(ttw.GOODS_AMOUNT,'FM999999990.0000') goodsAmount,
        ttw.GOODS_AMOUNT_TYPE goodsAmountType,
        ttw.TAX_WAYBILL_ID taxWaybillId,
        ttw.BO_TRANS_TASK_ID boTransTaskId,
        DECODE(SOR.STOWAGE_STATE,1,1,2,2,3,2,4,1,1) stowageState,
        CASE WHEN SOR.STOWAGE_STATE = 4 THEN NULL ELSE
        SOR.BO_TP_STOWAGE_RECORD_ID END boTpStowageRecordId,
        decode(TCO.DISTRIBUTE_STATE, 1, 1, 0) distributeStatus,
        bo.INDUSTRIAL_PARK industrialPark,
        bo.WORKSHOP workshop,
        bo.REMARK remark,
        <if test="configId != null and configId == '104'.toString()">
            DECODE(ttw.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 2, 500, 3, 600, 4, 650, 4, 700, 5, 800, 5, 1000, 5,
            1100, 5, 1200, 6, 1) transportState,
        </if>
        <if test="configId != null and configId == '105'.toString()">
            DECODE(ttw.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 3, 501, 2, 600, 4, 650, 4, 700, 5, 800, 5, 1000, 5,
            1100, 5, 1200, 6, 1) transportState,
        </if>
        DECODE(ttw.NODE_ID, 0, 0, 100, 0, 300, 0, 1) dispatchState,
        NVL2(ttw.BO_TRANS_TASK_ID,2,1) sort<!--,
        <if test="configId != null and configId == '104'.toString()">
            DECODE(ttw.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 2, 500, 3, 600, 4, 700, 5, 800, 5, 1000, 5, 1100, 5, 1200, 6) transportState,
        </if>
        <if test="configId != null and configId == '105'.toString()">
            DECODE(ttw.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 3, 501, 2, 600, 4, 700, 5, 800, 5, 1000, 5, 1100, 5, 1200, 6) transportState,
        </if>
		DECODE(ttw.NODE_ID, 0, 0, 100, 0, 300, 0, 1) dispatchState,-->
    </sql>

    <select id="getStatCount" resultType="int" parameterType="com.wtyt.dao.bean.syf.BoTransPlanIBean">
        SELECT
        count(distinct (bo.BO_TRANS_ORDER_ID))
        FROM
        <include refid="basePlan"/>
        left join
        T_BO_TRANS_ORDER bo on
        M.merge_id = bo.merge_id
        AND
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                bo.IS_DEL = 1
            </when>
            <otherwise>
                bo.IS_DEL = 0
            </otherwise>
        </choose>
        left join
        <!-- 中间关联表随意关联一条最近记录，取出来的运单数据不展示到列表，在Java代码列表循环中取运输任务属性 -->
        T_BO_TRANS_ORDER_REL TCO
        ON TCO.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND TCO.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK ttw ON
        TCO.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID AND ttw.IS_DEL = 0
    </select>

    <select id="getOverTimeStatCount" resultType="int">
        select count(0) from
        (select T.*,
        ROW_NUMBER() OVER(PARTITION BY T.boTransOrderId ORDER BY T.boTransOrderId DESC) RN
        from (
        SELECT
        *
        FROM
        (
        SELECT
        <include refid="queryList"/>,
        /*DECODE(ttw.NODE_ID, 300, 1, 0) dispatchRecord*/
        100 DSort,
        tl.DEAD_LINE_TIME,
        ADD_MONTHS(SYSDATE ,-120) CREATED_TIME_SORT
        FROM
        <include refid="baseUnDisPatchedPlan"/>
        left join
        T_BO_TRANS_ORDER bo on bo.merge_id = M.merge_id
        AND bo.IS_DEL = 0
        left join
        T_BO_TRANS_ORDER_REL TCO
        ON TCO.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND TCO.IS_DEL = 0

        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0


        LEFT JOIN T_BO_TRANS_TASK ttw ON
        TCO.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID AND ttw.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA ttwe ON
        ttw.BO_TRANS_TASK_ID = ttwe.BO_TRANS_TASK_ID AND ttwe.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_DEAD_LINE tl ON
        ttw.BO_TRANS_TASK_ID = tl.BO_TRANS_TASK_ID AND tl.IS_DEL = 0
        AND tl.NODE_ID = 200
        left join T_BO_ORDER_GROUP_REL tbogr on bo.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        <!-- 新颜模式 -->
        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            left join T_BO_INQUIRE tbi on bo.BO_TRANS_ORDER_ID = tbi.BO_TRANS_ORDER_ID
        </if>
        <!-- 异常连异常表 -->
        <if test="tabState != null and tabState == '3'.toString()">
            INNER JOIN (
            SELECT
            BO_TRANS_TASK_ID,
            BO_TRANS_NODE_ALARM_ID,
            ALARM_TYPE
            FROM
            (
            SELECT
            tna.BO_TRANS_TASK_ID,
            tna.BO_TRANS_NODE_ALARM_ID,
            tna.ALARM_TYPE,
            ROW_NUMBER() OVER(PARTITION BY tna.BO_TRANS_TASK_ID, tna.NODE_ID ORDER BY tna.CREATED_TIME DESC) RN
            FROM
            T_BO_TRANS_NODE_ALARM tna
            WHERE
            tna.NODE_ID = 200
            and tna.NODE_DATA_TYPE = 1
            and tna.IS_DEL = 0)
            WHERE
            RN = 1
            )TT ON ttw.BO_TRANS_TASK_ID = TT.BO_TRANS_TASK_ID
        </if>)
        UNION ALL
        SELECT
        *
        FROM
        (
        SELECT
        <include refid="queryList"/>,
        1 DSort,
        ADD_MONTHS(SYSDATE ,12) DEAD_LINE_TIME,
        tnr.CREATED_TIME CREATED_TIME_SORT
        /*1 dispatchRecord*/
        FROM
        <include refid="baseDisPatchedPlan"/>
        left join
        T_BO_TRANS_ORDER bo on bo.MERGE_ID = M.MERGE_ID
        AND bo.IS_DEL = 0
        left join
        T_BO_TRANS_ORDER_REL TCO
        ON TCO.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND TCO.IS_DEL = 0

        LEFT JOIN T_BO_TP_STOWAGE_ORDER_REL SOR ON
        SOR.BO_TRANS_ORDER_ID = bo.BO_TRANS_ORDER_ID AND SOR.IS_DEL = 0

        LEFT JOIN T_BO_TRANS_TASK ttw ON
        TCO.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID AND ttw.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA ttwe ON
        ttw.BO_TRANS_TASK_ID = ttwe.BO_TRANS_TASK_ID AND ttwe.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_NODE_RECORD tnr ON
        ttw.BO_TRANS_TASK_ID = tnr.BO_TRANS_TASK_ID AND tnr.NODE_ID = 200 AND tnr.IS_DEL = 0
        left join T_BO_ORDER_GROUP_REL tbogr on bo.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        <!-- 新颜模式 -->
        <if test="xinYanMode != null and xinYanMode == '1'.toString()">
            left join T_BO_INQUIRE tbi on bo.BO_TRANS_ORDER_ID = tbi.BO_TRANS_ORDER_ID
        </if>
        <!-- 异常连异常表 -->
        <if test="tabState != null and tabState == '3'.toString()">
            INNER JOIN (
            SELECT
            BO_TRANS_TASK_ID,
            BO_TRANS_NODE_ALARM_ID,
            ALARM_TYPE
            FROM
            (
            SELECT
            tna.BO_TRANS_TASK_ID,
            tna.BO_TRANS_NODE_ALARM_ID,
            tna.ALARM_TYPE,
            ROW_NUMBER() OVER(PARTITION BY tna.BO_TRANS_TASK_ID, tna.NODE_ID ORDER BY tna.CREATED_TIME DESC) RN
            FROM
            T_BO_TRANS_NODE_ALARM tna
            WHERE
            tna.NODE_ID = 200
            <!-- 已派车的只取ALARM_TYPE为1的数据 -->
            AND tna.ALARM_TYPE = 1
            )
            WHERE
            RN = 1
            )TT ON ttw.BO_TRANS_TASK_ID = TT.BO_TRANS_TASK_ID
        </if>))T)TTC where TTC.RN = 1
    </select>

    <select id="getAlarmState" resultType="com.wtyt.dao.bean.syf.BoTransNodeAlarm">
        SELECT ALARM_TYPE alarmType, OVER_TIME overTime
        FROM (SELECT ALARM_TYPE,
                    CASE WHEN ALARM_TYPE = 0 THEN ROUND((NVL(ALARM_START_TIME, SYSDATE) - SYSDATE) * 24 * 3600)
                         WHEN ALARM_TYPE = 1 THEN ROUND((NVL(ALARM_END_TIME, SYSDATE) - NVL(ALARM_START_TIME, CREATED_TIME)) * 24 * 3600)
                     END OVER_TIME
              FROM T_BO_TRANS_NODE_ALARM
              WHERE BO_TRANS_TASK_ID = #{boTransTaskId}
                AND NODE_ID = 200
                AND NODE_DATA_TYPE = 1
                AND IS_DEL = 0
              ORDER BY ALARM_TYPE DESC)
        WHERE rownum = 1
    </select>

    <select id="getAlarmStateByTaskIdList" resultType="com.wtyt.dao.bean.syf.BoTransNodeAlarm">
        SELECT
            ALARM_TYPE alarmType,
            CASE WHEN ALARM_TYPE = 0 THEN ROUND((NVL(ALARM_START_TIME, SYSDATE) - SYSDATE) * 24 * 3600)
                 WHEN ALARM_TYPE = 1 THEN ROUND((NVL(ALARM_END_TIME, SYSDATE) - NVL(ALARM_START_TIME, CREATED_TIME)) * 24 * 3600)
             END overTime,
            BO_TRANS_TASK_ID boTransTaskId
        FROM
        T_BO_TRANS_NODE_ALARM
        WHERE
        BO_TRANS_TASK_ID IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND NODE_ID = 200
        AND NODE_DATA_TYPE = 1
        AND IS_DEL = 0
        ORDER BY
        ALARM_TYPE DESC
    </select>

    <select id="getLastAlarm" resultType="com.wtyt.dao.bean.syf.BoTransNodeAlarm">
        SELECT * FROM (
                          SELECT
                              tna.BO_TRANS_TASK_ID as boTransTaskId,
                              tna.BO_TRANS_NODE_ALARM_ID as boTransNodeAlarmId
                          FROM (
                                   SELECT
                                       BO_TRANS_TASK_ID
                                   FROM T_BO_TRANS_TASK
                                   WHERE ORG_ID = #{orgId}
                                     AND CREATED_USER_ID =  #{userId}
                                     AND IS_DEL = 0
                                     AND CREATED_TIME >= SYSDATE -7
                               ) ttw INNER JOIN T_BO_TRANS_NODE_ALARM tna ON tna.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID
                          WHERE
                              tna.NODE_ID = 200
                            AND tna.NODE_DATA_TYPE = 1
                            AND tna.IS_DEL = 0
                          ORDER BY tna.CREATED_TIME DESC, tna.BO_TRANS_NODE_ALARM_ID DESC
                      ) WHERE ROWNUM = 1
    </select>

    <select id="getOrderByBoTransTaskId" parameterType="String" resultType="BoTransOrderBean">
        SELECT T.CUSTOMER_NAME                                  customerName,
               T.STORAGE_PLACE                                  storagePlace,
               T.END_ADDRESS                                    endAddress,
               TO_CHAR(r.GOODS_CASE_PACK, 'FM999999990.0000')   goodsCasePack,
               TO_CHAR(r.GOODS_WEIGHT, 'FM999999990.0000')      goodsWeight,
               TO_CHAR(r.GOODS_VOLUME, 'FM999999990.0000')      goodsVolume,
               T.CUSTOMER_ORDER_NO                              customerOrderNo,
               T.USER_ID                                        userId,
               T.SYS_ROLE_TYPE                                  sysRoleType,
               T.JOB_NAME                                       jobName,
               TO_CHAR(T.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
               T.FROM_SOURCE                                    fromSource
        FROM T_BO_TRANS_ORDER T
                 left join T_BO_TRANS_ORDER_REL r on t.BO_TRANS_ORDER_ID = r.BO_TRANS_ORDER_ID
        WHERE r.BO_TRANS_TASK_ID = #{boTransTaskId}
          AND T.IS_DEL = 0
          and r.IS_DEL = 0
        ORDER BY T.CREATED_TIME
    </select>

    <select id="getNeedDealAlarm" parameterType="string" resultType="BoTransNodeAlarmBean">
        SELECT ta.TAX_WAYBILL_ID         taxWaybillId,
               ta.BO_TRANS_TASK_ID       boTransTaskId,
               ta.BO_TRANS_NODE_ALARM_ID boTransNodeAlarmId
        FROM T_BO_TRANS_NODE_ALARM ta
                 LEFT JOIN T_BO_TRANS_TASK ttw ON
            ta.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID AND ttw.IS_DEL = 0
        WHERE ta.NODE_ID = 200
          AND ttw.org_id = #{orgId}
          AND NOT EXISTS (SELECT 1
                          FROM T_BO_TRANS_ALARM_READ
                          WHERE BO_TRANS_NODE_ALARM_ID = ta.BO_TRANS_NODE_ALARM_ID
                            AND USER_ID = #{userId})
    </select>

    <!--    <update id="delTransOrders" parameterType="list">-->
    <!--        UPDATE T_BO_TRANS_ORDER-->
    <!--        SET-->
    <!--        IS_DEL = 1,-->
    <!--        LAST_MODIFIED_TIME = SYSDATE-->
    <!--        WHERE-->
    <!--        IS_DEL = 0-->
    <!--        AND TAX_WAYBILL_ID IN-->
    <!--        <foreach collection="taxWaybillIds" index="index" item="item" separator="," open="(" close=")">-->
    <!--            #{item}-->
    <!--        </foreach>-->
    <!--    </update>-->

    <update id="delTransOrderByIds" parameterType="list">
        UPDATE T_BO_TRANS_ORDER
        SET
        IS_DEL = 1,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        IS_DEL = 0
        AND BO_TRANS_ORDER_ID IN
        <foreach collection="orderIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="getTotalCount" resultType="int">
        SELECT count(1)
        FROM T_BO_TRANS_TASK ttw
        WHERE ORG_ID = #{orgId}
          AND CREATED_TIME &gt; TO_DATE(#{startDate}, 'YYYY-MM-DD HH24:MI:SS')
          AND CREATED_TIME &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
          AND ORDER_CREATE_TYPE IN (2001, 2002, 2003, 2004, 2005)
          AND IS_DEL = 0
    </select>

    <select id="getDispatchCount" resultType="int">
        SELECT count(1)
        FROM T_BO_TRANS_TASK ttw
        WHERE ORG_ID = #{orgId}
          AND CREATED_TIME &gt; TO_DATE(#{startDate}, 'YYYY-MM-DD HH24:MI:SS')
          AND CREATED_TIME &lt;= TO_DATE(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
          AND ORDER_CREATE_TYPE IN (2001, 2002, 2003, 2004, 2005)
          AND NODE_ID IN (0, 100, 300)
          AND IS_DEL = 0
    </select>

    <select id="getUnDispatchedOverTime" resultType="int">
        SELECT count(1)
        FROM T_BO_TRANS_TASK ttw
                 INNER JOIN T_BO_TRANS_NODE_ALARM tna
                            ON ttw.BO_TRANS_TASK_ID = tna.BO_TRANS_TASK_ID
                                AND tna.NODE_ID = 200
        WHERE ttw.ORG_ID = #{orgId}
          AND ttw.ORDER_CREATE_TYPE IN (2001, 2002, 2003)
          AND ttw.NODE_ID IN (0, 100, 300)
          AND ttw.IS_DEL = 0
    </select>

    <select id="getOrderDetailByOrderIds" resultType="BoOrderDetailOBean">
        SELECT
        <include refid="orderColumn"/>
        FROM T_BO_TRANS_ORDER O
        WHERE is_del = 0
        AND BO_TRANS_ORDER_ID IN
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="getTaxWaybillInfos" resultType="BoTaxWaybillBean">
        SELECT
        ttw.TAX_WAYBILL_ID taxWaybillId,
        ttw.TAX_WAYBILL_NO taxWaybillNo,
        ttw.CART_BADGE_NO cartBadgeNo,
        ttw.DRIVER_NAME driverName,
        ttw.GOODS_NAME goodsName,
        ttw.GOODS_AMOUNT goodsAmount,
        ttw.GOODS_AMOUNT_TYPE goodsAmountType,
        NVL2(ttwe.CART_LENGTH ,ttwe.CART_LENGTH || '米','') carLength,
        ttwe.CART_TYPE carType,
        ttw.node_id nodeId
        FROM
        T_BO_TRANS_ORDER_REL rel
        LEFT JOIN T_BO_TRANS_TASK ttw ON
        rel.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_EXTRA ttwe ON
        ttwe.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID
        WHERE rel.IS_DEL = 0
        AND ttw.IS_DEL = 0
        AND rel.BO_TRANS_ORDER_ID IN
        <foreach collection="boTransOrderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        order by ttw.created_time desc
    </select>

    <select id="getTaxWaybillInfosForRecord" resultType="com.wtyt.dao.bean.syf.BoTaxWaybillBean">
        SELECT
        ttw.TAX_WAYBILL_ID taxWaybillId,
        ttw.TAX_WAYBILL_NO taxWaybillNo,
        ttw.CART_BADGE_NO cartBadgeNo,
        NVL2(ttwe.CART_LENGTH ,ttwe.CART_LENGTH || '米','') carLength,
        ttwe.CART_TYPE carType,
        ttw.node_id nodeId,
        ttw.BO_TRANS_TASK_ID boTransTaskId
        FROM
        T_BO_TRANS_ORDER_REL rel
        INNER JOIN T_BO_TRANS_TASK ttw ON
        rel.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID AND ttw.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK_EXTRA ttwe ON
        ttwe.BO_TRANS_TASK_ID = ttw.BO_TRANS_TASK_ID AND ttwe.IS_DEL = 0
        WHERE
        rel.BO_TRANS_ORDER_ID IN
        <foreach collection="boTransOrderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        AND rel.IS_DEL = 0
        order by ttw.created_time desc
    </select>


    <select id="existsStartTask" resultType="java.lang.String">
        SELECT
        1
        FROM
        DUAL
        WHERE
        EXISTS (
        SELECT
        1
        FROM
        T_BO_TRANS_ORDER_REL R
        WHERE
        R.IS_DEL = 0
        AND R.BO_TRANS_TASK_ID IS NOT NULL
        AND R.BO_TRANS_ORDER_ID IN
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>)
    </select>

    <select id="getOrderByPkIds" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        <include refid="orderColumn"/>
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.IS_DEL = 0
        AND O.BO_TRANS_ORDER_ID IN
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
    <select id="getSubOrderByPkIds" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        <include refid="orderColumn"/>
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.HIERARCHY_TYPE = 2
        AND O.BO_TRANS_ORDER_ID IN
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="getByPkId" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        <include refid="orderColumn"/>
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.IS_DEL = 0
        AND O.BO_TRANS_ORDER_ID = #{boTransOrderId}
    </select>

    <update id="updateGoodsNumRemain">
        UPDATE
            T_BO_TRANS_ORDER
        SET SPILT_FLAG             = #{spiltFlag},
            GOODS_CASE_PACK_REMAIN = #{goodsCasePackRemain},
            GOODS_WEIGHT_REMAIN    = #{goodsWeightRemain},
            GOODS_VOLUME_REMAIN    = #{goodsVolumeRemain},
            LAST_MODIFIED_TIME     = SYSDATE
        WHERE BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>

    <update id="updateGoodsNum">
        UPDATE
        T_BO_TRANS_ORDER
        SET
        <choose>
            <when test="spiltFlag == null">
                SPILT_FLAG = 0,
            </when>
            <otherwise>
                SPILT_FLAG = #{spiltFlag},
            </otherwise>
        </choose>
        <if test="goodsCasePackRemain != null and goodsCasePackRemain != ''">
            GOODS_CASE_PACK_REMAIN = #{goodsCasePackRemain},
        </if>
        <if test="goodsWeightRemain != null and goodsWeightRemain != ''">
            GOODS_WEIGHT_REMAIN = #{goodsWeightRemain},
        </if>
        <if test="goodsVolumeRemain != null and goodsVolumeRemain != ''">
            GOODS_VOLUME_REMAIN = #{goodsVolumeRemain},
        </if>
        <if test="mergeId != null and mergeId != ''">
            MERGE_ID = #{mergeId},
        </if>
        <if test="spiltProgress != null and spiltProgress != ''">
            <choose>
                <when test="spiltProgress == 0">
                    SPILT_PROGRESS = NULL,
                </when>
                <otherwise>
                    SPILT_PROGRESS = #{spiltProgress},
                </otherwise>
            </choose>
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>

    <update id="updateDispatchCarNum">
        UPDATE
        T_BO_TRANS_ORDER
        SET
        <if test="dispatchCarNum != null and dispatchCarNum != ''">
            DISPATCH_CAR_NUM = #{dispatchCarNum},
        </if>
        <if test="remainCarNum != null and remainCarNum != ''">
            REMAIN_CAR_NUM = #{remainCarNum},
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>


    <update id="updateOrderMergeId">
        UPDATE
            T_BO_TRANS_ORDER
        SET MERGE_ID           = #{mergeId},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>

    <update id="updateMergeOrderMergeId">
        UPDATE
            T_BO_TRANS_ORDER
        SET MERGE_ID           = #{mergeId},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_ORDER_ID  in
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>


    <select id="getDispatchRecord" resultType="BoTransDispatchRecordOBean">
        SELECT
        tr.TYPE typeCode,
        tr.DRIVER_NAME driverName,
        tr.MOBILE_NO mobileNo,
        tr.CART_BADGE_NO cartBadgeNo,
        TO_CHAR(tr.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') optTime,
        tr.REASON optReason,
        tr.USER_ID userId
        FROM
        T_BO_TRANS_SEND_CAR_RECORD tr
        WHERE tr.BO_TRANS_TASK_ID IN
        <foreach collection="boTransTaskIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        ORDER BY tr.CREATED_TIME DESC
    </select>

    <select id="checkRemain" resultType="int">
        SELECT count(1)
        FROM T_BO_TRANS_ORDER bo
        WHERE bo.IS_DEL = 0
          and bo.BO_TRANS_ORDER_ID = #{boTransOrderId}
          AND (
            (bo.GOODS_CASE_PACK > 0 AND bo.GOODS_CASE_PACK_REMAIN > 0)
                AND (bo.GOODS_VOLUME > 0 AND bo.GOODS_VOLUME_REMAIN > 0)
                AND (bo.GOODS_WEIGHT > 0 AND bo.GOODS_WEIGHT_REMAIN > 0))
    </select>


    <update id="updateByPk">
        UPDATE T_BO_TRANS_ORDER SET
        <if test="maxReserveMileage != null ">
            MAX_RESERVE_MILEAGE= #{maxReserveMileage},
        </if>
        <if test="maxReserveHour != null ">
            MAX_RESERVE_HOUR= #{maxReserveHour},
        </if>
        <if test="endTime != null ">
            END_TIME= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
        <if test="remark != null ">
            REMARK= #{remark},
        </if>
        <if test="serviceRequire != null ">
            SERVICE_REQUIRE = #{serviceRequire},
        </if>
        <if test="consignmentName != null ">
            CONSIGNMENT_NAME= #{consignmentName},
        </if>
        <if test="consignmentContact != null ">
            CONSIGNMENT_CONTACT= #{consignmentContact},
        </if>
        <if test="mergeId != null and mergeId != ''">
            MERGE_ID = #{mergeId},
        </if>
        <if test="thirdOrderNo != null and thirdOrderNo != ''">
            THIRD_ORDER_NO = #{thirdOrderNo},
        </if>
        <if test="customerOrderNo != null and customerOrderNo != ''">
            CUSTOMER_ORDER_NO = #{customerOrderNo},
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>

    <update id="modifyOrder">
        UPDATE T_BO_TRANS_ORDER SET
        <if test="receiptNo != null">
            RECEIPT_NO= #{receiptNo},
        </if>
        <if test="customerName != null">
            CUSTOMER_NAME= #{customerName},
        </if>
        <if test="customerContact != null">
            CUSTOMER_CONTACT= #{customerContact},
        </if>
        <if test="consignmentName != null">
            CONSIGNMENT_NAME= #{consignmentName},
        </if>
        <if test="consignmentMobileNo != null">
            CONSIGNMENT_MOBILE_NO= #{consignmentMobileNo},
        </if>
        <if test="consignmentContact != null">
            CONSIGNMENT_CONTACT= #{consignmentContact},
        </if>
        <if test="transDate != null">
            TRANS_DATE= to_date(#{transDate},'yyyy-mm-dd hh24:mi:ss'),
        </if>
        <if test="storagePlace != null">
            STORAGE_PLACE= #{storagePlace},
        </if>
        <if test="startAddress != null">
            START_ADDRESS= #{startAddress},
        </if>
        <if test="rawStartAddress != null">
            RAW_START_ADDRESS= #{rawStartAddress},
        </if>
        <if test="endAddress != null">
            END_ADDRESS= #{endAddress},
        </if>
        <if test="rawEndAddress != null">
            RAW_END_ADDRESS= #{rawEndAddress},
        </if>
        <if test="consigneeName != null">
            CONSIGNEE_NAME= #{consigneeName},
        </if>
        <if test="consigneeContact != null">
            CONSIGNEE_CONTACT= #{consigneeContact},
        </if>
        <if test="consigneeMobileNo != null">
            CONSIGNEE_MOBILE_NO= #{consigneeMobileNo},
        </if>
        <if test="goodsName != null">
            GOODS_NAME= #{goodsName},
        </if>
        <if test="goodsSpec != null">
            GOODS_SPEC= #{goodsSpec},
        </if>
        <if test="goodsCasePack != null">
            GOODS_CASE_PACK= to_char(#{goodsCasePack},'fm9999999990.0000'),
        </if>
        <if test="goodsWeight != null">
            GOODS_WEIGHT= to_char(#{goodsWeight},'fm9999999990.0000'),
        </if>
        <if test="goodsVolume != null">
            GOODS_VOLUME= to_char(#{goodsVolume},'fm9999999990.0000'),
        </if>
        <if test="goodsCasePackRemain != null">
            GOODS_CASE_PACK_REMAIN= to_char(#{goodsCasePackRemain},'fm9999999990.0000'),
        </if>
        <if test="goodsWeightRemain != null">
            GOODS_WEIGHT_REMAIN= to_char(#{goodsWeightRemain},'fm9999999990.0000'),
        </if>
        <if test="goodsVolumeRemain != null">
            GOODS_VOLUME_REMAIN= to_char(#{goodsVolumeRemain},'fm9999999990.0000'),
        </if>
        <if test="cartLength != null">
            CART_LENGTH= #{cartLength},
        </if>
        <if test="cartType != null">
            CART_TYPE= #{cartType},
        </if>
        <if test="remark != null">
            REMARK= #{remark},
        </if>
        <if test="serviceRequire != null ">
            SERVICE_REQUIRE = #{serviceRequire},
        </if>
        <if test="mileage != null">
            MILEAGE= #{mileage},
        </if>
        <if test="consigneeUnit != null">
            CONSIGNEE_UNIT= #{consigneeUnit},
        </if>
        <if test="customerOrderNo != null">
            CUSTOMER_ORDER_NO= #{customerOrderNo},
        </if>
        <if test="thirdOrderNo != null and thirdOrderNo != ''">
            THIRD_ORDER_NO= #{thirdOrderNo},
        </if>
        <if test="spiltProgress != null and spiltProgress != ''">
            SPILT_PROGRESS= #{spiltProgress},
        </if>
        <if test="startProvinceName != null ">
            START_PROVINCE_NAME= #{startProvinceName},
        </if>
        <if test="startCityName != null ">
            START_CITY_NAME= #{startCityName},
        </if>
        <if test="startCountyName != null ">
            START_COUNTY_NAME= #{startCountyName},
        </if>
        <if test="endProvinceName != null ">
            END_PROVINCE_NAME= #{endProvinceName},
        </if>
        <if test="endCityName != null ">
            END_CITY_NAME= #{endCityName},
        </if>
        <if test="endCountyName != null ">
            END_COUNTY_NAME= #{endCountyName},
        </if>
        <if test="startLat != null ">
            START_LAT= #{startLat},
        </if>
        <if test="startLng != null ">
            START_LNG= #{startLng},
        </if>
        <if test="endLat != null ">
            END_LAT= #{endLat},
        </if>
        <if test="endLng != null ">
            END_LNG= #{endLng},
        </if>
        <if test="driverGuaranteeAmount != null ">
            DRIVER_GUARANTEE_AMOUNT= #{driverGuaranteeAmount},
        </if>
        <if test="loadingCarNum != null ">
            LOADING_CAR_NUM= #{loadingCarNum},
        </if>
        <if test="unitPrice != null ">
            UNIT_PRICE= #{unitPrice},
        </if>
        <if test="carrier != null ">
            CARRIER= #{carrier},
        </if>
        <if test="promiseArriveTime != null ">
            PROMISE_ARRIVE_TIME=  TO_DATE(#{promiseArriveTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
        <if test="orderDate != null ">
            ORDER_DATE= #{orderDate},
        </if>
        <if test="contractPrice != null ">
            CONTRACT_PRICE= #{contractPrice},
        </if>
        <if test="cartNumId != null ">
            CART_NUM_ID= #{cartNumId},
        </if>
        <if test="userFreight != null ">
            USER_FREIGHT= #{userFreight},
        </if>
        <if test="backFee != null ">
            BACK_FEE= #{backFee},
        </if>
        <if test="latestPresenceTime != null ">
            LATEST_PRESENCE_TIME=  TO_DATE(#{latestPresenceTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
         <if test="actualLoadingTime != null">
            ACTUAL_LOADING_TIME= to_date(#{actualLoadingTime},'yyyy-mm-dd hh24:mi:ss'),
        </if>
        <if test="maxRouteTime != null ">
            MAX_ROUTE_TIME= #{maxRouteTime},
        </if>
        <if test="endStoragePlace != null ">
            END_STORAGE_PLACE= #{endStoragePlace},
        </if>
        <if test="maxReserveMileage != null ">
            MAX_RESERVE_MILEAGE= #{maxReserveMileage},
        </if>
        <if test="maxReserveHour != null ">
            MAX_RESERVE_HOUR= #{maxReserveHour},
        </if>
        <if test="startTime != null ">
            START_TIME=  TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
        <if test="endTime != null ">
            END_TIME=  TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
        <if test="driverName != null ">
            DRIVER_NAME= #{driverName},
        </if>
        <if test="cartBadgeNo != null ">
            CART_BADGE_NO= #{cartBadgeNo},
        </if>
        <if test="driverMobileNo != null ">
            DRIVER_MOBILE_NO= #{driverMobileNo},
        </if>
        <if test="allFreight != null ">
            ALL_FREIGHT= #{allFreight},
        </if>
        <if test="fuelCostFee != null ">
            FUEL_COST_FEE= #{fuelCostFee},
        </if>
        <if test="overhead != null ">
            OVERHEAD= #{overhead},
        </if>
        <if test="businessCustomer != null ">
            BUSINESS_CUSTOMER= #{businessCustomer},
        </if>
        <if test="realBusinessCustomer != null ">
            REAL_BUSINESS_CUSTOMER= #{realBusinessCustomer},
        </if>
        <if test="industrialPark != null ">
            INDUSTRIAL_PARK= #{industrialPark},
        </if>
        <if test="workshop != null ">
            WORKSHOP= #{workshop},
        </if>
        <if test="storageLocation != null ">
            STORAGE_LOCATION= #{storageLocation},
        </if>
        <if test="settleMode != null ">
            SETTLE_MODE= #{settleMode},
        </if>
        <if test="arriveEndTime != null ">
            ARRIVE_END_TIME=  TO_DATE(#{arriveEndTime},'YYYY-MM-DD HH24:MI:SS'),
        </if>
        <if test="receivable != null ">
            RECEIVABLE= #{receivable},
        </if>
        <if test="salesmanName != null ">
            SALESMAN_NAME= #{salesmanName},
        </if>
        <if test="goodsCasePackUnit != null and goodsCasePackUnit != ''">
            GOODS_CASE_PACK_UNIT=  UPPER(#{goodsCasePackUnit}),
        </if>
        <if test="goodsWeightUnit != null and goodsWeightUnit != ''">
            GOODS_WEIGHT_UNIT=  UPPER(#{goodsWeightUnit}),
        </if>
        <if test="goodsVolumeUnit != null and goodsVolumeUnit != ''">
            GOODS_VOLUME_UNIT=  UPPER(#{goodsVolumeUnit}),
        </if>
        <if test="dtInsideOrderMark != null and dtInsideOrderMark != ''">
            DT_INSIDE_ORDER_MARK=  #{dtInsideOrderMark},
        </if>
        <if test="dtElectronicReceiptMark != null and dtElectronicReceiptMark != ''">
            DT_ELECTRONIC_RECEIPT_MARK=  #{dtElectronicReceiptMark},
        </if>
        <if test="xcyUserId != null and xcyUserId != ''">
            XCY_USER_ID=  #{xcyUserId},
        </if>
         <if test="belongDispatcherId != null and belongDispatcherId != ''">
            BELONG_DISPATCHER_ID=  #{belongDispatcherId},
        </if>
        <if test="lineCode !=null">
            LINE_CODE= #{lineCode},
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_ORDER_ID = #{boTransOrderId}
        AND ORG_ID = #{orgId}
        AND IS_DEL = 0
    </update>

    <select id="getOrderDetailByOrderId" resultType="BoXYOrderDetailOBean">
        SELECT
        a.BO_TRANS_ORDER_ID boTransOrderId,
        a.CUSTOMER_ORDER_NO customerOrderNo,
        a.RECEIPT_NO receiptNo,
        a.CUSTOMER_NAME customerName,
        a.CUSTOMER_CONTACT customerContact,
        a.CONSIGNMENT_NAME consignmentName,
        a.CONSIGNMENT_MOBILE_NO consignmentMobileNo,
        a.CONSIGNMENT_CONTACT consignmentContact,
        to_char(a.TRANS_DATE, 'YYYY-MM-DD') transDate,
        a.START_PROVINCE_NAME || a.START_CITY_NAME || a.START_COUNTY_NAME || a.START_ADDRESS AS loadingPlace,
        a.END_PROVINCE_NAME || a.END_CITY_NAME || a.END_COUNTY_NAME || a.END_ADDRESS AS unloadingPlace,
        a.RAW_START_ADDRESS  rawStartAddress,
        a.RAW_END_ADDRESS rawEndAddress,
        a.CONSIGNEE_NAME consigneeName,
        a.CONSIGNEE_CONTACT consigneeContact,
        a.CONSIGNEE_MOBILE_NO consigneeMobileNo,
        a.GOODS_NAME goodsName,
        NVL2(a.GOODS_CASE_PACK,TO_CHAR(a.GOODS_CASE_PACK,'FM999999990.0000'),'') goodsCasePack,
        NVL2(a.GOODS_WEIGHT,TO_CHAR(a.GOODS_WEIGHT,'FM999999990.0000'),'') goodsWeight,
        NVL2(a.GOODS_VOLUME,TO_CHAR(a.GOODS_VOLUME,'FM999999990.0000'),'') goodsVolume,
        a.CART_LENGTH cartLength,
        a.CART_TYPE cartType,
        a.REMARK remark,
        a.CONSIGNEE_UNIT consigneeUnit,
        DECODE(b.INQUIRE_STATE,0,0,1,1,2) inquireState,
        NVL2(b.BO_INQUIRE_OFFER_ID,'0','1') orderSource,
        <if test="configId != null and configId == '104'.toString()">
            DECODE(d.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 2, 500, 3, 600, 4, 650, 4, 700, 5, 800, 5, 1000, 5,
            1100, 5, 1200, 6, 1) transportState,
        </if>
        <if test="configId != null and configId == '105'.toString()">
            DECODE(d.NODE_ID, 0, 1, 100, 1, 300, 1, 200, 2, 400, 3, 501, 2, 600, 4, 650, 4, 700, 5, 800, 5, 1000, 5,
            1100, 5, 1200, 6, 1) transportState,
        </if>
        d.TAX_WAYBILL_NO taxWaybillNo,
        e.GROUP_ID groupId
        FROM
        T_BO_TRANS_ORDER a
        LEFT JOIN T_BO_INQUIRE b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID
        LEFT JOIN T_BO_TRANS_ORDER_REL c ON c.BO_TRANS_ORDER_ID = a.BO_TRANS_ORDER_ID AND c.IS_DEL = 0
        LEFT JOIN T_BO_TRANS_TASK d ON d.BO_TRANS_TASK_ID = c.BO_TRANS_TASK_ID AND d.IS_DEL = 0
        LEFT JOIN T_BO_ORDER_GROUP_REL e ON a.BO_TRANS_ORDER_ID = e.BO_TRANS_ORDER_ID
        WHERE a.BO_TRANS_ORDER_ID IN
        <foreach collection="boTransOrderIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="checkTask" resultType="string">
        SELECT a.BO_TRANS_TASK_ID
        FROM T_BO_TRANS_ORDER_REL a
                 LEFT JOIN T_BO_TRANS_ORDER b ON
            a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID
        WHERE b.BO_TRANS_ORDER_ID = #{boTransOrderId}
          AND a.IS_DEL = 0
          AND b.IS_DEL = 0
          AND rownum = 1
    </select>

    <resultMap id="BoXYOrderMap" type="BoXYOrderListOBean">
        <result property="taxWaybillId" column="taxWaybillId"/>
        <result property="taxWaybillNo" column="taxWaybillNo"/>
        <result property="overTimeState" column="overTimeState"/>
        <result property="overTimeDetail" column="overTimeDetail"/>
        <result property="mergeId" column="mergeId"/>
        <collection property="orderList" ofType="com.wtyt.dao.bean.syf.BoXYOrderListOBean$OrderInfo">
            <result property="boTransOrderId" column="boTransOrderId"/>
            <result property="customerOrderNo" column="customerOrderNo"/>
            <result property="groupId" column="groupId"/>
            <result property="transDate" column="transDate"/>
            <result property="department" column="department"/>
            <result property="loadingPlace" column="loadingPlace"/>
            <result property="unloadingPlace" column="unloadingPlace"/>
            <result property="mileage" column="mileage"/>
            <result property="goodsInfo" column="goodsInfo"/>
            <result property="remark" column="remark"/>
            <result property="serviceRequire" column="serviceRequire"/>
            <result property="inquireState" column="inquireState"/>
            <result property="inquireOverTimeState" column="inquireOverTimeState"/>
            <result property="goodsName" column="goodsName"/>
            <result property="goodsWeight" column="goodsWeight"/>
            <result property="goodsVolume" column="goodsVolume"/>
            <result property="cartLength" column="cartLength"/>
            <result property="cartType" column="cartType"/>
            <result property="upstreamState" column="upstreamState"/>
            <result property="endProvinceName" column="endProvinceName"></result>
            <result property="endCityName" column="endCityName"></result>
            <result property="endCountyName" column="endCountyName"></result>
            <result property="startProvinceName" column="startProvinceName"></result>
            <result property="startCityName" column="startCityName"></result>
            <result property="startCountyName" column="startCountyName"></result>
        </collection>

    </resultMap>

    <select id="getOrderList" parameterType="BoOrderListIBean" resultMap="BoXYOrderMap">
        SELECT
        a.END_PROVINCE_NAME endProvinceName,
        a.END_CITY_NAME endCityName,
        a.END_COUNTY_NAME endCountyName,
        a.FROM_SOURCE fromSource,
        a.START_PROVINCE_NAME startProvinceName,
        a.START_CITY_NAME startCityName,
        a.START_COUNTY_NAME startCountyName,
        a.LAST_MODIFIED_TIME ,
        a.BO_TRANS_ORDER_ID boTransOrderId,
        a.CUSTOMER_ORDER_NO customerOrderNo,
        a.RECEIPT_NO receiptNo,
        a.CUSTOMER_NAME customerName,
        a.CUSTOMER_CONTACT customerContact,
        a.CONSIGNMENT_NAME consignmentName,
        a.CONSIGNMENT_MOBILE_NO consignmentMobileNo,
        a.CONSIGNMENT_CONTACT consignmentContact,
        to_char(a.TRANS_DATE, 'YYYY-MM-DD') transDate,
        a.START_PROVINCE_NAME || a.START_CITY_NAME || a.START_COUNTY_NAME || a.START_ADDRESS AS loadingPlace,
        a.END_PROVINCE_NAME || a.END_CITY_NAME || a.END_COUNTY_NAME || a.END_ADDRESS AS unloadingPlace,
        a.CONSIGNEE_NAME consigneeName,
        a.CONSIGNEE_CONTACT consigneeContact,
        a.CONSIGNEE_MOBILE_NO consigneeMobileNo,
        a.GOODS_NAME goodsName,
        NVL2(a.GOODS_CASE_PACK,TO_CHAR(a.GOODS_CASE_PACK,'FM999999990.0000'),'') goodsCasePack,
        a.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        NVL2(a.GOODS_WEIGHT,TO_CHAR(a.GOODS_WEIGHT,'FM999999990.0000'),'') goodsWeight,
        a.GOODS_WEIGHT_UNIT goodsWeightUnit,
        NVL2(a.GOODS_VOLUME,TO_CHAR(a.GOODS_VOLUME,'FM999999990.0000'),'') goodsVolume,
        a.GOODS_VOLUME_UNIT goodsVolumeUnit,
        a.CART_LENGTH cartLength,
        a.CART_TYPE cartType,
        a.REMARK remark,
        a.SERVICE_REQUIRE serviceRequire,
        a.CONSIGNEE_UNIT consigneeUnit,
        a.MILEAGE mileage,
        a.merge_id mergeId,
        decode(a.UPSTREAM_STATE,0,1,1,2,0) upstreamState,
        DECODE(b.INQUIRE_STATE,0,0,1,1,2) inquireState,
        b.INQUIRE_REAL_STATE inquireRealState,
        b.INQUIRE_OVER_TIME_STATE inquireOverTimeState,
        e.GROUP_ID groupId,
        DECODE(b.INQUIRE_STATE,0,0,1) sort,
        CASE
        WHEN
        b.INQUIRE_STATE = 0
        THEN
        -TO_NUMBER(TO_CHAR(a.CREATED_TIME, 'yyyymmddhh24miss'))
        WHEN (b.INQUIRE_STATE != 0 AND a.MERGE_ID = a.BO_TRANS_ORDER_ID)
        THEN TO_NUMBER(TO_CHAR(a.CREATED_TIME, 'yyyymmddhh24miss'))
        WHEN (b.INQUIRE_STATE != 0 AND a.MERGE_ID != a.BO_TRANS_ORDER_ID)
        THEN TO_NUMBER(TO_CHAR((SELECT CREATED_TIME FROM T_BO_TRANS_ORDER WHERE BO_TRANS_ORDER_ID = a.MERGE_ID)
        ,'yyyymmddhh24miss'))
        ELSE TO_NUMBER(TO_CHAR(a.CREATED_TIME, 'yyyymmddhh24miss'))
        END SORT2
        FROM(
        SELECT DISTINCT A.MERGE_ID
        FROM T_BO_TRANS_ORDER a
        INNER JOIN T_BO_INQUIRE b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID
        LEFT JOIN T_BO_ORDER_GROUP_REL tbogr ON a.BO_TRANS_ORDER_ID = tbogr.BO_TRANS_ORDER_ID AND tbogr.IS_DEL = 0
        where
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                a.is_del = 1
            </when>
            <otherwise>
                a.is_del = 0
            </otherwise>
        </choose>
        <if test="startDate != null and startDate !=''">
            AND a.TRANS_DATE &gt;= TO_DATE(#{startDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endDate != null and endDate !=''">
            AND a.TRANS_DATE &lt;= TO_DATE(#{endDate},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="startTime != null and startTime !=''">
            AND a.CREATED_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="endTime != null and endTime !=''">
            AND a.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="orgId != null and orgId !=''">
            AND a.ORG_ID = #{orgId}
        </if>
        <if test="inquireState != null and inquireState !='2'.toString() and inquireState != ''">
            AND b.INQUIRE_STATE = #{inquireState}
        </if>
        <if test="inquireState != null and inquireState =='2'.toString() and inquireState != ''">
            AND b.INQUIRE_STATE in (2,3)
        </if>
        <if test="minMileage != null and minMileage !=''">
            AND a.MILEAGE &gt;= #{minMileage}
        </if>
        <if test="maxMileage != null and maxMileage !=''">
            AND a.MILEAGE &lt;= #{maxMileage}
        </if>
        <if test="searchContent != null and searchContent !=''">
            AND a.CUSTOMER_ORDER_NO = #{searchContent}
        </if>
        <choose>
            <when test="upstreamState != null and upstreamState == '1'.toString()">
                AND a.UPSTREAM_STATE = 0
            </when>
            <when test="upstreamState != null and upstreamState == '2'.toString()">
                AND a.UPSTREAM_STATE = 1
            </when>
        </choose>
        <if test="endPlace != null and endPlace.size() > 0">
            AND(
            <foreach item="item" index="index" collection="endPlace" separator="or" open="(" close=")">
                <if test="item.provinceName != null and item.provinceName != ''">
                    a.END_PROVINCE_NAME = #{item.provinceName}
                </if>
                <if test="item.cityName != null and item.cityName != ''">
                    and a.END_CITY_NAME = #{item.cityName}
                </if>
                <if test="item.areaName != null and item.areaName != ''">
                    and a.END_COUNTY_NAME = #{item.areaName}
                </if>
            </foreach>
            )
        </if>
        <if test="customerOrderNoList != null and customerOrderNoList.size() > 0 ">
            AND a.CUSTOMER_ORDER_NO IN
            <foreach collection="customerOrderNoList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <!-- 接入数据权限 -->
        <if test="permissionNewList != null and permissionNewList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="permissionNewList" separator=" OR ">
                (
                <if test="item.scopeList != null and item.scopeList.size() > 0">
                    (
                    <foreach item="scopeItem" index="index" collection="item.scopeList" separator=" OR ">
                        (
                        tbogr.GROUP_ID = #{scopeItem.groupId}
                        <if test="scopeItem.userId!=null and scopeItem.userId!=''">
                            AND a.USER_ID = #{scopeItem.userId}
                        </if>
                        )
                    </foreach>
                    )
                </if>

                <if test="item.condition != null and item.condition !=''">
                    AND ${item.condition}
                </if>
                )
            </foreach>
            )
        </if>
        ) T LEFT JOIN T_BO_TRANS_ORDER a on T.MERGE_ID = a.MERGE_ID
        INNER JOIN T_BO_INQUIRE b ON
        a.BO_TRANS_ORDER_ID = b.BO_TRANS_ORDER_ID
        LEFT JOIN T_BO_ORDER_GROUP_REL e ON a.BO_TRANS_ORDER_ID = e.BO_TRANS_ORDER_ID AND e.IS_DEL = 0
        ORDER BY
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                a.LAST_MODIFIED_TIME DESC
            </when>
            <otherwise>
                sort ASC ,SORT2 desc,a.MERGE_ID desc
            </otherwise>
        </choose>
    </select>

    <update id="updateCustomerOrderNo">
        UPDATE
        T_BO_TRANS_ORDER
        SET
        CUSTOMER_ORDER_NO = #{customerOrderNo},
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        BO_TRANS_ORDER_ID IN
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <update id="updateUrgentMark">
        UPDATE
        T_BO_TRANS_ORDER
        SET
        URGENT_MARK = #{urgentMark},
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        BO_TRANS_ORDER_ID IN
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <sql id="orderColumn">
        O.BO_TRANS_ORDER_ID boTransOrderId,
        O.ORG_ID orgId,
        O.USER_ID userId,
        O.SYS_ROLE_TYPE sysRoleType,
        O.CUSTOMER_ORDER_NO customerOrderNo,
        O.CART_TYPE cartType,
        O.cart_Length cartLength,
        O.START_PROVINCE_NAME startProvinceName,
        O.START_CITY_NAME startCityName,
        O.START_COUNTY_NAME startCountyName,
        O.START_ADDRESS loadingAddressName,
        O.START_ADDRESS startAddress,
        O.START_LAT startLatitude,
        O.START_LNG startLongitude,
        O.START_LAT startLat,
        O.START_LNG startLng,
        O.END_PROVINCE_NAME endProvinceName,
        O.END_CITY_NAME endCityName,
        O.END_COUNTY_NAME endCountyName,
        O.END_ADDRESS unloadingAddressName,
        O.END_ADDRESS endAddress,
        O.END_LAT endLatitude,
        O.END_LNG endLongitude,
        O.END_LAT endLat,
        O.END_LNG endLng,
        O.SERVICE_REQUIRE serviceRequire,
        O.FROM_SOURCE fromSource,
        O.CONSIGNEE_NAME consigneeName,
        O.CONSIGNEE_CONTACT consigneeContact,
        TO_CHAR(O.GOODS_CASE_PACK,'FM999999990.0000') goodsCasePack,
        O.GOODS_CASE_PACK_UNIT goodsCasePackUnit,
        TO_CHAR(O.GOODS_WEIGHT,'FM999999990.0000') goodsWeight,
        O.GOODS_WEIGHT_UNIT goodsWeightUnit,
        TO_CHAR(O.GOODS_VOLUME,'FM999999990.0000') goodsVolume,
        O.GOODS_VOLUME_UNIT goodsVolumeUnit,
        TO_CHAR(O.GOODS_CASE_PACK_REMAIN,'FM999999990.0000') goodsCasePackRemain,
        TO_CHAR(O.GOODS_VOLUME_REMAIN,'FM999999990.0000') goodsVolumeRemain,
        TO_CHAR(O.GOODS_WEIGHT_REMAIN,'FM999999990.0000') goodsWeightRemain,
        O.GOODS_NAME goodsName,
        O.GOODS_SPEC goodsSpec,
        TO_CHAR(O.TRANS_DATE, 'YYYY-MM-DD HH24:MI:SS') transDate,
        TO_CHAR(O.PROMISE_ARRIVE_TIME, 'YYYY-MM-DD HH24:MI:SS') unLoadTime,
        TO_CHAR(O.PROMISE_ARRIVE_TIME, 'YYYY-MM-DD HH24:MI:SS') promiseArriveTime,
        TO_CHAR(O.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') orderCreateTime,
        NVL(O.SPILT_FLAG, 0) spiltFlag,
        O.MILEAGE mileage,
        O.MERGE_ID mergeId,
        O.MAX_ROUTE_TIME maxRouteTime,
        O.STORAGE_PLACE storagePlace,
        O.CUSTOMER_NAME customerName,
        O.MAX_RESERVE_MILEAGE maxReserveMileage,
        O.MAX_RESERVE_HOUR maxReserveHour,
        O.RECEIPT_NO receiptNo,
        TO_CHAR(O.START_TIME, 'YYYY-MM-DD HH24:MI:SS') startTime,
        TO_CHAR(O.END_TIME, 'YYYY-MM-DD HH24:MI:SS') endTime,
        O.WORKSHOP workshop,
        O.INDUSTRIAL_PARK industrialPark,
        O.STORAGE_LOCATION storageLocation,
        O.END_STORAGE_PLACE endStoragePlace,
        O.THIRD_ORDER_NO thirdOrderNo,
        O.REMARK remark,
        O.BUSINESS_CUSTOMER businessCustomer,
        O.REAL_BUSINESS_CUSTOMER realBusinessCustomer,
        O.DRIVER_NAME driverName,
        O.DRIVER_MOBILE_NO driverMobileNo,
        O.CART_BADGE_NO cartBadgeNo,
        O.ALL_FREIGHT allFreight,
        TO_CHAR(NVL(O.USER_FREIGHT, 0) , 'FM999999990.00') userFreight,
        TO_CHAR(O.BACK_FEE, 'FM999999990.00') backFee,
        O.LOADING_CAR_NUM loadingCarNum,
        O.FUEL_COST_FEE fuelCostFee,
        O.OVERHEAD overhead,
        O.CONSIGNEE_UNIT consigneeUnit,
        O.CONSIGNEE_MOBILE_NO consigneeMobileNo,
        O.CONSIGNMENT_NAME consignmentName,
        O.CONSIGNMENT_MOBILE_NO consignmentMobileNo,
        O.CONSIGNMENT_CONTACT consignmentContact,
        O.SETTLE_MODE settleMode,
        O.RAW_END_ADDRESS rawEndAddress,
        O.RAW_START_ADDRESS rawStartAddress,
        O.CARRIER carrier,
        O.RECEIVABLE receivable,
        O.DRIVER_GUARANTEE_AMOUNT driverGuaranteeAmount,
        O.SALESMAN_NAME salesmanName,
        TO_CHAR(O.ARRIVE_END_TIME, 'YYYY-MM-DD HH24:MI:SS') arriveEndTime,
        TO_CHAR(O.ACTUAL_LOADING_TIME, 'YYYY-MM-DD HH24:MI:SS') actualLoadingTime,
        ORDER_TYPE orderType,
        DT_INSIDE_ORDER_MARK dtInsideOrderMark,
        DT_ELECTRONIC_RECEIPT_MARK dtElectronicReceiptMark,
        TO_CHAR(TRUNC(O.DISPATCH_CAR_NUM),'FM999999990')  dispatchCarNum,
        TO_CHAR(TRUNC(O.REMAIN_CAR_NUM),'FM999999990') remainCarNum,
        ACCOUNT_GROUP_ID accountGroupId,
        XCY_USER_ID xcyUserId,
        BELONG_DISPATCHER_ID belongDispatcherId,
        UNIT_PRICE unitPrice,
        O.URGENT_MARK urgentMark,
        NVL(O.HIERARCHY_TYPE,0) hierarchyType,
        O.LINE_CODE lineCode
    </sql>

    <select id="getOrderListByRetrieval" resultType="com.wtyt.bo.bean.request.Res5329149OBean">
        SELECT
        <include refid="orderColumn"/>
        FROM
        T_BO_TRANS_ORDER O
        WHERE O.IS_DEL = 0
        AND ORG_ID = #{orgId}
        AND NOT EXISTS (
        SELECT 1 FROM T_BO_TRANS_ORDER_REL WHERE IS_DEL = 0 AND BO_TRANS_ORDER_ID = O.BO_TRANS_ORDER_ID
        )
        <choose>
            <when test="transTaskFlag != null and transTaskFlag == '1'.toString()">
                AND  o.DT_INSIDE_ORDER_MARK =1
            </when>
            <otherwise>
                  AND  (o.DT_INSIDE_ORDER_MARK = 0 or o.DT_INSIDE_ORDER_MARK is null)
             </otherwise>
        </choose>
            AND O.ORDER_TYPE =1
        <if test="retrieval != null and retrieval.length > 0">
            AND (
            INSTR(O.CUSTOMER_ORDER_NO, #{retrieval}) > 0
            OR INSTR(O.SERVICE_REQUIRE, #{retrieval}) > 0
            OR INSTR(O.CUSTOMER_NAME, #{retrieval}) > 0
            OR INSTR(O.CONSIGNEE_UNIT, #{retrieval}) > 0
            )
        </if>
        ORDER BY O.CREATED_TIME DESC
    </select>

    <select id="selectMergeIdByBoTaskId" resultType="java.lang.String">
        SELECT *
        FROM (SELECT MERGE_ID
              FROM T_BO_TRANS_ORDER_REL R
                       INNER JOIN T_BO_TRANS_ORDER O ON
                  O.BO_TRANS_ORDER_ID = R.BO_TRANS_ORDER_ID
              WHERE R.IS_DEL = 0
                AND O.IS_DEL = 0
                AND R.BO_TRANS_TASK_ID = #{boTransTaskId}
                AND O.MERGE_ID IS NOT NULL
              ORDER BY O.CREATED_TIME DESC)
        WHERE ROWNUM = 1
    </select>

    <select id="getBoTaskStat" resultType="java.util.Map">
        SELECT
        TO_CHAR(CREATED_TIME, 'YYYY-MM-DD') START_TIME,
        count(1)
        FROM
        T_BO_TRANS_ORDER_REL
        WHERE
        BO_TRANS_ORDER_ID = #{boTransOrderId}
        AND TO_CHAR(CREATED_TIME, 'YYYY-MM-DD') IN
        <foreach collection="dateList" item="item" separator="," close=")" open="(">
            #{item}
        </foreach>
        AND IS_DEL = 0
        ORDER BY
        CREATED_TIME DESC
    </select>

    <select id="getAllBoTaskStat" resultType="int">
        SELECT count(1)
        FROM T_BO_TRANS_ORDER_REL
        WHERE BO_TRANS_ORDER_ID = #{boTransOrderId}
          AND IS_DEL = 0
        ORDER BY CREATED_TIME DESC
    </select>

    <select id="selectThirdOrderNo" resultType="java.lang.String">
        SELECT
        1
        FROM DUAL
        WHERE EXISTS (
        SELECT
        1
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        IS_DEL = 0
        <if test="boTransOrderId != null">
            AND BO_TRANS_ORDER_ID != #{boTransOrderId}
        </if>
        AND ORG_ID =#{orgId}
        AND THIRD_ORDER_NO = #{thirdOrderNo})
    </select>

    <select id="selectExistsThirdOrderNo" resultType="java.lang.String">
        SELECT
        distinct THIRD_ORDER_NO
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        IS_DEL = 0
        AND ORG_ID =#{orgId}
        AND THIRD_ORDER_NO in
         <foreach collection="thirdOrderNoList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>


    <select id="getByThirdOrderNo" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        <include refid="orderColumn"/>
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.IS_DEL = 0
        AND O.THIRD_ORDER_NO = #{thirdOrderNo}
        AND O.ORG_ID = #{orgId}
    </select>

    <select id="selectByMergeId" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        <include refid="orderColumn"/>
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.IS_DEL = 0
        AND O.MERGE_ID = #{mergeId}
    </select>

    <select id="getSearchList" resultType="string">
        SELECT * FROM (
        SELECT * FROM (
        SELECT
        <if test="searchType != null and searchType == '1'.toString()">
            CUSTOMER_ORDER_NO,
            ROW_NUMBER() OVER(PARTITION BY CUSTOMER_ORDER_NO ORDER BY CREATED_TIME DESC ) RN
        </if>
        <if test="searchType != null and searchType == '2'.toString()">
            CUSTOMER_NAME,
            ROW_NUMBER() OVER(PARTITION BY CUSTOMER_NAME ORDER BY CREATED_TIME DESC ) RN
        </if>
        <if test="searchType != null and searchType == '3'.toString()">
            THIRD_ORDER_NO,
            ROW_NUMBER() OVER(PARTITION BY THIRD_ORDER_NO ORDER BY CREATED_TIME DESC ) RN
        </if>
        FROM
        T_BO_TRANS_ORDER
        WHERE
        <choose>
            <when test="tabState != null and tabState == '100'.toString()">
                IS_DEL = 1
            </when>
            <otherwise>
                IS_DEL = 0
            </otherwise>
        </choose>
        AND ORG_ID = #{orgId}
        <if test="tabState != null and tabState == '1'.toString()">
            AND USER_ID = #{userId}
        </if>
        <if test="searchType != null and searchType == '1'.toString()">
            AND CUSTOMER_ORDER_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
        </if>
        <if test="searchType != null and searchType == '2'.toString()">
            AND CUSTOMER_NAME like CONCAT(CONCAT('%',#{searchContent}),'%')
        </if>
        <if test="searchType != null and searchType == '3'.toString()">
            AND THIRD_ORDER_NO like CONCAT(CONCAT('%',#{searchContent}),'%')
        </if>
        ORDER BY CREATED_TIME DESC) T
        WHERE T.RN = 1
        )M
        WHERE ROWNUM &lt;= 10
    </select>

    <update id="restoreOrder" parameterType="list">
        UPDATE T_BO_TRANS_ORDER
        SET
        IS_DEL = 0,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE
        BO_TRANS_ORDER_ID IN
        <foreach collection="orderIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <!-- 通过主键查询订单信息   目前就计划列表展示 查询园区数据 ,存在查询已删除tab列表数据去掉isDel=0  -->
    <select id="getStaticDataList" resultType="java.util.HashMap">
        SELECT
        TO_CHAR(BO_TRANS_ORDER_ID) BO_TRANS_ORDER_ID,
        <foreach collection="staticColumnBeanList" item="staticColumnBean" separator=",">
            ${staticColumnBean.columnName} STATIC_${staticColumnBean.boTpDhStaticColRelId}
        </foreach>
        FROM T_BO_TRANS_ORDER
        WHERE BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getBusinessCustomerList" resultType="java.lang.String">
        SELECT DISTINCT BUSINESS_CUSTOMER
        FROM T_BO_TRANS_ORDER tbto
        WHERE is_del = 0
          AND ORG_ID = #{orgId}
          AND BUSINESS_CUSTOMER IS NOT NULL
    </select>
    <select id="getBoTransOrderIdsByPlanId" resultType="java.lang.String">
        SELECT BO_TRANS_ORDER_ID
        FROM T_BO_TRANS_ORDER
        WHERE IS_DEL = 0
        AND BO_TRANS_PLAN_ID = #{boTransPlanId}
        <if test="orgId != null and orgId != ''">
            AND ORG_ID = #{orgId}
        </if>
    </select>
    <select id="queryOrderValueMapById" resultType="java.util.Map">
        SELECT to_char(BO_TRANS_ORDER_ID) BO_TRANS_ORDER_ID ,
        <foreach collection="list" item="item" close="" open="" separator=",">
            ${item}
        </foreach>
        FROM T_BO_TRANS_ORDER
        WHERE IS_DEL = 0
        AND BO_TRANS_ORDER_ID = #{boTransOrderId}
    </select>

    <select id="queryOrderValueMapListByIds" resultType="java.util.Map">
        SELECT to_char(o.BO_TRANS_ORDER_ID) BO_TRANS_ORDER_ID
        <if test="list!=null and list.size() > 0">
            ,
            <foreach collection="list" item="item" close="" open="" separator=",">
                <choose>
                    <when test="item == 'LOADING_CAR_NUM_REMAIN'">
                    (NVL(o.LOADING_CAR_NUM, 0) - (
                        SELECT COUNT(1)
                        FROM T_BO_TRANS_ORDER_REL r
                        WHERE r.IS_DEL = 0 AND r.BO_TRANS_ORDER_ID = o.BO_TRANS_ORDER_ID
                      )) AS LOADING_CAR_NUM_REMAIN
                    </when>
                    <otherwise>
                        o.${item}
                    </otherwise>
                </choose>
            </foreach>
        </if>
        FROM T_BO_TRANS_ORDER o
        WHERE o.IS_DEL = 0
        AND o.BO_TRANS_ORDER_ID in
        <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getRecentlyCreatedOrderByOrgUserId" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT *
        FROM (SELECT T.GOODS_CASE_PACK_UNIT                              goodsCasePackUnit,
                     T.GOODS_WEIGHT_UNIT                                 goodsWeightUnit,
                     T.GOODS_VOLUME_UNIT                                 goodsVolumeUnit,
                     ROW_NUMBER() OVER (ORDER BY T.CREATED_TIME DESC) AS RN
              FROM T_BO_TRANS_ORDER T
              WHERE T.IS_DEL = 0
                AND T.USER_ID = #{userId}
                AND T.ORG_ID = #{orgId})
        WHERE RN = 1
    </select>

    <update id="updateByOrderIdList">
        UPDATE T_BO_TRANS_ORDER SET
        <if test="mergeId != null and mergeId != ''">
            MERGE_ID = #{mergeId},
        </if>
        <if test="createTaskFlag == '1'.toString()">
            GOODS_CASE_PACK_REMAIN = 0,
            GOODS_WEIGHT_REMAIN = 0,
            GOODS_VOLUME_REMAIN = 0,
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateReceivable">
        UPDATE T_BO_TRANS_ORDER
        SET RECEIVABLE         = #{receivable},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0
          AND BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>
    <update id="dynamicUpdateOrderById">
        UPDATE T_BO_TRANS_ORDER SET
        <foreach collection="dataMap.keys" item="k" separator=",">
            <if test="null != dataMap[k]">
                ${k} = #{dataMap[${k}]}
            </if>
        </foreach>
        ,LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0 AND BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>

    <select id="countOrderByOrgId" resultType="com.wtyt.bean.dto.GroupCountDTO">
        SELECT ORG_ID groupField, count(*) count
        FROM T_BO_TRANS_ORDER
        WHERE IS_DEL = 0
        <if test="list != null and list.size() > 0">
            AND ORG_ID IN
            <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY ORG_ID
    </select>
    <update id="clearCarrier">
        UPDATE
            T_BO_TRANS_ORDER
        SET
            CARRIER = NULL,
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TRANS_ORDER_ID IN
            <foreach collection="boTransOrderIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND CARRIER IS NOT NULL
    </update>

    <update id="updateActualLoadingTime">
        UPDATE T_BO_TRANS_ORDER SET
        ACTUAL_LOADING_TIME =#{actualLoadingTime},
        LAST_MODIFIED_TIME = SYSDATE
        WHERE IS_DEL = 0 AND BO_TRANS_ORDER_ID IN
        <foreach collection="boTransOrderIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="getLoadingTime" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT BO_TRANS_ORDER_ID boTransOrderId,
               to_char(ACTUAL_LOADING_TIME,'YYYY-MM-DD HH24:MI:SS') actualLoadingTime
        FROM T_BO_TRANS_ORDER
        WHERE IS_DEL = 0
        <if test="list != null and list.size() > 0">
            AND BO_TRANS_ORDER_ID IN
            <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="clearUserFreightOrUnitPrice">
        UPDATE
        T_BO_TRANS_ORDER
        SET
        USER_FREIGHT = #{userFreight,javaType=String},
        UNIT_PRICE = #{unitPrice,javaType=String}
        WHERE
        IS_DEL = 0
        AND BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>
    <select id="selectTransOrderByMergeIdList" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        <include refid="orderColumn"/>
        FROM T_BO_TRANS_ORDER O
        WHERE IS_DEL = 0 AND ORDER_TYPE = 1
        <if test="mergeIds != null and mergeIds.size() > 0">
            AND MERGE_ID IN
            <foreach collection="mergeIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY CREATED_TIME ASC, MERGE_ID
    </select>

    <select id="selectByMergeIdList" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        <include refid="orderColumn"/>
        FROM T_BO_TRANS_ORDER O
        WHERE IS_DEL = 0
        <if test="mergeIds != null and mergeIds.size() > 0">
            AND MERGE_ID IN
            <foreach collection="mergeIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getOrderFieldDataList" resultType="java.util.Map">
        SELECT
            TO_CHAR(BO_TRANS_ORDER_ID) BO_TRANS_ORDER_ID
            <if test="fieldNames != null and fieldNames != ''">
                , ${fieldNames}
            </if>
            FROM T_BO_TRANS_ORDER
        WHERE BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getBoTransOrderListByPlanId" resultType="com.wtyt.dao.bean.syf.BoTransOrderBean">
        SELECT
        <include refid="orderColumn"/>
        FROM
        T_BO_TRANS_ORDER O
        WHERE
        O.IS_DEL = 0
        AND O.BO_TRANS_PLAN_ID = #{boTransPlanId}
    </select>
    <select id="checkMergeIds" resultType="java.lang.String">
        SELECT MERGE_ID FROM t_bo_trans_order o
        WHERE o.IS_DEL =0
        AND   MERGE_ID in
        <foreach collection="mergeIds" open="(" close=")" separator="," item="mergeId">
            #{mergeId}
        </foreach>
        GROUP BY MERGE_ID HAVING count(1)>1

    </select>

</mapper>
