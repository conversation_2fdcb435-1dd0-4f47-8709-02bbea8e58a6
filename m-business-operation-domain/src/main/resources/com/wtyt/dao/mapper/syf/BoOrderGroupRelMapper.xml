<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoOrderGroupRelMapper">

    <insert id="insertGroupRel">
        INSERT INTO
        T_BO_ORDER_GROUP_REL (BO_ORDER_GROUP_REL_ID,
        BO_TRANS_ORDER_ID,
        GROUP_ID,
        GROUP_TYPE,
        IS_DEL,
        CREATED_TIME,
        LAST_MODIFIED_TIME,
        NOTE)
        SELECT
        A.*
        FROM (
        <foreach item="item" index="index" collection="orderGroupRelList" separator="UNION ALL">
            SELECT #{item.boOrderGroupRelId} boOrderGroupRelId,
            #{item.boTransOrderId} boTransOrderId,
            #{item.groupId} groupId,
            #{item.groupType} groupType,
            0 isDel,
            SYSDATE createdTime,
            SY<PERSON><PERSON><PERSON> lastModifiedTime,
            NULL note
            FROM DUAL
        </foreach>
        ) A
    </insert>

    <update id="updateBoOrderGroupRel" parameterType="BoOrderGroupRelBean">
        UPDATE T_BO_ORDER_GROUP_REL SET
        <if test="groupId != null and groupId != ''">
            GROUP_ID= #{groupId},
        </if>
        <if test="groupType != null and groupType != ''">
            GROUP_TYPE= #{groupType},
        </if>
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_TRANS_ORDER_ID = #{boTransOrderId}
    </update>

    <update id="delOrderGroupRelByPk" parameterType="BoOrderGroupRelBean">
        UPDATE T_BO_ORDER_GROUP_REL SET
        IS_DEL = 1,
        LAST_MODIFIED_TIME = SYSDATE
        WHERE BO_ORDER_GROUP_REL_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </update>
    <update id="clearOrderSupplierGroupRel">
        UPDATE
            T_BO_ORDER_GROUP_REL
        SET
            IS_DEL = 1,
            note = #{note},
            LAST_MODIFIED_TIME = SYSDATE
        WHERE
            BO_TRANS_ORDER_ID IN
            <foreach collection="boTransOrderIdList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            AND GROUP_TYPE IN (4, 5) and is_del =0
    </update>

    <select id="selectByOrderIdList" resultType="com.wtyt.dao.bean.syf.BoOrderGroupRelBean">
    SELECT
        BO_TRANS_ORDER_ID boTransOrderId,
        GROUP_ID groupId,
        GROUP_TYPE groupType,
        BO_ORDER_GROUP_REL_ID boOrderGroupRelId
    FROM
        T_BO_ORDER_GROUP_REL
    WHERE
        IS_DEL = 0
        AND BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByOrderIdListGroupByGidAndType" resultType="com.wtyt.dao.bean.syf.BoOrderGroupRelBean">
        SELECT
        GROUP_ID groupId,
        GROUP_TYPE groupType
        FROM
        T_BO_ORDER_GROUP_REL
        WHERE
        IS_DEL = 0
        AND BO_TRANS_ORDER_ID IN
        <foreach collection="orderIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        GROUP BY GROUP_ID,GROUP_TYPE
    </select>
</mapper>
