<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransFieldExtMapper">
	<insert id="insert">
		INSERT
		INTO
		T_BO_TRANS_ORDER_FIELD_EXT (BO_TRANS_ORDER_FIELD_EXT_ID,
		BO_TRANS_ORDER_ID,
		ORG_ID,
		USER_ID,
		FIELD_NAME,
		FIELD_VALUE,
		SORT,
		HEADER_TYPE,
		IS_DEL,
		CREATED_TIME,
		LAST_MODIFIED_TIME,
		NOTE)
		SELECT
		A.*
		FROM (
		<foreach item="item" index="index" collection="dataList" separator="UNION ALL">
			SELECT #{item.boTransOrderFieldExtId} boTransOrderFieldExtId,
			#{item.boTransOrderId} boTransOrderId,
			#{item.orgId} orgId,
			#{item.userId} userId,
			#{item.fieldName} fieldName,
			#{item.fieldValue} fieldValue,
			#{item.sort} sort,
			#{item.headerType} headerType,
			0 isDel,
			SYSDATE createdTime,
			SYSDATE lastModifiedTime,
			NULL note
			FROM DUAL
		</foreach>
		) A
	</insert>
	<delete id="deleteByPkIds">
		UPDATE T_BO_TRANS_ORDER_FIELD_EXT
		SET
		IS_DEL = 1,
		LAST_MODIFIED_TIME = SYSDATE
		WHERE
		IS_DEL = 0
		AND BO_TRANS_ORDER_FIELD_EXT_ID IN
		<foreach collection="oldExtIdList" index="index" item="item" separator="," open="(" close=")">
			#{item}
		</foreach>
	</delete>

	<select id="selectByOrderId" resultType="com.wtyt.dao.bean.syf.BoTransOrderFieldExtBean">
	SELECT
		BO_TRANS_ORDER_FIELD_EXT_ID boTransOrderFieldExtId,
		BO_TRANS_ORDER_ID boTransOrderId,
		FIELD_NAME fieldName,
		FIELD_VALUE fieldValue,
		SORT sort
	FROM
		T_BO_TRANS_ORDER_FIELD_EXT
	WHERE
		IS_DEL = 0
		AND BO_TRANS_ORDER_ID = #{boTransOrderId}
	ORDER BY
		FIELD_NAME ASC
    </select>

	<select id="selectByOrderIdForExport" resultType="com.wtyt.dao.bean.syf.BoTransOrderFieldExtBean">
		SELECT
		BO_TRANS_ORDER_FIELD_EXT_ID boTransOrderFieldExtId,
		BO_TRANS_ORDER_ID boTransOrderId,
		FIELD_NAME fieldName,
		FIELD_VALUE fieldValue,
		SORT sort
		FROM
		T_BO_TRANS_ORDER_FIELD_EXT
		WHERE
		IS_DEL = 0
		AND HEADER_TYPE in (3,4)
		<if test="fieldNames != null and fieldNames.size() > 0">
			AND FIELD_NAME IN
			<foreach collection="fieldNames" index="index" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
		</if>
		AND BO_TRANS_ORDER_ID IN
		<foreach collection="boTransOrderIds" index="index" item="item" separator="," open="(" close=")">
			#{item}
		</foreach>
		ORDER BY
		FIELD_NAME ASC
    </select>

	<select id="selectByTaxWaybillNo" resultType="com.wtyt.dao.bean.syf.BoTransOrderFieldExtBean">
	SELECT
		tbe.BO_TRANS_ORDER_FIELD_EXT_ID boTransOrderFieldExtId,
		tbe.BO_TRANS_ORDER_ID boTransOrderId,
		tbe.FIELD_NAME fieldName,
		tbe.FIELD_VALUE fieldValue,
		tbe.SORT sort
	FROM
		T_BO_TRANS_ORDER_FIELD_EXT tbe
		left join T_BO_TRANS_ORDER bo
		on bo.BO_TRANS_ORDER_ID = tbe.BO_TRANS_ORDER_ID
	WHERE
		tbe.IS_DEL = 0
		AND bo.is_del = 0
		AND bo.TAX_WAYBILL_NO = #{taxWaybillNo}
		AND tbe.HEADER_TYPE IN (3,4)
	ORDER BY
		tbe.FIELD_NAME ASC
    </select>

	<select id="selectByOrderIds" resultType="com.wtyt.dao.bean.syf.BoTransOrderFieldExtBean">
		SELECT
		tbe.BO_TRANS_ORDER_FIELD_EXT_ID boTransOrderFieldExtId,
		tbe.BO_TRANS_ORDER_ID boTransOrderId,
		tbe.FIELD_NAME fieldName,
		tbe.FIELD_VALUE fieldValue,
		tbe.SORT sort
		FROM
		T_BO_TRANS_ORDER_FIELD_EXT tbe
		WHERE
		tbe.IS_DEL = 0
		AND tbe.HEADER_TYPE IN (3,4)
		AND tbe.BO_TRANS_ORDER_ID IN
		<foreach collection="orderIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		ORDER BY
		tbe.FIELD_NAME ASC
	</select>

</mapper>