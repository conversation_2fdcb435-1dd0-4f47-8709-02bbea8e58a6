<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoDioThresholdMapper">

    <select id="queryThresholdList" resultType="java.lang.String">
        SELECT
            DISTINCT THRESHOLD
        FROM
            T_BO_DIO_THRESHOLD
        WHERE
            IS_DEL = 0
            AND BO_DIO_CONFIG_ID = #{boDioConfigId}
            AND DIO_NO = #{dioNo}
            <choose>
                <when test="wbItems != null and wbItems.size() > 0">
                    AND WB_ITEM IN
                    <foreach collection="wbItems" item="item" close=")" open="(" separator=",">
                          #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND WB_ITEM IS NULL
                </otherwise>
            </choose>
            <choose>
                <when test="wbItemSets != null and wbItemSets.size() > 0">
                    AND WB_ITEM_SET IN
                    <foreach collection="wbItemSets" item="item" close=")" open="(" separator=",">
                          #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND WB_ITEM_SET IS NULL
                </otherwise>
            </choose>
    </select>
</mapper>
