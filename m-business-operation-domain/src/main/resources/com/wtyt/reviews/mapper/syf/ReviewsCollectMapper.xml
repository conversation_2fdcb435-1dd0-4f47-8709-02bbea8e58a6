<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.reviews.mapper.syf.ReviewsCollectMapper">

    <select id="getFinishedReviewsCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM T_PM_REVIEWS T
        WHERE T.IS_DEL = 0
            AND T.REVIEWER_ID = #{reviewerId}
            AND T.REVIEWER_IDENTITY = #{reviewerIdentity}
            AND T.CREATED_TIME >= TRUNC(SYSDATE - 180)
    </select>

    <select id="getHistoryFinishedReviewsCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM T_PM_REVIEWS T
        WHERE T.REVIEWER_ID = #{reviewerId}
            AND T.REVIEWER_IDENTITY = #{reviewerIdentity}
    </select>

    <select id="getUnfinishedReviewsCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM T_PM_REVIEWS_SUBJECT T1
        LEFT JOIN T_PM_REVIEWS_SCOPE T2
            ON T2.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        LEFT JOIN T_PM_REVIEWS T3
            ON T3.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        WHERE T1.IS_DEL = 0
            AND T2.IS_DEL = 0
            AND T2.SCOPE_ID = #{scopeId}
            AND T2.SCOPE = #{scope}
            AND T3.PM_REVIEWS_ID IS NULL
            AND T1.CREATED_TIME >= TRUNC(SYSDATE - 180)
    </select>

    <select id="getFinishedReviewsList" resultType="com.wtyt.reviews.bean.ReviewsCollectBean">
        SELECT
            T1.PM_REVIEWS_ID pmReviewsId,
            T1.PM_REVIEWS_SUBJECT_ID pmReviewsSubjectId,
            T1.PARENT_ID parentId,
            T1.REVIEWER_IDENTITY reviewerIdentity,
            T1.REVIEWER_ID reviewerId,
            T1.REVIEWER_NAME reviewerName,
            T1.REVIEWER_MOBILE reviewerMobile,
            T1.SCORE score,
            T1.CONTENT content,
            T1.IS_ANONYMITY isAnonymity,
            TO_CHAR(T1.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') reviewsTime,
            T2.ORG_ID orgId,
            T2.SUBJECT_TYPE subjectType,
            T2.SUBJECT_ID subjectId,
            T2.REVIEWS_TARGET reviewsTarget,
            T2.REVIEWS_TARGET_ID reviewsTargetId,
            T2.REVIEWS_TARGET_NAME reviewsTargetName,
            T2.REVIEWS_TARGET_MOBILE reviewsTargetMobile
        FROM T_PM_REVIEWS T1
        LEFT JOIN T_PM_REVIEWS_SUBJECT T2
            ON T2.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        WHERE T1.IS_DEL = 0
            AND T1.REVIEWER_ID = #{reviewerId}
            AND T1.REVIEWER_IDENTITY = #{reviewerIdentity}
            AND T1.CREATED_TIME >= TRUNC(SYSDATE - 180)
        ORDER BY T1.CREATED_TIME DESC
    </select>

    <select id="getUnfinishedReviewsList" resultType="com.wtyt.reviews.bean.ReviewsCollectBean">
        SELECT
            T1.ORG_ID orgId,
            T1.SUBJECT_TYPE subjectType,
            T1.SUBJECT_ID subjectId,
            T1.REVIEWS_TARGET reviewsTarget,
            T1.REVIEWS_TARGET_ID reviewsTargetId,
            T1.REVIEWS_TARGET_NAME reviewsTargetName,
            T1.REVIEWS_TARGET_MOBILE reviewsTargetMobile
        FROM T_PM_REVIEWS_SUBJECT T1
        LEFT JOIN T_PM_REVIEWS_SCOPE T2
            ON T2.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        LEFT JOIN T_PM_REVIEWS T3
            ON T3.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        WHERE T1.IS_DEL = 0
            AND T2.SCOPE_ID = #{scopeId}
            AND T2.SCOPE = #{scope}
            AND T3.PM_REVIEWS_ID IS NULL
            AND T1.CREATED_TIME >= TRUNC(SYSDATE - 180)
        ORDER BY T1.CREATED_TIME DESC
    </select>

    <select id="getReviewsSummaryData1" resultType="com.wtyt.reviews.bean.ReviewsCollectBean">
        SELECT
            T1.REVIEWS_TARGET_ID reviewsTargetId,
            COUNT(*) totalCount,
            COUNT(
                CASE
                    WHEN T3.PM_REVIEWS_ID IS NOT NULL THEN 1
                    ELSE NULL
                END
            ) reviewsCount,
            SUM(NVL(T3.SCORE, 0)) totalScore
        FROM T_PM_REVIEWS_SUBJECT T1
        LEFT JOIN T_PM_REVIEWS_SCOPE T2
            ON T2.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        LEFT JOIN T_PM_REVIEWS T3
            ON T3.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        WHERE T1.IS_DEL = 0
            <if test="startDate != null and startDate.length > 0">
                AND T1.CREATED_TIME >= TO_DATE(#{startDate}, 'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="endDate != null and endDate.length > 0">
                AND T1.CREATED_TIME <![CDATA[<]]> TO_DATE(#{endDate}, 'yyyy-mm-dd hh24:mi:ss') + 1
            </if>
            AND T1.REVIEWS_TARGET_ID IN
            <foreach collection="reviewsTargetIdList" item="reviewsTargetId" open="(" separator="," close=")">
                #{reviewsTargetId}
            </foreach>
            AND T1.REVIEWS_TARGET = #{reviewsTarget}
            AND T1.ORG_ID = #{orgId}
        GROUP BY T1.REVIEWS_TARGET_ID
    </select>

    <select id="getReviewsSummaryData2" resultType="com.wtyt.reviews.bean.ReviewsCollectBean">
        SELECT
            T1.SCORE score,
            COUNT(*) totalCount
        FROM T_PM_REVIEWS T1
        LEFT JOIN T_PM_REVIEWS_SUBJECT T2
            ON T2.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        WHERE T2.IS_DEL = 0
            <if test="searchType != null and searchType == '1'.toString()">
                AND T1.CONTENT IS NOT NULL
            </if>
            AND T2.REVIEWS_TARGET_ID = #{reviewsTargetId}
            AND T2.REVIEWS_TARGET = #{reviewsTarget}
            AND T2.ORG_ID = #{orgId}
        GROUP BY T1.SCORE
    </select>

    <select id="getReviewsListByReviewsTarget" resultType="com.wtyt.reviews.bean.ReviewsCollectBean">
        SELECT
            T1.PM_REVIEWS_ID pmReviewsId,
            T1.PM_REVIEWS_SUBJECT_ID pmReviewsSubjectId,
            T1.PARENT_ID parentId,
            T1.REVIEWER_IDENTITY reviewerIdentity,
            T1.REVIEWER_ID reviewerId,
            T1.REVIEWER_NAME reviewerName,
            T1.REVIEWER_MOBILE reviewerMobile,
            T1.SCORE score,
            T1.CONTENT content,
            T1.IS_ANONYMITY isAnonymity,
            TO_CHAR(T1.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') reviewsTime,
            T2.SUBJECT_TYPE subjectType,
            T2.SUBJECT_ID subjectId,
            TO_CHAR(T2.CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') createdTime
        FROM T_PM_REVIEWS T1
        LEFT JOIN T_PM_REVIEWS_SUBJECT T2
            ON T2.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        WHERE T2.IS_DEL = 0
            <if test="score != null and score.length > 0">
                AND T1.SCORE = #{score}
            </if>
            <if test="startDate != null and startDate.length > 0">
                AND T2.CREATED_TIME <![CDATA[>=]]> TO_DATE(#{startDate}, 'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="endDate != null and endDate.length > 0">
                AND T2.CREATED_TIME <![CDATA[<=]]> TO_DATE(#{endDate}, 'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="searchType != null and searchType == '1'.toString()">
                AND T1.CONTENT IS NOT NULL
            </if>
            AND T2.REVIEWS_TARGET_ID = #{reviewsTargetId}
            AND T2.REVIEWS_TARGET = #{reviewsTarget}
            AND T2.ORG_ID = #{orgId}
        ORDER BY T1.CREATED_TIME DESC
    </select>

    <select id="getReviewsLimitByReviewsTarget" resultType="com.wtyt.reviews.bean.ReviewsCollectBean">
        SELECT
            TO_CHAR(MIN(T2.CREATED_TIME), 'yyyy-mm-dd') minTime,
            TO_CHAR(MAX(T2.CREATED_TIME), 'yyyy-mm-dd') maxTime
        FROM T_PM_REVIEWS T1
        LEFT JOIN T_PM_REVIEWS_SUBJECT T2
            ON T2.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        WHERE T2.IS_DEL = 0
            <if test="score != null and score.length > 0">
                AND T1.SCORE = #{score}
            </if>
            <if test="searchType != null and searchType == '1'.toString()">
                AND T1.CONTENT IS NOT NULL
            </if>
            AND T2.REVIEWS_TARGET_ID = #{reviewsTargetId}
            AND T2.REVIEWS_TARGET = #{reviewsTarget}
            AND T2.ORG_ID = #{orgId}
    </select>

    <select id="getReviewsScoreListBySubject" resultType="com.wtyt.reviews.bean.ReviewsCollectBean">
        SELECT
            T1.SUBJECT_TYPE subjectType,
            T1.SUBJECT_ID subjectId,
            T2.SCORE score,
            T2.IS_DEL isDel
        FROM T_PM_REVIEWS_SUBJECT T1
        LEFT JOIN T_PM_REVIEWS T2
            ON T2.PM_REVIEWS_SUBJECT_ID = T1.PM_REVIEWS_SUBJECT_ID
        WHERE T1.IS_DEL = 0
            AND T1.SUBJECT_ID IN
            <foreach collection="subjectIdList" item="subjectId" open="(" separator="," close=")">
                #{subjectId}
            </foreach>
            AND T1.SUBJECT_TYPE = #{subjectType}
    </select>

</mapper>
