<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.dao.mapper.syf.BoTaskTransferRecordMapper">

    <sql id="tableName">
        T_BO_TASK_TRANSFER_RECORD
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/>
        (
            BO_TASK_TRANSFER_RECORD_ID,
            BO_TRANS_TASK_ID,
            TAX_WAYBILL_ID,
            TAX_WAYBILL_NO,
            BEFORE_ORG_ID,
            AFTER_ORG_ID,
            BEFORE_CREATE_USER_ID,
            AFTER_CREATE_USER_ID,
            BEFORE_BELONG_ACT_SYS,
            AFTER_BELONG_ACT_SYS,
            OPERATOR,
            IS_DEL,
            CREATED_TIME,
            LAST_MODIFIED_TIME,
            NOTE
        )
        <foreach collection="recordList" index="index" item="item" separator="UNION ALL">
            SELECT
            ${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()} BO_TASK_TRANSFER_RECORD_ID,
            #{item.boTransTaskId} BO_TRANS_TASK_ID,
            #{item.taxWaybillId} TAX_WAYBILL_ID,
            #{item.taxWaybillNo} TAX_WAYBILL_NO,
            #{item.beforeOrgId} BEFORE_ORG_ID,
            #{item.afterOrgId} AFTER_ORG_ID,
            #{item.beforeCreateUserId} BEFORE_CREATE_USER_ID,
            #{item.afterCreateUserId} AFTER_CREATE_USER_ID,
            #{item.beforeBelongActSys} BEFORE_BELONG_ACT_SYS,
            #{item.afterBelongActSys} AFTER_BELONG_ACT_SYS,
            #{item.operator} OPERATOR,
            0 IS_DEL,
            SYSDATE CREATED_TIME,
            SYSDATE LAST_MODIFIED_TIME,
            #{item.note} NOTE
            FROM DUAL
        </foreach>
    </insert>
</mapper>