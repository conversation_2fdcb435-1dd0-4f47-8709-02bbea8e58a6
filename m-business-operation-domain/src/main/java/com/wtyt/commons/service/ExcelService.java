package com.wtyt.commons.service;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.wtyt.common.toolkits.AlarmToolkit;
import com.wtyt.common.toolkits.AssertToolkit;
import com.wtyt.common.toolkits.DigestToolkit;
import com.wtyt.common.util.base.BaseException;
import com.wtyt.commons.apollo.ApolloConfigCenter;
import com.wtyt.commons.consts.ApolloConstant;
import com.wtyt.lg.commons.exception.BaseCustomException;
import com.wtyt.lg.commons.exception.BaseTipException;
import com.wtyt.logoss.sdk.LogossClient;
import com.wtyt.logoss.sdk.bean.OssUpodBean;
import com.wtyt.logoss.sdk.bean.OssUrlsBean;
import com.wtyt.logoss.sdk.commons.consts.EncodeTypeEnum;
import com.wtyt.logoss.sdk.commons.consts.RemoveStrategyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Qingcheng
 * @date 2024/8/9
 * @desc excel服务
 */
@Slf4j
@Service
public class ExcelService {

    private static final String FILE_NAME_PATTERN = "%s.xlsx";

    // 默认超时时间是10秒，
    public static final int DEFAULT_TIMEOUT_MS = 10000;

    @Resource
    private ApolloConfigCenter apolloConfigCenter;


    /**
     * 导出数据
     * @param excelWriter excelWriter
     * @param header 表头
     * @param data 数据
     * @param fileName 文件名 
     * @return String 
     * <AUTHOR> Qingcheng 
     * @since 2024/8/9
     */
    public String exportData(ExcelWriter excelWriter, List<String> header, List<Map<String, String>> data, String fileName) throws BaseException, BaseTipException, BaseCustomException {
        try {
            excelWriter.writeHeadRow(header);
            excelWriter.write(data, false);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            excelWriter.flush(out);
            // 将文件上传到oss服务器
            LogossClient client = new LogossClient(DEFAULT_TIMEOUT_MS, DEFAULT_TIMEOUT_MS);
            OssUrlsBean urlsBean = client.upload(createOssUplodBean(fileName, out));
            return client.getAuthTokenUrl(urlsBean.getUrl()).getUrl();
        } catch (Exception e) {
            log.error("导出" + fileName +"失败", e);
            AlarmToolkit.logAlarm("导出" + fileName +"失败", e, StringUtils.EMPTY);
            return null;
        } finally {
            excelWriter.close();
        }
    }
    public String exportData(ExcelWriter excelWriter,  String fileName) throws BaseException, BaseTipException, BaseCustomException {
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            excelWriter.flush(out);
            // 将文件上传到oss服务器
            LogossClient client = new LogossClient(DEFAULT_TIMEOUT_MS, DEFAULT_TIMEOUT_MS);
            OssUrlsBean urlsBean = client.upload(createOssUplodBean(fileName, out));
            return client.getAuthTokenUrl(urlsBean.getUrl()).getUrl();
        } catch (Exception e) {
            log.error("导出" + fileName +"失败", e);
            AlarmToolkit.logAlarm("导出" + fileName +"失败", e, StringUtils.EMPTY);
            return null;
        } finally {
            excelWriter.close();
        }
    }

    /**
     * 创建ExcelWriter
     * @param sheetName 工作表名称
     * @return ExcelWriter 
     * <AUTHOR> Qingcheng 
     * @since 2024/8/9
     */
    public ExcelWriter createDefaultExcelWrite(String sheetName) {
        //开始组装excel
        // 通过工具类创建writer，创建xlsx格式的
        ExcelWriter writer = ExcelUtil.getWriterWithSheet(sheetName);
        // 默认的，未添加alias的属性也会写出，如果想只写出加了别名的字段，可以调用此方法排除之
        writer.setOnlyAlias(true);
        //设置单元格内容自动换行
        writer.getCellStyle().setWrapText(true);
        //设置所有列为自动宽度
        writer.getSheet().setDefaultColumnWidth(20);
        return writer;
    }

    /**
     * 创建oss上传对象
     * @param fileName 文件名称
     * @param out 输出流
     * @return OssUpodBean 
     * <AUTHOR> Qingcheng 
     * @since 2024/8/9
     */
    public OssUpodBean createOssUplodBean(String fileName, ByteArrayOutputStream out) throws BaseException {
        AssertToolkit.notNull(out, "文件内容不能为空");
        OssUpodBean uploadBean = new OssUpodBean();
        uploadBean.setDataField(out.toByteArray());
        uploadBean.setFilename(String.format(FILE_NAME_PATTERN, fileName));
        uploadBean.setDownloadFilename(uploadBean.getFilename());
        uploadBean.setTypeCode(apolloConfigCenter.boCommons.getProperty(ApolloConstant.PropertyKey.OSS_TYPE_CODE));
        uploadBean.setEncodeType(EncodeTypeEnum.BTYE_ARRAY);
        uploadBean.setRemoveStrategy(RemoveStrategyEnum.DEL_OBJECT_30_MINUTE);
        uploadBean.setCheckValue(DigestToolkit.md5(uploadBean.getFilename()+apolloConfigCenter.boCommons.getProperty(ApolloConstant.PropertyKey.OSS_ACCESS_KEY)));
        return uploadBean;
    }
}
