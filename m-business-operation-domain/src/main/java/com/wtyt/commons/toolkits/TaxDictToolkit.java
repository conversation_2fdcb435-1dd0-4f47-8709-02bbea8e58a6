package com.wtyt.commons.toolkits;

import com.wtyt.common.rpc.bean.Rpc4010IBean;
import com.wtyt.common.rpc.bean.Rpc4010OBean;
import com.wtyt.common.rpc.service.RpcMCacheService;
import com.wtyt.common.toolkits.SpringContextToolkit;
import com.wtyt.dao.bean.syf.TaxDictBean;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 数据字典翻译工具类
 * 
 * <AUTHOR>
 *
 */
@Component
public class TaxDictToolkit {

    public static final String GOODS_UNIT = "GOODS_UNIT";// 货物数量类型
    private static Map<String, List<TaxDictBean>> cache = new ConcurrentHashMap<>();
    private static long timestamp = 0L;
    private static RpcMCacheService rpcMCacheService;

    private TaxDictToolkit() {
        super();
    }

    /**
     * 从数据库中，通过dictType，dictKey直接获取字典数据
     * 
     * @param dictType
     * @param dictKey
     * @return
     */
    public TaxDictBean findDictByTypeAndKey(String dictType, String dictKey) {
        if (StringUtils.isBlank(dictType) || StringUtils.isBlank(dictKey)) {
            return null;
        }
        isOverdue();
        List<TaxDictBean> taxDictList = cache.get(dictType);
        if (CollectionUtils.isNotEmpty(taxDictList)) {
            for (TaxDictBean taxDictBean : taxDictList) {
                if (dictKey.equals(taxDictBean.getDictKey())) {
                    return taxDictBean;
                }
            }
        }
        return null;
    }

    public Map<String,String> getDictByDictType(String dictType){
        if (StringUtils.isBlank(dictType)) {
            return null;
        }
        isOverdue();
        List<TaxDictBean> taxDictList = cache.get(dictType);
        Map<String,String> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(taxDictList)) {
            map = taxDictList.stream().collect(Collectors.toMap(TaxDictBean::getDictKey, TaxDictBean::getDictValue, (v1, v2) -> v1));
        }
        return map;
    }

    /**
     * 获取字典map（反向的），key为dictValue，value为dictKey
     * @param dictType
     * @return
     */
    public Map<String,String> getReverseDictByDictType(String dictType){
        if (StringUtils.isBlank(dictType)) {
            return null;
        }
        isOverdue();
        List<TaxDictBean> taxDictList = cache.get(dictType);
        Map<String,String> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(taxDictList)) {
            map = taxDictList.stream().collect(Collectors.toMap(TaxDictBean::getDictValue, TaxDictBean::getDictKey, (v1, v2) -> v1));
        }
        return map;
    }

    /**
     *
     * 从远处服务获取数据，如果上次数据获取的时间距离现在超过半小时，则重新获取
     */
    private static void isOverdue() {
        if ((System.currentTimeMillis() - timestamp) > (30 * 60 * 1000)) {
            synchronized (TaxDictToolkit.class) {
                if ((System.currentTimeMillis() - timestamp) > (30 * 60 * 1000)) {
                    rpcMCacheService = SpringContextToolkit.getBean(RpcMCacheService.class);
                    Rpc4010IBean rpc4010IBean = new Rpc4010IBean();
                    List<Rpc4010IBean> dictTypes = new ArrayList<Rpc4010IBean>();
                    Rpc4010IBean dictTypeIBean = new Rpc4010IBean();
                    dictTypeIBean.setDictType(GOODS_UNIT);
                    dictTypes.add(dictTypeIBean);
                    rpc4010IBean.setDictTypes(dictTypes);
                    Rpc4010OBean rpc4010OBean = null;
                    try {
                        rpc4010OBean = rpcMCacheService.rpc4010(rpc4010IBean);
                    } catch (Exception e) {
                        throw new UnifiedBusinessException(e.getMessage());
                    }
                    if (rpc4010OBean != null && CollectionUtils.isNotEmpty(rpc4010OBean.getResult())) {
                        List<Rpc4010OBean> result = rpc4010OBean.getResult();
                        for (Rpc4010OBean dictTypeOBean : result) {
                            String dictType = dictTypeOBean.getDictType();
                            List<Rpc4010OBean> dictList = dictTypeOBean.getDictList();
                            List<TaxDictBean> taxDictList = new ArrayList<>();
                            for (Rpc4010OBean dict : dictList) {
                                TaxDictBean taxDictBean = new TaxDictBean();
                                taxDictBean.setDictId(dict.getDictId());
                                taxDictBean.setDictKey(dict.getDictKey());
                                taxDictBean.setDictValue(dict.getDictValue());
                                taxDictBean.setDictType(dictType);
                                taxDictList.add(taxDictBean);
                            }
                            cache.put(dictType, taxDictList);
                        }
                    }
                    timestamp = System.currentTimeMillis();
                }
            }
        }
    }

}
