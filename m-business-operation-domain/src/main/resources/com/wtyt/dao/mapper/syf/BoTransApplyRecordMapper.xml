<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.dao.mapper.syf.BoTransApplyRecordMapper">
    <resultMap type="com.wtyt.dao.bean.syf.BoTransApplyRecordBean" id="TBoTransApplyRecordMap">
        <result property="boTransApplyRecordId" column="BO_TRANS_APPLY_RECORD_ID" jdbcType="VARCHAR"/>
        <result property="applyId" column="APPLY_ID" jdbcType="VARCHAR"/>
        <result property="applyType" column="APPLY_TYPE" jdbcType="VARCHAR"/>
        <result property="sysRoleType" column="SYS_ROLE_TYPE" jdbcType="VARCHAR"/>
        <result property="userId" column="USER_ID" jdbcType="VARCHAR"/>
        <result property="userName" column="USER_NAME" jdbcType="VARCHAR"/>
        <result property="processDesc" column="PROCESS_DESC" jdbcType="VARCHAR"/>
        <result property="processStatus" column="PROCESS_STATUS" jdbcType="VARCHAR"/>
        <result property="reason" column="REASON" jdbcType="VARCHAR"/>
        <result property="isDel" column="IS_DEL" jdbcType="VARCHAR"/>
        <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
        <result property="lastModifiedTime" column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">
        T_BO_TRANS_APPLY_RECORD
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="TBoTransApplyRecordMap">
        select BO_TRANS_APPLY_RECORD_ID,
               APPLY_ID,
               APPLY_TYPE,
               SYS_ROLE_TYPE,
               USER_ID,
               USER_NAME,
               PROCESS_DESC,
               PROCESS_STATUS,
               REASON,
               IS_DEL,
               CREATED_TIME,
               LAST_MODIFIED_TIME,
               NOTE
        from T_BO_TRANS_APPLY_RECORD
        where BO_TRANS_APPLY_RECORD_ID = #{boTransApplyRecordId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="TBoTransApplyRecordMap">
        select
        BO_TRANS_APPLY_RECORD_ID, APPLY_ID, APPLY_TYPE, SYS_ROLE_TYPE, USER_ID, USER_NAME, PROCESS_DESC, PROCESS_STATUS,
        REASON, IS_DEL, CREATED_TIME, LAST_MODIFIED_TIME, NOTE
        from T_BO_TRANS_APPLY_RECORD
        <where>
            <if test="boTransApplyRecordId != null and boTransApplyRecordId != ''">
                and BO_TRANS_APPLY_RECORD_ID = #{boTransApplyRecordId}
            </if>
            <if test="applyId != null and applyId != ''">
                and APPLY_ID = #{applyId}
            </if>
            <if test="applyType != null and applyType != ''">
                and APPLY_TYPE = #{applyType}
            </if>
            <if test="sysRoleType != null and sysRoleType != ''">
                and SYS_ROLE_TYPE = #{sysRoleType}
            </if>
            <if test="userId != null and userId != ''">
                and USER_ID = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and USER_NAME = #{userName}
            </if>
            <if test="processDesc != null and processDesc != ''">
                and PROCESS_DESC = #{processDesc}
            </if>
            <if test="processStatus != null and processStatus != ''">
                and PROCESS_STATUS = #{processStatus}
            </if>
            <if test="reason != null and reason != ''">
                and REASON = #{reason}
            </if>
            <if test="isDel != null and isDel != ''">
                and IS_DEL = #{isDel}
            </if>
            <if test="createdTime != null">
                and CREATED_TIME = #{createdTime}
            </if>
            <if test="lastModifiedTime != null">
                and LAST_MODIFIED_TIME = #{lastModifiedTime}
            </if>
            <if test="note != null and note != ''">
                and NOTE = #{note}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from T_BO_TRANS_APPLY_RECORD
        <where>
            <if test="boTransApplyRecordId != null and boTransApplyRecordId != ''">
                and BO_TRANS_APPLY_RECORD_ID = #{boTransApplyRecordId}
            </if>
            <if test="applyId != null and applyId != ''">
                and APPLY_ID = #{applyId}
            </if>
            <if test="applyType != null and applyType != ''">
                and APPLY_TYPE = #{applyType}
            </if>
            <if test="sysRoleType != null and sysRoleType != ''">
                and SYS_ROLE_TYPE = #{sysRoleType}
            </if>
            <if test="userId != null and userId != ''">
                and USER_ID = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and USER_NAME = #{userName}
            </if>
            <if test="processDesc != null and processDesc != ''">
                and PROCESS_DESC = #{processDesc}
            </if>
            <if test="processStatus != null and processStatus != ''">
                and PROCESS_STATUS = #{processStatus}
            </if>
            <if test="reason != null and reason != ''">
                and REASON = #{reason}
            </if>
            <if test="isDel != null and isDel != ''">
                and IS_DEL = #{isDel}
            </if>
            <if test="createdTime != null">
                and CREATED_TIME = #{createdTime}
            </if>
            <if test="lastModifiedTime != null">
                and LAST_MODIFIED_TIME = #{lastModifiedTime}
            </if>
            <if test="note != null and note != ''">
                and NOTE = #{note}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="boTransApplyRecordId">
        insert into T_BO_TRANS_APPLY_RECORD(BO_TRANS_APPLY_RECORD_ID,APPLY_ID, APPLY_TYPE, SYS_ROLE_TYPE, USER_ID, USER_NAME, PROCESS_DESC,
                                            PROCESS_STATUS, REASON)
        values (${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},#{applyId}, #{applyType}, #{sysRoleType}, #{userId}, #{userName}, #{processDesc}, #{processStatus},
                #{reason})
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO T_BO_TRANS_APPLY_RECORD
        (BO_TRANS_APPLY_RECORD_ID, APPLY_ID, APPLY_TYPE,

        SYS_ROLE_TYPE,
        USER_ID, USER_NAME, PROCESS_DESC,
        PROCESS_STATUS, REASON, IS_DEL, CREATED_TIME, LAST_MODIFIED_TIME, NOTE)
        <foreach item="item" index="index" collection="entities" separator="UNION ALL">
            SELECT
            #{item.boTransApplyRecordId} BO_TRANS_APPLY_RECORD_ID,
            #{item.applyId} APPLY_ID,
            #{item.applyType} APPLY_TYPE,
            #{item.sysRoleType} SYS_ROLE_TYPE,
            #{item.userId} USER_ID,
            #{item.userName} USER_NAME,
            #{item.processDesc} PROCESS_DESC,
            #{item.processStatus} PROCESS_STATUS,
            #{item.reason} REASON,
            0 IS_DEL,
            SYSDATE CREATED_TIME,
            SYSDATE LAST_MODIFIED_TIME,
            #{item.note} NOTE
            FROM
            DUAL
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update T_BO_TRANS_APPLY_RECORD
        <set>
            <if test="applyId != null and applyId != ''">
                APPLY_ID = #{applyId},
            </if>
            <if test="applyType != null and applyType != ''">
                APPLY_TYPE = #{applyType},
            </if>
            <if test="sysRoleType != null and sysRoleType != ''">
                SYS_ROLE_TYPE = #{sysRoleType},
            </if>
            <if test="userId != null and userId != ''">
                USER_ID = #{userId},
            </if>
            <if test="userName != null and userName != ''">
                USER_NAME = #{userName},
            </if>
            <if test="processDesc != null and processDesc != ''">
                PROCESS_DESC = #{processDesc},
            </if>
            <if test="processStatus != null and processStatus != ''">
                PROCESS_STATUS = #{processStatus},
            </if>
            <if test="reason != null and reason != ''">
                REASON = #{reason},
            </if>
            <if test="isDel != null and isDel != ''">
                IS_DEL = #{isDel},
            </if>
            <if test="createdTime != null">
                CREATED_TIME = #{createdTime},
            </if>
            <if test="lastModifiedTime != null">
                LAST_MODIFIED_TIME = #{lastModifiedTime},
            </if>
            <if test="note != null and note != ''">
                NOTE = #{note},
            </if>
        </set>
        where BO_TRANS_APPLY_RECORD_ID = #{boTransApplyRecordId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from T_BO_TRANS_APPLY_RECORD
        where BO_TRANS_APPLY_RECORD_ID = #{boTransApplyRecordId}
    </delete>

    <select id="queryAuditDataByTransTaskId" parameterType="list" resultMap="TBoTransApplyRecordMap">
        SELECT
            r.APPLY_ID,
            r.PROCESS_STATUS,
            TO_CHAR(r.CREATED_TIME, 'YYYY-MM-DD HH24:MI') CREATED_TIME,
            TO_CHAR(r.LAST_MODIFIED_TIME, 'YYYY-MM-DD HH24:MI') LAST_MODIFIED_TIME,
            r.REASON,
            r.USER_ID,
            r.USER_NAME,
            r.SYS_ROLE_TYPE,
            r.PROCESS_DESC,
            r.NOTE
        FROM
            T_BO_TRANS_APPLY_RECORD r
                INNER JOIN T_BO_TRANS_TASK_FEE_APPLY a
                           ON
                               r.APPLY_ID = a.BO_TRANS_TASK_FEE_APPLY_ID
        WHERE
            r.IS_DEL = 0
          AND a.IS_DEL = 0
          AND a.BO_TRANS_TASK_ID = #{boTransTaskId}
          AND r.APPLY_TYPE = #{applyType}
        ORDER BY
            r.CREATED_TIME ASC
    </select>
</mapper>

