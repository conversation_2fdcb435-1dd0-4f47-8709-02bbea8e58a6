<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardDioQueryMapper">

    <select id="queryPredictArriveDateCount" resultType="com.wtyt.board.bean.DateCountBean">
        SELECT
            <choose>
                <when test="queryType == '1'.toString()">
                    TO_CHAR(ARRIVE_END_TIME, 'YYYY-MM-DD') dateStr,
                </when>
                <when test="queryType == '2'.toString()">
                    TO_CHAR(ARRIVE_END_TIME, 'YYYY-MM') dateStr,
                </when>
            </choose>
            COUNT(BO_TRANS_TASK_ID) total,
            SUM(arriveOntime) sub
        FROM
            (
            SELECT
                t.BO_TRANS_TASK_ID ,
                e.ARRIVE_END_TIME ,
                CASE
                    WHEN e.ARRIVE_END_TIME >= NVL(e.ARRIVE_TIME, t.START_TIME) THEN 1
                    ELSE 0
                END arriveOntime
            FROM
                T_BO_TRANS_TASK t
            JOIN T_BO_TRANS_TASK_EXTRA e ON
                e.IS_DEL = 0
                AND e.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            WHERE
                t.IS_DEL = 0
                <include refid="dioQuery" />
                AND e.ARRIVE_END_TIME >= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
                AND e.ARRIVE_END_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
            ) temp
        GROUP BY
            <choose>
                <when test="queryType == '1'.toString()">
                    TO_CHAR(ARRIVE_END_TIME, 'YYYY-MM-DD')
                </when>
                <when test="queryType == '2'.toString()">
                    TO_CHAR(ARRIVE_END_TIME, 'YYYY-MM')
                </when>
            </choose>
    </select>
    <select id="queryPredictDeliveryDateCount" resultType="com.wtyt.board.bean.DateCountBean">
        SELECT
            <choose>
                <when test="queryType == '1'.toString()">
                    TO_CHAR(DEAD_LINE_TIME, 'YYYY-MM-DD') dateStr,
                </when>
                <when test="queryType == '2'.toString()">
                    TO_CHAR(DEAD_LINE_TIME, 'YYYY-MM') dateStr,
                </when>
            </choose>
            COUNT(BO_TRANS_TASK_ID) total,
            SUM(arriveOntime) sub
        FROM
            (
            SELECT
                t.BO_TRANS_TASK_ID ,
                dl.DEAD_LINE_TIME ,
                r.CREATED_TIME,
                CASE
                    WHEN dl.DEAD_LINE_TIME >= NVL(r.CREATED_TIME, t.END_TIME) THEN 1
                    ELSE 0
                END arriveOntime
            FROM
                T_BO_TRANS_TASK t
            JOIN T_BO_TRANS_TASK_EXTRA e ON
                e.IS_DEL = 0
                AND e.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            JOIN T_BO_TRANS_NODE_DEAD_LINE dl ON
                dl.IS_DEL = 0
                AND t.BO_TRANS_TASK_ID = dl.BO_TRANS_TASK_ID
                AND dl.NODE_ID = 800
            LEFT JOIN T_BO_TRANS_NODE_RECORD r ON
                r.IS_DEL = 0
                AND t.BO_TRANS_TASK_ID = r.BO_TRANS_TASK_ID
                AND r.NODE_ID = 650
            WHERE
                t.IS_DEL = 0
                <include refid="dioQuery" />
                AND dl.DEAD_LINE_TIME >= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
                AND dl.DEAD_LINE_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
            ) temp
        GROUP BY
            <choose>
                <when test="queryType == '1'.toString()">
                    TO_CHAR(DEAD_LINE_TIME, 'YYYY-MM-DD')
                </when>
                <when test="queryType == '2'.toString()">
                    TO_CHAR(DEAD_LINE_TIME, 'YYYY-MM')
                </when>
            </choose>
    </select>
    <select id="queryDioTaskDetailList" resultType="com.wtyt.board.bean.BoardDioTaskBean">
        SELECT
            TO_CHAR(CREATED_TIME, 'yyyy-mm-dd hh24:mi:ss') AS createdTime,
            TAX_WAYBILL_NO AS taxWaybillNo,
            ORG_ID AS orgId,
            WB_ITEM AS wbItem,
            START_PROVINCE_NAME || START_CITY_NAME AS startPlace,
            END_PROVINCE_NAME || END_CITY_NAME AS endPlace,
            DRIVER_NAME AS driverName,
            MOBILE_NO AS mobileNo,
            CART_BADGE_NO AS cartBadgeNo,
            BELONG_DISPATCHER_ID AS belongDispatcherId,
            dispatcherId,
            TO_CHAR(DISPATCH_CAR_TIME, 'yyyy-mm-dd hh24:mi:ss') AS dispatchCarTime,
            TO_CHAR(arriveEndTime, 'yyyy-mm-dd hh24:mi:ss') AS arriveEndTime,
            TO_CHAR(deliveryEndTime, 'yyyy-mm-dd hh24:mi:ss') AS deliveryEndTime,
            TO_CHAR(arriveTime, 'yyyy-mm-dd hh24:mi:ss') AS arriveTime,
            TO_CHAR(deliveryTime, 'yyyy-mm-dd hh24:mi:ss') AS deliveryTime,
            case when arriveEndTime >= arriveTime then 1 else 0 end arriveOntime,
            case when deliveryEndTime >= deliveryTime then 1 else 0 end deliveryOntime,
            cargoDamage,
            abnormal
        FROM
            (
            SELECT
                t.CREATED_TIME ,
                t.TAX_WAYBILL_NO ,
                t.ORG_ID ,
                t.WB_ITEM ,
                t.START_PROVINCE_NAME ,
                t.START_CITY_NAME ,
                t.END_PROVINCE_NAME ,
                t.END_CITY_NAME,
                t.DRIVER_NAME,
                t.MOBILE_NO,
                t.CART_BADGE_NO,
                e.BELONG_DISPATCHER_ID,
                nr.USER_ID as dispatcherId,
                e.DISPATCH_CAR_TIME ,
                e.ARRIVE_END_TIME arriveEndTime,
                (SELECT MAX(DEAD_LINE_TIME) FROM T_BO_TRANS_NODE_DEAD_LINE WHERE NODE_ID = 800 AND IS_DEL = 0 AND BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID) AS deliveryEndTime,
                NVL(e.ARRIVE_TIME, t.START_TIME)  arriveTime,
                (SELECT NVL(MAX(CREATED_TIME), t.END_TIME) FROM T_BO_TRANS_NODE_RECORD WHERE NODE_ID = 650 AND IS_DEL = 0 AND BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID) AS deliveryTime,
                (SELECT NVL2(MAX(BO_TRANS_TASK_ID), 1, 0) FROM T_BO_TRANS_TASK_FEE WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND CONFIG_NAME = '货损金额' AND CONFIG_VALUE>0) AS cargoDamage,
                (SELECT NVL2(MAX(BO_TRANS_TASK_ID), 1, 0) FROM T_BO_TRANS_TASK_FEE WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND CONFIG_NAME IN ('货损金额', '压车费') AND CONFIG_VALUE>0) AS abnormal
            FROM
                T_BO_TRANS_TASK t
            JOIN T_BO_TRANS_TASK_EXTRA e ON e.IS_DEL = 0 AND e.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            LEFT JOIN T_BO_TRANS_NODE_RECORD nr ON nr.BO_TRANS_NODE_RECORD_ID =e.DISPATCH_CAR_RECORD_ID
            WHERE
                t.IS_DEL = 0
                <include refid="dioQuery"/>
                <!-- 查询时间 -->
                <choose>
                    <when test="timeType == '1'.toString()">
                        AND t.CREATED_TIME >= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
                        AND t.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
                    </when>
                    <when test="timeType == '2'.toString()">
                        AND e.ARRIVE_END_TIME >= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
                        AND e.ARRIVE_END_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
                    </when>
                    <when test="timeType == '3'.toString()">
                        AND EXISTS(
                        SELECT 1 FROM
                            T_BO_TRANS_NODE_DEAD_LINE
                        WHERE
                            NODE_ID = 800
                            AND IS_DEL = 0
                            AND BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
                            AND DEAD_LINE_TIME >= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
                            AND DEAD_LINE_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
                        )
                    </when>
                </choose>
                <if test="overviewType != null">
                    <choose>
                        <!-- 预计装货地迟到 -->
                        <when test="overviewType == '1'.toString()">
                            AND t.STATE = 0
                            AND e.DISPATCH_CAR_RECORD_ID IS NOT NULL
				            AND e.ARRIVE_TIME IS NULL
                            AND EXISTS( SELECT 1 FROM T_BO_TRANS_NODE_ALARM WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID=t.BO_TRANS_TASK_ID AND NODE_DATA_TYPE=2 AND ((ALARM_TYPE=0 AND ALARM_PROCESS_RESULT=0) OR ALARM_TYPE=1))
                        </when>
                        <!-- 预计正常到场 -->
                        <when test="overviewType == '3'.toString()">
                            AND t.STATE = 0
                        	AND e.DISPATCH_CAR_RECORD_ID IS NOT NULL
				            AND e.ARRIVE_TIME IS NULL
                            AND NOT EXISTS( SELECT 1 FROM T_BO_TRANS_NODE_ALARM WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID=t.BO_TRANS_TASK_ID AND NODE_DATA_TYPE=2 AND ((ALARM_TYPE=0 AND ALARM_PROCESS_RESULT=0) OR ALARM_TYPE=1))
                        </when>
                        <!-- 预计卸货地迟到 -->
                        <when test="overviewType == '5'.toString()">
                            AND t.STATE = 1
                            AND EXISTS( SELECT 1 FROM T_BO_TRANS_NODE_DEAD_LINE WHERE NODE_ID = 800 AND IS_DEL = 0 AND BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID)
                            AND EXISTS( SELECT 1 FROM T_BO_TRANS_NODE_ALARM WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID=t.BO_TRANS_TASK_ID AND NODE_DATA_TYPE=16 AND ((ALARM_TYPE=0 AND ALARM_PROCESS_RESULT=0) OR ALARM_TYPE=1))
                        </when>
                        <!-- 长时间停留 -->
                        <when test="overviewType == '7'.toString()">
                            AND EXISTS( SELECT 1 FROM T_BO_TRANS_NODE_ALARM WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID=t.BO_TRANS_TASK_ID AND ALARM_TYPE=1 AND NODE_DATA_TYPE=14 AND ALARM_PROCESS_RESULT=0)
                        </when>
                        <!-- 预计正常送达 -->
                        <when test="overviewType == '8'.toString()">
                            AND t.STATE = 1
                            AND EXISTS( SELECT 1 FROM T_BO_TRANS_NODE_DEAD_LINE WHERE NODE_ID = 800 AND IS_DEL = 0 AND BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID)
                            AND NOT EXISTS( SELECT 1 FROM T_BO_TRANS_NODE_ALARM WHERE IS_DEL = 0 AND BO_TRANS_TASK_ID=t.BO_TRANS_TASK_ID AND NODE_DATA_TYPE=16 AND ((ALARM_TYPE=0 AND ALARM_PROCESS_RESULT=0) OR ALARM_TYPE=1))
                        </when>
                    </choose>
                </if>
                <if test="driverInfo != null and driverInfo != ''">
                   AND (INSTR(t.DRIVER_NAME, #{driverInfo}) > 0
                        OR INSTR(t.MOBILE_NO, #{driverInfo}) > 0
                        OR INSTR(t.CART_BADGE_NO, #{driverInfo}) > 0
                        )
                </if>
               <if test="taxWaybillNo != null and taxWaybillNo != ''">
                   AND INSTR(t.TAX_WAYBILL_NO, #{taxWaybillNo}) > 0
                </if>
                <if test="dispatchedIdList != null and dispatchedIdList.size() > 0">
                    AND nr.USER_ID IN
                        <foreach collection="dispatchedIdList" item="item" close=")" open="(" separator=",">
                                #{item}
                        </foreach>
                </if>
            ) temp
        WHERE 1 = 1
            <!-- 是否及时 -->
            <choose>
                <when test="arriveOntime == '1'.toString()">
                    AND arriveEndTime >= arriveTime
                </when>
                <when test="arriveOntime == '0'.toString()">
                    AND (arriveEndTime &lt; arriveTime OR arriveTime IS NULL )
                </when>
            </choose>
            <choose>
                <when test="deliveryOntime == '1'.toString()">
                    AND deliveryEndTime >= deliveryTime
                </when>
                <when test="deliveryOntime == '0'.toString()">
                    AND (deliveryEndTime &lt; deliveryTime OR deliveryTime IS NULL)
                </when>
            </choose>
            <choose>
                <when test="overviewType == '2'.toString()">
                    AND arriveEndTime &lt; arriveTime
                </when>
                <when test="overviewType == '4'.toString()">
                    AND arriveEndTime >= arriveTime
                </when>
                <when test="overviewType == '6'.toString()">
                    AND deliveryEndTime &lt; deliveryTime
                </when>
                <when test="overviewType == '9'.toString()">
                    AND deliveryEndTime >= deliveryTime
                </when>
            </choose>
            <!-- 异常类型 -->
            <choose>
                <when test="cargoDamage == '1'.toString()">
                    AND cargoDamage = 1
                </when>
                <when test="cargoDamage == '0'.toString()">
                    AND cargoDamage = 0
                </when>
            </choose>
            <choose>
                <when test="abnormal == '1'.toString()">
                    AND abnormal = 1
                </when>
                <when test="abnormal == '0'.toString()">
                    AND abnormal = 0
                </when>
            </choose>
            ORDER BY CREATED_TIME DESC
    </select>
    <sql id="dioQuery">
        AND t.ORG_ID IN
        <foreach collection="queryOrgIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        <if test="wbItems != null and wbItems.size() > 0">
            AND t.WB_ITEM IN
            <foreach collection="wbItems" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startPlace != null and startPlace.size() > 0">
            AND (
            <foreach item="item" index="index" collection="startPlace" separator="or">
                (
                    <if test="item.provinceName != null and item.provinceName != ''">
                        t.START_PROVINCE_NAME = #{item.provinceName}
                    </if>
                    <if test="item.cityName != null and item.cityName != ''">
                        and t.START_CITY_NAME = #{item.cityName}
                    </if>
                    <if test="item.countyName != null and item.countyName != ''">
                        and t.START_COUNTY_NAME = #{item.countyName}
                    </if>
                )
            </foreach>)
        </if>
        <if test="endPlace != null and endPlace.size() > 0">
            AND (
            <foreach item="item" index="index" collection="endPlace" separator="or">
                (
                    <if test="item.provinceName != null and item.provinceName != ''">
                        t.END_PROVINCE_NAME = #{item.provinceName}
                    </if>
                    <if test="item.cityName != null and item.cityName != ''">
                        and t.END_CITY_NAME = #{item.cityName}
                    </if>
                    <if test="item.countyName != null and item.countyName != ''">
                        and t.END_COUNTY_NAME = #{item.countyName}
                    </if>
                )
            </foreach>)
        </if>

        <if test="belongDispatcherIds != null and belongDispatcherIds.size() > 0">
            AND e.BELONG_DISPATCHER_ID IN
            <foreach collection="belongDispatcherIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cartTypeList != null and cartTypeList.size() > 0">
            AND e.CART_TYPE IN
            <foreach collection="cartTypeList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cartLengthList != null and cartLengthList.size() > 0">
            AND e.CART_LENGTH IN
            <foreach collection="cartLengthList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="queryTaskFreightDateCount" resultType="com.wtyt.board.bean.DateCountBean"  parameterType="com.wtyt.board.bean.request.BoardDioQueryBean">
        SELECT
            <choose>
                <when test="queryType == '1'.toString()">
                    TO_CHAR(e.ARRIVE_END_TIME, 'YYYY-MM-DD') dateStr,
                </when>
                <when test="queryType == '2'.toString()">
                    TO_CHAR(e.ARRIVE_END_TIME, 'YYYY-MM') dateStr,
                </when>
            </choose>
           <choose>
                <when test="priceSubGuarantee !=null and priceSubGuarantee!=''">
                      NVL(MAX(t.USER_FREIGHT - NVL(G.GUARANTEE_AMOUNT,0)),0) AS maxNum,
                      NVL(MIN(t.USER_FREIGHT - NVL(G.GUARANTEE_AMOUNT,0)),0) AS minNum,
                      TO_CHAR(NVL(AVG(T.USER_FREIGHT - NVL(G.GUARANTEE_AMOUNT,0)),0), 'FM999999990.00')  as avgValue
                </when>
                <otherwise>
                      NVL(MAX(t.USER_FREIGHT),0) AS maxNum,
                      NVL(MIN(t.USER_FREIGHT),0) AS minNum,
                      TO_CHAR(NVL(AVG(T.USER_FREIGHT),0), 'FM999999990.00')  as avgValue
                </otherwise>
            </choose>
        FROM T_BO_TRANS_TASK t
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON  e.IS_DEL = 0 AND e.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        <if test="priceSubGuarantee !=null and priceSubGuarantee!=''">
            LEFT JOIN T_BO_TASK_DRIVER_GUARANTEE G ON G.BO_TRANS_TASK_ID=T.BO_TRANS_TASK_ID AND G.IS_DEL=0 AND G.GUARANTEE_CHANNEL IN
                <foreach collection="guaranteeChannelList" item="item" close=")" open="(" separator=",">
                 #{item}
                </foreach>
        </if>
        WHERE t.IS_DEL = 0
        <include refid="dioQuery"/>
        <!--约定到场时间startTime-->
        <if test="startTime != null and startTime !=''">
            AND  e.ARRIVE_END_TIME &gt;= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
        <!--约定到场时间endTime-->
        <if test="endTime != null and endTime !=''">
            AND  e.ARRIVE_END_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        </if>
            GROUP BY
        <choose>
             <when test="queryType == '1'.toString()">
                    TO_CHAR(e.ARRIVE_END_TIME, 'YYYY-MM-DD')
             </when>
             <when test="queryType == '2'.toString()">
                    TO_CHAR(e.ARRIVE_END_TIME, 'YYYY-MM')
             </when>
        </choose>
    </select>



</mapper>
