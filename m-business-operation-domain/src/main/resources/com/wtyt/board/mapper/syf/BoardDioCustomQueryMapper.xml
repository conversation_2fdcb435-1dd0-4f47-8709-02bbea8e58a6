<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardDioCustomQueryMapper">
    <select id="queryBelongDispatcherIds" resultType="java.lang.String">
        SELECT
            DISTINCT e.BELONG_DISPATCHER_ID
        FROM
            T_BO_TRANS_TASK t
        INNER JOIN T_BO_TRANS_TASK_EXTRA e ON
            t.BO_TRANS_TASK_ID = e.BO_TRANS_TASK_ID
        WHERE
            t.IS_DEL = 0
            AND e.IS_DEL = 0
            AND t.ORG_ID IN
           <foreach collection="orgIdList" item="item" close=")" open="(" separator=",">
               #{item}
           </foreach>
            AND e.BELONG_DISPATCHER_ID IS NOT NULL
    </select>

    <select id="queryDispatcherByTaskOrgId" resultType="java.lang.String">
        SELECT
            DISTINCT nr.USER_ID  dispatcherId
        FROM
        T_BO_TRANS_TASK t
         INNER JOIN T_BO_TRANS_TASK_EXTRA te ON te.BO_TRANS_TASK_ID =t.BO_TRANS_TASK_ID
         INNER JOIN T_BO_TRANS_NODE_RECORD nr ON nr.BO_TRANS_NODE_RECORD_ID =te.DISPATCH_CAR_RECORD_ID
        WHERE
        (t.ORG_ID IN
        <foreach collection="orgIds" index="index" item="orgId" open="(" separator="," close=")">
            <if test="(index % 999) == 998"> NULL) OR t.ORG_ID IN(</if>#{orgId}
        </foreach>
        )
        AND t.IS_DEL =0
    </select>

    <select id="queryHyGoodsDifferenceDateCount" resultType="com.wtyt.board.bean.DateCountBean">
    SELECT
        <choose>
            <when test="queryType == '1'.toString()">
                TO_CHAR(CREATED_TIME, 'YYYY-MM-DD') dateStr,
            </when>
            <when test="queryType == '2'.toString()">
                TO_CHAR(CREATED_TIME, 'YYYY-MM') dateStr,
            </when>
        </choose>
        COUNT(BO_TRANS_TASK_ID) total,
        SUM(hsNum) sub
    FROM
        (
        SELECT
            t.BO_TRANS_TASK_ID ,
            t.CREATED_TIME,
            CASE
                WHEN f.config_value > 0 THEN 1
                ELSE 0
            END hsNum
        FROM
            T_BO_TRANS_TASK t
        JOIN T_BO_TRANS_TASK_EXTRA e ON
            e.IS_DEL = 0
            AND e.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
        LEFT JOIN T_BO_TRANS_TASK_FEE f ON
            f.IS_DEL = 0
            AND f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            AND f.CONFIG_NAME = '货损金额'
        WHERE
            t.IS_DEL = 0
            <include refid="dioQuery"/>
            AND t.CREATED_TIME >= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
            AND t.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
        ) temp
    GROUP BY
        <choose>
            <when test="queryType == '1'.toString()">
                TO_CHAR(CREATED_TIME, 'YYYY-MM-DD')
            </when>
            <when test="queryType == '2'.toString()">
                TO_CHAR(CREATED_TIME, 'YYYY-MM')
            </when>
        </choose>
    </select>
    <select id="queryHyGoodsDamageDateCount" resultType="com.wtyt.board.bean.DateCountBean">
        SELECT
            <choose>
                <when test="queryType == '1'.toString()">
                    TO_CHAR(CREATED_TIME, 'YYYY-MM-DD') dateStr,
                </when>
                <when test="queryType == '2'.toString()">
                    TO_CHAR(CREATED_TIME, 'YYYY-MM') dateStr,
                </when>
            </choose>
            SUM(hzAmount) total,
            SUM(hsAmount) sub
        FROM
            (
            SELECT
                t.CREATED_TIME,
                CASE
                    WHEN f.CONFIG_NAME = '货值' THEN NVL(f.CONFIG_VALUE, 0)
                    ELSE 0
                END hzAmount,
                CASE
                    WHEN f.CONFIG_NAME = '货损金额' THEN NVL(f.CONFIG_VALUE, 0)
                    ELSE 0
                END hsAmount
            FROM
                T_BO_TRANS_TASK t
            JOIN T_BO_TRANS_TASK_EXTRA e ON
                e.IS_DEL = 0
                AND e.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            LEFT JOIN T_BO_TRANS_TASK_FEE f ON f.IS_DEL=0 AND f.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID AND f.CONFIG_NAME IN ('货损金额','货值')
        
            WHERE
                t.IS_DEL = 0
                <include refid="dioQuery"/>
                AND t.CREATED_TIME >= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
                AND t.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
            ) temp
        GROUP BY
            <choose>
                <when test="queryType == '1'.toString()">
                    TO_CHAR(CREATED_TIME, 'YYYY-MM-DD')
                </when>
                <when test="queryType == '2'.toString()">
                    TO_CHAR(CREATED_TIME, 'YYYY-MM')
                </when>
            </choose>
    </select>
    <select id="queryHyGoodsDamageOverviewDateCount" resultType="com.wtyt.board.bean.DateCountBean">
        SELECT
            <choose>
                <when test="queryType == '1'.toString()">
                    TO_CHAR(CREATED_TIME, 'YYYY-MM-DD') dateStr,
                </when>
                <when test="queryType == '2'.toString()">
                    TO_CHAR(CREATED_TIME, 'YYYY-MM') dateStr,
                </when>
            </choose>
            COUNT(1) total,
            SUM(NVL(abnormal,0)) sub
        FROM
            (
            SELECT
                t.CREATED_TIME,
                (
                SELECT
                    CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
                FROM
                    t_bo_trans_task_fee
                WHERE
                    is_del = 0
                    AND BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
                    AND CONFIG_VALUE > 0
                    AND CONFIG_NAME IN ('货损金额', '压车费')) AS abnormal
            FROM
                T_BO_TRANS_TASK t
            JOIN T_BO_TRANS_TASK_EXTRA e ON
                e.IS_DEL = 0
                AND e.BO_TRANS_TASK_ID = t.BO_TRANS_TASK_ID
            WHERE
                t.IS_DEL = 0
                <include refid="dioQuery"/>
                AND t.CREATED_TIME >= TO_DATE(#{startTime},'YYYY-MM-DD HH24:MI:SS')
                AND t.CREATED_TIME &lt;= TO_DATE(#{endTime},'YYYY-MM-DD HH24:MI:SS')
            ) temp
        GROUP BY
            <choose>
                <when test="queryType == '1'.toString()">
                    TO_CHAR(CREATED_TIME, 'YYYY-MM-DD')
                </when>
                <when test="queryType == '2'.toString()">
                    TO_CHAR(CREATED_TIME, 'YYYY-MM')
                </when>
            </choose>
    </select>
    <sql id="dioQuery">
        AND t.ORG_ID IN
        <foreach collection="queryOrgIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        <if test="wbItems != null and wbItems.size() > 0">
            AND t.WB_ITEM IN
            <foreach collection="wbItems" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startPlace != null and startPlace.size() > 0">
            AND (
            <foreach item="item" index="index" collection="startPlace" separator="or">
                (
                    <if test="item.provinceName != null and item.provinceName != ''">
                        t.START_PROVINCE_NAME = #{item.provinceName}
                    </if>
                    <if test="item.cityName != null and item.cityName != ''">
                        and t.START_CITY_NAME = #{item.cityName}
                    </if>
                    <if test="item.countyName != null and item.countyName != ''">
                        and t.START_COUNTY_NAME = #{item.countyName}
                    </if>
                )
            </foreach>)
        </if>
        <if test="endPlace != null and endPlace.size() > 0">
            AND (
            <foreach item="item" index="index" collection="endPlace" separator="or">
                (
                    <if test="item.provinceName != null and item.provinceName != ''">
                        t.END_PROVINCE_NAME = #{item.provinceName}
                    </if>
                    <if test="item.cityName != null and item.cityName != ''">
                        and t.END_CITY_NAME = #{item.cityName}
                    </if>
                    <if test="item.countyName != null and item.countyName != ''">
                        and t.END_COUNTY_NAME = #{item.countyName}
                    </if>
                )
            </foreach>)
        </if>

        <if test="belongDispatcherIds != null and belongDispatcherIds.size() > 0">
            AND e.BELONG_DISPATCHER_ID IN
            <foreach collection="belongDispatcherIds" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cartTypeList != null and cartTypeList.size() > 0">
            AND e.CART_TYPE IN
            <foreach collection="cartTypeList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cartLengthList != null and cartLengthList.size() > 0">
            AND e.CART_LENGTH IN
            <foreach collection="cartLengthList" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>
