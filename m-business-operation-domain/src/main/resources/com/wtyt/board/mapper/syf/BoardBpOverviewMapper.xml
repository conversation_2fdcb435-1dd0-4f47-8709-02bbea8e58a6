<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.board.mapper.syf.BoardBpOverviewMapper">

    <select id="getReceivedNotArriveNumList" resultType="com.wtyt.board.bean.BoardBpOverviewStatisticsBean">
        SELECT
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            COUNT(T.BO_BUSINESS_LINE_ID) countNum
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
        AND T.CREATED_TIME &lt;= to_date(#{overviewDate} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
        AND T.BO_BUSINESS_LINE_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND T.NODE_ID = 200
        AND EXISTS(SELECT 1 FROM T_BO_TRANS_TASK_EXTRA R WHERE R.HYB_RECEIVED_TIME IS NOT NULL AND R.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID AND R.IS_DEL = 0)
        GROUP BY T.BO_BUSINESS_LINE_ID
    </select>

    <select id="getArriveNotStartNumList" resultType="com.wtyt.board.bean.BoardBpOverviewStatisticsBean">
        SELECT
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            COUNT(T.BO_BUSINESS_LINE_ID) countNum
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
        AND T.CREATED_TIME &lt;= to_date(#{overviewDate} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
        AND T.BO_BUSINESS_LINE_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND ((T.NODE_ID = 500 AND T.TRANS_PATTERN = 1) OR (T.NODE_ID = 400 AND T.TRANS_PATTERN = 2))
        GROUP BY T.BO_BUSINESS_LINE_ID
    </select>

    <select id="getStartNotArriveNumList" resultType="com.wtyt.board.bean.BoardBpOverviewStatisticsBean">
        SELECT
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            COUNT(T.BO_BUSINESS_LINE_ID) countNum
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
        AND T.CREATED_TIME &lt;= to_date(#{overviewDate} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
        AND T.BO_BUSINESS_LINE_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND T.NODE_ID = 600
        GROUP BY T.BO_BUSINESS_LINE_ID
    </select>

    <select id="getArriveNotUnloadNumList" resultType="com.wtyt.board.bean.BoardBpOverviewStatisticsBean">
        SELECT
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            COUNT(T.BO_BUSINESS_LINE_ID) countNum
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
        AND T.CREATED_TIME &lt;= to_date(#{overviewDate} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
        AND T.BO_BUSINESS_LINE_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND (T.NODE_ID = 650 OR T.NODE_ID = 700)
        AND T.FIRST_RECEIPT_TIME IS NULL
        GROUP BY T.BO_BUSINESS_LINE_ID
    </select>

    <select id="getUnsettledNumList" resultType="com.wtyt.board.bean.BoardBpOverviewStatisticsBean">
        SELECT
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            COUNT(T.BO_BUSINESS_LINE_ID) countNum
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
        AND T.CREATED_TIME &lt;= to_date(#{overviewDate} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
        AND T.BO_BUSINESS_LINE_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND (T.NODE_ID = 650 OR T.NODE_ID = 700)
        AND T.FIRST_RECEIPT_TIME IS NOT NULL
        GROUP BY T.BO_BUSINESS_LINE_ID
    </select>

    <select id="getCompletedNumList" resultType="com.wtyt.board.bean.BoardBpOverviewStatisticsBean">
        SELECT
            T.BO_BUSINESS_LINE_ID boBusinessLineId,
            COUNT(T.BO_BUSINESS_LINE_ID) countNum
        FROM T_BO_TRANS_TASK T
        WHERE T.IS_DEL = 0
        AND T.CREATED_TIME &lt;= to_date(#{overviewDate} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
        AND T.BO_BUSINESS_LINE_ID IN
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        AND T.NODE_ID = 1200
        GROUP BY T.BO_BUSINESS_LINE_ID
    </select>

    <select id="getDzAlarmTaskByBusinessLine" resultType="com.wtyt.board.bean.BoardBpAlarmTaskNumBean">
        SELECT
        a.NODE_DATA_TYPE nodeDataType,
        tt.BO_BUSINESS_LINE_ID   boBusinessLineId,
        COUNT(a.BO_TRANS_NODE_ALARM_ID) alarmNum
        FROM T_BO_TRANS_NODE_ALARM a
        left join T_BO_TRANS_TASK tt on a.BO_TRANS_TASK_ID = tt.BO_TRANS_TASK_ID
        WHERE a.IS_DEL = 0 and tt.is_del =0
        and a.ALARM_TYPE =1 and a.ALARM_PROCESS_RESULT =0
        AND EXISTS(SELECT 1 FROM T_BO_TRANS_TASK_EXTRA R WHERE R.HYB_RECEIVED_TIME IS NOT NULL AND R.BO_TRANS_TASK_ID = tt.BO_TRANS_TASK_ID AND R.IS_DEL = 0)
        AND a.NODE_DATA_TYPE IN
        <foreach collection="nodeDataTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND tt.BO_BUSINESS_LINE_ID IN
        <foreach collection="businessLineList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        group by a.NODE_DATA_TYPE,tt.BO_BUSINESS_LINE_ID
    </select>


    <select id="getDzAlarmTaskByGroupId" resultType="com.wtyt.board.bean.BoardBpAlarmTaskNumBean">
        SELECT
        a.NODE_DATA_TYPE nodeDataType,
        COUNT(distinct a.BO_TRANS_TASK_ID) alarmNum
        FROM T_BO_TRANS_NODE_ALARM a
        left join (SELECT DISTINCT R.BO_TRANS_TASK_ID FROM T_BO_TRANS_TASK_GROUP_REL R INNER  JOIN T_BO_TRANS_TASK T ON R.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID WHERE  T.IS_DEL =0 AND R.IS_DEL=0  AND R.GROUP_ID = #{groupId}) tt on a.BO_TRANS_TASK_ID = tt.BO_TRANS_TASK_ID
        left join T_BO_TRANS_TASK_EXTRA E ON   E.BO_TRANS_TASK_ID = tt.BO_TRANS_TASK_ID
        WHERE a.IS_DEL = 0 AND E.IS_DEL=0
        AND a.ALARM_TYPE =1  AND E.HYB_RECEIVED_TIME IS NOT NULL
        <if test="alarmProcessResult != null and alarmProcessResult != ''">
            AND a.ALARM_PROCESS_RESULT = #{alarmProcessResult}
        </if>
        AND a.NODE_DATA_TYPE IN
        <foreach collection="nodeDataTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by a.NODE_DATA_TYPE
    </select>


    <select id="getDispatchCarCountList" resultType="com.wtyt.board.bean.response.Req5329535OBean$DateNum">
        SELECT
        TO_CHAR(trunc(T.CREATED_TIME),'yyyy-MM-dd') AS dateStr ,count(distinct  t.BO_TRANS_TASK_ID) taskNum
        FROM T_BO_TRANS_TASK T
        left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID=E.BO_TRANS_TASK_ID
        WHERE T.IS_DEL = 0  AND E.IS_DEL=0
        AND E.HYB_RECEIVED_TIME IS NOT NULL
        AND EXISTS (SELECT 1 FROM T_BO_TRANS_TASK_GROUP_REL tgr WHERE tgr.BO_TRANS_TASK_ID=T.BO_TRANS_TASK_ID AND tgr.IS_DEL=0 AND tgr.GROUP_ID = #{groupId})
        AND T.ORG_ID = #{orgId}  AND T.TRANS_PATTERN IS NOT NULL
        AND E.DISPATCH_CAR_RECORD_ID IS NOT NULL
        <if test="startTime !=null and startTime!=''">
            <![CDATA[ AND T.CREATED_TIME >= to_date(#{startTime},'yyyy-MM-dd') ]]>
        </if>
        <if test="endTime !=null and endTime !=''">
            <![CDATA[ AND T.CREATED_TIME <= to_date(#{endTime} || '23:59:59','yyyy-MM-dd Hh24:mi:ss') ]]>
        </if>
       group by  trunc(T.CREATED_TIME)
    </select>

    <select id="getLoadingTonnageCount" resultType="com.wtyt.board.bean.BoardTaskLoadingTonnage">
        SELECT T.GOODS_AMOUNT_TYPE    goodsAmountType,
               t.SETTLE_MODE          settleMode,
                <choose>
                    <when test="progressRule !=null and progressRule != '' and progressRule == '2'.toString()">
                        NVL(SUM(T.UNLOADING_TONNAGE), 0) resultCount
                    </when>
                    <otherwise>
                        NVL(SUM(T.LOADING_TONNAGE), 0) resultCount
                    </otherwise>
                </choose>
        FROM T_BO_TRANS_TASK T
                 left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        WHERE T.IS_DEL = 0
          AND E.IS_DEL = 0
          AND E.HYB_RECEIVED_TIME IS NOT NULL
          AND EXISTS (SELECT 1
                      FROM T_BO_TRANS_TASK_GROUP_REL tgr
                      WHERE tgr.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
                        AND tgr.IS_DEL = 0
                        AND tgr.GROUP_ID = #{groupId})
          AND T.ORG_ID = #{orgId}
          AND T.TRANS_PATTERN IS NOT NULL
          AND T.SETTLE_MODE IN (1, 2)
        group by T.GOODS_AMOUNT_TYPE, t.SETTLE_MODE
    </select>

    <select id="getDepartCount" resultType="com.wtyt.board.bean.BoardTaskLoadingTonnage">
        SELECT T.SETTLE_MODE                      settleMode,
               COUNT(distinct t.BO_TRANS_TASK_ID) resultCount
        FROM T_BO_TRANS_TASK T
                 left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        WHERE T.IS_DEL = 0
          AND E.IS_DEL = 0
          AND E.HYB_RECEIVED_TIME IS NOT NULL
          AND EXISTS (SELECT 1
                      FROM T_BO_TRANS_TASK_GROUP_REL tgr
                      WHERE tgr.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
                        AND tgr.IS_DEL = 0
                        AND tgr.GROUP_ID = #{groupId})
          AND T.ORG_ID = #{orgId}
          AND T.TRANS_PATTERN IS NOT NULL
          AND T.START_TIME IS NOT NULL
          AND T.LOADING_TONNAGE >0
          AND T.SETTLE_MODE IN (1, 2)
        group by t.SETTLE_MODE
    </select>

    <select id="getCompletedCount" resultType="com.wtyt.board.bean.BoardTaskLoadingTonnage">
        SELECT T.SETTLE_MODE                      settleMode,
               COUNT(distinct t.BO_TRANS_TASK_ID) resultCount
        FROM T_BO_TRANS_TASK T
                 left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        WHERE T.IS_DEL = 0
          AND E.IS_DEL = 0
          AND E.HYB_RECEIVED_TIME IS NOT NULL
          AND EXISTS (SELECT 1
                      FROM T_BO_TRANS_TASK_GROUP_REL tgr
                      WHERE tgr.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
                        AND tgr.IS_DEL = 0
                        AND tgr.GROUP_ID = #{groupId})
          AND T.ORG_ID = #{orgId}
          AND T.TRANS_PATTERN IS NOT NULL
          AND t.STATE = 2 AND T.SETTLE_MODE IN (1, 2)
        group by t.SETTLE_MODE
    </select>


    <select id="getSettledCount" resultType="com.wtyt.board.bean.BoardTaskLoadingTonnage">
        SELECT
            COUNT(distinct t.BO_TRANS_TASK_ID) resultCount
        FROM T_BO_TRANS_TASK T
                 left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
        WHERE T.IS_DEL = 0
          AND E.IS_DEL = 0
          AND E.HYB_RECEIVED_TIME IS NOT NULL
          AND EXISTS (SELECT 1
                      FROM T_BO_TRANS_TASK_GROUP_REL tgr
                      WHERE tgr.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
                        AND tgr.IS_DEL = 0
                        AND tgr.GROUP_ID = #{groupId})
          AND T.ORG_ID = #{orgId}
          AND T.TRANS_PATTERN IS NOT NULL
          AND t.STATE = 2 and t.PAY_STATE = 2
    </select>

    <select id="getDayDepartCarNum" resultType="com.wtyt.board.bean.BoardDayDepartCar">
        SELECT
        TO_CHAR(startTime,'yyyy-MM-dd') AS dateStr,
        capacityType ,
        COUNT(distinct BO_TRANS_TASK_ID) as taskCount
        FROM (
            SELECT
                trunc(T.START_TIME) AS startTime,
                COALESCE(E.CPD_POOL_GROUP_NAME, T.CAPACITY_TYPE_NAME, '社会临调车') as capacityType,
                t.BO_TRANS_TASK_ID
            FROM T_BO_TRANS_TASK T
                     left join T_BO_TRANS_TASK_EXTRA E ON T.BO_TRANS_TASK_ID = E.BO_TRANS_TASK_ID
            WHERE T.IS_DEL = 0
              AND E.IS_DEL = 0
              AND E.HYB_RECEIVED_TIME IS NOT NULL
              AND EXISTS (SELECT 1
                          FROM T_BO_TRANS_TASK_GROUP_REL tgr
                          WHERE tgr.BO_TRANS_TASK_ID = T.BO_TRANS_TASK_ID
                            AND tgr.IS_DEL = 0
                            AND tgr.GROUP_ID = #{groupId})
              AND T.ORG_ID = #{orgId}
              AND T.TRANS_PATTERN IS NOT NULL
              AND T.START_TIME IS NOT NULL
              AND T.LOADING_TONNAGE > 0
              AND T.GOODS_AMOUNT_TYPE =#{transportUnitPriceType}
            <if test="startTime !=null and startTime!=''">
                <![CDATA[ AND T.START_TIME >= to_date(#{startTime},'yyyy-MM-dd') ]]>
            </if>
            <if test="endTime !=null and endTime !=''">
                <![CDATA[ AND T.START_TIME <= to_date(#{endTime} || '23:59:59','yyyy-MM-dd Hh24:mi:ss') ]]>
            </if>
        ) TEMP
        GROUP BY startTime,capacityType
    </select>

</mapper>
